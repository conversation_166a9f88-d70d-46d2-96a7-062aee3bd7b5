﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace IPM.Vision.Common.Converts
{
    public class HalfConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double fullValue)
            {
                double halfValue = fullValue / 2;
                if (double.TryParse(parameter?.ToString(), out double offset))
                {
                    return halfValue + offset;
                }
                return halfValue;
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class CenterXConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double canvasWidth && double.TryParse(parameter?.ToString(), out double offset))
            {
                return (canvasWidth / 2) + offset;
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class CenterYConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double canvasHeight && double.TryParse(parameter?.ToString(), out double offset))
            {
                return (canvasHeight / 2) + offset;
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class Half2Converter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is double val)
            {
                // 如果参数为 0.5，返回一半的值
                if (parameter != null && double.TryParse(parameter.ToString(), out double factor))
                {
                    return (val / 2) - (factor / 2);
                }
                return val / 2; // 默认返回一半
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
