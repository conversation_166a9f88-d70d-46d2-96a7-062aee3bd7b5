﻿using AutoMapper;
using IPM.Vision.Common;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Mappers
{
    public class AutoMapperConfiguration
    {
        private static readonly object obj = new object();
        private static AutoMapperConfiguration _instance;
        public IMapper Mapper { get; set; }
        public MapperConfiguration MapperConfiguration { get; set; }

        public static AutoMapperConfiguration Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (obj)
                    {
                        _instance = new AutoMapperConfiguration();
                    }
                }
                return _instance;
            }
        }

        private AutoMapperConfiguration() { }

        public void Init()
        {
            MapperConfiguration = new MapperConfiguration(config =>
            {
                config.CreateMap<ConfigModel, ObservableConfigModel>()
                    .ForMember(dest => dest.LogoPath, opt => opt.MapFrom(src => 
                        string.IsNullOrEmpty(src.LogoPath) ? "pack://application:,,,/Assets/logo.png" : src.LogoPath));
                config.CreateMap<ObservableConfigModel, ConfigModel>();
                config.CreateMap<ObservableUserInfoModel, UserModel>()
                    .ForMember(deist => deist.MenuId, opt => opt.MapFrom(src => string.Join(",", src.MenuId)));
                config.CreateMap<UserModel, ObservableUserInfoModel>()
                    .ForMember(deist => deist.MenuId, opt => opt.MapFrom(src => new ObservableCollection<string>(src.MenuId.Split(',').ToList())));
                config.CreateMap<CustomNameModel, ObservableCustomNameModel>();
                config.CreateMap<ObservableCustomNameModel, CustomNameModel>();
                config.CreateMap<ObservableBarcodeRuleModel, BarcodeRuleModel>();
                config.CreateMap<BarcodeRuleModel, ObservableBarcodeRuleModel>()
                    .ForMember(deist => deist.RuleTypeName, opt => opt.MapFrom(src => EnumHelper.GetEnumDescription(src.RuleType)))
                    .ForMember(deist => deist.IsRemoveName, opt => opt.MapFrom(src => EnumHelper.GetEnumDescription(src.IsRemove)));
                config.CreateMap<ObservableEquipmentModel,EquipmentModel>();
                config.CreateMap<EquipmentModel, ObservableEquipmentModel>();
                config.CreateMap<ObservableProcessModel, ProcessModel>();
                config.CreateMap<ProcessModel,ObservableProcessModel>();
                config.CreateMap<ObservableProductModel, ProductParamModel>()
                    .ForMember(deist => deist.ProcessId, opt => opt.MapFrom(src => string.Join(",", src.ProcessIds)));
                config.CreateMap<ProductParamModel, ObservableProductModel>()
                    .ForMember(deist => deist.ProcessIds, opt => opt.MapFrom(src => new ObservableCollection<string>(src.ProcessId.Split(',').ToList())));
                config.CreateMap<ObservableMainCameraParaModel,MainCameraParamModel>();
                config.CreateMap<MainCameraParamModel, ObservableMainCameraParaModel>();
                config.CreateMap<ObservableLightModel, LightParamModel>();
                config.CreateMap<LightParamModel, ObservableLightModel>();
                config.CreateMap<ObservableReportModel,ReportModel>();
                config.CreateMap<ReportModel, ObservableReportModel>();
                config.CreateMap<ObservableCompPointModel, CompPointModel>();
                config.CreateMap<CompPointModel, ObservableCompPointModel>()
                     .ForMember(deist => deist.PinTypeName, opt => opt.MapFrom(src => EnumHelper.GetEnumDescription(src.PinType)));
                config.CreateMap<ObservableCompModel, CompInfoModel>();
                config.CreateMap<CompInfoModel, ObservableCompModel>();
            });
            Mapper = MapperConfiguration.CreateMapper();
        }
    }
}
