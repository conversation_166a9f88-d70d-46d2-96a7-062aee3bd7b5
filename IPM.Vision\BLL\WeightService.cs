﻿using IPM.Vision.Common;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Markup;

namespace IPM.Vision.BLL
{
    public class WeightService
    {
        private readonly NLogHelper _logger;
        private SerialPortHelper _serialPortHelper;
        public event Action<string> WeightChangedEvent;
        public readonly ObservableGlobalState _globalState;
        private bool _isConnect;
        private SerialPortEnum _port;

        public bool IsConnect { get => _isConnect; }

        public WeightService(NLogHelper logger,ObservableGlobalState globalState)
        {
            _logger = logger;
            _globalState = globalState;
            _globalState.ConfigChanged += _globalState_ConfigChanged;
        }

        private void _globalState_ConfigChanged(ObservableConfigModel obj)
        {
            if (obj.WeighAddress == _port) return; 
            DisconnectMC();
            _port = obj.WeighAddress;
            ConnectMC();
        }

        public void ConnectMC()
        {
            try
            {
                string port = _port.ToString();
                if (_serialPortHelper == null) _serialPortHelper = new SerialPortHelper(port, 9600);
                _serialPortHelper.NotifyDataEvent += _serialPortHelper_NotifyDataEvent;
                _serialPortHelper.Open();
                _isConnect = true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                _isConnect = false;
                _serialPortHelper = null;
            }
        }

        private void _serialPortHelper_NotifyDataEvent(string obj)
        {
            if (WeightChangedEvent != null)
                WeightChangedEvent.Invoke(obj);
        }

        public void DisconnectMC()
        {

            if (_serialPortHelper != null)
            {
                _serialPortHelper.Dispose();
                _serialPortHelper = null;
            }
        }
    }
}
