﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common.DBCommon
{
    public interface IBaseRepository<T> where T : class, new()
    {
        Task<bool> Add(T model);

        Task<bool> AddRange(List<T> list);

        Task<bool> AddReturnIdentity(T model);

        Task<T> AddReturnEntity(T model);

        Task<bool> AddColumns(T model, params string[] columns);

        Task<bool> AddColumnsByIgnoreColumns(T model, params string[] IgnoreColumns);

        Task<bool> Delete<S>(S key);

        Task<bool> DeleteRange<S>(params S[] keys);

        Task<bool> DeleteWhere(Expression<System.Func<T, bool>> where);

        Task<bool> Update(T model);

        Task<bool> UpdateRange(List<T> list);

        Task<bool> Update(T model, Expression<System.Func<T, object>> expression);

        Task<bool> UpdateColumns(T model, params string[] columns);

        Task<bool> UpdateColumns(T model, Expression<System.Func<T, object>> columns);

        Task<bool> UpdateColumnsByIgnoreColumns(T model, params string[] columns);

        Task<bool> UpdateColumnsByIgnoreColumns(T model, Expression<System.Func<T, object>> columns);

        Task<bool> UpdateNotNullColumns(
          T model,
          bool ignoreAllNullColumns,
          bool isOffIdentity = false,
          bool ignoreAllDefaultValue = false);

        Task<bool> UpdateIF(T model, Dictionary<Expression<System.Func<T, object>>, bool> dic);

        Task<List<T>> getAll(
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getTakeList(
          int num,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getPageList(
          int skip,
          int take,
          Expression<System.Func<T, bool>> whereExp,
          Expression<System.Func<T, object>> orderBy,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getTakeList(
          Expression<System.Func<T, bool>> where,
          int num,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<T> getByPrimaryKey(object pkValue);

        Task<T> getFirstOrDefault(Expression<System.Func<T, bool>> where);

        Task<List<T>> getByIn<S>(
          List<S> list,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getByIn<S>(
          Expression<System.Func<T, object>> column,
          List<S> list,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getByNotIn<S>(
          List<S> list,
          object field,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getByWhere(
          Expression<System.Func<T, bool>> where,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getByWhereIF(
          bool isWhere,
          Expression<System.Func<T, bool>> where,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getByWhereIF(
          Dictionary<Expression<System.Func<T, bool>>, bool> wheres,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getByBetween(
          object value,
          object start,
          object end,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<bool> getIsAny(Expression<System.Func<T, bool>> where);

        Task<List<T>> getPageList<TResult>(
          int pageIndex,
          int pageSize,
          bool isWhere = false,
          Expression<System.Func<T, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<object> getJoinList<T1, T2>(
          Expression<Func<T1, T2, JoinQueryInfos>> joinExp,
          Expression<Func<T1, T2, object>> selectExp,
          bool isWhere = false,
          Expression<Func<T1, T2, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<Func<T1, T2, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<object> getJoinPageList<T1, T2>(
          int pageIndex,
          int pageSize,
          Expression<Func<T1, T2, JoinQueryInfos>> joinExp,
          Expression<Func<T1, T2, object>> selectExp,
          bool isWhere = false,
          Expression<Func<T1, T2, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<Func<T1, T2, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<object> getJoinList<T1, T2, T3>(
          Expression<Func<T1, T2, T3, JoinQueryInfos>> joinExp,
          Expression<Func<T1, T2, T3, object>> selectExp,
          bool isWhere = false,
          Expression<Func<T1, T2, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<Func<T1, T2, T3, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<object> getJoinList<T1, T2, T3>(
          int pageIndex,
          int pageSize,
          Expression<Func<T1, T2, T3, JoinQueryInfos>> joinExp,
          Expression<Func<T1, T2, T3, object>> selectExp,
          bool isWhere = false,
          Expression<Func<T1, T2, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<Func<T1, T2, T3, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc);

        Task<List<T>> getListBySql(string sql);

        Task<bool> ExecuteCommandSql(string sql, params SugarParameter[] parameters);

        Task<List<T>> getListBySqlQuery(string sql, params SugarParameter[] parameters);

        Task<object> getScalar(string sql, params SugarParameter[] parameters);

        Task<DataTable> UseStoredProcedure(string procedureName, params SugarParameter[] parameters);

        Task<DbResult<bool>> UseTran(Func<Task> action, Action<Exception> errorCallBack);

        Task<DbResult<S>> UseTran<S>(Func<S> func, Action<Exception> errorCallBack);
    }
}
