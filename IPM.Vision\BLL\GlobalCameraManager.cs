using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Threading.Tasks;
using System.Collections.Generic; // Added for List

namespace IPM.Vision.BLL
{
    /// <summary>
    /// 全局相机连接管理器
    /// 实现应用程序级别的相机连接管理：
    /// 1. 应用启动时建立并保持相机连接
    /// 2. 页面切换时只暂停/恢复画面流，不断开连接
    /// 3. 应用关闭时统一释放所有相机资源
    /// </summary>
    public class GlobalCameraManager : IDisposable
    {
        #region 私有字段
        private readonly OptVisionService _optVisionService;
        private readonly HKVisionService _hkVisionService;
        private readonly ObservableGlobalState _globalState;
        private readonly NLogHelper _logger;
        
        private bool _isInitialized = false;
        private bool _isDisposed = false;
        private bool _optCameraConnected = false;
        private bool _hkCameraConnected = false;
        private bool _optStreamActive = false;
        private bool _hkStreamActive = false;
        
        // 添加配置缓存，用于检测配置变化
        private CameraEnum _lastMainCameraType = CameraEnum.OPT;
        private OpenEnum _lastMarkCameraEnabled = OpenEnum.CLOSE;
        
        private readonly object _connectionLock = new object();
        private volatile bool _isCheckingConnections = false; // 防止重复检查连接
        
        public event Action<string> ConnectionStatusChanged;
        #endregion

        #region 属性
        /// <summary>
        /// OPT相机是否已连接
        /// </summary>
        public bool IsOptCameraConnected => _optCameraConnected;
        
        /// <summary>
        /// 海康相机是否已连接
        /// </summary>
        public bool IsHkCameraConnected => _hkCameraConnected;
        
        /// <summary>
        /// OPT相机流是否激活
        /// </summary>
        public bool IsOptStreamActive => _optStreamActive;
        
        /// <summary>
        /// 海康相机流是否激活
        /// </summary>
        public bool IsHkStreamActive => _hkStreamActive;
        
        /// <summary>
        /// 管理器是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;
        #endregion

        public GlobalCameraManager(OptVisionService optVisionService, HKVisionService hkVisionService, 
            ObservableGlobalState globalState, NLogHelper logger)
        {
            _optVisionService = optVisionService;
            _hkVisionService = hkVisionService;
            _globalState = globalState;
            _logger = logger;
            
            // 订阅相机连接状态变化事件
            _optVisionService.NewCameraAddEvent += OnOptCameraConnected;
            _optVisionService.CameraDisConnectedEvent += OnOptCameraDisconnected;
            _hkVisionService.CameraConnectedEvent += OnHkCameraConnected;
            _hkVisionService.DisConnectEvent += OnHkCameraDisconnected;
            
            // 订阅配置变化事件，用于检测IP地址变化
            _globalState.ConfigChanged += OnConfigurationChanged;
            
            AddLog("全局相机管理器已创建");
        }

        #region 相机连接事件处理
        private void OnOptCameraConnected()
        {
            _optCameraConnected = true;
            AddLog("OPT相机连接成功");
            ConnectionStatusChanged?.Invoke("OPT相机已连接");
        }

        private void OnOptCameraDisconnected()
        {
            _optCameraConnected = false;
            _optStreamActive = false;
            AddLog("OPT相机连接断开", true);
            ConnectionStatusChanged?.Invoke("OPT相机连接断开");
        }

        private void OnHkCameraConnected()
        {
            _hkCameraConnected = true;
            AddLog("海康相机连接成功");
            ConnectionStatusChanged?.Invoke("海康相机已连接");
        }

        private void OnHkCameraDisconnected()
        {
            _hkCameraConnected = false;
            _hkStreamActive = false;
            AddLog("海康相机连接断开", true);
            ConnectionStatusChanged?.Invoke("海康相机连接断开");
        }
        #endregion

        #region 应用程序级别的连接管理
        /// <summary>
        /// 应用程序启动时初始化所有相机连接
        /// **安全修复：全面异常处理，防止崩溃**
        /// </summary>
        public async Task InitializeCamerasAsync()
        {
            if (_isInitialized || _isDisposed)
            {
                AddLog("相机管理器已初始化或已释放，跳过初始化");
                return;
            }

            lock (_connectionLock)
            {
                if (_isInitialized) return;
                _isInitialized = true;
            }

            AddLog("=== 🚀 开始全局相机连接初始化（安全模式）===");

            try
            {
                // **安全修复：包装所有可能抛出异常的操作**
                await SafeInitializeCamerasInternalAsync();
            }
            catch (Exception ex)
            {
                AddLog($"❌ 相机初始化顶层异常: {ex.Message}", true);
                _logger?.LogError($"相机初始化崩溃异常: {ex.Message}\r\n{ex.StackTrace}");
                
                // **防止崩溃：重置状态**
                try
                {
                    _isInitialized = false;
                    _optCameraConnected = false;
                    _hkCameraConnected = false;
                }
                catch { }
            }
        }

        /// <summary>
        /// 安全的相机初始化内部实现
        /// </summary>
        private async Task SafeInitializeCamerasInternalAsync()
        {
            try
            {
                // **立即输出配置诊断信息，确保能看到配置状态**
                AddLog("=== 🔍 立即配置诊断信息 ===");
                if (_globalState?.AppConfig == null)
                {
                    AddLog("❌ 应用配置为 NULL！");
                    return;
                }
                else
                {
                    AddLog($"✅ 应用配置已加载");
                    AddLog($"  - HaveMarkCamera: {_globalState.AppConfig.HaveMarkCamera}");
                    AddLog($"  - MarkCameraAddress: '{_globalState.AppConfig.MarkCameraAddress}'");
                    AddLog($"  - MainCameraType: {_globalState.AppConfig.MainCameraType}");
                    AddLog($"  - MainCameraAddress: '{_globalState.AppConfig.MainCameraAddress}'");
                }
                AddLog("========================");

                // 检查配置是否有效
                if (_globalState.AppConfig == null)
                {
                    AddLog("应用配置未加载，跳过相机初始化", true);
                    return;
                }

                // **新增：详细的配置诊断信息**
                AddLog("=== 📋 配置诊断信息 ===");
                AddLog($"  - HaveMarkCamera: {_globalState.AppConfig.HaveMarkCamera}");
                AddLog($"  - MarkCameraAddress: {_globalState.AppConfig.MarkCameraAddress}");
                AddLog($"  - MainCameraType: {_globalState.AppConfig.MainCameraType}");
                AddLog($"  - MainCameraAddress: {_globalState.AppConfig.MainCameraAddress}");
                AddLog("========================");

                // **新增：智能检测当前相机连接状态**
                bool optAlreadyConnected = _optCameraConnected; // OPT相机连接状态由事件维护
                bool hkAlreadyConnected = _hkCameraConnected && _hkVisionService.IsConnected;

                AddLog($"📋 当前相机连接状态检查:");
                AddLog($"  - OPT相机: {(optAlreadyConnected ? "已连接" : "未连接")}");
                AddLog($"  - 海康相机: {(hkAlreadyConnected ? "已连接" : "未连接")}");

                // 根据配置决定需要初始化的相机
                bool needMainCamera = ShouldInitializeMainCamera() && !optAlreadyConnected;
                bool needMarkCamera = ShouldInitializeMarkCamera() && !hkAlreadyConnected;

                AddLog($"📋 初始化需求分析:");
                AddLog($"  - 需要初始化OPT相机: {needMainCamera}");
                AddLog($"  - 需要初始化海康相机: {needMarkCamera}");

                if (!needMainCamera && !needMarkCamera)
                {
                    AddLog("✅ 所有需要的相机都已连接，或配置中未启用任何相机，跳过相机初始化");

                    // 更新连接状态
                    if (optAlreadyConnected) _optCameraConnected = true;
                    if (hkAlreadyConnected) _hkCameraConnected = true;

                    return;
                }

                // 显示相机配置信息
                LogCameraConfiguration(needMainCamera, needMarkCamera);

                // **安全修复：串行初始化，每个阶段都有异常保护**
                if (needMainCamera)
                {
                    await SafeInitializeMainCameraAsync(needMarkCamera, hkAlreadyConnected);
                }

                if (needMarkCamera)
                {
                    await SafeInitializeMarkCameraAsync();
                }

                // 最终状态报告
                ReportInitializationResults(needMainCamera, needMarkCamera);
                
                AddLog("=== 全局相机连接初始化完成（安全模式）===");
            }
            catch (Exception ex)
            {
                AddLog($"❌ 相机初始化内部异常: {ex.Message}", true);
                _logger?.LogError($"相机初始化内部异常: {ex.Message}\r\n{ex.StackTrace}");
                throw; // 重新抛出让上层处理
            }
        }

        /// <summary>
        /// 安全的主相机初始化
        /// </summary>
        private async Task SafeInitializeMainCameraAsync(bool needMarkCamera, bool hkAlreadyConnected)
        {
            try
            {
                AddLog("🔧 第一阶段：初始化主相机（OPT）...");

                // **修复：只有在需要初始化海康相机且海康相机未连接时，才停止海康相机服务**
                if (needMarkCamera && !hkAlreadyConnected)
                {
                    await SafeStopHkCameraForOptInitAsync();
                }
                else if (hkAlreadyConnected)
                {
                    AddLog("✅ 海康相机已连接，跳过预防性停止，避免断开现有连接");
                }

                await InitializeMainCameraAsync();

                // **性能优化：大幅减少等待时间但保持必要的稳定性检查**
                if (_optCameraConnected)
                {
                    AddLog("✅ 主相机初始化成功，快速稳定检查...");
                    await Task.Delay(1000); // 从3000ms减少到1000ms
                }
                else
                {
                    AddLog("❌ 主相机初始化失败，快速状态清理...");
                    await Task.Delay(500); // 从2000ms减少到500ms
                }

                // **性能优化：减少GC清理等待时间**
                AddLog("🔄 快速设备状态清理...");
                GC.Collect();
                GC.WaitForPendingFinalizers();
                await Task.Delay(300); // 从1000ms减少到300ms
            }
            catch (Exception ex)
            {
                AddLog($"❌ 主相机初始化异常: {ex.Message}", true);
                _logger?.LogError($"主相机初始化异常: {ex.Message}\r\n{ex.StackTrace}");
                // 不重新抛出，继续后续流程
            }
        }

        /// <summary>
        /// 安全停止海康相机以便OPT初始化
        /// </summary>
        private async Task SafeStopHkCameraForOptInitAsync()
        {
            try
            {
                AddLog("📋 预防性完全停止海康相机服务，确保无设备冲突...");
                
                // 强制停止海康相机的所有活动
                _hkVisionService.StopGrap();
                await Task.Delay(300); // 从1000ms减少到300ms

                _hkVisionService.CloseCamera();
                await Task.Delay(500); // 从1000ms减少到500ms

                // **关键修复：如果海康SDK已初始化，安全释放**
                if (_hkVisionService.SDKInited)
                {
                    AddLog("📋 暂时释放海康SDK，避免设备枚举冲突...");
                    try
                    {
                        // **安全修复：使用多重保护的Dispose调用**
                        await Task.Run(() =>
                        {
                            try
                            {
                                _hkVisionService.Dispose();
                                AddLog("✅ 海康SDK已安全释放");
                            }
                            catch (AccessViolationException avEx)
                            {
                                AddLog($"⚠️ SDK释放访问违规异常: {avEx.Message}", false);
                                AddLog("SDK可能已被强制释放，继续执行");
                            }
                            catch (System.Runtime.InteropServices.SEHException sehEx)
                            {
                                AddLog($"⚠️ SDK释放SEH异常: {sehEx.Message}", false);
                                AddLog("SDK释放系统异常，继续执行");
                            }
                            catch (Exception disposeEx)
                            {
                                AddLog($"⚠️ SDK释放异常: {disposeEx.Message}", false);
                                AddLog("SDK释放失败，继续执行");
                            }
                        });
                    }
                    catch (Exception taskEx)
                    {
                        AddLog($"❌ SDK释放任务异常: {taskEx.Message}", false);
                    }
                    await Task.Delay(1000); // 从2000ms减少到1000ms
                }

                _hkCameraConnected = false;
                AddLog("✅ 海康相机服务已完全停止并释放");
                await Task.Delay(1000); // 从3000ms减少到1000ms
            }
            catch (Exception ex)
            {
                AddLog($"❌ 预防性停止海康相机异常: {ex.Message}", false);
                _logger?.LogError($"停止海康相机异常: {ex.Message}");
                // 不重新抛出，继续后续流程
            }
        }

        /// <summary>
        /// 安全的Mark相机初始化
        /// </summary>
        private async Task SafeInitializeMarkCameraAsync()
        {
            try
            {
                AddLog("🔧 第二阶段：初始化Mark点相机（海康）...");
                AddLog("⚡ 快速模式：Mark相机初始化前的系统状态检查完成，立即开始连接...");
                await InitializeMarkCameraAsync();

                // **新增：如果海康相机连接失败，执行诊断**
                if (!_hkCameraConnected)
                {
                    AddLog("⚠️ 海康相机连接失败，开始执行诊断...");
                    try
                    {
                        _hkVisionService.DiagnoseHKCameraIssues();
                    }
                    catch (Exception diagEx)
                    {
                        AddLog($"❌ 诊断执行异常: {diagEx.Message}", false);
                    }
                }

                // **性能优化：大幅减少Mark相机稳定等待时间**
                AddLog("✅ Mark相机初始化完成，快速状态确认...");
                await Task.Delay(200); // 从1000ms减少到200ms
            }
            catch (Exception ex)
            {
                AddLog($"❌ Mark相机初始化异常: {ex.Message}", true);
                _logger?.LogError($"Mark相机初始化异常: {ex.Message}\r\n{ex.StackTrace}");
                // 不重新抛出，继续后续流程
            }
        }

        /// <summary>
        /// 判断是否需要初始化主相机
        /// </summary>
        private bool ShouldInitializeMainCamera()
        {
            // **修复：不再检查IP地址，直接根据相机类型判断**
            return true; // 总是尝试初始化主相机
        }

        /// <summary>
        /// 判断是否需要初始化Mark点相机
        /// **强制修改：忽略配置，总是尝试连接海康相机**
        /// </summary>
        private bool ShouldInitializeMarkCamera()
        {
            AddLog($"🔍 强制连接模式：忽略配置，总是尝试连接海康相机");
            AddLog($"  - 原配置HaveMarkCamera值: {_globalState.AppConfig.HaveMarkCamera} (已忽略)");

            // **强制修改：忽略配置检查，总是返回true**
            AddLog($"✅ 强制启用海康相机连接，忽略配置设置");
            return true;
        }

        /// <summary>
        /// 记录相机配置信息
        /// </summary>
        private void LogCameraConfiguration(bool needMainCamera, bool needMarkCamera)
        {
            AddLog("=== 相机配置详细信息 ===");
            
            AddLog($"主相机配置状态:");
            AddLog($"  - 主相机类型: {_globalState.AppConfig.MainCameraType}");
            AddLog($"  - 是否需要初始化: {(needMainCamera ? "是" : "否")}");
            
            AddLog($"Mark相机配置状态:");
            AddLog($"  - Mark相机启用: {_globalState.AppConfig.HaveMarkCamera}");
            AddLog($"  - 是否需要初始化: {(needMarkCamera ? "是" : "否")}");
            
            AddLog("========================");
        }

        /// <summary>
        /// 初始化主相机连接（根据配置类型选择OPT或Canon）
        /// </summary>
        private async Task InitializeMainCameraAsync()
        {
            try
            {
                string cameraType = _globalState.AppConfig.MainCameraType.ToString();
                string cameraIP = _globalState.AppConfig.MainCameraAddress;
                
                AddLog($"正在初始化主相机连接... 类型: {cameraType}, IP: {cameraIP}");
                
                if (_globalState.AppConfig.MainCameraType == CameraEnum.OPT)
                {
                    await InitializeOptCameraAsync();
                }
                else if (_globalState.AppConfig.MainCameraType == CameraEnum.Canon)
                {
                    await InitializeCanonCameraAsync();
                }
                else
                {
                    AddLog($"不支持的主相机类型: {cameraType}", true);
                }
            }
            catch (Exception ex)
            {
                AddLog($"主相机初始化异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 初始化Mark点相机连接（海康相机）
        /// </summary>
        private async Task InitializeMarkCameraAsync()
        {
            try
            {
                string cameraIP = _globalState.AppConfig.MarkCameraAddress;
                AddLog($"正在初始化Mark点相机连接... IP: {cameraIP}");
                
                await InitializeHkCameraAsync();
            }
            catch (Exception ex)
            {
                AddLog($"Mark点相机初始化异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 初始化OPT相机连接（增强版）
        /// **重要修复：改为通过设备名称前缀连接，不再依赖IP地址**
        /// </summary>
        private async Task InitializeOptCameraAsync()
        {
            const int MAX_INIT_RETRIES = 3;
            int retryCount = 0;

            while (retryCount < MAX_INIT_RETRIES)
            {
                try
                {
                    AddLog($"开始连接OPT相机 (第{retryCount + 1}次尝试)");

                    await Task.Run(() =>
                    {
                        // 如果是重试，先进行更彻底的清理
                        if (retryCount > 0)
                        {
                            AddLog("重试前进行OPT相机资源清理...");
                            try
                            {
                                _optVisionService.Release();
                                System.Threading.Thread.Sleep(3000); // 增加等待时间
                            }
                            catch { }
                        }

                        // **重要修复：移除强制断开海康相机的逻辑，避免意外断开已连接的海康相机**
                        // 注释掉原有的强制断开逻辑，因为这会导致已连接的海康相机被意外断开
                        /*
                        if (retryCount == 1 && _hkCameraConnected)
                        {
                            AddLog("⚠️ 检测到OPT相机连接冲突，强制断开海康相机以释放设备资源...");
                            // 这个逻辑已被移除，因为会导致海康相机意外断开
                        }
                        */

                        if (retryCount > 0)
                        {
                            AddLog("🔄 OPT相机重试中，保持海康相机连接不变...");
                        }

                        // 初始化SDK
                        _optVisionService.InitSDK();
                        AddLog("OPT SDK初始化完成");

                        // 等待SDK完全初始化，重试时增加等待时间
                        int sdkWaitTime = retryCount > 0 ? 5000 : 2000;
                        AddLog($"等待SDK稳定... ({sdkWaitTime}ms)");
                        System.Threading.Thread.Sleep(sdkWaitTime);

                        // 连接相机（通过设备名称前缀）
                        _optVisionService.RefreshCamera();

                        // 等待连接状态稳定，重试时增加等待时间
                        int maxWaitCycles = retryCount > 0 ? 50 : 30; // 重试时最多等待5秒
                        for (int i = 0; i < maxWaitCycles; i++)
                        {
                            if (_optVisionService.IsConnect)
                            {
                                break;
                            }
                            System.Threading.Thread.Sleep(100);
                        }
                    });

                    if (_optVisionService.IsConnect)
                    {
                        AddLog($"✓ OPT相机连接成功");
                        // 连接成功后，事件处理器会自动设置_optCameraConnected = true
                        return; // 成功连接，退出重试循环
                    }
                    else
                    {
                        AddLog($"✗ OPT相机连接失败 (第{retryCount + 1}次尝试)", true);
                        retryCount++;

                        if (retryCount < MAX_INIT_RETRIES)
                        {
                            int retryDelay = 2000 * retryCount; // 逐渐增加重试间隔
                            AddLog($"将在 {retryDelay/1000} 秒后重试...");
                            await Task.Delay(retryDelay);
                        }
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"OPT相机初始化异常 (第{retryCount + 1}次尝试): {ex.Message}", true);
                    retryCount++;

                    if (retryCount < MAX_INIT_RETRIES)
                    {
                        int retryDelay = 2000 * retryCount;
                        AddLog($"将在 {retryDelay/1000} 秒后重试...");
                        await Task.Delay(retryDelay);
                    }
                }
            }

            // 所有重试都失败
            _optCameraConnected = false;
            AddLog($"✗ OPT相机连接最终失败，已尝试 {MAX_INIT_RETRIES} 次", true);
            
            // **新增：提供专门的诊断建议**
            AddLog("🔧 OPT相机连接失败，建议执行以下操作：");
            AddLog("  1. 重启OPT相机设备（断电重启）");
            AddLog("  2. 重启计算机释放所有设备句柄");
            AddLog("  3. 检查是否有其他程序占用OPT相机");
            AddLog("  4. 联系技术支持进行进一步诊断");
        }

        /// <summary>
        /// 初始化Canon相机连接（通过IP地址）
        /// </summary>
        private async Task InitializeCanonCameraAsync()
        {
            try
            {
                string targetIP = _globalState.AppConfig.MainCameraAddress;
                AddLog($"开始连接Canon相机，目标IP: {targetIP}");
                
                await Task.Run(() =>
                {
                    // Canon相机初始化逻辑
                    // 注意：Canon相机通常通过USB连接，IP连接需要特殊处理
                    AddLog("Canon相机通过IP连接功能待实现", true);
                });
            }
            catch (Exception ex)
            {
                AddLog($"Canon相机初始化异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 初始化海康相机连接（增强版）
        /// **修复：恢复关键等待时间，确保连接稳定性**
        /// </summary>
        private async Task InitializeHkCameraAsync()
        {
            // **强制修改：移除配置检查，强制连接海康相机**
            AddLog("🔧 强制连接模式：忽略配置，开始连接海康相机");

            const int MAX_INIT_RETRIES = 2; // 保持重试次数不变
            int retryCount = 0;

            while (retryCount < MAX_INIT_RETRIES)
            {
                try
                {
                    AddLog($"开始连接海康相机 (第{retryCount + 1}次尝试)");

                    await Task.Run(() =>
                    {
                        // **修复：恢复重试清理时间，确保资源完全释放**
                        if (retryCount > 0)
                        {
                            AddLog("重试前进行海康相机资源清理...");
                            try
                            {
                                _hkVisionService.CloseCamera();
                                System.Threading.Thread.Sleep(800); // 恢复到800ms，确保清理完成
                            }
                            catch { }
                        }

                        // **修复：确保SDK初始化有足够时间**
                        if (!_hkVisionService.SDKInited)
                        {
                            _hkVisionService.InitSDK();
                            AddLog("海康相机SDK初始化完成");
                            
                            // **修复：恢复SDK初始化等待时间**
                            System.Threading.Thread.Sleep(1000); // 恢复到1000ms，确保SDK完全初始化
                        }
                        else
                        {
                            AddLog("海康相机SDK已初始化，跳过重复初始化");
                            // **修复：即使SDK已初始化，也需要等待一下确保稳定**
                            System.Threading.Thread.Sleep(300);
                        }

                        // **修复：连接前增加稳定等待**
                        AddLog("🔄 开始海康相机连接...");
                        _hkVisionService.RefreshAndConnect();

                        // **修复：恢复连接验证时间，确保连接真正成功**
                        AddLog("⏳ 等待海康相机连接状态稳定...");
                        bool connectionVerified = false;
                        
                        // **第一阶段：基本连接检查（前2秒）**
                        for (int i = 0; i < 20; i++) // 增加到20次，每次100ms
                        {
                            if (_hkVisionService.IsConnected)
                            {
                                AddLog($"✅ 海康相机基本连接成功 (耗时{(i + 1) * 100}ms)");
                                connectionVerified = true;
                                break;
                            }
                            System.Threading.Thread.Sleep(100);
                        }

                        // **第二阶段：如果基本连接失败，进行深度检查（最多3秒）**
                        if (!connectionVerified)
                        {
                            AddLog("⏳ 基本连接检查未通过，进行深度验证...");
                            for (int i = 0; i < 30; i++) // 30次，总共3秒
                            {
                                if (_hkVisionService.IsConnected)
                                {
                                    // **使用快速连接检查验证**
                                    if (i > 5 && _hkVisionService.QuickConnectionCheck()) // 500ms后开始快速检查
                                    {
                                        AddLog($"✅ 海康相机深度验证通过 (总耗时{2000 + (i + 1) * 100}ms)");
                                        connectionVerified = true;
                                        break;
                                    }
                                    else if (i > 15) // 1.5秒后如果基本连接成功就接受
                                    {
                                        AddLog($"✅ 海康相机连接基本成功 (总耗时{2000 + (i + 1) * 100}ms)");
                                        connectionVerified = true;
                                        break;
                                    }
                                }
                                System.Threading.Thread.Sleep(100);
                            }
                        }

                        // **修复：根据验证结果调整稳定等待时间**
                        if (connectionVerified)
                        {
                            AddLog("✅ 连接验证通过，等待状态稳定...");
                            System.Threading.Thread.Sleep(500); // 恢复到500ms，确保状态稳定
                        }
                        else if (_hkVisionService.IsConnected)
                        {
                            AddLog("⚠️ 基本连接成功但验证未完全通过，等待状态稳定...");
                            System.Threading.Thread.Sleep(800); // 适度等待
                        }
                    });

                    // **修复：更可靠的连接状态检查**
                    bool isReallyConnected = _hkVisionService.IsConnected;

                    AddLog($"海康相机连接状态检查: IsConnected={isReallyConnected}");

                    if (isReallyConnected)
                    {
                        AddLog($"✅ 海康相机连接成功 (第{retryCount + 1}次尝试)");

                        // **修复：恢复连接成功后的等待时间**
                        await Task.Delay(800); // 恢复到800ms，确保事件处理器有时间更新状态

                        // 连接成功后，事件处理器会自动设置_hkCameraConnected = true
                        return; // 成功连接，退出重试循环
                    }
                    else
                    {
                        AddLog($"❌ 海康相机连接失败 (第{retryCount + 1}次尝试)", true);
                        retryCount++;

                        if (retryCount < MAX_INIT_RETRIES)
                        {
                            AddLog("⏳ 将在 3 秒后重试..."); // 恢复到3秒
                            await Task.Delay(3000);
                        }
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"❌ 海康相机初始化异常 (第{retryCount + 1}次尝试): {ex.Message}", true);
                    retryCount++;

                    if (retryCount < MAX_INIT_RETRIES)
                    {
                        AddLog("⏳ 将在 2 秒后重试..."); 
                        await Task.Delay(2000);
                    }
                }
            }

            // 所有重试都失败
            _hkCameraConnected = false;
            AddLog($"❌ 海康相机连接最终失败，已尝试 {MAX_INIT_RETRIES} 次", true);
            
            // **增加详细的诊断信息**
            AddLog("🔧 连接失败详细诊断：");
            AddLog($"  - SDK初始化状态: {_hkVisionService.SDKInited}");
            AddLog($"  - 设备数量: {_hkVisionService.DeviceCount}");
            AddLog($"  - 当前设备状态: {(_hkVisionService.IsConnected ? "已连接" : "未连接")}");
            
            AddLog("💡 解决建议：");
            AddLog("  1. 以管理员权限重新运行程序");
            AddLog("  2. 检查海康相机驱动是否正确安装");
            AddLog("  3. 确认相机未被其他程序占用");
            AddLog("  4. 重启相机设备后重试");
            AddLog("  5. 检查网络连接和IP地址配置");
        }
        #endregion

        #region 画面流管理（页面切换时调用）
        /// <summary>
        /// 开始主相机实时画面流（OPT相机）
        /// </summary>
        public async Task StartOptStreamAsync()
        {
            if (!_optCameraConnected)
            {
                AddLog($"主相机未连接，无法开始画面流", true);
                return;
            }

            if (_optStreamActive)
            {
                AddLog($"主相机画面流已激活");
                return;
            }

            try
            {
                AddLog($"开始主相机实时画面流...");
                
                await Task.Run(() =>
                {
                    // 检查连接状态，如果意外断开，尝试快速恢复
                    if (!_optVisionService.IsConnect)
                    {
                        AddLog($"主相机连接意外断开，尝试恢复...");
                        _optVisionService.RefreshCamera();
                        System.Threading.Thread.Sleep(2000);
                    }
                });
                
                _optStreamActive = _optVisionService.IsConnect;
                AddLog($"主相机画面流状态: {(_optStreamActive ? "已开始" : "开始失败")}");
            }
            catch (Exception ex)
            {
                AddLog($"开始主相机画面流异常: {ex.Message}", true);
                _optStreamActive = false;
            }
        }

        /// <summary>
        /// 暂停主相机实时画面流（OPT相机）
        /// </summary>
        public void PauseOptStream()
        {
            if (!_optStreamActive)
            {
                AddLog($"主相机画面流未激活");
                return;
            }

            try
            {
                AddLog($"暂停主相机实时画面流...");
                
                // 注意：这里不调用CloseCamera或Release，只是暂停画面显示
                // 保持连接状态，只停止画面流
                _optStreamActive = false;
                
                AddLog($"主相机画面流已暂停（连接保持）");
            }
            catch (Exception ex)
            {
                AddLog($"暂停主相机画面流异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 开始Mark相机实时画面流（海康相机）
        /// **性能优化：大幅提升画面流启动速度**
        /// </summary>
        public async Task StartHkStreamAsync()
        {
            if (!_hkCameraConnected)
            {
                AddLog($"Mark相机未连接，无法开始画面流", true);
                return;
            }

            if (_hkStreamActive)
            {
                AddLog($"Mark相机画面流已激活，跳过重复启动");
                return;
            }

            try
            {
                AddLog($"⚡ 快速启动Mark相机实时画面流...");

                await Task.Run(() =>
                {
                    // **性能优化：快速停止可能存在的采集**
                    try
                    {
                        AddLog($"🛑 快速停止可能存在的采集...");
                        _hkVisionService.StopGrap();
                        System.Threading.Thread.Sleep(200); // 从500ms减少到200ms
                    }
                    catch (Exception stopEx)
                    {
                        AddLog($"⚠️ 停止采集时异常（可忽略）: {stopEx.Message}");
                    }

                    // **性能优化：更快速的连接状态检查**
                    if (!_hkVisionService.IsConnected)
                    {
                        AddLog($"🔧 Mark相机连接意外断开，快速恢复...");
                        _hkVisionService.RefreshAndConnect();
                        
                        // **性能优化：减少连接恢复等待时间**
                        for (int i = 0; i < 10; i++) // 最多等待1秒
                        {
                            if (_hkVisionService.IsConnected)
                            {
                                AddLog($"✅ Mark相机连接快速恢复成功");
                                break;
                            }
                            System.Threading.Thread.Sleep(100);
                        }
                    }

                    // **性能优化：立即开始采集，减少准备时间**
                    AddLog($"▶️ 立即开始Mark相机采集...");
                    _hkVisionService.StartGrap();
                    
                    // **性能优化：使用快速验证代替长等待**
                    bool grabStarted = false;
                    for (int i = 0; i < 5; i++) // 最多等待500ms
                    {
                        if (_hkVisionService.IsGrabbing)
                        {
                            AddLog($"✅ Mark相机采集快速启动成功 (耗时{(i + 1) * 100}ms)");
                            grabStarted = true;
                            break;
                        }
                        System.Threading.Thread.Sleep(100);
                    }
                    
                    if (!grabStarted)
                    {
                        AddLog($"⚠️ 采集启动验证超时，但继续执行（可能仍在启动中）");
                    }
                });

                // **性能优化：更快速准确的状态判断**
                bool isStreamStarted = _hkVisionService.IsConnected;
                _hkStreamActive = isStreamStarted;

                AddLog($"✅ Mark相机画面流状态: {(isStreamStarted ? "已快速启动" : "启动失败")}");

                if (!isStreamStarted)
                {
                    AddLog($"❌ Mark相机画面流启动失败，请检查相机连接状态", true);
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 开始Mark相机画面流异常: {ex.Message}", true);
                _hkStreamActive = false;
            }
        }

        /// <summary>
        /// 暂停Mark相机实时画面流（海康相机）
        /// </summary>
        public void PauseHkStream()
        {
            try
            {
                AddLog($"🔄 暂停Mark相机实时画面流...");

                // **修复：无论当前状态如何，都尝试停止采集**
                if (_hkVisionService != null)
                {
                    _hkVisionService.StopGrap();
                    AddLog($"🛑 已调用StopGrap停止Mark相机采集");
                }

                _hkStreamActive = false;
                AddLog($"✅ Mark相机画面流已暂停（连接保持）");
            }
            catch (Exception ex)
            {
                AddLog($"❌ 暂停Mark相机画面流异常: {ex.Message}", true);
                // **确保状态正确设置**
                _hkStreamActive = false;
            }
        }
        #endregion

        #region 应用程序关闭时的资源释放
        /// <summary>
        /// 应用程序关闭时彻底释放所有相机资源
        /// </summary>
        public void ReleaseAllCameraResources()
        {
            if (_isDisposed)
            {
                AddLog("全局相机管理器已释放");
                return;
            }

            AddLog("=== 开始释放所有相机资源 ===");

            try
            {
                // 先停止所有画面流
                if (_optStreamActive)
                {
                    PauseOptStream();
                }

                if (_hkStreamActive)
                {
                    PauseHkStream();
                }

                // 等待流停止
                System.Threading.Thread.Sleep(1000);

                // 然后彻底断开连接并释放资源
                var releaseTasks = new List<Task>();
                
                if (_optCameraConnected)
                {
                    releaseTasks.Add(Task.Run(() =>
                    {
                        try
                        {
                            AddLog("正在释放OPT相机资源...");
                            _optVisionService.Release();
                            _optCameraConnected = false;
                            AddLog("OPT相机资源已释放");
                        }
                        catch (Exception ex)
                        {
                            AddLog($"释放OPT相机资源异常: {ex.Message}", true);
                        }
                    }));
                }

                if (_hkCameraConnected)
                {
                    releaseTasks.Add(Task.Run(() =>
                    {
                        try
                        {
                            AddLog("正在释放海康相机资源...");
                            
                            // **安全修复：先关闭相机**
                            try
                            {
                                _hkVisionService.CloseCamera();
                                AddLog("海康相机已关闭");
                            }
                            catch (Exception closeEx)
                            {
                                AddLog($"关闭海康相机异常: {closeEx.Message}", false);
                            }
                            
                            // **安全修复：安全释放SDK**
                            try
                            {
                                _hkVisionService.Dispose();
                                AddLog("海康相机SDK已释放");
                            }
                            catch (AccessViolationException avEx)
                            {
                                AddLog($"⚠️ 海康SDK释放访问违规异常: {avEx.Message}", false);
                                AddLog("SDK可能已被系统强制释放");
                            }
                            catch (System.Runtime.InteropServices.SEHException sehEx)
                            {
                                AddLog($"⚠️ 海康SDK释放SEH异常: {sehEx.Message}", false);
                                AddLog("SDK释放系统级异常");
                            }
                            catch (Exception disposeEx)
                            {
                                AddLog($"⚠️ 海康SDK释放异常: {disposeEx.Message}", false);
                            }
                            
                            _hkCameraConnected = false;
                            AddLog("海康相机资源已释放");
                        }
                        catch (Exception ex)
                        {
                            AddLog($"释放海康相机资源容器异常: {ex.Message}", true);
                            // **确保状态重置**
                            _hkCameraConnected = false;
                        }
                    }));
                }

                // 等待所有释放任务完成，最多等待5秒
                if (releaseTasks.Count > 0)
                {
                    Task.WaitAll(releaseTasks.ToArray(), TimeSpan.FromSeconds(5));
                }

                AddLog("=== 所有相机资源释放完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"释放相机资源总体异常: {ex.Message}", true);
            }
            finally
            {
                // 重置状态
                _optCameraConnected = false;
                _hkCameraConnected = false;
                _optStreamActive = false;
                _hkStreamActive = false;
            }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _isDisposed = true;
                
                // 取消事件订阅
                try
                {
                    _optVisionService.NewCameraAddEvent -= OnOptCameraConnected;
                    _optVisionService.CameraDisConnectedEvent -= OnOptCameraDisconnected;
                    _hkVisionService.CameraConnectedEvent -= OnHkCameraConnected;
                    _hkVisionService.DisConnectEvent -= OnHkCameraDisconnected;
                    _globalState.ConfigChanged -= OnConfigurationChanged; // 取消配置变化事件订阅
                }
                catch (Exception ex)
                {
                    AddLog($"取消事件订阅异常: {ex.Message}", false);
                }
                
                ReleaseAllCameraResources();
            }
        }
        #endregion

        #region 状态检查和恢复
        /// <summary>
        /// 检查并恢复相机连接状态（智能检测配置变化）
        /// **性能优化：快速检查和恢复连接状态**
        /// </summary>
        public async Task CheckAndRecoverConnectionsAsync()
        {
            if (_isDisposed) return;

            // 防止重复调用
            if (_isCheckingConnections)
            {
                return; // 静默跳过重复请求，不记录日志
            }

            try
            {
                _isCheckingConnections = true;
                AddLog("⚡ 快速检查相机连接状态和配置变化...");

                // **性能优化：快速检查配置是否发生变化**
                bool configChanged = CheckConfigurationChanges();
                
                if (configChanged)
                {
                    AddLog("检测到相机配置变化，需要重新连接相机");
                    
                    // 如果配置发生变化，直接触发配置变化处理流程
                    await HandleConfigurationChangeAsync(_globalState.AppConfig);
                    return;
                }

                // **性能优化：并行检查连接状态，而不是串行**
                await CheckAndRecoverExistingConnectionsAsync();
            }
            catch (Exception ex)
            {
                AddLog($"检查连接状态异常: {ex.Message}", true);
            }
            finally
            {
                _isCheckingConnections = false;
            }
        }

        /// <summary>
        /// 检查配置是否发生变化
        /// </summary>
        private bool CheckConfigurationChanges()
        {
            if (_globalState.AppConfig == null) return false;

            return HasCameraConfigurationChanged(_globalState.AppConfig);
        }

        /// <summary>
        /// 检查并恢复现有连接（配置未变化的情况）
        /// **性能优化：并行检查和恢复多个相机连接**
        /// </summary>
        private async Task CheckAndRecoverExistingConnectionsAsync()
        {
            // **性能优化：并行检查连接状态**
            bool optNeedsReconnect = ShouldReconnectOptCamera();
            bool hkNeedsReconnect = ShouldReconnectHkCamera();

            var recoveryTasks = new List<Task>();

            if (optNeedsReconnect)
            {
                AddLog($"⚡ 主相机连接异常，快速恢复中 (IP: {_globalState.AppConfig?.MainCameraAddress})...");
                recoveryTasks.Add(InitializeOptCameraAsync());
            }

            // **强制修改：忽略配置，总是尝试重连海康相机**
            if (hkNeedsReconnect)
            {
                AddLog($"🔧 强制连接模式：海康相机连接异常，快速恢复中 (IP: {_globalState.AppConfig?.MarkCameraAddress})...");
                recoveryTasks.Add(InitializeHkCameraAsync());
            }

            // **性能优化：并行执行恢复任务**
            if (recoveryTasks.Count > 0)
            {
                await Task.WhenAll(recoveryTasks);
                AddLog("⚡ 相机连接快速恢复完成");
            }
            else
            {
                AddLog("✅ 相机连接状态正常，无需重连");
            }
        }

        /// <summary>
        /// 判断是否需要重连主相机
        /// </summary>
        private bool ShouldReconnectOptCamera()
        {
            // 如果应该有主相机连接但实际未连接
            bool shouldHaveMainCamera = ShouldInitializeMainCamera();
            bool actuallyConnected = _optCameraConnected && _optVisionService.IsConnect;
            
            return shouldHaveMainCamera && !actuallyConnected;
        }

        /// <summary>
        /// 判断是否需要重连Mark相机
        /// </summary>
        private bool ShouldReconnectHkCamera()
        {
            // 如果应该有Mark相机连接但实际未连接
            bool shouldHaveMarkCamera = ShouldInitializeMarkCamera();
            bool actuallyConnected = _hkCameraConnected && _hkVisionService.IsConnected;

            return shouldHaveMarkCamera && !actuallyConnected;
        }
        #endregion

        #region 调试和诊断方法
        /// <summary>
        /// 输出当前相机连接状态的详细诊断信息
        /// </summary>
        public void DiagnosteCameraStatus()
        {
            AddLog("=== 相机连接状态诊断 ===");
            
            try
            {
                // 配置信息
                AddLog("配置信息:");
                AddLog($"  主相机类型: {_globalState.AppConfig?.MainCameraType}");
                AddLog($"  Mark相机启用: {_globalState.AppConfig?.HaveMarkCamera}");
                
                // 连接状态
                AddLog("连接状态:");
                AddLog($"  OPT相机连接状态: {(_optCameraConnected ? "已连接" : "未连接")}");
                AddLog($"  OPT相机流状态: {(_optStreamActive ? "激活" : "未激活")}");
                AddLog($"  海康相机连接状态: {(_hkCameraConnected ? "已连接" : "未连接")}");
                AddLog($"  海康相机流状态: {(_hkStreamActive ? "激活" : "未激活")}");
                
                // 服务状态
                if (_optVisionService != null)
                {
                    AddLog($"  OPT服务连接状态: {_optVisionService.IsConnect}");
                    AddLog($"  OPT设备数量: {_optVisionService.GetDeviceCount}");
                }
                
                if (_hkVisionService != null)
                {
                    AddLog($"  海康服务设备数量: {_hkVisionService.DeviceCount}");
                    AddLog($"  海康服务连接状态: {_hkVisionService.IsConnected}");
                    AddLog($"  海康SDK初始化状态: {_hkVisionService.SDKInited}");
                }
                
                // 配置缓存
                AddLog("配置缓存:");
                AddLog($"  缓存主相机类型: {_lastMainCameraType}");
                AddLog($"  缓存Mark相机启用: {_lastMarkCameraEnabled}");
                
                AddLog("=========================");
            }
            catch (Exception ex)
            {
                AddLog($"诊断过程异常: {ex.Message}", true);
            }
            
            // 添加配置建议
            try
            {
                AddLog("=== 配置建议 ===");
                
                // 检查主相机配置
                if (_globalState.AppConfig?.MainCameraType == CameraEnum.OPT)
                {
                    if (_optCameraConnected)
                    {
                        AddLog("✓ OPT主相机配置正确且连接成功");
                    }
                    else
                    {
                        AddLog("✗ OPT主相机未连接，请检查：");
                        AddLog("  1. 设备是否正确连接");
                        AddLog("  2. 网络连接是否正常");
                        AddLog("  3. 相机是否被其他程序占用");
                    }
                }
                
                // **强制修改：忽略配置，总是检查海康相机状态**
                AddLog("🔧 强制连接模式：检查海康相机状态（忽略配置）");
                if (_hkCameraConnected)
                {
                    AddLog("✓ 海康相机连接成功");
                }
                else
                {
                    AddLog("⚠️ 海康相机连接失败");
                    AddLog("  可能原因:");
                    AddLog("  1. 海康相机设备未连接或IP地址错误");
                    AddLog("  2. 海康相机驱动未安装");
                    AddLog("  3. 相机被其他程序占用");
                }
                
                AddLog("================");
            }
            catch (Exception ex)
            {
                AddLog($"配置建议生成异常: {ex.Message}", false);
            }
        }
        
        /// <summary>
        /// 强制重新连接所有相机（用于调试）
        /// </summary>
        public async Task ForceReconnectAllCamerasAsync()
        {
            AddLog("=== 强制重连所有相机 ===");
            
            try
            {
                // 先释放现有连接
                ReleaseAllCameraResources();
                
                // 等待资源释放
                await Task.Delay(3000);
                
                // 重置初始化状态
                _isInitialized = false;
                
                // 重新初始化
                await InitializeCamerasAsync();
                
                AddLog("=== 强制重连完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"强制重连异常: {ex.Message}", true);
            }
        }
        
        /// <summary>
        /// 强制重置OPT相机连接（专门解决设备占用问题）
        /// </summary>
        public async Task ForceResetOptCameraAsync()
        {
            AddLog("=== 开始强制重置OPT相机连接 ===");
            
            try
            {
                // 1. 完全释放OPT相机资源
                if (_optCameraConnected)
                {
                    AddLog("正在释放当前OPT相机连接...");
                    try
                    {
                        _optVisionService.Release();
                        _optCameraConnected = false;
                        _optStreamActive = false;
                        AddLog("OPT相机资源已释放");
                    }
                    catch (Exception ex)
                    {
                        AddLog($"释放OPT相机资源异常: {ex.Message}", false);
                    }
                }
                
                // 2. 等待设备状态完全重置
                AddLog("等待设备状态重置...");
                await Task.Delay(5000);
                
                // 3. 尝试重新连接
                AddLog("开始重新连接OPT相机...");
                await InitializeOptCameraAsync();
                
                if (_optCameraConnected)
                {
                    AddLog($"✓ OPT相机强制重置成功！IP: {_globalState.AppConfig?.MainCameraAddress}");
                    ConnectionStatusChanged?.Invoke($"OPT相机强制重置成功，IP: {_globalState.AppConfig?.MainCameraAddress}");
                }
                else
                {
                    AddLog($"✗ OPT相机强制重置失败，IP: {_globalState.AppConfig?.MainCameraAddress}", true);
                    
                    // 提供详细的故障排除建议
                    AddLog("=== 故障排除建议 ===");
                    AddLog("1. 检查相机电源和网络连接");
                    AddLog("2. 确认IP地址配置正确");
                    AddLog("3. 检查防火墙设置");
                    AddLog("4. 尝试重启相机设备");
                    AddLog("5. 检查是否有其他程序占用相机");
                    AddLog("6. 验证OPT SDK驱动是否正确安装");
                    AddLog("===================");
                }
                
                AddLog("=== OPT相机强制重置完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"强制重置OPT相机异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 强制诊断并修复OPT相机连接问题（专门解决设备占用问题）
        /// **新增功能：全面诊断和解决设备冲突**
        /// </summary>
        public async Task DiagnoseAndFixOptCameraAsync()
        {
            AddLog("=== 🔧 开始强制诊断并修复OPT相机连接问题 ===");
            
            try
            {
                // 1. 执行OPT相机内部诊断
                AddLog("📋 第一步：执行OPT相机内部诊断...");
                _optVisionService.DiagnoseOptCameraIssues();
                
                // 2. 检查海康相机是否影响OPT相机
                AddLog("📋 第二步：检查海康相机影响...");
                if (_hkCameraConnected)
                {
                    AddLog("ℹ️ 检测到海康相机已连接");
                    AddLog("✅ 保持海康相机连接不变，避免意外断开用户正在使用的相机");
                    // **重要修复：移除强制断开海康相机的逻辑**
                    // 原有的强制断开逻辑会导致用户正在使用的海康相机被意外断开
                }
                else
                {
                    AddLog("✅ 海康相机未连接，无冲突影响");
                }
                
                // 3. 强制重置OPT相机
                AddLog("📋 第三步：强制重置OPT相机连接...");
                await ForceResetOptCameraAsync();
                
                // 4. 如果OPT相机连接成功，检查海康相机状态
                if (_optCameraConnected)
                {
                    AddLog("✅ OPT相机连接成功！");

                    // **强制修改：忽略配置，总是检查海康相机状态**
                    AddLog("📋 第四步：检查海康相机状态（强制连接模式）...");

                    if (_hkCameraConnected)
                    {
                        AddLog("✅ 海康相机仍然连接正常，无需重新连接");
                    }
                    else
                    {
                        AddLog("📋 海康相机未连接，尝试连接...");
                        await Task.Delay(5000); // 等待OPT相机完全稳定

                        try
                        {
                            await InitializeHkCameraAsync();
                            if (_hkCameraConnected)
                            {
                                AddLog("✅ 海康相机连接成功！");
                            }
                            else
                            {
                                AddLog("⚠️ 海康相机连接失败，但OPT主相机已正常工作");
                            }
                        }
                        catch (Exception ex)
                        {
                            AddLog($"海康相机连接异常: {ex.Message}", true);
                        }
                    }
                }
                else
                {
                    AddLog("❌ OPT相机连接仍然失败");
                    
                    // 提供详细的故障排除建议
                    AddLog("=== 🔧 进一步故障排除建议 ===");
                    AddLog("1. 【硬件检查】");
                    AddLog("   - 检查OPT相机电源指示灯是否正常");
                    AddLog("   - 检查网线连接是否牢固");
                    AddLog("   - 尝试更换网线或网口");
                    
                    AddLog("2. 【网络检查】");
                    AddLog($"   - 使用ping命令测试: ping {_globalState.AppConfig?.MainCameraAddress}");
                    AddLog("   - 检查IP地址配置是否正确");
                    AddLog("   - 确认相机和电脑在同一网段");
                    
                    AddLog("3. 【软件检查】");
                    AddLog("   - 关闭所有相机相关的软件（包括海康客户端）");
                    AddLog("   - 以管理员权限重新运行本程序");
                    AddLog("   - 重启计算机释放所有设备句柄");
                    
                    AddLog("4. 【驱动检查】");
                    AddLog("   - 检查OPT相机驱动是否正确安装");
                    AddLog("   - 更新网卡驱动程序");
                    AddLog("   - 确认防火墙未阻止GigE连接");
                    
                    AddLog("===========================================");
                }
                
                AddLog("=== 🔧 OPT相机诊断和修复完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"诊断和修复过程异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 应急海康相机连接方法 - 忽略所有优化，使用最保守的连接方式
        /// **用于解决连接问题的紧急修复 - 全面异常保护**
        /// </summary>
        public async Task EmergencyHkCameraConnectionAsync()
        {
            AddLog("=== 🚨 应急海康相机连接开始 ===");
            
            try
            {
                // **安全修复：包装所有操作在安全的异常处理中**
                await SafeEmergencyConnectionAsync();
            }
            catch (Exception ex)
            {
                AddLog($"❌ 应急连接顶层异常: {ex.Message}", true);
                _logger?.LogError($"应急连接崩溃异常: {ex.Message}\r\n{ex.StackTrace}");
                
                // **防止崩溃：重置状态**
                try
                {
                    _hkCameraConnected = false;
                }
                catch { }
            }
            finally
            {
                AddLog("=== 🚨 应急海康相机连接结束 ===");
            }
        }

        /// <summary>
        /// 安全的应急连接内部实现
        /// </summary>
        private async Task SafeEmergencyConnectionAsync()
        {
            try
            {
                // 1. 完全清理现有连接
                try
                {
                    AddLog("🔄 完全清理现有海康相机连接...");
                    
                    try { _hkVisionService.StopGrap(); } catch { }
                    await Task.Delay(1000);
                    
                    try { _hkVisionService.CloseCamera(); } catch { }
                    await Task.Delay(1000);
                    
                    if (_hkVisionService.SDKInited)
                    {
                        await SafeDisposeHkVisionServiceAsync();
                        await Task.Delay(2000);
                    }
                    
                    _hkCameraConnected = false;
                    AddLog("✅ 海康相机资源完全清理完成");
                }
                catch (Exception ex)
                {
                    AddLog($"❌ 清理异常: {ex.Message}", false);
                }

                // 2. 重新初始化SDK
                try
                {
                    AddLog("🔄 重新初始化海康相机SDK...");
                    bool sdkResult = _hkVisionService.InitSDK();
                    if (!sdkResult)
                    {
                        AddLog("❌ SDK初始化失败", true);
                        return;
                    }
                    
                    AddLog("✅ SDK初始化成功");
                    await Task.Delay(2000); // 充足等待时间
                }
                catch (Exception ex)
                {
                    AddLog($"❌ SDK初始化异常: {ex.Message}", true);
                    return;
                }

                // 3. 保守的连接尝试
                try
                {
                    AddLog("🔄 开始保守连接模式...");
                    _hkVisionService.RefreshAndConnect();
                }
                catch (Exception ex)
                {
                    AddLog($"❌ 连接尝试异常: {ex.Message}", true);
                    return;
                }
                
                // 4. 长时间等待连接结果
                AddLog("⏳ 等待连接结果（最多20秒）...");
                bool connected = false;
                try
                {
                    for (int i = 0; i < 200; i++) // 20秒
                    {
                        if (_hkVisionService.IsConnected)
                        {
                            connected = true;
                            AddLog($"✅ 海康相机连接成功！耗时：{(i + 1) * 100}ms");
                            break;
                        }
                        await Task.Delay(100);
                        
                        if (i % 50 == 0) // 每5秒报告一次
                        {
                            AddLog($"⏳ 连接中... ({(i + 1) / 10}秒)");
                        }
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"❌ 连接等待异常: {ex.Message}", true);
                }

                if (connected)
                {
                    try
                    {
                        // 5. 额外验证连接状态
                        await Task.Delay(1000);
                        bool finalCheck = _hkVisionService.QuickConnectionCheck();
                        AddLog($"🔍 最终连接验证: {(finalCheck ? "通过" : "失败")}");
                        
                        if (finalCheck)
                        {
                            _hkCameraConnected = true;
                            AddLog("🎉 海康相机应急连接成功！");
                            try { ConnectionStatusChanged?.Invoke("海康相机应急连接成功"); } catch { }
                        }
                        else
                        {
                            AddLog("⚠️ 连接验证失败，但基本连接成功", false);
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLog($"❌ 最终验证异常: {ex.Message}", false);
                    }
                }
                else
                {
                    AddLog("❌ 海康相机应急连接失败", true);
                    
                    // 输出详细诊断信息
                    try
                    {
                        AddLog("🔧 详细诊断信息：");
                        AddLog($"  - SDK状态: {_hkVisionService.SDKInited}");
                        AddLog($"  - 设备数量: {_hkVisionService.DeviceCount}");
                        AddLog($"  - 连接状态: {_hkVisionService.IsConnected}");
                        
                        // 执行诊断
                        _hkVisionService.DiagnoseHKCameraIssues();
                    }
                    catch (Exception ex)
                    {
                        AddLog($"❌ 诊断异常: {ex.Message}", false);
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 应急连接内部异常: {ex.Message}", true);
                _logger?.LogError($"应急连接内部异常: {ex.Message}\r\n{ex.StackTrace}");
                throw; // 重新抛出让上层处理
            }
        }

        #endregion

        #region 辅助方法
        /// <summary>
        /// 添加日志 - 修复重复日志问题
        /// 只输出到日志系统，不通过HObservable发送（避免InfoControlViewModel重复记录）
        /// </summary>
        private void AddLog(string message, bool isError = false)
        {
            try
            {
                // **修复重复日志问题：只输出到日志系统，不通过HObservable发送**
                // 原因：InfoControlViewModel会监听HObservable事件并再次调用_logger.LogInfo，导致重复记录
                if (isError)
                {
                    _logger?.LogError($"[全局相机管理] {message}");
                }
                else
                {
                    _logger?.LogInfo($"[全局相机管理] {message}");
                }
            }
            catch
            {
                // 忽略日志异常
            }
        }



        /// <summary>
        /// 处理配置变化事件
        /// **重要修复：避免重复的相机初始化导致设备冲突**
        /// </summary>
        private void OnConfigurationChanged(ObservableConfigModel newConfig)
        {
            try
            {
                if (_isDisposed || newConfig == null) return;

                // **关键修复：防止重复处理配置变化事件**
                if (_isCheckingConnections)
                {
                    AddLog("正在处理配置变化，跳过重复请求");
                    return;
                }

                // 检测相机配置是否发生变化
                bool configChanged = HasCameraConfigurationChanged(newConfig);
                
                if (configChanged)
                {
                    AddLog("检测到相机配置变化，准备重新连接相机...");
                    // **修复：标记正在处理状态，避免并发处理**
                    _isCheckingConnections = true;

                    // 异步处理重新连接，避免阻塞UI
                    Task.Run(async () =>
                    {
                        try
                        {
                            await HandleConfigurationChangeAsync(newConfig);
                        }
                        catch (Exception ex)
                        {
                            AddLog($"处理配置变化异常: {ex.Message}", true);
                        }
                        finally
                        {
                            // **修复：处理完成后重置状态标志**
                            _isCheckingConnections = false;
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                AddLog($"配置变化事件处理异常: {ex.Message}", true);
                _isCheckingConnections = false; // 确保异常时也重置状态
            }
        }

        /// <summary>
        /// 检测相机配置是否发生变化
        /// </summary>
        private bool HasCameraConfigurationChanged(ObservableConfigModel newConfig)
        {
            bool mainCameraTypeChanged = _lastMainCameraType != newConfig.MainCameraType;
            bool markCameraEnabledChanged = _lastMarkCameraEnabled != newConfig.HaveMarkCamera;

            if (mainCameraTypeChanged)
            {
                AddLog($"主相机类型变化: {_lastMainCameraType} → {newConfig.MainCameraType}");
            }
            
            if (markCameraEnabledChanged)
            {
                AddLog($"Mark相机启用状态变化: {_lastMarkCameraEnabled} → {newConfig.HaveMarkCamera}");
            }

            return mainCameraTypeChanged || markCameraEnabledChanged;
        }

        /// <summary>
        /// 处理配置变化，重新连接相机
        /// </summary>
        private async Task HandleConfigurationChangeAsync(ObservableConfigModel newConfig)
        {
            AddLog("=== 开始处理相机配置变化 ===");
            
            try
            {
                // 先停止当前相机流
                if (_optStreamActive)
                {
                    PauseOptStream();
                }
                if (_hkStreamActive)
                {
                    PauseHkStream();
                }

                // 释放现有连接
                await ReleaseChangedCamerasAsync(newConfig);

                // 等待资源释放完成
                await Task.Delay(2000);

                // 更新配置缓存
                UpdateConfigurationCache(newConfig);

                // 重新初始化相机连接
                _isInitialized = false; // 重置初始化状态
                await InitializeCamerasAsync();

                AddLog("=== 相机配置变化处理完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"处理相机配置变化异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 释放发生变化的相机连接
        /// </summary>
        private async Task ReleaseChangedCamerasAsync(ObservableConfigModel newConfig)
        {
            var releaseTasks = new List<Task>();

            // 检查主相机是否需要重连
            bool mainCameraChanged = (_lastMainCameraType != newConfig.MainCameraType);
            
            if (mainCameraChanged && _optCameraConnected)
            {
                releaseTasks.Add(Task.Run(() =>
                {
                    try
                    {
                        AddLog($"释放主相机连接 (原类型: {_lastMainCameraType})...");
                        _optVisionService.Release();
                        _optCameraConnected = false;
                        AddLog("主相机连接已释放");
                    }
                    catch (Exception ex)
                    {
                        AddLog($"释放主相机连接异常: {ex.Message}", true);
                    }
                }));
            }

            // 检查Mark相机是否需要重连
            bool markCameraChanged = (_lastMarkCameraEnabled != newConfig.HaveMarkCamera);
            
            if (markCameraChanged && _hkCameraConnected)
            {
                releaseTasks.Add(Task.Run(() =>
                {
                    try
                    {
                        AddLog($"释放Mark相机连接 (原启用状态: {_lastMarkCameraEnabled})...");
                        _hkVisionService.CloseCamera();
                        _hkCameraConnected = false;
                        AddLog("Mark相机连接已释放");
                    }
                    catch (Exception ex)
                    {
                        AddLog($"释放Mark相机连接异常: {ex.Message}", true);
                    }
                }));
            }

            if (releaseTasks.Count > 0)
            {
                await Task.WhenAll(releaseTasks);
            }
        }

        /// <summary>
        /// 更新配置缓存
        /// </summary>
        private void UpdateConfigurationCache(ObservableConfigModel config)
        {
            _lastMainCameraType = config.MainCameraType;
            _lastMarkCameraEnabled = config.HaveMarkCamera;
            
            AddLog($"配置缓存已更新 - 主相机: {_lastMainCameraType}, Mark相机: {_lastMarkCameraEnabled}");
        }

        /// <summary>
        /// 报告相机初始化结果
        /// </summary>
        private void ReportInitializationResults(bool needMainCamera, bool needMarkCamera)
        {
            try
            {
                // 更新配置缓存（首次初始化或重新连接后）
                UpdateConfigurationCache(_globalState.AppConfig);
                
                // 生成详细的状态报告
                var statusParts = new List<string>();
                
                if (needMainCamera)
                {
                    string mainStatus = _optCameraConnected ? "✓ 已连接" : "✗ 未连接";
                    statusParts.Add($"主相机({_globalState.AppConfig?.MainCameraType}): {mainStatus}");
                }
                
                if (needMarkCamera)
                {
                    string markStatus = _hkCameraConnected ? "✓ 已连接" : "✗ 未连接";
                    statusParts.Add($"Mark相机: {markStatus}");
                }
                
                string finalReport = string.Join(", ", statusParts);
                AddLog($"=== 相机初始化结果 - {finalReport} ===");
                
                // 触发连接状态变化事件
                string statusMessage = $"相机初始化完成 - {finalReport}";
                ConnectionStatusChanged?.Invoke(statusMessage);
            }
            catch (Exception ex)
            {
                AddLog($"生成初始化报告异常: {ex.Message}", false);
            }
        }
        
        #endregion

        #region 安全释放海康相机SDK的专用方法
        /// <summary>
        /// 安全释放海康相机SDK的专用方法
        /// **关键修复：统一的SDK释放保护，防止所有可能的闪退**
        /// </summary>
        private async Task SafeDisposeHkVisionServiceAsync()
        {
            try
            {
                AddLog("🛡️ 开始安全释放海康相机SDK...");
                
                await Task.Run(() =>
                {
                    try
                    {
                        // **多重安全保护的Dispose调用**
                        _hkVisionService?.Dispose();
                        AddLog("✅ 海康相机SDK已安全释放");
                    }
                    catch (AccessViolationException avEx)
                    {
                        AddLog($"⚠️ SDK释放访问违规异常: {avEx.Message}", false);
                        AddLog("这通常是正常的，SDK可能已被系统强制释放");
                    }
                    catch (System.Runtime.InteropServices.SEHException sehEx)
                    {
                        AddLog($"⚠️ SDK释放SEH异常: {sehEx.Message}", false);
                        AddLog("系统级异常，但程序将继续安全运行");
                    }
                    catch (InvalidOperationException ioEx)
                    {
                        AddLog($"⚠️ SDK释放操作异常: {ioEx.Message}", false);
                        AddLog("SDK状态异常，但已安全处理");
                    }
                    catch (System.ComponentModel.Win32Exception win32Ex)
                    {
                        AddLog($"⚠️ SDK释放Win32异常: {win32Ex.Message}", false);
                        AddLog("Windows系统异常，但已安全处理");
                    }
                    catch (Exception generalEx)
                    {
                        AddLog($"⚠️ SDK释放通用异常: {generalEx.Message}", false);
                        AddLog($"异常类型: {generalEx.GetType().Name}");
                    }
                });
                
                // **确保状态重置**
                _hkCameraConnected = false;
                
                AddLog("🛡️ 海康相机SDK安全释放完成");
            }
            catch (Exception taskEx)
            {
                AddLog($"❌ SDK安全释放任务异常: {taskEx.Message}", false);
                _hkCameraConnected = false;
            }
        }

        /// <summary>
        /// 同步版本的安全Dispose方法（用于不能异步的场合）
        /// </summary>
        private void SafeDisposeHkVisionServiceSync()
        {
            try
            {
                AddLog("🛡️ 开始同步安全释放海康相机SDK...");
                
                try
                {
                    _hkVisionService?.Dispose();
                    AddLog("✅ 海康相机SDK已同步释放");
                }
                catch (AccessViolationException avEx)
                {
                    AddLog($"⚠️ 同步SDK释放访问违规异常: {avEx.Message}", false);
                }
                catch (System.Runtime.InteropServices.SEHException sehEx)
                {
                    AddLog($"⚠️ 同步SDK释放SEH异常: {sehEx.Message}", false);
                }
                catch (Exception ex)
                {
                    AddLog($"⚠️ 同步SDK释放异常: {ex.Message}", false);
                }
                
                _hkCameraConnected = false;
                AddLog("🛡️ 海康相机SDK同步释放完成");
            }
            catch (Exception ex)
            {
                AddLog($"❌ 同步SDK释放容器异常: {ex.Message}", false);
                _hkCameraConnected = false;
            }
        }

        /// <summary>
        /// 强制重置海康相机连接（专门解决连接失败问题）
        /// </summary>
        public async Task ForceResetHkCameraAsync()
        {
            AddLog("=== 开始强制重置海康相机连接 ===");

            try
            {
                // 1. 完全释放海康相机资源
                if (_hkCameraConnected)
                {
                    AddLog("正在释放当前海康相机连接...");
                    try
                    {
                        _hkVisionService.StopGrap();
                        await Task.Delay(300);
                        _hkVisionService.CloseCamera();
                        await Task.Delay(500);
                        _hkCameraConnected = false;
                        _hkStreamActive = false;
                        AddLog("海康相机资源已释放");
                    }
                    catch (Exception ex)
                    {
                        AddLog($"释放海康相机资源异常: {ex.Message}", false);
                    }
                }

                // 2. 等待设备状态完全重置
                AddLog("等待设备状态重置...");
                await Task.Delay(2000);

                // 3. 执行诊断
                AddLog("执行海康相机连接诊断...");
                _hkVisionService.DiagnoseHKCameraIssues();
                await Task.Delay(1000);

                // 4. 尝试重新连接
                AddLog("开始重新连接海康相机...");
                await InitializeHkCameraAsync();

                if (_hkCameraConnected)
                {
                    AddLog($"✓ 海康相机强制重置成功！");
                    ConnectionStatusChanged?.Invoke($"海康相机强制重置成功");

                    // 5. 尝试启动采集
                    AddLog("尝试启动海康相机采集...");
                    await Task.Delay(500);
                    await StartHkStreamAsync();
                }
                else
                {
                    AddLog($"✗ 海康相机强制重置失败", true);

                    // 提供详细的故障排除建议
                    AddLog("=== 故障排除建议 ===");
                    AddLog("1. 检查相机电源和网络连接");
                    AddLog("2. 确认IP地址配置正确");
                    AddLog("3. 以管理员权限运行程序");
                    AddLog("4. 检查防火墙设置");
                    AddLog("5. 尝试重启相机设备");
                    AddLog("6. 检查是否有其他程序占用相机");
                    AddLog("7. 验证海康SDK驱动是否正确安装");
                    AddLog("8. 使用海康官方工具测试相机连接");
                    AddLog("===================");
                }

                AddLog("=== 海康相机强制重置完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"强制重置海康相机异常: {ex.Message}", true);
            }
        }

        #endregion
    }
}