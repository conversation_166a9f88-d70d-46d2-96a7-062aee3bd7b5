﻿<UserControl x:Class="IPM.Vision.Views.Dialogs.LightCtrlDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:controls="clr-namespace:IPM.Vision.Views.CustomControls"
             DataContext="{Binding LightCtrlDialogViewModel, Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="White" CornerRadius="5" Width="720">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Background="#283643" CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText Text="{Binding Title}" Foreground="White" FontSize="18" VerticalAlignment="Center" Margin="10 0 0 0"/>
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Template="{StaticResource CloseTemplate}"
                            Width="40"
                            Height="40"
                            Content="&#xf00d;"
                            Command="{Binding CloseCommand}"/>
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1"
                    Height="420"
                    Margin="4"
                    BorderBrush="Gray"
                    BorderThickness="0.4"
                    Visibility="{Binding ShowCamera,Converter={StaticResource BoolToVisibilityConverter}}">
                <hc:Interaction.Triggers>
                    <hc:EventTrigger EventName="Loaded">
                        <hc:EventToCommand Command="{Binding CameraLoadedCommand}" PassEventArgsToCommand="True"/>
                    </hc:EventTrigger>
                </hc:Interaction.Triggers>
                <controls:CaptureView/>
            </Border>
            <Border Grid.Row="2" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <hc:UniformSpacingPanel Margin="0 0 10 0" Orientation="Vertical" Spacing="10"  Grid.Column="0">
                        <hc:TextBox
                            FontSize="14"
                            hc:InfoElement.Title="参数名称"
                            hc:InfoElement.TitlePlacement="Left"
                            Text="{Binding LightModel.ParamName,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源1"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LOneLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LOneLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="1" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源2"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LTwoLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LTwoLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="2" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源3"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LThreeLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LThreeLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="3" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源4"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LFourLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LFourLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="4" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="副相机光源"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LNineLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LNineLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="9" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                    </hc:UniformSpacingPanel>
                    <hc:UniformSpacingPanel Margin="10 0 0 0" Orientation="Vertical" Spacing="10" Grid.Column="1">
                        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                            <Button  Style="{StaticResource ButtonInfo}" Content="{Binding BtnMessage,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Command="{Binding OpenCommand}"/>
                        </hc:UniformSpacingPanel>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源5"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LFiveLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LFiveLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="5" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源6"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LSixLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LSixLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="6" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源7"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LSevenLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LSevenLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="7" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源8"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LEightLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LEightLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="8" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                    </hc:UniformSpacingPanel>
                </Grid>
            </Border>
            <Border Grid.Row="3" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Button Grid.Column="0" Height="35" Width="220" Style="{StaticResource ButtonPrimary}" Content="保存" Command="{Binding SaveCommand}"/>
                    <Button Grid.Column="1" Height="35" Width="220" Content="取消" Command="{Binding CloseCommand}"/>

                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
