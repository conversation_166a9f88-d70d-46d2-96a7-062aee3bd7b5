﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.ViewModel.CustomControls;
using IPM.Vision.Views.CustomControls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows;
using IPM.Vision.Model;
using IPM.Vision.Mappers;
using IPM.Vision.ViewModel.ObservableModel;
using HandyControl.Controls;
using IPM.Vision.Camera.Com;
using IPM.Vision.Views.CanonControls;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class CameraCtrlDialogViewModel:ViewModelBase, IDialogResultable<bool>
    {
        private bool _result = false;
        private bool _isInsert = false;
        private string _title = string.Empty;
        private readonly NLogHelper _logger;
        private readonly MainCameraParaService _cameraService;
        private ObservableMainCameraParaModel _cameraParamModel;
        private readonly ObservableGlobalState _globalState;
        private bool _showCamera = false;
        private string _btnMessage = "打开相机";

        public CameraCtrlDialogViewModel(IMainCameraParaService cameraService,NLogHelper logger, ObservableGlobalState globalState)
        {
            _cameraService = (MainCameraParaService)cameraService;
            _logger = logger;
            _globalState = globalState;
        }

        public string BtnMessage
        {
            get => _btnMessage;
            set => SetProperty(ref _btnMessage, value);
        }

        public ObservableMainCameraParaModel CameraParamModel
        {
            get => _cameraParamModel;
            set => SetProperty(ref _cameraParamModel, value);
        }

        public bool Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        public bool ShowCamera
        {
            get => _showCamera;
            set => SetProperty(ref _showCamera, value);
        }
        public bool IsInsert
        {
            get => _isInsert;
            set => SetProperty(ref _isInsert, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public Action CloseAction { get; set; }
        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            Result = false;
            CloseAction?.Invoke();
        });

        public IRelayCommand LoadCommand => new RelayCommand(() => {
            
        });

        public IRelayCommand SaveCommand => new RelayCommand(async () =>
        {
            if (string.IsNullOrEmpty(CameraParamModel.ParamName))
            {
                HandyControl.Controls.MessageBox.Error("参数名称不能为空！");
                return;
            }
            try
            {
                if (IsInsert)
                {
                    var temp = CameraParamModel.MapTo<ObservableMainCameraParaModel, MainCameraParamModel>();
                    temp.Id = Guid.NewGuid().ToString();
                    temp.CreateTime = DateTime.Now;
                    temp.Operator = _globalState.LoginUser.Account;
                    Result = await _cameraService.Add(temp);
                }
                else
                {
                    var temp = CameraParamModel.MapTo<ObservableMainCameraParaModel, MainCameraParamModel>();
                    Result = await _cameraService.Update(temp);
                }
                CloseAction?.Invoke();
            }
            catch (Exception ex) {
                _logger.LogError(ex);
            }
        });

        public IRelayCommand<RoutedEventArgs> CameraLoadedCommand => new RelayCommand<RoutedEventArgs>((e) =>
        {
            var temp = (e.Source as Border).Child as CaptureView;
            if (temp != null)
            {
                var models = temp.DataContext as CaptureViewModel;
                models.NeedSetting = false;
                models.HaveChild = false;
            }

        });

        public IRelayCommand OpenCommand => new RelayCommand(() => {
            ShowCamera = !ShowCamera;
            if (!ShowCamera) BtnMessage = "打开相机";
            else BtnMessage = "关闭相机";
        });

        public IRelayCommand<RoutedEventArgs> LeftPanelLoadCommand => new RelayCommand<RoutedEventArgs>((e) => {
            var source = e.Source as UniformSpacingPanel;
            if (source != null) {
                var child = source.Children;
                if (child != null && child.Count > 0) {
                    foreach (var item in child)
                    {
                        var observable = item as IObserver;
                        if(observable != null) _cameraService.AddSource(observable);
                        
                    }
                }
            }
        });
        public IRelayCommand<RoutedEventArgs> RightPanelLoadCommand => new RelayCommand<RoutedEventArgs>((e) => {
            var source = e.Source as UniformSpacingPanel;
            if (source != null)
            {
                var child = source.Children;
                if (child != null && child.Count > 0)
                {
                    foreach (var item in child)
                    {
                        var observable = item as IObserver;
                        if (observable != null) _cameraService.AddSource(observable);
                    }
                }
                _cameraService.GetAllPropertyDesc();
                if (IsInsert) _cameraService.GetAllProperty();
            }
        });

        public IRelayCommand UnLoadCommand => new RelayCommand(() => {
            _cameraService.RemoveAllSource();
        });
    }
}
