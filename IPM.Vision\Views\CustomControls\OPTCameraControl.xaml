﻿<UserControl x:Class="IPM.Vision.Views.CustomControls.OPTCameraControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.CustomControls"
             DataContext="{Binding OptCameraControlViewModel, Source={StaticResource Locator}}"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             HorizontalAlignment="Stretch"
             VerticalAlignment="Stretch">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}" PassEventArgsToCommand="True"/>
        </hc:EventTrigger>
        <hc:EventTrigger EventName="Unloaded">
            <hc:EventToCommand Command="{Binding UnLoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="#f5f5f5" 
            BorderBrush="Gray" 
            BorderThickness="0.3"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch">
        <Canvas HorizontalAlignment="Stretch" 
                VerticalAlignment="Stretch">
            <Border Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}}"
                    Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch">
                <Image
                    Source="{Binding RenderImageData}"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch"
                    Panel.ZIndex="22"
                    Stretch="Uniform"
                    RenderTransformOrigin="0.5,0.5"
                    Visibility="{Binding IsConnect, Converter={StaticResource BoolToVisibilityConverter}}">

                    <Image.LayoutTransform>
                        <TransformGroup>
                            <!-- 确保缩放参数不为0，默认为1 -->
                            <ScaleTransform ScaleX="{Binding ScaleAX, FallbackValue=1}" ScaleY="{Binding ScaleAY, FallbackValue=1}"/>
                            <ScaleTransform ScaleX="{Binding ScaleBX, FallbackValue=1}" ScaleY="{Binding ScaleBY, FallbackValue=1}"/>
                            <RotateTransform Angle="{Binding RotateAngle, FallbackValue=0}"/>
                        </TransformGroup>
                    </Image.LayoutTransform>
                </Image>
            </Border>
            <!-- 中心十字准星 -->
            <!-- 水平线 -->
            <Line X1="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}, ConverterParameter=-20}"
                  Y1="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}}"
                  X2="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}, ConverterParameter=20}"
                  Y2="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}}"
                  Visibility="{Binding IsConnect, Converter={StaticResource BoolToVisibilityConverter}}"
                  Stroke="Green"
                  StrokeThickness="2"
                  Panel.ZIndex="1000" />

            <!-- 垂直线 -->
            <Line X1="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}}"
                  Y1="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}, ConverterParameter=-20}"
                  X2="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}}"
                  Y2="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}, ConverterParameter=20}"
                  Visibility="{Binding IsConnect, Converter={StaticResource BoolToVisibilityConverter}}"
                  Stroke="Green"
                  StrokeThickness="2"
                  Panel.ZIndex="1000" />

            <!-- 显示未连接的提示信息 -->
            <Border
                Visibility="{Binding IsConnect, Converter={StaticResource BoolToVisibilityConverter},ConverterParameter=True}"
                Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}}"
                Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch">
                <Grid HorizontalAlignment="Stretch" VerticalAlignment="Stretch">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <Button Grid.Row="0"
                            Content="{Binding ConnectionStatus}"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Style="{StaticResource ButtonDashedDanger}"
                            FontSize="14"
                            Command="{Binding ReconnectCommand}"
                            IsEnabled="{Binding IsConnecting, Converter={StaticResource Boolean2BooleanReConverter}}"
                            Visibility="{Binding IsConnect, Converter={StaticResource BoolToVisibilityConverter},ConverterParameter=True}"/>
                    
                    <ProgressBar Grid.Row="1" 
                                 IsIndeterminate="True"
                                 Height="4"
                                 Foreground="#FF0078D7"
                                 Background="Transparent"
                                 HorizontalAlignment="Stretch"
                                 Visibility="{Binding IsConnecting, Converter={StaticResource BoolToVisibilityConverter}}"/>
                </Grid>
            </Border>

        </Canvas>

    </Border>
</UserControl>
