﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.CustomControls;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.CustomControls;
using System;
using System.Collections.Generic;
using System.Data.Entity.Infrastructure;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class EquipmentCtrlDialogViewModel : ViewModelBase, IDialogResultable<bool>
    {
        private bool _result = false;
        private bool _isInsert = false;
        private string _title = string.Empty;
        private readonly NLogHelper _logger;
        private ObservableEquipmentModel _equipmentModel;
        private readonly EquipmentService _equipmentService;
        private readonly ObservableGlobalState _globalState;
        private bool _showCamera = false;

        public EquipmentCtrlDialogViewModel(IEquipmentService equipmentService,NLogHelper logger,ObservableGlobalState globalState)
        {
            _equipmentService = (EquipmentService)equipmentService;
            _logger = logger;
            _globalState = globalState;
        }

        public bool Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        public bool ShowCamera
        {
            get => _showCamera;
            set => SetProperty(ref _showCamera, value);
        }

        public ObservableEquipmentModel EquipmentModel
        {
            get => _equipmentModel;
            set => SetProperty(ref _equipmentModel, value);
        }

        public bool IsInsert
        {
            get => _isInsert;
            set => SetProperty(ref _isInsert, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public Action CloseAction { get; set; }

        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            Result = false;
            CloseAction?.Invoke();
        });

        public IRelayCommand LoadCommand => new RelayCommand(() => {
            if (IsInsert)
            {
                //_equipmentService.ReadTagAsync();
            }
        });

        public IRelayCommand SaveCommand => new RelayCommand(async () => {

            if (string.IsNullOrEmpty(EquipmentModel.ParamName))
            {
                HandyControl.Controls.MessageBox.Error("参数名称不能为空！");
                return;
            }
            try
            {
                if (IsInsert)
                {
                    EquipmentModel.Id = Guid.NewGuid().ToString();
                    EquipmentModel.CreateTime = DateTime.Now;
                    EquipmentModel.Operator = _globalState.LoginUser.Account;
                    var temp = EquipmentModel.MapTo<ObservableEquipmentModel, EquipmentModel>();
                    Result = await _equipmentService.Add(temp);
                }
                else
                {
                    var temp = EquipmentModel.MapTo<ObservableEquipmentModel, EquipmentModel>();
                    Result = await _equipmentService.Update(temp);
                }
                CloseAction?.Invoke();
            }
            catch (Exception ex) {
            
                _logger.LogError(ex);
            }
            
        });

        public IRelayCommand ClearCommand => new RelayCommand(() => {
            _equipmentService.ResetError();
        });

        public IRelayCommand ResetCommand => new RelayCommand(() => {
            _equipmentService.ResetAll();
        });

        public IRelayCommand<string> ResetSingleCommand => new RelayCommand<string>((args) =>
        {
            switch (args)
            {
                case "XY":
                    _equipmentService.ReSetXY();
                    EquipmentModel.X = 0;
                    EquipmentModel.Y = 0;
                    break;
                case "Z":
                    _equipmentService.ReSetZ();
                    EquipmentModel.Z = 0;
                    break;
                case "O":
                    _equipmentService.ReSetO();
                    EquipmentModel.O = 0;
                    break;
                case "R":
                    _equipmentService.ReSetR();
                    EquipmentModel.R = 0;
                    break;
                case "T":
                    _equipmentService.ReSetT();
                    EquipmentModel.T = 0;
                    break;
            }
        });

        public IRelayCommand<string> MoveCommand => new RelayCommand<string>((args) =>
        {
            MoveEvent(args);
        });

        private void MoveEvent(string args)
        {
            switch (args)
            {
                case "XY":
                    _equipmentService.WriteXYArray(new float[] { EquipmentModel.X }, new float[] { EquipmentModel.Y }, 1);

                    //_equipmentService.SetXY(EquipmentModel.X, EquipmentModel.Y);
                    break;
                case "Z":
                    _equipmentService.SetZAsync(EquipmentModel.Z);
                    break;
                case "O":
                    _equipmentService.SetO(EquipmentModel.O);
                    break;
                case "R":
                    _equipmentService.SetR(EquipmentModel.R);
                    break;
                case "T":
                    _equipmentService.SetT(EquipmentModel.T);
                    break;
            }
        }

        public IRelayCommand<KeyEventArgs> XYKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("XY");
            }
        });
        public IRelayCommand<KeyEventArgs> ZKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("Z");
            }
        });
        public IRelayCommand<KeyEventArgs> RKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("R");
            }
        });
        public IRelayCommand<KeyEventArgs> TKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("T");
            }
        });
        public IRelayCommand<KeyEventArgs> OKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("O");
            }
        });

        public IRelayCommand<RoutedEventArgs> CameraLoadedCommand => new RelayCommand<RoutedEventArgs>((e) =>
        {
            var temp = (e.Source as Border).Child as CaptureView;
            if (temp != null)
            {
                var models = temp.DataContext as CaptureViewModel;
                models.NeedSetting = false;
                models.HaveChild = _globalState.AppConfig.HaveMarkCamera == OpenEnum.OPEN;
            }

        });

        public IRelayCommand OpenCommand => new RelayCommand(() => {
            ShowCamera = !ShowCamera;
        });
    }
}
