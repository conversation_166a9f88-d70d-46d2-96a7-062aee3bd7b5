﻿using ClosedXML.Excel;
using IPM.Common.Tools.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace IPM.Common.Tools
{
    public partial class Form1 : Form
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void button1_Click(object sender, EventArgs e)
        {
            OpenFileDialog openFileDialog = new OpenFileDialog();

            // 设置对话框的标题
            openFileDialog.Title = "请选择一个文件";

            // 设置允许用户选择的文件类型
            openFileDialog.Filter = "所有文件|*.*";

            // 设置初始目录为文档目录
            openFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

            // 设置对话框不允许选择多个文件
            openFileDialog.Multiselect = false;

            // 显示对话框
            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                // 获取用户选择的文件路径
                string filePath = openFileDialog.FileName;
                
                File.WriteAllText("opc_config.lof", ReadExcel(filePath));
                
            }
        }

        private string ReadExcel(string path)
        {
            if (!File.Exists(path))
            {
                Console.WriteLine("文件不存在");
                return string.Empty;
            }
            List<OpcTagModel> opcTags = new List<OpcTagModel>();
            // 使用EPPlus读取Excel文件
            using (var workbook = new XLWorkbook(path))
            {
                IXLWorksheet worksheet;
                // 获取第一个工作表
                workbook.Worksheets.TryGetWorksheet("Sheet1", out worksheet);
                foreach (var row in worksheet.RowsUsed().Skip(1)) // Skip(1)跳过表头
                {
                    var temp = row.Cell(1).GetString();
                    var temp2 = row.Cell(2).GetString();
                    var temp3 = row.Cell(3).GetString();
                    var temp4 = row.Cell(4).GetString();
                    var temp5 = row.Cell(5).GetString();
                    var temp6 = row.Cell(6).GetString();
                    var opcTag = new OpcTagModel
                    {
                        TagName = row.Cell(1).GetString(),
                        ValueType = row.Cell(2).GetString(),
                        TagId = row.Cell(3).GetValue<int>(),
                        Upper = row.Cell(4).GetValue<float>(),
                        Lower = row.Cell(5).GetValue<float>()
                    };
                    opcTags.Add(opcTag);
                }

            }
            return JsonConvert.SerializeObject(opcTags,Formatting.Indented);
        }
    }
}
