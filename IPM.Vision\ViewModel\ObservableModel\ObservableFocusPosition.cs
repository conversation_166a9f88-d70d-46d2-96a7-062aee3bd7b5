﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableFocusPosition:ViewModelBase
    {
        private double _width;
        private double _height;
        private double _left;
        private double _top;
        private bool _visible = false;
        private Brush _brush = Brushes.White;

        public double Width
        {
            get => _width;
            set => SetProperty(ref _width, value);
        }

        public double Height
        {
            get => _height;
            set => SetProperty(ref _height, value);
        }

        public double Left
        {
            get => _left; 
            set => SetProperty(ref _left, value);
        }

        public double Top
        {
            get => _top; 
            set => SetProperty(ref _top, value);
        }

        public Brush BorderColor
        {
            get => _brush; 
            set => SetProperty(ref _brush, value);
        }

        public bool Visible
        {
            get => _visible;
            set => SetProperty(ref _visible, value);
        }

    }
}
