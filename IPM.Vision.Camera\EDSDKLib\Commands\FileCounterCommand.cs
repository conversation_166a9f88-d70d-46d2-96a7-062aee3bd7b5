﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class FileCounterCommand : BasicCommand
    {
        public struct FileNumber
        {
            public IntPtr DcimItem;

            public int dirnum;

            public int[] filenum;

            public FileNumber(int p1)
            {
                DcimItem = IntPtr.Zero;
                dirnum = p1;
                filenum = new int[dirnum];
            }
        }

        private IntPtr _volume;
        public FileCounterCommand(ref CanonCameraModel model,ref IntPtr volume) : base(ref model)
        {
            _volume = volume;
        }

        public uint CountDirectory(IntPtr camera, ref IntPtr directoryItem, out int directory_count)
        {
            uint num = 0u;
            int outCount = 0;
            directory_count = 0;
            EDSDK.EdsDirectoryItemInfo outDirItemInfo = default(EDSDK.EdsDirectoryItemInfo);
            outDirItemInfo.szFileName = "";
            outDirItemInfo.Size = 0uL;
            num = EDSDK.EdsGetChildCount(_volume, out outCount);
            if (num != 0)
            {
                return num;
            }

            for (int i = 0; i < outCount; i++)
            {
                if (EDSDK.EdsGetChildAtIndex(_volume, i, out var outRef) == 0)
                {
                    num = EDSDK.EdsGetDirectoryItemInfo(outRef, out outDirItemInfo);
                    if (num != 0)
                    {
                        return num;
                    }

                    if (outDirItemInfo.szFileName == "DCIM" && outDirItemInfo.isFolder == 1)
                    {
                        directoryItem = outRef;
                        break;
                    }

                    if (outRef != IntPtr.Zero)
                    {
                        EDSDK.EdsRelease(outRef);
                    }
                }
            }

            return num = EDSDK.EdsGetChildCount(directoryItem, out directory_count);
        }

        public uint CountImages(IntPtr camera, ref IntPtr directoryItem, ref int directory_count, ref int fileCount, ref FileNumber fileNumber, ref List<IntPtr> imageItems)
        {
            uint num = 0u;
            fileCount = 0;
            for (int i = 0; i < directory_count; i++)
            {
                int image_count = 0;
                num = CountImagesByDirectory(ref directoryItem, i, ref image_count, ref imageItems);
                if (num != 0)
                {
                    return num;
                }

                fileCount += image_count;
                fileNumber.filenum[i] = image_count;
            }

            return 0u;
        }

        private uint CountImagesByDirectory(ref IntPtr directoryItem, int directoryNo, ref int image_count, ref List<IntPtr> imageItems)
        {
            int outCount = 0;
            uint num = EDSDK.EdsGetChildAtIndex(directoryItem, directoryNo, out var outRef);
            if (num != 0)
            {
                return num;
            }

            num = EDSDK.EdsGetDirectoryItemInfo(outRef, out var outDirItemInfo);
            if (num != 0)
            {
                return num;
            }

            int num2 = 0;
            int num3 = 0;
            num = EDSDK.EdsGetChildCount(outRef, out outCount);
            if (num != 0)
            {
                return num;
            }

            for (num2 = 0; num2 < outCount; num2++)
            {
                num = EDSDK.EdsGetChildAtIndex(outRef, num2, out var outRef2);
                if (num != 0)
                {
                    return num;
                }

                num = EDSDK.EdsGetDirectoryItemInfo(outRef2, out outDirItemInfo);
                if (num != 0)
                {
                    return num;
                }

                if (outDirItemInfo.isFolder == 0)
                {
                    imageItems.Add(outRef2);
                    num3++;
                }
            }

            image_count = num3;
            return 0u;
        }
    }
}
