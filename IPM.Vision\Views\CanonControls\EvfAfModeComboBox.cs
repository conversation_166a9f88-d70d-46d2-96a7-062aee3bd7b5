﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class EvfAfModeComboBox : PropertyComboBox, IObserver
    {
        private EDSDK.EdsPropertyDesc _desc;
        public EvfAfModeComboBox()
        {
            this.Name = "evf_af_combo";
            if (this.items == null) items = new Dictionary<uint, string>();
            items.Add(1U, "单点自动对焦");
            items.Add(4U, "区域自动对焦");
            items.Add(5U, "扩展自动对焦区域:上下左右");
            items.Add(6U, "扩展自动对焦区域:周围");
            items.Add(7U, "大区域自动对焦:水平");
            items.Add(8U, "大区域自动对焦:垂直");
            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }

        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = (KeyValuePair<uint, string>)e.AddedItems[0];
                CameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_EVF_AFMODE, (IntPtr)selectedItem.Key));

            }
            base.OnSelectionChanged(e);
        }

        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();

                if (propertyID == EDSDK.PropID_Evf_AFMode)
                {
                    uint property = model.EvfAFMode;

                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            // At the time of application start, if the camera setting is 'Catch AF', make 'Evf_AFMode_LiveFace' forcibly.
                            if (0x09 == property)
                            {
                                EDSDK.EdsSetPropertyData(model.Camera, EDSDK.PropID_Evf_AFMode, 0, sizeof(uint), 0x02);
                            }
                            else
                            {
                                this.UpdateProperty(property);
                            }
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:
                            // If property is 'Catch AF', do not call UpdateProperty().
                            _desc = model.EvfAFModeDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            if (0x09 != property)
                            {
                                this.UpdateProperty(property);
                            }
                            break;
                    }
                }
            }
        }
    }
}

