﻿using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common
{
    public static class JsonHelper
    {
        public static string ToJson(this object obj, params string[] ignoreProperties) => JsonConvert.SerializeObject(obj, new JsonSerializerSettings()
        {
            Formatting = Formatting.Indented,
            DateFormatString = "yyyy-MM-dd HH:mm:ss",
            ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
            ContractResolver = (IContractResolver)new JsonPropertyContractResolver(ignoreProperties)
        });

        public static T ToObject<T>(this string json) => json == null ? default(T) : JsonConvert.DeserializeObject<T>(json);

        public static List<T> ToList<T>(this string json) => json == null ? (List<T>)null : JsonConvert.DeserializeObject<List<T>>(json);

        public static DataTable ToTable(this string json) => json == null ? (DataTable)null : JsonConvert.DeserializeObject<DataTable>(json);
    }
}
