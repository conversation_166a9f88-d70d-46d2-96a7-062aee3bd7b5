﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("user")]
    public class UserModel: BasicModel
    {

        [SugarColumn(ColumnName = "account")]
        public string Account { get; set; }

        [SugarColumn(ColumnName = "password")]
        public string Password { get; set; }

        [SugarColumn(ColumnName = "user_name")]
        public string UserName { get; set; }

        [SugarColumn(ColumnName = "menu_id",IsNullable = true)]
        public string MenuId { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "is_supper")]
        public bool IsSupper { get; set; } = false;
    }
}
