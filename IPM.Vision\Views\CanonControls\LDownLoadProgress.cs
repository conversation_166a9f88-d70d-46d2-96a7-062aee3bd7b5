﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Views.CanonControls
{
    public class LDownLoadProgress : DownloadProgress, IObserver
    {
        public LDownLoadProgress() : base()
        {
            this.Value = 0;
        }
        private delegate void _UpdateProperty(Observable observable, CameraEvent e);
        public async void UpdateAsync(Observable observable, CameraEvent e)
        {
            CameraModel model = (CameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROGRESS)
            {
                uint percentage = (uint)e.GetArg();
                this.UpdateProperty(percentage);
            }
            if ((eventType = e.GetEventType()) == CameraEvent.Type.DOWNLOAD_COMPLETE)
            {
                await Task.Delay(1000);
                uint percentage = (uint)0;
                this.UpdateProperty(percentage);
            }

        }
    }
}
