﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableLogModel:ViewModelBase
    {
        private string _message;
        private Brush _color;

        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        public Brush Color
        {
            get => _color;
            set => SetProperty(ref _color, value);
        }
    }
}
