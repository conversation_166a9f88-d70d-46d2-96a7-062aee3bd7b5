﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.LEvents
{
    public enum HEventCode
    {
        SUCCESS,
        ERROR,
        WARNING
    }

    public enum SourceType
    {
        PLC,
        CAMERA,
        LIGHT
    }
    public enum EquipmentStatus
    {
        NONE = 0,
        WORKING = 1,
        WAITING = 2,
        STOPPED = 3,
        ERROR = 4
    }

    public enum ShowType
    {
        ICON,
        LABEL,
        ALL
    }
}
