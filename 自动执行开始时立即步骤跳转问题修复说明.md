# 自动执行开始时立即步骤跳转问题修复说明

## 问题描述

用户反馈：当前用户先切换到步骤二然后设备处于等待状态，用户点击开始自动执行，步骤直接跳到第三步骤了。

## 问题根源分析

### 1. 问题发生场景

1. **用户手动切换到步骤二**：此时设备可能已经处于就绪状态（状态码2）
2. **用户点击开始自动执行**：触发`StartProcess()`方法，设置`IsRunning = true`
3. **设备状态通知立即触发**：由于设备已经是就绪状态，`_equipmentService_McStatusNotify(2)`被立即调用
4. **步骤切换逻辑被触发**：检测到`obj == 2 && IsRunning`为true，且当前是拍照步骤，立即触发步骤切换

### 2. 核心问题

**时序冲突**：用户点击开始自动执行时，系统没有考虑设备可能已经处于就绪状态的情况，导致立即触发步骤切换逻辑。

### 3. 相关代码位置

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`

#### 设备状态通知处理逻辑 (第1939行)
```csharp
if (obj == 2 && IsRunning)
{
    // 设备就绪且正在自动运行，立即处理步骤逻辑
    if (CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE && CurrentProcess.CameraType == CameraTypeEnum.Main)
    {
        // 触发步骤切换
        await ContinueToNextProcessSafely();
    }
}
```

#### 开始自动执行逻辑 (第3157行)
```csharp
private void StartProcess()
{
    // 设置自动运行状态
    _state.IsRunning = true;
    IsRunning = true;
    // 此时如果设备已经是就绪状态，会立即触发状态通知处理
}
```

## 修复方案

### 1. 添加自动运行开始时间记录

**新增字段**：
```csharp
private DateTime _autoRunStartTime = DateTime.MinValue; // 自动运行开始时间，用于避免立即步骤切换
```

### 2. 在开始自动执行时记录时间

**修改位置**: `StartProcess()`方法
```csharp
// **新增：记录自动运行开始时间，避免立即步骤切换**
_autoRunStartTime = DateTime.Now;
NotifyLog("⏰ 已记录自动运行开始时间，避免立即响应设备状态通知");

_state.IsRunning = true;
IsRunning = true;
```

### 3. 在设备状态通知处理中添加时间检查

**修改位置**: `_equipmentService_McStatusNotify()`方法
```csharp
if (obj == 2 && IsRunning)
{
    // **新增：检查是否刚开始自动运行，避免立即触发步骤切换**
    var timeSinceAutoRunStart = DateTime.Now - _autoRunStartTime;
    if (timeSinceAutoRunStart < TimeSpan.FromSeconds(2))
    {
        NotifyLog($"⏰ 自动运行刚开始 {timeSinceAutoRunStart.TotalSeconds:F1}秒，忽略设备状态通知，避免立即步骤切换");
        return;
    }
    
    // 继续原有的步骤处理逻辑...
}
```

### 4. 在强制停止时重置时间

**修改位置**: `ForceStopCommand`
```csharp
// **新增：重置自动运行开始时间**
_autoRunStartTime = DateTime.MinValue;
NotifyLog("⏰ 强制停止：已重置自动运行开始时间");
```

## 修复效果

### 1. 解决立即步骤跳转问题

- 用户点击开始自动执行后，系统会忽略2秒内的设备状态通知
- 避免因设备已处于就绪状态而立即触发步骤切换
- 确保用户有足够时间看到当前步骤开始执行

### 2. 保持正常步骤切换功能

- 2秒后恢复正常的设备状态通知处理
- 不影响正常的步骤切换逻辑
- 保持系统的自动化执行能力

### 3. 增强系统稳定性

- 防止时序冲突导致的异常步骤跳转
- 提供更好的用户体验
- 增加调试日志便于问题排查

## 测试建议

### 1. 测试场景

1. **正常场景**：从第一步开始自动执行，验证步骤切换正常
2. **问题场景**：手动切换到步骤二，点击开始自动执行，验证不会立即跳到第三步
3. **边界场景**：在不同设备状态下开始自动执行，验证系统稳定性

### 2. 验证要点

1. 开始自动执行后，当前步骤应该保持不变（至少2秒）
2. 2秒后，正常的步骤切换逻辑应该恢复工作
3. 强制停止后，时间记录应该被正确重置
4. 日志应该清楚显示时间检查和忽略的情况

## 相关文件

- `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs` - 主要修改文件
- 新增字段：`_autoRunStartTime`
- 修改方法：`StartProcess()`, `_equipmentService_McStatusNotify()`, `ForceStopCommand`
