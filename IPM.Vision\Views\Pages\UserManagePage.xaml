﻿<Page x:Class="IPM.Vision.Views.Pages.UserManagePage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:IPM.Vision.Views.Pages"
      DataContext="{Binding UserManageViewModel, Source={StaticResource Locator}}"
      mc:Ignorable="d"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="UserManagePage">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Grid >
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <hc:UniformSpacingPanel Margin="10 0 0 0" Grid.Row="0" Orientation="Horizontal" Spacing="10" >
            <hc:TextBox Width="320" Height="34" hc:TitleElement.Title="账号:" FontSize="14" hc:TitleElement.TitlePlacement="Left" Text="{Binding Account,UpdateSourceTrigger=PropertyChanged,Mode=TwoWay}">
                <hc:Interaction.Triggers>
                    <hc:EventTrigger EventName="TextChanged">
                        <hc:EventToCommand Command="{Binding SearchCommand}"/>
                    </hc:EventTrigger>
                </hc:Interaction.Triggers>
            </hc:TextBox>
        </hc:UniformSpacingPanel>
        <Border Grid.Row="1" Style="{StaticResource Body}" Padding="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <hc:UniformSpacingPanel Orientation="Horizontal" Grid.Row="0" Spacing="10">
                    <Button Style="{StaticResource ButtonPrimary}" FontSize="16" Content="&#xf067; 添加用户" FontFamily="{StaticResource FontAwesome}" Command="{Binding AddUserCommand}"/>
                    <!--<Button Style="{StaticResource ButtonDanger}" Content="&#xf014; 批量删除" FontFamily="{StaticResource FontAwesome}"/>-->
                </hc:UniformSpacingPanel>
                <DataGrid Margin="0 10 0 0"
                          Grid.Row="1"
                          Style="{StaticResource DataGridBaseStyle}"
                          AutoGenerateColumns="False"
                          ItemsSource="{Binding DataList}"
                          RowHeaderWidth="60"
                          hc:DataGridAttach.ShowSelectAllButton="False"
                          hc:DataGridAttach.CanUnselectAllWithBlankArea="True"
                          hc:DataGridAttach.ShowRowNumber="True"
                          hc:DataGridAttach.ApplyDefaultStyle="True"
                          RowDetailsVisibilityMode="Collapsed"
                          CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                          ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="账号" Width="*" IsReadOnly="True" Binding="{Binding Account}"/>
                        <DataGridTextColumn Header="用户名" Width="*"  IsReadOnly="True"  Binding="{Binding UserName}"/>
                        <DataGridTextColumn Header="菜单" Width="*"  IsReadOnly="True"  Binding="{Binding MenuName,Converter={StaticResource  List2StringConverter}}"/>
                        <DataGridTemplateColumn IsReadOnly="True" Width="220" CanUserResize="False" Header="操作">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="&#xf044;"
                                                FontFamily="{StaticResource FontAwesome}"
                                                Style="{StaticResource ButtonInfo}"
                                                Command="{Binding DataContext.ModifyUserCommand,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"/>
                                        <Button Content="&#xf1f8;"
                                                Style="{StaticResource ButtonDanger}"
                                                Margin="5 0 0 0"
                                                FontFamily="{StaticResource FontAwesome}"
                                                Visibility="{Binding IsSupper,Converter={StaticResource BoolToVisibilityConverter},ConverterParameter=True}"
                                                Command="{Binding DataContext.DeleteCommand,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                    <DataGrid.RowDetailsTemplate>
                        <DataTemplate>
                            <TextBlock Text="暂无数据" HorizontalAlignment="Center" VerticalAlignment="Center"
                                       Visibility="{Binding DataList, Converter={StaticResource NullToVisibilityConverter}}" />
                        </DataTemplate>
                    </DataGrid.RowDetailsTemplate>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</Page>
