﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace IPM.Vision.Common.Converts
{
    public class ListToStringConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 将 List<string> 转换为用逗号分隔的字符串
            var list = value as List<string>;
            if (list != null)
            {
                return string.Join(",", list);
            }
            return string.Empty;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // 可选的，转换回 List<string> 如果需要
            var str = value as string;
            if (!string.IsNullOrEmpty(str))
            {
                return str.Split(',').ToList();
            }
            return new List<string>();
        }
    }
}
