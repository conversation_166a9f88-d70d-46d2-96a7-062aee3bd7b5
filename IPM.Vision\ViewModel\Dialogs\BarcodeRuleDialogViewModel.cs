﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class BarcodeRuleDialogViewModel : ViewModelBase, IDialogResultable<bool>
    {
        private readonly BarcodeRuleService _service;
        private bool _result = false;
        private string _title;
        private string _message;
        private ObservableBarcodeRuleModel _ruleModel;
        public ObservableBarcodeRuleModel RuleModel
        {
            get => _ruleModel;
            set => SetProperty(ref _ruleModel, value);
        }

        public BarcodeRuleDialogViewModel(IBarcodeRuleService service)
        {
            _service = (BarcodeRuleService)service;
        }

        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        public List<EnumItem> BarcodeRuleTypes { get => EnumHelper.GetEnumList<RuleType>(); }
        public List<EnumItem> RemoveTypes { get => EnumHelper.GetEnumList<YesOrNo>(); }
        public bool Result { get => _result; set => SetProperty(ref _result, value); }
        public Action CloseAction { get; set; }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public IRelayCommand ConfirmCommand => new RelayCommand(async () =>
        {
            if (string.IsNullOrEmpty(RuleModel.Rule))
            {
                Message = "规则不能为空！";
                return;
            }
            else Message = string.Empty;
            var tempData = RuleModel.MapTo<ObservableBarcodeRuleModel, BarcodeRuleModel>();
            if (string.IsNullOrEmpty(tempData.Id))
            {

                tempData.Id = Guid.NewGuid().ToString();
                Result = await _service.Add(tempData);
            }
            else
            {
                Result = await _service.Update(tempData);
            }
            CloseAction.Invoke();
        });

        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            CloseAction.Invoke();
        });



    }
}
