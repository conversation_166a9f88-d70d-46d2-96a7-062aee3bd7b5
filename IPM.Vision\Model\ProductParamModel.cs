﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("product_para")]
    public class ProductParamModel : BasicModel
    {
        [SugarColumn(ColumnName = "product_number")]
        public string ProductNumber { get; set; }

        [SugarColumn(ColumnName = "product_name", IsNullable = true)]
        public string ProductName { get; set; }

        [SugarColumn(ColumnName = "product_description", IsNullable = true)]
        public string ProductDescription { get; set; }

        [SugarColumn(ColumnName = "comp_height_max", IsNullable = true)]
        public float CompHeight { get; set; }

        [SugarColumn(ColumnName = "process_id", IsNullable = true,ColumnDataType = "TEXT")]
        public string ProcessId { get; set; }

        [SugarColumn(ColumnName = "comp_info_id", IsNullable = true)]
        public string CompInfoId { get; set; }

        [SugarColumn(ColumnName = "create_time")]
        public DateTime CreateTime { get; set; }

        [SugarColumn(ColumnName = "operator", IsNullable = true)]
        public string Operator { get; set; }

    }
}
