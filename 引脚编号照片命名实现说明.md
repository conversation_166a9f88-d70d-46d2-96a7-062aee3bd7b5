# 引脚编号照片命名实现说明

## 需求描述

在现有照片命名的基础上，要将pin脚的引脚编号添加到文件命名最后面，例如：
- 第一个引脚：`原文件名-1`
- 第二个引脚：`原文件名-2`
- 以此类推...

序号累加通过回调次数来累加，并且要考虑方向。

## 实现方案

### 1. 添加引脚计数器

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`

添加私有字段来跟踪当前引脚编号：
```csharp
private int _currentPinIndex = 0; // 当前引脚编号，用于照片命名
```

### 2. 在开始自动运行时重置计数器

**修改方法**: `StartProcess()`

```csharp
// 重置引脚计数器
_currentPinIndex = 0;

NotifyLog($"🚀 开始自动运行 - 已重置检测计数器和引脚计数器，当前已检测数量: {CheckCount}");
```

### 3. 在切换到拍照步骤时重置计数器

**修改方法**: `HandleProcessChangeInAutoMode()`

```csharp
if (newProcess != null &&
    newProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE &&
    newProcess.CameraType == CameraTypeEnum.Main)
{
    // 切换到side拍照步骤，重置引脚计数器并打开AutoTake
    NotifyLog($"🔄 自动运行模式：切换到side拍照步骤 [{newProcess.ParamName}]");

    // 重置引脚计数器，为新的拍照步骤开始计数
    _currentPinIndex = 0;
    NotifyLog($"🔢 已重置引脚计数器，开始新步骤的引脚拍摄");
    
    // 其他逻辑...
}
```

### 4. 在OPT相机自动触发保存时添加引脚编号

**修改方法**: `_optVisionService_RequestAutoTriggerSave()`

```csharp
private void _optVisionService_RequestAutoTriggerSave()
{
    try
    {
        // 使用现有的文件夹和文件名生成逻辑
        string folderName = CheckFolder();
        string basePictureName = CheckPictureName();

        // 递增引脚计数器
        _currentPinIndex++;

        // 在基础文件名后添加引脚编号
        string pictureNameWithPin = $"{basePictureName}-{_currentPinIndex}";

        // 调用OPT相机的拍照方法
        _optVisionService.TakePicture(folderName, pictureNameWithPin);

        NotifyLog($"📷 OPT相机自动触发保存 - 文件夹: {folderName}, 文件名: {pictureNameWithPin} (引脚编号: {_currentPinIndex})");
    }
    catch (Exception ex)
    {
        NotifyLog($"❌ OPT相机自动触发保存失败: {ex.Message}");
    }
}
```

## 工作流程

### 1. 自动运行开始
1. 用户点击开始自动运行
2. 调用 `StartProcess()` 方法
3. 重置 `_currentPinIndex = 0`
4. 开始自动运行流程

### 2. 步骤切换
1. 设备状态变化触发步骤切换
2. 调用 `HandleProcessChangeInAutoMode()` 方法
3. 如果切换到拍照步骤，重置 `_currentPinIndex = 0`
4. 为新步骤的引脚拍摄做准备

### 3. 引脚拍摄
1. PLC发送硬件触发信号
2. OPT相机接收触发信号
3. 触发 `RequestAutoTriggerSave` 事件
4. 调用 `_optVisionService_RequestAutoTriggerSave()` 方法
5. 递增 `_currentPinIndex++`
6. 生成带引脚编号的文件名：`原文件名-{引脚编号}`
7. 保存照片

## 命名示例

假设原始文件名为 `Product_20250122_143025`，引脚拍摄的文件名将是：

- 第1个引脚：`Product_20250122_143025-1.jpg`
- 第2个引脚：`Product_20250122_143025-2.jpg`
- 第3个引脚：`Product_20250122_143025-3.jpg`
- ...以此类推

## 方向考虑

引脚编号的递增是基于回调次数，不依赖于PLC的处理逻辑。这样设计的优点：

1. **简单可靠**：不需要复杂的方向计算，直接按回调顺序递增
2. **与硬件解耦**：不依赖PLC的引脚顺序处理
3. **容错性好**：即使PLC顺序有变化，照片命名仍然连续

### 方向处理逻辑

- **步骤级重置**：每次切换到新的拍照步骤时，引脚计数器重置为0
- **回调递增**：每次OPT相机触发保存时，计数器递增
- **连续编号**：确保同一步骤内的引脚照片编号连续（1, 2, 3, ...）

## 注意事项

1. **计数器重置时机**：
   - 开始自动运行时重置
   - 切换到新的拍照步骤时重置

2. **编号从1开始**：引脚编号从1开始，符合常规习惯

3. **异常处理**：照片保存失败不影响计数器递增，确保编号连续性

4. **多步骤支持**：每个拍照步骤都有独立的引脚编号序列

## 测试建议

1. **单步骤测试**：测试单个拍照步骤的引脚编号是否正确递增
2. **多步骤测试**：测试多个拍照步骤之间的计数器重置
3. **异常测试**：测试照片保存失败时编号的连续性
4. **重启测试**：测试重新开始自动运行时计数器的重置
