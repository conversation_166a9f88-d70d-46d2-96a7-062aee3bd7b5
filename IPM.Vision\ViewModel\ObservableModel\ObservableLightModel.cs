﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableLightModel : ViewModelBase
    {
        private string _id;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _paraName;
        public string ParamName
        {
            get => _paraName;
            set => SetProperty(ref _paraName, value);
        }

        private int _lOneLuminance;
        public int LOneLuminance
        {
            get => _lOneLuminance;
            set => SetProperty(ref _lOneLuminance, value);
        }

        private int _lTwoLuminance;
        public int LTwoLuminance
        {
            get => _lTwoLuminance;
            set => SetProperty(ref _lTwoLuminance, value);
        }

        private int _lThreeLuminance;
        public int LThreeLuminance
        {
            get => _lThreeLuminance;
            set => SetProperty(ref _lThreeLuminance, value);
        }

        private int _lFourLuminance;
        public int LFourLuminance
        {
            get => _lFourLuminance;
            set => SetProperty(ref _lFourLuminance, value);
        }

        private int _lFiveLuminance;
        public int LFiveLuminance
        {
            get => _lFiveLuminance;
            set => SetProperty(ref _lFiveLuminance, value);
        }

        private int _lSixLuminance;
        public int LSixLuminance
        {
            get => _lSixLuminance;
            set => SetProperty(ref _lSixLuminance, value);
        }

        private int _lSevenLuminance;
        public int LSevenLuminance
        {
            get => _lSevenLuminance;
            set => SetProperty(ref _lSevenLuminance, value);
        }

        private int _lEightLuminance;
        public int LEightLuminance
        {
            get => _lEightLuminance;
            set => SetProperty(ref _lEightLuminance, value);
        }

        private int _lNineLuminance;
        public int LNineLuminance
        {
            get => _lNineLuminance;
            set => SetProperty(ref _lNineLuminance, value);
        }

        private int _lTenLuminance;
        public int LTenLuminance
        {
            get => _lTenLuminance;
            set => SetProperty(ref _lTenLuminance, value);
        }

        private int _lElevenLuminance;
        public int LElevenLuminance
        {
            get => _lElevenLuminance;
            set => SetProperty(ref _lElevenLuminance, value);
        }

        private int _lTwelveLuminance;
        public int LTwelveLuminance
        {
            get => _lTwelveLuminance;
            set => SetProperty(ref _lTwelveLuminance, value);
        }

        private int _lThirteenLuminance;
        public int LThirteenLuminance
        {
            get => _lThirteenLuminance;
            set => SetProperty(ref _lThirteenLuminance, value);
        }

        private int _lFourteenLuminance;
        public int LFourteenLuminance
        {
            get => _lFourteenLuminance;
            set => SetProperty(ref _lFourteenLuminance, value);
        }

        private int _lFifteenLuminance;
        public int LFifteenLuminance
        {
            get => _lFifteenLuminance;
            set => SetProperty(ref _lFifteenLuminance, value);
        }

        private int _lSixteenLuminance;
        public int LSixteenLuminance
        {
            get => _lSixteenLuminance;
            set => SetProperty(ref _lSixteenLuminance, value);
        }

        private string _operator;
        public string Operator
        {
            get => _operator;
            set => SetProperty(ref _operator, value);
        }

        private DateTime _createTime;
        public DateTime CreateTime
        {
            get => _createTime;
            set => SetProperty(ref _createTime, value);
        }
    }
}
