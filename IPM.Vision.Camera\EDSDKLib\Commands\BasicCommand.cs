﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public abstract class BasicCommand
    {
        protected CanonCameraModel _model;
        public BasicCommand(ref CanonCameraModel model)
        {
            _model = model;
        }

        public CameraModel GetCameraModel()
        {
            return _model;
        }

        public virtual bool Execute()
        {
            return true;
        }
    }
}
