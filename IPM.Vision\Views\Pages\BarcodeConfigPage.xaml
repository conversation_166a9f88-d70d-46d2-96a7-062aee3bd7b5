﻿<Page x:Class="IPM.Vision.Views.Pages.BarcodeConfigPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:IPM.Vision.Views.Pages"
      DataContext="{Binding BarcodeConfigPageViewModel, Source={StaticResource Locator}}"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      mc:Ignorable="d"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="BarcodeConfigPage">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Grid.Row="1" Style="{StaticResource Body}" Padding="10">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <hc:UniformSpacingPanel Orientation="Horizontal" Grid.Row="0" Spacing="10">
                <Button Style="{StaticResource ButtonPrimary}" FontSize="16" Height="35" Content="&#xf067; 添加规则" FontFamily="{StaticResource FontAwesome}" Command="{Binding AddRuleCommand}"/>
                <!--<Button Style="{StaticResource ButtonDanger}" Content="&#xf014; 批量删除" FontFamily="{StaticResource FontAwesome}"/>-->
            </hc:UniformSpacingPanel>
            <DataGrid Margin="0 10 0 0" Grid.Row="1" Style="{StaticResource DataGridBaseStyle}"
                      AutoGenerateColumns="False"
                      ItemsSource="{Binding DataList}"
                      RowHeaderWidth="60"
                      HeadersVisibility="All"
                      hc:DataGridAttach.ShowRowNumber="True"
                      RowDetailsVisibilityMode="Collapsed"
                      CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                      ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="条码规则" Width="*"  IsReadOnly="True"  Binding="{Binding Rule}"/>
                    <DataGridTextColumn Header="条码类型" Width="*"  IsReadOnly="True"  Binding="{Binding RuleTypeName}"/>
                    <DataGridTextColumn Header="执行顺序" Width="*" IsReadOnly="True" Binding="{Binding SortNumber}"/>
                    <DataGridTextColumn Header="是否移除" Width="*"  IsReadOnly="True"  Binding="{Binding IsRemoveName}"/>
                    <DataGridTemplateColumn IsReadOnly="True" Width="220" CanUserResize="False" Header="操作">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="&#xf044;"
                                            FontFamily="{StaticResource FontAwesome}"
                                            Style="{StaticResource ButtonInfo}"
                                            Command="{Binding DataContext.ModifyRuleCommand,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=DataGrid}}"
                                            CommandParameter="{Binding}"/>
                                    <Button Content="&#xf1f8;"
                                            Style="{StaticResource ButtonDanger}"
                                            Margin="5 0 0 0"
                                            FontFamily="{StaticResource FontAwesome}"
                                            Command="{Binding DataContext.DeleteCommand,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=DataGrid}}"
                                            CommandParameter="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
                <DataGrid.RowDetailsTemplate>
                    <DataTemplate>
                        <TextBlock Text="暂无数据" HorizontalAlignment="Center" VerticalAlignment="Center"
                                   Visibility="{Binding DataList, Converter={StaticResource NullToVisibilityConverter}}" />
                    </DataTemplate>
                </DataGrid.RowDetailsTemplate>
            </DataGrid>
        </Grid>
    </Border>
</Page>
