﻿using IPM.Vision.Common;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("barcode_para")]
    public class BarcodeRuleModel:BasicModel
    {
        [SugarColumn(ColumnName = "rule", IsNullable = true)]
        public string Rule { get; set; }

        [SugarColumn(ColumnName = "rule_type", IsNullable = true)]
        public RuleType RuleType { get; set; }

        [SugarColumn(ColumnName = "is_remove", IsNullable = true)]
        public YesOrNo IsRemove { get; set; }

        [SugarColumn(ColumnName = "sort_number", IsNullable = true)]
        public int SortNumber { get; set; }
    }
}
