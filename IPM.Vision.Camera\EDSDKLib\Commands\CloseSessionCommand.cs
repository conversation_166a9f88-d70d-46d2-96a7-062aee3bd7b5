﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class CloseSessionCommand : BasicCommand
    {
        public CloseSessionCommand(ref CanonCameraModel model)
            : base(ref model)
        {
        }

        public override bool Execute()
        {
            uint num = EDSDK.EdsCloseSession(_model.Camera);
            if (num != 0)
            {
                CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                _model.NotifyObservers(e);
            }

            return true;
        }
    }
}
