﻿<UserControl x:Class="IPM.Vision.Layout.MenuControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Layout"
             DataContext="{Binding MenuControlViewModel ,Source={StaticResource Locator}}"
             mc:Ignorable="d"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <UserControl.Resources>
        <Style TargetType="{x:Type ListBox}" x:Key="NavigationListBoxStyle">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="FontFamily" Value="{StaticResource FontAwesome}"/>
            <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
            <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Disabled"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="NavigationListBoxItemStyle" TargetType="{x:Type ListBoxItem}">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ListBoxItem}">
                        <Border x:Name="back" Background="Transparent" Height="60" Margin="10 0 10 0" >
                            <StackPanel VerticalAlignment="Center" Orientation="Vertical">
                                <TextBlock FontSize="20" x:Name="textElement" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding MenuIcon}"/>
                                <TextBlock FontSize="12" x:Name="textElement1" Margin="0 10 0 0" VerticalAlignment="Center" HorizontalAlignment="Center" Text="{Binding MenuName}"/>
                            </StackPanel>
                        </Border>

                        <ControlTemplate.Triggers>
                            <Trigger Property="UIElement.IsMouseOver" Value="True">
                                <Setter Property="UIElement.Opacity" Value=".9" />
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter TargetName="back" Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                <Setter TargetName="back" Property="CornerRadius" Value="5"/>
                                <Setter TargetName="textElement" Property="Foreground" Value="White"/>
                                <Setter TargetName="textElement1" Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>
    <ListBox
        IsEnabled="{Binding IsRunning,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged,Converter={StaticResource Boolean2BooleanReConverter}}"
        Style="{DynamicResource NavigationListBoxStyle}"
        ItemContainerStyle="{StaticResource NavigationListBoxItemStyle}"
        SelectedIndex="{Binding SelectedIndex,UpdateSourceTrigger=PropertyChanged,Mode=OneWay}"
        ItemsSource="{Binding MenuList}">
        <hc:Interaction.Triggers>
            <hc:EventTrigger EventName="SelectionChanged">
                <hc:EventToCommand Command="{Binding SelectedChangedCommand}" PassEventArgsToCommand="True"/>
            </hc:EventTrigger>
        </hc:Interaction.Triggers>
    </ListBox>
</UserControl>
