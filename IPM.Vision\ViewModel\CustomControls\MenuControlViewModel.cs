﻿using CommunityToolkit.Mvvm.Input;
using IPM.Vision.Common;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows;

namespace IPM.Vision.ViewModel.CustomControls
{
    public class MenuControlViewModel : ViewModelBase
    {
        private readonly ObservableGlobalState _globalState;
        private ObservableCollection<MenuModel> _menuList = new ObservableCollection<MenuModel>();
        private int _selectedIndex;
        private bool _isRunning = false;
        public MenuControlViewModel(ObservableGlobalState globalState)
        {
            _globalState = globalState;

            _globalState.NotifyProgressStatus += _globalState_NotifyProgressStatus;
        }

        public bool IsRunning
        {
            get => _isRunning;
            set => SetProperty(ref _isRunning, value);
        }

        private void _globalState_NotifyProgressStatus(bool obj)
        {
            IsRunning = obj;
        }

        public ObservableCollection<MenuModel> MenuList
        {
            get => _menuList;
            set => SetProperty(ref _menuList, value);
        }

        public int SelectedIndex
        {
            get => _selectedIndex;
            set => SetProperty(ref _selectedIndex, value);
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            string content = FileHelper.ReadFile(FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER, GlobalConstants.MENUCONFIG));
            ObservableCollection<MenuModel> menus = JsonConvert.DeserializeObject<ObservableCollection<MenuModel>>(content);
            if (menus != null)
            {
                var temp = menus.Where(item => _globalState.LoginUser.MenuId.Contains(item.MenuId)).ToList();
                MenuList = new ObservableCollection<MenuModel>(temp);
                if (_globalState.CurrentMenu == null) _globalState.CurrentMenu = MenuList[0];
            }
        });

        public IRelayCommand<SelectionChangedEventArgs> SelectedChangedCommand => new RelayCommand<SelectionChangedEventArgs>((param) =>
        {
            if (_globalState.IsRunning)
            {
                HandyControl.Controls.MessageBox.Show("当前正在执行拍照步骤，无法切换菜单！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            var temp = param.AddedItems[0] as MenuModel;
            SelectedIndex = MenuList.IndexOf(temp);
            _globalState.CurrentMenu = temp;
        });
    }
}
