﻿using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using IPM.Vision.ViewModel.ObservableModel;

namespace IPM.Vision.BLL
{
    public class LightParamService : BaseService<LightParamModel>, ILightParamService
    {
        private SerialPortHelper serialPortHelper;
        private readonly ObservableGlobalState _globalState;
        private readonly NLogHelper _logger;
        private bool _isConnect = false;
        public LightParamService(IBaseRepository<LightParamModel> baseRepository,ObservableGlobalState globalState, NLogHelper logger) : base(baseRepository)
        {
            _globalState = globalState;
            _globalState.ConfigChanged += _globalState_ConfigChanged;
            _logger = logger;
        }

        private void _globalState_ConfigChanged(ObservableConfigModel obj)
        {
            if (obj.LightPort == _globalState.AppConfig.LightPort) return;
            DisconnectLight();
            ConnectLight();
        }

        public bool IsConnect { get => _isConnect; }

        public void ConnectLight()
        {
            try
            {
                if (serialPortHelper == null) serialPortHelper = new SerialPortHelper(_globalState.AppConfig.LightPort.ToString());
                serialPortHelper.Open();
                _isConnect = true;
                _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                {
                    StatusShowType = LEvents.ShowType.LABEL,
                    EventCode = LEvents.HEventCode.SUCCESS,
                    SourceType = LEvents.SourceType.LIGHT,
                    EventMessage = "光源连接成功！",
                    EquipmentStatus = 1
                });
            }
            catch (Exception ex) {
                _logger.LogError(ex);
                _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                {
                    StatusShowType = LEvents.ShowType.LABEL,
                    EventCode = LEvents.HEventCode.ERROR,
                    SourceType = LEvents.SourceType.LIGHT,
                    EventMessage = "光源连接失败，检查连接端口！",
                    EquipmentStatus = 3
                });
            }
            
        }

        public void DisconnectLight() { 
            _isConnect = false;
            if(serialPortHelper != null)
                serialPortHelper.Close();
        }

        private string CreateStr(byte command, byte channel, byte data)
        {
            StringBuilder returnStr = new StringBuilder("$");

            returnStr.Append(command);
            returnStr.Append(channel);

            string conHex;
            if (data > 15)
            {
                conHex = string.Format("0{0:X}", data);
            }
            else
            {
                conHex = string.Format("00{0:X}", data);
            }

            returnStr.Append(conHex);

            // Perform XOR operation on all characters
            byte xorData = (byte)returnStr[0];
            for (int i = 1; i < returnStr.Length; i++)
            {
                xorData ^= (byte)returnStr[i];
            }

            returnStr.AppendFormat("{0:X}", xorData);

            return returnStr.ToString();
        }

        public void SetLight(byte channel, byte data)
        {
            string result = CreateStr(3, channel, data);
            _logger.LogInfo(result);

            if (serialPortHelper != null)
            {
                serialPortHelper.Write(result);
            }
        }

        public void SetAllLight(ObservableLightModel model)
        {
            SetLight(1, (byte)model.LOneLuminance);
            SetLight(2, (byte)model.LTwoLuminance);
            SetLight(3, (byte)model.LThreeLuminance);
            SetLight(4, (byte)model.LFourLuminance);
            SetLight(5, (byte)model.LFiveLuminance);
            SetLight(6, (byte)model.LSixLuminance);
            SetLight(7, (byte)model.LSevenLuminance);
            SetLight(8, (byte)model.LEightLuminance);
            SetLight(9, (byte)model.LNineLuminance);
            SetLight(10, (byte)model.LTenLuminance);
            SetLight(11, (byte)model.LElevenLuminance);
            SetLight(12, (byte)model.LTwelveLuminance);
            SetLight(13, (byte)model.LThirteenLuminance);
            SetLight(14, (byte)model.LFourteenLuminance);
            SetLight(15, (byte)model.LFifteenLuminance);
            SetLight(16, (byte)model.LSixteenLuminance);
        }

        public void CloseAllLight()
        {
            for (int i = 1; i <= 16; i++)
            {
                SetLight((byte)i, 0);
            }
        }
    }
}
