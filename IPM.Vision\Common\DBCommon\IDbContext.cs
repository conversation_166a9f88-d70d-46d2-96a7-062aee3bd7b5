﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common.DBCommon
{
    public interface IDbContext : IDisposable
    {
        SqlSugarClient Db { get; }

        void CreateTable(bool Backup = false, int StringDefaultLength = 50, params Type[] types);

        void CreateAllTable(bool Backup = false, int StringDefaultLength = 50);
    }
}
