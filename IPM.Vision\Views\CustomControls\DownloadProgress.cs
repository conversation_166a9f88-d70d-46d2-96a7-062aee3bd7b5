﻿using IPM.Vision.Camera.EDSDKLib.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.CustomControl
{
    public class DownloadProgress : ProgressBar
    {
        public ActionEvent.Command Command { get; set; }

        private delegate void _UpdateProperty(uint value);

        protected void UpdateProperty(uint value)
        {
            if (!this.Dispatcher.CheckAccess()) this.Dispatcher.Invoke(new _UpdateProperty(UpdateProperty), value);
            else
            {
                this.Value = (int)value;
            }


        }
    }
}
