﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class DownloadCommand : BasicCommand
    {
        private IntPtr _directoryItem = IntPtr.Zero;
        private PictureModel _pictureModel;

        private CameraEvent _event;

        public DownloadCommand(ref CanonCameraModel model, ref IntPtr inRef)
            : base(ref model)
        {
            _directoryItem = inRef;
        }

        ~DownloadCommand()
        {
            if (_directoryItem != IntPtr.Zero)
            {
                EDSDK.EdsRelease(_directoryItem);
                _directoryItem = IntPtr.Zero;
            }
        }

        public override bool Execute()
        {
            if (!_model.canDownloadImage)
            {
                return true;
            }
            uint num = 0u;
            IntPtr outStream = IntPtr.Zero;
            num = EDSDK.EdsGetDirectoryItemInfo(_directoryItem, out var outDirItemInfo);
            if (num == 0)
            {
                CameraEvent e = new CameraEvent(CameraEvent.Type.DOWNLOAD_START, outStream);
                _model.NotifyObservers(e);
            }

            if (num == 0)
            {
                if (_pictureModel == null)
                {
                    _pictureModel = new PictureModel();
                }
                FileHelper.CreateFolder(_model.FolderPath);
                string[] array = outDirItemInfo.szFileName.Split('.');
                if (!string.IsNullOrEmpty(_model.FileName))
                {
                    _pictureModel.FileName = _model.FileName + "." + array[1].ToLower();
                }else
                    _pictureModel.FileName = outDirItemInfo.szFileName;

                _pictureModel.FileName = FileHelper.GenerateFileName(_model.FolderPath, _pictureModel.FileName);

                _pictureModel.FileFullPath = FileHelper.ConcatFile(_model.FolderPath, _pictureModel.FileName);
                outDirItemInfo.szFileName = _pictureModel.FileFullPath;
                num = EDSDK.EdsCreateFileStream(outDirItemInfo.szFileName, EDSDK.EdsFileCreateDisposition.CreateAlways, EDSDK.EdsAccess.ReadWrite, out outStream);
            }

            if (num == 0)
            {
                num = EDSDK.EdsSetProgressCallback(outStream, ProgressFunc, EDSDK.EdsProgressOption.Periodically, outStream);
            }

            if (num == 0)
            {
                num = EDSDK.EdsDownload(_directoryItem, outDirItemInfo.Size, outStream);
            }

            if (num == 0)
            {
                num = EDSDK.EdsDownloadComplete(_directoryItem);
            }

            if (_directoryItem != IntPtr.Zero)
            {
                num = EDSDK.EdsRelease(_directoryItem);
                _directoryItem = IntPtr.Zero;
            }

            if (outStream != IntPtr.Zero)
            {
                num = EDSDK.EdsRelease(outStream);
                outStream = IntPtr.Zero;
            }

            if (num == 0)
            {
                CameraEvent e2 = new CameraEvent(CameraEvent.Type.DOWNLOAD_COMPLETE, outStream);
                _model.notifyDownloadComplete(_pictureModel);
                _model.NotifyObservers(e2);
            }

            if (num != 0)
            {
                CameraEvent e3 = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                _model.NotifyObservers(e3);
            }

            return true;
        }

        private uint ProgressFunc(uint inPercent, IntPtr inContext, ref bool outCancel)
        {
            _event = new CameraEvent(CameraEvent.Type.PROGRESS, (IntPtr)inPercent);
            _model.NotifyObservers(_event);
            return 0u;
        }
    }
}
