﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    /// <summary>
    /// 步骤
    /// </summary>
    [SugarTable("process")]
    public class ProcessModel: BasicModel
    {

        [SugarColumn(ColumnName = "process_name")]
        public string ParamName { get; set; }

        [SugarColumn(ColumnName = "process_number")]
        public int ProcessNumber {  get; set; }

        [SugarColumn(ColumnName = "take_mode")]
        public int TakeMode { get; set; }

        [SugarColumn(ColumnName = "take_type")]
        public int TakeType { get; set; }

        [SugarColumn(ColumnName = "picture_layout")]
        public int PictureLayout { get; set; }

        [SugarColumn(ColumnName = "process_type")]
        public int ProcessType { get; set; }

        [SugarColumn(ColumnName = "camera_type")]
        public int CameraType {  get; set; }

        [SugarColumn(ColumnName = "take_time")]
        public int TakeTime { get; set; }

        [SugarColumn(ColumnName = "equipment_para_id", IsNullable = true)]
        public string EquipmentParaId {  get; set; }

        [SugarColumn(ColumnName = "main_camera_id", IsNullable = true)]
        public string MainCameraId {  get; set; }

        [SugarColumn(ColumnName = "mark_camera_id",IsNullable = true)]
        public string MarkCameraId { get; set; }

        [SugarColumn(ColumnName = "light_para_id", IsNullable = true)]
        public string LightParamId {  get; set; }

        

    }
}
