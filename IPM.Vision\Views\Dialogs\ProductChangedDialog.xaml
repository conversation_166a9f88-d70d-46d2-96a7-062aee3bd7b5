﻿<UserControl x:Class="IPM.Vision.Views.Dialogs.ProductChangedDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             DataContext="{Binding ProductChangedDialogViewModel, Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="White" CornerRadius="5" Width="420">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Background="#283643" CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText Text="切换产品" Foreground="White" FontSize="18" VerticalAlignment="Center" Margin="10 0 0 0"/>
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Template="{StaticResource CloseTemplate}"
                            Width="40"
                            Height="40"
                            Content="&#xf00d;"
                            Command="{Binding CloseCommand}"/>
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="2" Margin="20">
                <Grid>
                    <hc:ComboBox Height="36"
                                 BorderBrush="Black"
                                 DisplayMemberPath="ProductNumber"
                                 SelectedItem="{Binding CurrentProduct,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                                 ItemsSource="{Binding DataList}"/>
                </Grid>
            </Border>
            <Border Grid.Row="3" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Button Grid.Column="0" Height="35" Width="220" Style="{StaticResource ButtonPrimary}" Content="确认切换" Command="{Binding SaveCommand}"/>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
