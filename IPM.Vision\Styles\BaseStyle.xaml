﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style x:Key="Body" TargetType="Border">
        <Setter Property="Background" Value="#ffffff" />
        <Setter Property="CornerRadius" Value="10" />
        <Setter Property="BorderThickness" Value="5" />
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect
                    Direction="0"
                    Opacity="0.1"
                    ShadowDepth="0"
                    Color="Gray" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style
        x:Key="LListBoxStyle"
        BasedOn="{StaticResource ListBoxBaseStyle}"
        TargetType="{x:Type ListBox}">
        <Setter Property="ScrollViewer.CanContentScroll" Value="True" />
        <Setter Property="ItemContainerStyle" Value="{StaticResource ClockListBoxItemStyle}" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
    </Style>

    <Style
        x:Key="LDataGridTextCenterColumnStyle"
        BasedOn="{StaticResource DataGridCellStyle}"
        TargetType="DataGridCell">
        <Setter Property="HorizontalAlignment" Value="Center" />
    </Style>
    <Style
        x:Key="LDataGridTextCenterHeaderStyle"
        BasedOn="{StaticResource DataGridColumnHeaderStyle}"
        TargetType="DataGridColumnHeader">
        <Setter Property="HorizontalAlignment" Value="Center" />
    </Style>
    <ControlTemplate x:Key="CloseTemplate" TargetType="Button">
        <Border
            Name="back"
            Background="Transparent"
            CornerRadius="0,5,0,0">
            <TextBlock
                Name="back_content"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontFamily="{DynamicResource FontAwesome}"
                FontSize="18"
                Foreground="White"
                Text="{Binding Content, RelativeSource={RelativeSource AncestorType=Button, Mode=FindAncestor}}" />
        </Border>

        <ControlTemplate.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter TargetName="back" Property="Background" Value="#EE2C2C" />
                <Setter TargetName="back_content" Property="Foreground" Value="#ffffff" />
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter TargetName="back" Property="Background" Value="#EE2C2C" />
                <Setter TargetName="back_content" Property="Foreground" Value="#ffffff" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
</ResourceDictionary>