﻿using CommunityToolkit.Mvvm.DependencyInjection;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Comm;
using IPM.Vision.Common;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Common.OPCHelper;
using IPM.Vision.DAL;
using IPM.Vision.DAL.Interfaces;
using IPM.Vision.Mappers;
using IPM.Vision.ViewModel.CustomControls;
using IPM.Vision.ViewModel.Dialogs;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.ViewModel.Pages;
using IPM.Vision.Views.Pages;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel
{
    public class ViewModelLocator
    {
        public ViewModelLocator() {

            RegisterMapper();
            var services = new ServiceCollection();

            services.AddSingleton<IDbContext,AppDbContext>();
            services.AddSingleton(typeof(IBaseRepository<>), typeof(RepositoryBase<>));

            #region viewModel

            services.AddSingleton<MenuControlViewModel>();
            services.AddSingleton<MainWindowViewModel>();
            services.AddSingleton<LoginWindowViewModel>();
            services.AddSingleton<HKVisionControlViewModel>();
            services.AddSingleton<UserManageViewModel>();
            services.AddSingleton<SettingPageViewModel>();
            services.AddSingleton<BarcodeConfigPageViewModel>();
            services.AddSingleton<EquipmentManageViewModel>();
            services.AddSingleton<ProductManagePageViewModel>();
            services.AddSingleton<LightControlViewModel>();
            services.AddSingleton<EquipmentControlViewModel>();
            services.AddSingleton<Pages.CameraControlViewModel>();
            services.AddSingleton<CaptureViewModel>();
            services.AddTransient<CameraCtrlDialogViewModel>();
            services.AddSingleton<InfoControlViewModel>();
            services.AddSingleton<ReportPageViewModel>();
            services.AddSingleton<VisionPageViewModel>();
            services.AddSingleton<OptCameraControlViewModel>();
            services.AddTransient<TestWindowViewModel>();
            services.AddSingleton<ManualReviewPageViewModel>();

            services.AddTransient<BarcodeRuleDialogViewModel>();
            services.AddTransient<UserManageDialogViewModel>();
            services.AddTransient<EquipmentCtrlDialogViewModel>();
            services.AddTransient<ProductManageDialogViewModel>();
            services.AddTransient<ProcessManageDialogViewModel>();
            services.AddTransient<LightCtrlDialogViewModel>();
            services.AddTransient<CanonEvfBoxViewModel>();
            services.AddTransient<CameraCtrlControlViewModel>();
            services.AddTransient<EquipmentCtrlControlViewModel>();
            services.AddTransient<LightCtrlControlViewModel>();
            services.AddTransient<ProductChangedDialogViewModel>();
            services.AddTransient<CompInfoDialogViewModel>();
            services.AddTransient<ImportPinCoordinatesDialogViewModel>();
            
           
            
            #endregion

            services.AddSingleton<ObservableGlobalState>();
            services.AddSingleton<NLogHelper>();
            services.AddSingleton<LoginService>();
            services.AddSingleton<HKVisionService>();
            services.AddSingleton<OpcUAHelper>();
            services.AddSingleton<RestHelper>();
            services.AddSingleton<OptVisionService>();
            services.AddSingleton<APIServices>();
            services.AddSingleton<CheckService>();
            services.AddSingleton<ExcelHelper>();
            services.AddSingleton<GlobalCameraManager>(); // 注册全局相机管理器

            services.AddSingleton<IUserRepository, UserRepository>();
            services.AddSingleton<IBarcodeRuleRepository, BarcodeRuleRepository>();
            services.AddSingleton<IEquipmentRepository, EquipmentRepository>();
            services.AddSingleton<IMainCameraParaRepository, MainCameraParaRepository>();
            services.AddSingleton<IProductParaRepository, ProductParaRepository>();
            services.AddSingleton<IProcessParaRepository, ProcessParaRepository>();
            services.AddSingleton<IFocusPointRepository, FocusPointRepository>();
            services.AddSingleton<IReportRepository, ReportRepository>();
            services.AddSingleton<ICompRepository, CompRepository>();
            services.AddSingleton<ICompInfoRepository, CompInfoRepository>();

            services.AddSingleton<IUserService,UserService>();
            services.AddSingleton<IBarcodeRuleService, BarcodeRuleService>();
            services.AddSingleton<EquipmentService>();
            services.AddSingleton<IEquipmentService>(provider => provider.GetService<EquipmentService>());
            services.AddSingleton<IMainCameraParaService, MainCameraParaService>();
            services.AddSingleton<IProductParaService, ProductParaService>();
            services.AddSingleton<IProcessParaService, ProcessParaService>();
            services.AddSingleton<IFocusPointService, FocusPointService>();
            services.AddSingleton<ILightParamService, LightParamService>();
            services.AddSingleton<IReportService, ReportService>();
            services.AddSingleton<ICompService, CompService>();
            services.AddSingleton<ICompInfoService, CompInfoService>();
            services.AddSingleton<WeightService>();
            

            Ioc.Default.ConfigureServices(services.BuildServiceProvider());
        }

        public static MenuControlViewModel MenuControlViewModel => Ioc.Default.GetService<MenuControlViewModel>();

        public static OptCameraControlViewModel OptCameraControlViewModel => Ioc.Default.GetService<OptCameraControlViewModel>();

        public static SettingPageViewModel SettingPageViewModel => Ioc.Default.GetService<SettingPageViewModel>();

        public static EquipmentCtrlDialogViewModel EquipmentCtrlDialogViewModel => Ioc.Default.GetService<EquipmentCtrlDialogViewModel>();

        public static MainWindowViewModel MainWindowViewModel => Ioc.Default.GetService<MainWindowViewModel>();

        public static LightControlViewModel LightControlViewModel => Ioc.Default.GetService<LightControlViewModel>();

        public static EquipmentControlViewModel EquipmentControlViewModel => Ioc.Default.GetService<EquipmentControlViewModel>();

        public static Pages.CameraControlViewModel CameraControlViewModel => Ioc.Default.GetService<Pages.CameraControlViewModel>();

        public static LoginWindowViewModel LoginWindowViewModel => Ioc.Default.GetService<LoginWindowViewModel>();

        public static EquipmentManageViewModel EquipmentManageViewModel => Ioc.Default.GetService<EquipmentManageViewModel>();

        public static HKVisionControlViewModel HKVisionControlViewModel => Ioc.Default.GetService<HKVisionControlViewModel>();

        public static UserManageViewModel UserManageViewModel => Ioc.Default.GetService<UserManageViewModel>();

        public static VisionPageViewModel VisionPageViewModel => Ioc.Default.GetService<VisionPageViewModel>();

        public static BarcodeConfigPageViewModel BarcodeConfigPageViewModel => Ioc.Default.GetService<BarcodeConfigPageViewModel>();

        public static CompInfoDialogViewModel CompInfoDialogViewModel => Ioc.Default.GetService<CompInfoDialogViewModel>();

        public static UserManageDialogViewModel UserManageDialogViewModel => Ioc.Default.GetService<UserManageDialogViewModel>();

        public static ProductManagePageViewModel ProductManagePageViewModel => Ioc.Default.GetService<ProductManagePageViewModel>();

        public static BarcodeRuleDialogViewModel BarcodeRuleDialogViewModel => Ioc.Default.GetService<BarcodeRuleDialogViewModel>();

        public static ProductManageDialogViewModel ProductManageDialogViewModel => Ioc.Default.GetService<ProductManageDialogViewModel>();

        public static ProcessManageDialogViewModel ProcessManageDialogViewModel => Ioc.Default.GetService<ProcessManageDialogViewModel>();

        public static CanonEvfBoxViewModel CanonEvfBoxViewModel => Ioc.Default.GetService<CanonEvfBoxViewModel>();

        public static CaptureViewModel CaptureViewModel => Ioc.Default.GetService<CaptureViewModel>();

        public static CameraCtrlDialogViewModel CameraCtrlDialogViewModel => Ioc.Default.GetService<CameraCtrlDialogViewModel>();

        public static LightCtrlDialogViewModel LightCtrlDialogViewModel => Ioc.Default.GetService<LightCtrlDialogViewModel>();

        public static CameraCtrlControlViewModel CameraCtrlControlViewModel => Ioc.Default.GetService<CameraCtrlControlViewModel>();

        public static EquipmentCtrlControlViewModel EquipmentCtrlControlViewModel => Ioc.Default.GetService<EquipmentCtrlControlViewModel>();

        public static LightCtrlControlViewModel LightCtrlControlViewModel => Ioc.Default.GetService<LightCtrlControlViewModel>();

        public static ProductChangedDialogViewModel ProductChangedDialogViewModel => Ioc.Default.GetService<ProductChangedDialogViewModel>();

        public static InfoControlViewModel InfoControlViewModel => Ioc.Default.GetService<InfoControlViewModel>();

        public static ReportPageViewModel ReportPageViewModel => Ioc.Default.GetService<ReportPageViewModel>();

        public static ImportPinCoordinatesDialogViewModel ImportPinCoordinatesDialogViewModel => Ioc.Default.GetService<ImportPinCoordinatesDialogViewModel>();

        public static TestWindowViewModel TestWindowViewModel => Ioc.Default.GetService<TestWindowViewModel>();

        public static ManualReviewPageViewModel ManualReviewPageViewModel => Ioc.Default.GetService<ManualReviewPageViewModel>();

        private void RegisterMapper()
        {
            AutoMapperConfiguration.Instance.Init();
        }
    }
}
