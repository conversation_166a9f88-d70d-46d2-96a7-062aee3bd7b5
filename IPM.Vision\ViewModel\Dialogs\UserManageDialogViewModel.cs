﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows;
using IPM.Vision.Mappers;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class UserManageDialogViewModel : ViewModelBase,IDialogResultable<bool>
    {
        private bool _result;
        private string _message = string.Empty;
        private string _title = string.Empty;
        private ObservableUserInfoModel _userInfo;
        private ObservableCollection<MenuModel> _menuList;
        private readonly UserService _userService;
        private bool _isInsert = false;
        public UserManageDialogViewModel(IUserService userService) {
            _userService = (UserService)userService;
        }

        public bool IsInsert
        {
            get => _isInsert;
            set => SetProperty(ref _isInsert, value);
        }

        public ObservableCollection<MenuModel> MenuList
        {
            get => _menuList;
            set => SetProperty(ref _menuList, value);
        }

        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }
        public ObservableUserInfoModel UserInfo
        {
            get => _userInfo;
            set => SetProperty(ref _userInfo, value);
        }
        public bool Result { get => _result; set => SetProperty(ref _result, value); }
        public Action CloseAction { get; set; }
        public RelayCommand CloseCommand => new RelayCommand(() =>
        {
            CloseAction?.Invoke();
        });

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            if (UserInfo == null) UserInfo = new ObservableUserInfoModel();
            string content = FileHelper.ReadFile(FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER, GlobalConstants.MENUCONFIG));
            ObservableCollection<MenuModel> menus = JsonConvert.DeserializeObject<ObservableCollection<MenuModel>>(content);
            if (menus != null)
            {
                MenuList = menus;
            }
        });

        public IRelayCommand SaveCommand => new RelayCommand(async () => {
            if (string.IsNullOrEmpty(UserInfo.Account) || string.IsNullOrEmpty(UserInfo.Password)) return;
            var userInfo = UserInfo.MapTo<ObservableUserInfoModel, UserModel>();
            if (IsInsert)
            {
                var checkTemp = await _userService.getFirstOrDefault(item => item.Account == UserInfo.Account);
                if (checkTemp != null)
                {
                    Message = "账号已存在！";
                    return;
                }
                userInfo.Id = Guid.NewGuid().ToString();
                Result = await _userService.Add(userInfo);
            }
            else
            {
                Result = await _userService.Update(userInfo);
            }

            CloseAction?.Invoke();
        });

        public IAsyncRelayCommand<RoutedEventArgs> MenuLoadedCommand => new AsyncRelayCommand<RoutedEventArgs>(MenuLoadEvent);
        public IAsyncRelayCommand<SelectionChangedEventArgs> MenuChangeCommand => new AsyncRelayCommand<SelectionChangedEventArgs>(MenuChangeEvent);

        private Task MenuLoadEvent(RoutedEventArgs args)
        {
            var temp = args.Source as CheckComboBox;
            if (UserInfo?.MenuId == null) return Task.CompletedTask;
            if (temp != null)
            {
                var menuList = MenuList?.Where(item => UserInfo.MenuId.Contains(item.MenuId.ToString()));
                foreach (var item in menuList)
                {
                    temp.SelectedItems.Add(item);
                }
            }
            return Task.CompletedTask;
        }

        private Task MenuChangeEvent(SelectionChangedEventArgs args)
        {
            var temp = args.Source as CheckComboBox;
            if (temp != null)
            {
                var result = temp.SelectedItems.Cast<MenuModel>();
                ObservableCollection<string> codes = new ObservableCollection<string>();
                foreach (var item in result)
                {
                    codes.Add(item.MenuId);
                }
                UserInfo.MenuId = codes;
            }
            return Task.CompletedTask;
        }

    }
}
