﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common.DBCommon
{
    public class AppDbContext : IDbContext
    {
        private ConnectionConfig _config;
        private SqlSugarClient _sqlSugarClient;
        private readonly NLogHelper _logger;
        private readonly object _lockObject = new object(); // 线程安全锁
        private volatile bool _isInitialized = false; // 初始化标志

        public AppDbContext(NLogHelper logger)
        {
            _logger = logger;
        }

        public SqlSugarClient Db
        {
            get
            {
                // 双重检查锁定模式，确保线程安全
                if (!_isInitialized)
                {
                    lock (_lockObject)
                    {
                        if (!_isInitialized)
                        {
                            InitClient();
                            _isInitialized = true;
                        }
                    }
                }
                return _sqlSugarClient;
            }
        }

        private void InitClient()
        {
            if (_config == null)
            {
                throw new InvalidOperationException("数据库配置未加载，请先调用 LoadDBConfig 方法");
            }

            // 创建 SqlSugarClient 实例
            _sqlSugarClient = new SqlSugarClient(_config);
            ConfigClient();
        }

        private void ConfigClient()
        {
            if (_sqlSugarClient == null)
                return;
            
            // 配置 SqlSugar
            _sqlSugarClient.Aop.OnLogExecuting = (sql, parameters) =>
            {
                try
                {
                    _logger.LogInfo("SQL:" + sql + ";Param:" + GetSqlParamter(parameters));
                }
                catch (Exception ex)
                {
                    // 避免日志记录异常影响主业务
                    System.Diagnostics.Debug.WriteLine($"SQL日志记录异常: {ex.Message}");
                }
            };
        }

        private string GetSqlParamter(SugarParameter[] param)
        {
            if (param == null || param.Length == 0)
                return "[]";

            try
            {
                List<string> stringList = new List<string>();
                foreach (SugarParameter sugarParameter in param)
                {
                    if (sugarParameter != null)
                    {
                        stringList.Add($"{sugarParameter.ParameterName}:{sugarParameter.Value?.ToString() ?? "null"}");
                    }
                }
                return stringList.ToJson();
            }
            catch (Exception ex)
            {
                return $"[参数序列化异常: {ex.Message}]";
            }
        }

        public void CreateAllTable(bool Backup = false, int StringDefaultLength = 50)
        {
            lock (_lockObject)
            {
                this.Db.CodeFirst.SetStringDefaultLength(StringDefaultLength);
                Type[] array = ((IEnumerable<Type>)Assembly.Load("SqlSugar.Model").GetTypes()).Where<Type>((Func<Type, bool>)(t => t.FullName.Contains("Model"))).ToArray<Type>();
                this.Db.DbMaintenance.CreateDatabase();
                if (Backup)
                    this.Db.CodeFirst.BackupTable().InitTables(array);
                else
                    this.Db.CodeFirst.InitTables(array);
            }
        }

        public void CreateTable(bool Backup = false, int StringDefaultLength = 50, params Type[] types)
        {
            lock (_lockObject)
            {
                try
                {
                    this.Db.CodeFirst.SetStringDefaultLength(StringDefaultLength);
                    if (Backup)
                        this.Db.CodeFirst.BackupTable().InitTables(types);
                    else
                        this.Db.CodeFirst.InitTables(types);
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"创建表失败: {ex.Message}");
                    
                    // 如果表创建失败，尝试重建表
                    foreach (var type in types)
                    {
                        try
                        {
                            RepairTableStructure(type);
                        }
                        catch (Exception repairEx)
                        {
                            _logger?.LogError($"修复表结构失败 ({type.Name}): {repairEx.Message}");
                            throw;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 修复表结构，解决字段类型不匹配问题
        /// </summary>
        /// <param name="modelType">模型类型</param>
        public void RepairTableStructure(Type modelType)
        {
            try
            {
                _logger?.LogInfo($"开始修复表结构: {modelType.Name}");
                
                // 获取表名
                var tableAttribute = modelType.GetCustomAttributes(typeof(SugarTable), false).FirstOrDefault() as SugarTable;
                string tableName = tableAttribute?.TableName ?? modelType.Name.ToLower();
                
                // 检查表是否存在
                bool tableExists = this.Db.DbMaintenance.IsAnyTable(tableName);
                
                if (!tableExists)
                {
                    _logger?.LogInfo($"表不存在，创建新表: {tableName}");
                    this.Db.CodeFirst.InitTables(modelType);
                    return;
                }
                
                // 如果表存在但有问题，重建表
                _logger?.LogInfo($"表存在但可能有结构问题，尝试重建: {tableName}");
                RecreateTable(modelType, tableName);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"修复表结构异常 ({modelType.Name}): {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 重建表（备份数据后重新创建）
        /// </summary>
        private void RecreateTable(Type modelType, string tableName)
        {
            try
            {
                _logger?.LogInfo($"开始重建表: {tableName}");
                
                // 1. 备份现有数据
                string backupTableName = $"{tableName}_backup_{DateTime.Now:yyyyMMddHHmmss}";
                
                try
                {
                    this.Db.Ado.ExecuteCommand($"ALTER TABLE {tableName} RENAME TO {backupTableName}");
                    _logger?.LogInfo($"已备份原表数据到: {backupTableName}");
                }
                catch (Exception backupEx)
                {
                    _logger?.LogError($"备份表失败: {backupEx.Message}");
                    // 如果备份失败，直接删除原表
                    try
                    {
                        this.Db.Ado.ExecuteCommand($"DROP TABLE IF EXISTS {tableName}");
                        _logger?.LogInfo($"已删除原表: {tableName}");
                    }
                    catch { }
                }
                
                // 2. 创建新表
                this.Db.CodeFirst.InitTables(modelType);
                _logger?.LogInfo($"已创建新表: {tableName}");
                
                // 3. 尝试迁移数据
                try
                {
                    var backupExists = this.Db.DbMaintenance.IsAnyTable(backupTableName);
                    if (backupExists)
                    {
                        MigrateDataFromBackup(tableName, backupTableName);
                        
                        // 4. 删除备份表
                        this.Db.Ado.ExecuteCommand($"DROP TABLE {backupTableName}");
                        _logger?.LogInfo($"数据迁移完成，已删除备份表: {backupTableName}");
                    }
                }
                catch (Exception migrateEx)
                {
                    _logger?.LogError($"数据迁移失败: {migrateEx.Message}，备份表保留为: {backupTableName}");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"重建表失败: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 从备份表迁移数据
        /// </summary>
        private void MigrateDataFromBackup(string newTableName, string backupTableName)
        {
            try
            {
                // 使用简单的方式迁移数据，只迁移公共字段
                string sql = $@"
                    INSERT INTO {newTableName} (
                        id, order_number, serial_number, picture_name, picture_path, 
                        product_number, weight, process_id, process_name, create_time, 
                        operator, check_path, work_station, status, check_time
                    )
                    SELECT 
                        id, order_number, serial_number, picture_name, picture_path,
                        product_number, weight, process_id, process_name, create_time,
                        operator, check_path, work_station, status, check_time
                    FROM {backupTableName}
                    WHERE id IS NOT NULL";
                
                this.Db.Ado.ExecuteCommand(sql);
                _logger?.LogInfo($"已迁移基础数据字段");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"数据迁移异常: {ex.Message}");
                // 迁移失败不抛异常，保留备份表
            }
        }

        public void LoadDBConfig(string source)
        {
            lock (_lockObject)
            {
                if (_config == null)
                {
                    _config = new ConnectionConfig()
                    {
                        DbType = DbType.Sqlite,
                        // **修复：添加SQLite并发访问优化参数**
                        ConnectionString = $"Data Source=\"{source}\";Cache=Shared;Journal Mode=WAL;Synchronous=Normal;Timeout=30000;Pooling=true;Max Pool Size=100;",
                        IsAutoCloseConnection = true,
                        // **修复：添加更多配置以提高并发性能**
                        ConfigureExternalServices = new ConfigureExternalServices()
                        {
                            // 设置序列化服务，避免并发序列化问题
                            SerializeService = new SerializeService()
                        }
                    };
                }
                
                // 重置初始化状态，以便重新初始化客户端
                _isInitialized = false;
            }
        }

        // 实现 IDisposable 接口以正确释放资源
        public void Dispose()
        {
            lock (_lockObject)
            {
                if (_sqlSugarClient != null)
                {
                    try
                    {
                        _sqlSugarClient.Dispose();
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError($"释放 SqlSugarClient 异常: {ex.Message}");
                    }
                    finally
                    {
                        _sqlSugarClient = null;
                        _isInitialized = false;
                    }
                }
            }
        }
    }
}
