using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.ViewModel.Pages;
using IPM.Vision.Model;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;
using System.Diagnostics;
using System.IO;
using System.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using IPM.Vision.Camera.Com;

namespace IPM.Vision.ViewModel
{
    /// <summary>
    /// 自动运行模式逻辑测试场景
    /// </summary>
    public enum AutoRunTestScenario
    {
        Normal,              // 正常流程
        DeviceError,         // 设备错误
        FastStatusChange,    // 快速状态变化
        MarkPointFailure,    // Mark点识别失败
        CameraTriggerTest,   // 相机触发测试
        ConcurrentEvents     // 并发事件测试（设备状态2 + Mark点识别成功）
    }

    public class TestWindowViewModel : ViewModelBase, INotifyPropertyChanged, IDisposable
    {
        private readonly OptVisionService _optVisionService;
        private readonly EquipmentService _equipmentService;
        private readonly HKVisionService _hkVisionService;
        private readonly CheckService _checkService;
        private readonly NLogHelper _logger;
        private readonly ObservableGlobalState _globalState;

        // 测试相关字段
        private bool _isTestRunning = false;
        private CancellationTokenSource _cancellationTokenSource;
        private Timer _testTimer;
        private int _executionCount = 0;
        private int _currentStepIndex = 0;
        private List<ObservableProcessModel> _testSteps;

        // UI绑定属性
        private AutoRunTestScenario _selectedScenario = AutoRunTestScenario.Normal;
        private string _deviceStatusInterval = "2000";
        private string _markPointDelay = "1500";
        private string _photoTriggerDelay = "800";
        private bool _simulateErrors = false;
        private bool _enableDetailedLog = true;
        private string _logContent = "";
        private bool _autoScrollToBottom = true;
        private string _testStatus = "未开始";
        private string _currentStep = "无";
        private string _deviceStatus = "未知";

        public TestWindowViewModel(OptVisionService optVisionService, EquipmentService equipmentService,
            HKVisionService hkVisionService, CheckService checkService, NLogHelper logger, ObservableGlobalState globalState)
        {
            _optVisionService = optVisionService;
            _equipmentService = equipmentService;
            _hkVisionService = hkVisionService;
            _checkService = checkService;
            _logger = logger;
            _globalState = globalState;

            InitializeTestScenarios();
            LogMessage("自动运行模式逻辑测试工具已启动");
            LogMessage("💡 此工具将模拟真实硬件数据，触发主逻辑系统的实际运行流程");
        }

        #region 属性

        public ObservableCollection<string> TestScenarios { get; private set; }

        public string SelectedScenario
        {
            get => _selectedScenario.ToString();
            set
            {
                if (Enum.TryParse<AutoRunTestScenario>(value, out var scenario))
                {
                    SetProperty(ref _selectedScenario, scenario);
                    OnPropertyChanged(nameof(ScenarioDescription));
                }
            }
        }

        public string ScenarioDescription
        {
            get
            {
                switch (_selectedScenario)
                {
                    case AutoRunTestScenario.Normal:
                        return "模拟正常的自动运行流程，包括设备状态监控、步骤切换、相机触发等";
                    case AutoRunTestScenario.DeviceError:
                        return "模拟设备错误情况，测试错误处理和恢复机制";
                    case AutoRunTestScenario.FastStatusChange:
                        return "模拟快速连续的设备状态变化，测试防抖机制";
                    case AutoRunTestScenario.MarkPointFailure:
                        return "模拟Mark点识别失败，测试重试和错误处理逻辑";
                    case AutoRunTestScenario.CameraTriggerTest:
                        return "专门测试相机触发流程和引脚位置遍历";
                    case AutoRunTestScenario.ConcurrentEvents:
                        return "同时发送设备状态2（就绪）和Mark点识别成功事件，测试并发事件处理";
                    default:
                        return "未知测试场景";
                }
            }
        }

        public string DeviceStatusInterval
        {
            get => _deviceStatusInterval;
            set => SetProperty(ref _deviceStatusInterval, value);
        }

        public string MarkPointDelay
        {
            get => _markPointDelay;
            set => SetProperty(ref _markPointDelay, value);
        }

        public string PhotoTriggerDelay
        {
            get => _photoTriggerDelay;
            set => SetProperty(ref _photoTriggerDelay, value);
        }

        public bool SimulateErrors
        {
            get => _simulateErrors;
            set => SetProperty(ref _simulateErrors, value);
        }

        public bool EnableDetailedLog
        {
            get => _enableDetailedLog;
            set => SetProperty(ref _enableDetailedLog, value);
        }

        public string LogContent
        {
            get => _logContent;
            set => SetProperty(ref _logContent, value);
        }

        public bool AutoScrollToBottom
        {
            get => _autoScrollToBottom;
            set => SetProperty(ref _autoScrollToBottom, value);
        }

        public bool IsTestRunning
        {
            get => _isTestRunning;
            set
            {
                SetProperty(ref _isTestRunning, value);
                OnPropertyChanged(nameof(CanStartTest));
            }
        }

        public bool CanStartTest => !_isTestRunning;

        public string TestStatus
        {
            get => _testStatus;
            set => SetProperty(ref _testStatus, value);
        }

        public string CurrentStep
        {
            get => _currentStep;
            set => SetProperty(ref _currentStep, value);
        }

        public string DeviceStatus
        {
            get => _deviceStatus;
            set => SetProperty(ref _deviceStatus, value);
        }

        public int ExecutionCount
        {
            get => _executionCount;
            set => SetProperty(ref _executionCount, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 开始测试命令
        /// </summary>
        public IRelayCommand StartTestCommand => new RelayCommand(async () =>
        {
            try
            {
                await StartAutoRunTest();
            }
            catch (Exception ex)
            {
                LogMessage($"❌ 开始测试失败: {ex.Message}");
                _logger.LogError($"开始测试异常: {ex}");
            }
        });

        /// <summary>
        /// 停止测试命令
        /// </summary>
        public IRelayCommand StopTestCommand => new RelayCommand(() =>
        {
            try
            {
                StopAutoRunTest();
            }
            catch (Exception ex)
            {
                LogMessage($"❌ 停止测试失败: {ex.Message}");
                _logger.LogError($"停止测试异常: {ex}");
            }
        });

        /// <summary>
        /// 清空日志命令
        /// </summary>
        public IRelayCommand ClearLogCommand => new RelayCommand(() =>
        {
            LogContent = "";
            LogMessage("日志已清空");
        });

        /// <summary>
        /// 测试设备状态命令
        /// </summary>
        public IRelayCommand TestDeviceStatusCommand => new RelayCommand(async () =>
        {
            LogMessage("🧪 开始快速测试设备状态事件...");
            await SimulateDeviceStatusChange(2, "设备就绪");
            await Task.Delay(1000);
            await SimulateDeviceStatusChange(4, "设备错误");
            await Task.Delay(1000);
            await SimulateDeviceStatusChange(2, "设备就绪");
            LogMessage("✅ 设备状态测试完成");
        });

        /// <summary>
        /// 测试相机回调命令
        /// </summary>
        public IRelayCommand TestCameraCallbackCommand => new RelayCommand(async () =>
        {
            LogMessage("🧪 开始快速测试相机回调事件...");

            // 创建模拟步骤
            var optStep = new ObservableProcessModel
            {
                ParamName = "OPT相机测试",
                ProcessType = ProcessTypeEnum.TAKEPICTURE,
                CameraType = CameraTypeEnum.Main
            };

            var hkStep = new ObservableProcessModel
            {
                ParamName = "HK相机测试",
                ProcessType = ProcessTypeEnum.POINT,
                CameraType = CameraTypeEnum.Child
            };

            await SimulatePhotoTrigger(optStep);
            await Task.Delay(1000);
            await SimulateMarkPointRecognition(true);
            LogMessage("✅ 相机回调测试完成");
        });

        /// <summary>
        /// 测试设备参数命令
        /// </summary>
        public IRelayCommand TestDeviceParametersCommand => new RelayCommand(async () =>
        {
            LogMessage("🧪 开始快速测试设备参数事件...");
            await SimulateDeviceParameters();
            await Task.Delay(1000);
            await SimulateManualPhotoTrigger();
            LogMessage("✅ 设备参数测试完成");
        });

        /// <summary>
        /// 测试并发事件命令
        /// </summary>
        public IRelayCommand TestConcurrentEventsCommand => new RelayCommand(async () =>
        {
            LogMessage("🧪 开始快速测试并发事件...");

            // 创建模拟的Mark点识别图片
            var pictureModel = new PictureModel
            {
                FileName = $"quick_concurrent_test_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                FileFullPath = @"C:\temp\test_images\quick_concurrent_test.jpg"
            };

            LogMessage("⚡ 同时发送设备状态2和Mark点识别成功事件...");

            // 同时发送两个事件
            var task1 = Task.Run(() =>
            {
                _equipmentService.SimulateDeviceStatusChange(2);
                LogMessage("📡 已发送设备状态2事件");
            });

            var task2 = Task.Run(() =>
            {
                _hkVisionService.SimulatePictureCallback(pictureModel);
                LogMessage("📡 已发送Mark点识别成功事件");
            });

            await Task.WhenAll(task1, task2);
            LogMessage("✅ 并发事件测试完成");
        });

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化测试场景
        /// </summary>
        private void InitializeTestScenarios()
        {
            TestScenarios = new ObservableCollection<string>
            {
                AutoRunTestScenario.Normal.ToString(),
                AutoRunTestScenario.DeviceError.ToString(),
                AutoRunTestScenario.FastStatusChange.ToString(),
                AutoRunTestScenario.MarkPointFailure.ToString(),
                AutoRunTestScenario.CameraTriggerTest.ToString(),
                AutoRunTestScenario.ConcurrentEvents.ToString()
            };
        }

        /// <summary>
        /// 记录日志消息
        /// </summary>
        private void LogMessage(string message)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            var logEntry = $"[{timestamp}] {message}\r\n";

            App.Current.Dispatcher.Invoke(() =>
            {
                LogContent += logEntry;
                if (AutoScrollToBottom)
                {
                    // 触发UI滚动到底部的逻辑可以在UI层处理
                }
            });

            if (EnableDetailedLog)
            {
                _logger.LogInfo(message);
            }
        }

        /// <summary>
        /// 开始自动运行测试
        /// </summary>
        private async Task StartAutoRunTest()
        {
            if (IsTestRunning)
            {
                LogMessage("⚠️ 测试已在运行中");
                return;
            }

            // 获取测试步骤
            _testSteps = GetTestSteps();
            if (_testSteps == null || !_testSteps.Any())
            {
                LogMessage("❌ 没有可测试的步骤，请先配置产品流程");
                return;
            }

            // 验证参数
            if (!ValidateParameters())
            {
                return;
            }

            LogMessage($"🚀 开始执行自动运行测试 - 场景: {_selectedScenario}");
            LogMessage($"📋 测试步骤数量: {_testSteps.Count}");
            LogMessage($"⚙️ 参数配置: 设备状态间隔={DeviceStatusInterval}ms, Mark点延迟={MarkPointDelay}ms, 拍照延迟={PhotoTriggerDelay}ms");

            IsTestRunning = true;
            TestStatus = "运行中";
            ExecutionCount = 0;
            _currentStepIndex = 0;

            _cancellationTokenSource = new CancellationTokenSource();

            try
            {
                await ExecuteTestScenario(_selectedScenario);
                LogMessage("✅ 测试执行完成");
                TestStatus = "已完成";
            }
            catch (OperationCanceledException)
            {
                LogMessage("⏹️ 测试已被用户停止");
                TestStatus = "已停止";
            }
            catch (Exception ex)
            {
                LogMessage($"❌ 测试执行异常: {ex.Message}");
                TestStatus = "异常";
                _logger.LogError($"测试执行异常: {ex}");
            }
            finally
            {
                IsTestRunning = false;
                CurrentStep = "无";
                DeviceStatus = "未知";
            }
        }

        /// <summary>
        /// 停止自动运行测试
        /// </summary>
        private void StopAutoRunTest()
        {
            if (!IsTestRunning)
            {
                LogMessage("⚠️ 没有正在运行的测试");
                return;
            }

            LogMessage("🛑 正在停止测试...");

            _cancellationTokenSource?.Cancel();
            _testTimer?.Dispose();
            _testTimer = null;

            IsTestRunning = false;
            TestStatus = "已停止";
            CurrentStep = "无";
            DeviceStatus = "未知";

            LogMessage("✅ 测试已停止");
        }

        /// <summary>
        /// 获取测试步骤
        /// </summary>
        private List<ObservableProcessModel> GetTestSteps()
        {
            var currentProduct = _globalState.CurrentProduct;
            if (currentProduct?.StepList != null && currentProduct.StepList.Any())
            {
                return currentProduct.StepList.ToList();
            }

            // 如果没有配置的步骤，创建默认测试步骤
            LogMessage("📋 使用默认测试步骤");
            return new List<ObservableProcessModel>
            {
                new ObservableProcessModel
                {
                    ProcessNumber = 1,
                    ParamName = "Mark点识别",
                    ProcessType = ProcessTypeEnum.POINT
                },
                new ObservableProcessModel
                {
                    ProcessNumber = 2,
                    ParamName = "Side2",
                    ProcessType = ProcessTypeEnum.TAKEPICTURE
                }
            };
        }

        /// <summary>
        /// 验证测试参数
        /// </summary>
        private bool ValidateParameters()
        {
            if (!int.TryParse(DeviceStatusInterval, out int deviceInterval) || deviceInterval <= 0)
            {
                LogMessage($"❌ 设备状态间隔参数无效: {DeviceStatusInterval}");
                return false;
            }

            if (!int.TryParse(MarkPointDelay, out int markDelay) || markDelay < 0)
            {
                LogMessage($"❌ Mark点识别延迟参数无效: {MarkPointDelay}");
                return false;
            }

            if (!int.TryParse(PhotoTriggerDelay, out int photoDelay) || photoDelay < 0)
            {
                LogMessage($"❌ 拍照触发延迟参数无效: {PhotoTriggerDelay}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 执行测试场景
        /// </summary>
        private async Task ExecuteTestScenario(AutoRunTestScenario scenario)
        {
            switch (scenario)
            {
                case AutoRunTestScenario.Normal:
                    await ExecuteNormalFlow();
                    break;
                case AutoRunTestScenario.DeviceError:
                    await ExecuteDeviceErrorFlow();
                    break;
                case AutoRunTestScenario.FastStatusChange:
                    await ExecuteFastStatusChangeFlow();
                    break;
                case AutoRunTestScenario.MarkPointFailure:
                    await ExecuteMarkPointFailureFlow();
                    break;
                case AutoRunTestScenario.CameraTriggerTest:
                    await ExecuteCameraTriggerFlow();
                    break;
                case AutoRunTestScenario.ConcurrentEvents:
                    await ExecuteConcurrentEventsFlow();
                    break;
                default:
                    LogMessage($"❌ 未知的测试场景: {scenario}");
                    break;
            }
        }

        /// <summary>
        /// 执行正常流程测试
        /// </summary>
        private async Task ExecuteNormalFlow()
        {
            LogMessage("🔄 开始执行正常流程测试");

            for (int cycle = 0; cycle < 3 && !_cancellationTokenSource.Token.IsCancellationRequested; cycle++)
            {
                ExecutionCount = cycle + 1;
                LogMessage($"📋 第 {ExecutionCount} 轮测试开始");

                // 模拟设备状态变为就绪
                await SimulateDeviceStatusChange(2, "设备就绪");

                // 模拟设备参数变化
                await SimulateDeviceParameters();

                // 遍历所有步骤
                for (int i = 0; i < _testSteps.Count && !_cancellationTokenSource.Token.IsCancellationRequested; i++)
                {
                    _currentStepIndex = i;
                    var step = _testSteps[i];
                    CurrentStep = $"步骤{step.ProcessNumber}: {step.ParamName}";

                    LogMessage($"🔧 执行步骤 {step.ProcessNumber}: {step.ParamName}");

                    if (step.ProcessType == ProcessTypeEnum.POINT)
                    {
                        await SimulateMarkPointRecognition(true);
                    }
                    else if (step.ProcessType == ProcessTypeEnum.TAKEPICTURE)
                    {
                        await SimulatePhotoTrigger(step);
                    }

                    // 步骤间延迟
                    await Task.Delay(500, _cancellationTokenSource.Token);
                }

                // 在轮次结束时模拟手动拍照触发
                if (cycle == 1) // 在第二轮测试中模拟手动拍照
                {
                    await SimulateManualPhotoTrigger();
                }

                LogMessage($"✅ 第 {ExecutionCount} 轮测试完成");

                // 轮次间延迟
                if (cycle < 2)
                {
                    await Task.Delay(1000, _cancellationTokenSource.Token);
                }
            }
        }

        /// <summary>
        /// 执行设备错误流程测试
        /// </summary>
        private async Task ExecuteDeviceErrorFlow()
        {
            LogMessage("⚠️ 开始执行设备错误流程测试");

            // 模拟设备错误
            await SimulateDeviceStatusChange(4, "设备错误");
            await Task.Delay(2000, _cancellationTokenSource.Token);

            // 模拟错误恢复
            await SimulateDeviceStatusChange(2, "设备就绪");
            LogMessage("✅ 设备错误流程测试完成");
        }

        /// <summary>
        /// 执行快速状态变化流程测试
        /// </summary>
        private async Task ExecuteFastStatusChangeFlow()
        {
            LogMessage("⚡ 开始执行快速状态变化流程测试");

            for (int i = 0; i < 5 && !_cancellationTokenSource.Token.IsCancellationRequested; i++)
            {
                await SimulateDeviceStatusChange(2, "设备就绪");
                await Task.Delay(200, _cancellationTokenSource.Token);
                await SimulateDeviceStatusChange(1, "设备忙碌");
                await Task.Delay(200, _cancellationTokenSource.Token);
            }

            LogMessage("✅ 快速状态变化流程测试完成");
        }

        /// <summary>
        /// 执行Mark点识别失败流程测试
        /// </summary>
        private async Task ExecuteMarkPointFailureFlow()
        {
            LogMessage("❌ 开始执行Mark点识别失败流程测试");

            await SimulateDeviceStatusChange(2, "设备就绪");

            // 模拟Mark点识别失败
            await SimulateMarkPointRecognition(false);
            await Task.Delay(1000, _cancellationTokenSource.Token);

            // 模拟重试成功
            await SimulateMarkPointRecognition(true);

            LogMessage("✅ Mark点识别失败流程测试完成");
        }

        /// <summary>
        /// 执行相机触发流程测试
        /// </summary>
        private async Task ExecuteCameraTriggerFlow()
        {
            LogMessage("📷 开始执行相机触发流程测试");

            await SimulateDeviceStatusChange(2, "设备就绪");

            // 模拟多个拍照步骤
            for (int i = 0; i < _testSteps.Count && !_cancellationTokenSource.Token.IsCancellationRequested; i++)
            {
                var step = _testSteps[i];
                if (step.ProcessType == ProcessTypeEnum.TAKEPICTURE)
                {
                    CurrentStep = $"步骤{step.ProcessNumber}: {step.ParamName}";
                    await SimulatePhotoTrigger(step);
                    await Task.Delay(1000, _cancellationTokenSource.Token);
                }
            }

            LogMessage("✅ 相机触发流程测试完成");
        }

        /// <summary>
        /// 执行并发事件流程测试 - 同时发送设备状态2和Mark点识别成功
        /// </summary>
        private async Task ExecuteConcurrentEventsFlow()
        {
            LogMessage("⚡ 开始执行并发事件流程测试");
            LogMessage("💡 此测试将同时发送设备状态2（就绪）和Mark点识别成功事件");

            for (int cycle = 0; cycle < 3 && !_cancellationTokenSource.Token.IsCancellationRequested; cycle++)
            {
                ExecutionCount = cycle + 1;
                LogMessage($"📋 第 {ExecutionCount} 轮并发事件测试开始");

                // 等待一段时间，模拟真实场景
                await Task.Delay(1000, _cancellationTokenSource.Token);

                // 创建模拟的Mark点识别图片
                var pictureModel = new PictureModel
                {
                    FileName = $"concurrent_mark_point_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                    FileFullPath = @"C:\temp\test_images\concurrent_mark_point.jpg"
                };

                LogMessage("⚡ 准备同时发送并发事件...");
                LogMessage("   ├─ 事件1: 设备状态变化 → 状态2（设备就绪）");
                LogMessage("   └─ 事件2: Mark点识别成功回调");

                // 同时发送两个事件 - 这是关键测试点
                var task1 = Task.Run(() =>
                {
                    _equipmentService.SimulateDeviceStatusChange(2);
                    LogMessage("📡 已发送设备状态2事件 → 主逻辑系统");
                });

                var task2 = Task.Run(() =>
                {
                    _hkVisionService.SimulatePictureCallback(pictureModel);
                    LogMessage("📡 已发送Mark点识别成功事件 → 主逻辑系统");
                });

                // 等待两个事件都发送完成
                await Task.WhenAll(task1, task2);

                LogMessage("⚡ 并发事件已发送，观察主逻辑处理情况...");

                // 等待主逻辑处理
                await Task.Delay(2000, _cancellationTokenSource.Token);

                LogMessage($"✅ 第 {ExecutionCount} 轮并发事件测试完成");
            }

            LogMessage("✅ 并发事件流程测试完成");
            LogMessage("💡 请观察主逻辑是否正确处理了同时到达的设备状态和Mark点识别事件");
        }

        /// <summary>
        /// 模拟设备状态变化 - 发送真实的设备状态事件给主逻辑
        /// </summary>
        private async Task SimulateDeviceStatusChange(int statusCode, string statusDescription)
        {
            // 更新UI显示
            DeviceStatus = $"{statusCode} - {statusDescription}";
            LogMessage($"🔄 设备状态变化: {statusDescription} (代码: {statusCode})");

            // 发送真实的设备状态事件给主逻辑系统
            try
            {
                // 使用新的模拟方法
                _equipmentService.SimulateDeviceStatusChange(statusCode);
                LogMessage($"📡 已发送设备状态事件: {statusCode} → 主逻辑系统");

                if (EnableDetailedLog)
                {
                    LogMessage($"   └─ 模拟PLC设备状态更新，状态码: {statusCode}");
                    LogMessage($"   └─ 事件类型: McStatusNotify");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ 发送设备状态事件失败: {ex.Message}");
            }

            var interval = int.Parse(DeviceStatusInterval);
            await Task.Delay(interval, _cancellationTokenSource.Token);
        }

        /// <summary>
        /// 模拟Mark点识别 - 触发真实的HK相机回调事件
        /// </summary>
        private async Task SimulateMarkPointRecognition(bool success)
        {
            LogMessage($"🎯 开始Mark点识别...");

            var delay = int.Parse(MarkPointDelay);
            await Task.Delay(delay, _cancellationTokenSource.Token);

            // 创建模拟的图片模型
            var pictureModel = new PictureModel
            {
                FileName = $"mark_point_test_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                FileFullPath = @"C:\temp\test_images\mark_point_test.jpg"
            };

            try
            {
                // 使用新的模拟方法触发HK相机回调事件
                _hkVisionService.SimulatePictureCallback(pictureModel);

                if (success)
                {
                    LogMessage($"✅ Mark点识别成功 - 已触发HK相机回调事件");
                    if (EnableDetailedLog)
                    {
                        LogMessage($"   └─ 模拟图片: {pictureModel.FileName}");
                        LogMessage($"   └─ 识别到Mark点坐标: (150, 250)");
                    }
                }
                else
                {
                    LogMessage($"❌ Mark点识别失败 - 已触发HK相机回调事件");
                    if (EnableDetailedLog)
                    {
                        LogMessage($"   └─ 识别失败原因: 图像质量不佳或Mark点被遮挡");
                    }

                    if (SimulateErrors)
                    {
                        throw new Exception("Mark点识别失败");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ 触发HK相机回调事件失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 模拟拍照触发 - 触发真实的相机事件
        /// </summary>
        private async Task SimulatePhotoTrigger(ObservableProcessModel step)
        {
            LogMessage($"📸 开始拍照触发 - {step.ParamName}");

            var delay = int.Parse(PhotoTriggerDelay);
            await Task.Delay(delay, _cancellationTokenSource.Token);

            // 创建模拟的图片模型
            var pictureModel = new PictureModel
            {
                FileName = $"photo_test_{DateTime.Now:yyyyMMdd_HHmmss}.jpg",
                FileFullPath = @"C:\temp\test_images\photo_test.jpg"
            };

            try
            {
                // 根据相机类型触发不同的事件
                if (step.CameraType == CameraTypeEnum.Main) // OPT相机
                {
                    // 使用新的模拟方法触发OPT相机拍照事件
                    _optVisionService.SimulatePictureCallback(pictureModel);
                    LogMessage($"📡 已触发OPT相机拍照事件 → 主逻辑系统");

                    if (EnableDetailedLog)
                    {
                        LogMessage($"   └─ 模拟图片: {pictureModel.FileName}");
                    }
                }
                else if (step.CameraType == CameraTypeEnum.Child) // HK相机
                {
                    // 使用新的模拟方法触发HK相机拍照事件
                    _hkVisionService.SimulatePictureCallback(pictureModel);
                    LogMessage($"📡 已触发HK相机拍照事件 → 主逻辑系统");

                    if (EnableDetailedLog)
                    {
                        LogMessage($"   └─ 模拟图片: {pictureModel.FileName}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ 触发相机拍照事件失败: {ex.Message}");
            }

            if (EnableDetailedLog)
            {
                LogMessage($"   └─ 相机类型: {(step.CameraType == CameraTypeEnum.Main ? "主相机(OPT)" : "副相机(HK)")}");
                LogMessage($"   └─ 触发模式: 软件触发");
            }

            // 模拟拍照成功
            LogMessage($"✅ 拍照完成 - {step.ParamName}");

            if (EnableDetailedLog)
            {
                LogMessage($"   └─ 图像尺寸: 1920x1080");
                LogMessage($"   └─ 保存路径: /images/test_{DateTime.Now:yyyyMMdd_HHmmss}.jpg");
            }
        }

        /// <summary>
        /// 模拟设备参数变化 - 触发真实的设备参数事件
        /// </summary>
        private async Task SimulateDeviceParameters()
        {
            LogMessage($"🔧 开始模拟设备参数变化...");

            try
            {
                // 模拟角度数据变化
                var angleValue = 45.5f;
                _equipmentService.SimulateDeviceParameterChange(1001, angleValue); // 假设1001是角度参数的标识
                LogMessage($"📡 已发送设备角度参数事件: {angleValue}° → 主逻辑系统");

                if (EnableDetailedLog)
                {
                    LogMessage($"   └─ 参数类型: 角度数据");
                    LogMessage($"   └─ 参数值: {angleValue}°");
                }

                await Task.Delay(500);

                // 模拟位置数据变化
                _equipmentService.SimulateDevicePositionChange(100.5f, 200.3f); // X, Y坐标
                LogMessage($"📡 已发送设备位置参数事件: (100.5, 200.3) → 主逻辑系统");

                if (EnableDetailedLog)
                {
                    LogMessage($"   └─ 参数类型: 位置数据");
                    LogMessage($"   └─ X坐标: 100.5");
                    LogMessage($"   └─ Y坐标: 200.3");
                }

                await Task.Delay(500);

                // 模拟AutoTake事件
                _equipmentService.SimulateAutoTakeEvent();
                LogMessage($"📡 已发送AutoTake事件 → 主逻辑系统");

                if (EnableDetailedLog)
                {
                    LogMessage($"   └─ 事件类型: 自动拍照触发");
                }

                LogMessage($"✅ 设备参数模拟完成");
            }
            catch (Exception ex)
            {
                LogMessage($"❌ 模拟设备参数失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 模拟手动拍照触发 - 触发真实的TakePictureEvent事件
        /// </summary>
        private Task SimulateManualPhotoTrigger()
        {
            LogMessage($"📸 模拟手动拍照触发...");

            try
            {
                // 使用新的模拟方法触发手动拍照事件
                _equipmentService.SimulateManualPhotoEvent();
                LogMessage($"📡 已发送手动拍照事件 → 主逻辑系统");

                if (EnableDetailedLog)
                {
                    LogMessage($"   └─ 事件类型: 手动拍照触发");
                    LogMessage($"   └─ 模拟按钮1+按钮2同时按下");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ 模拟手动拍照触发失败: {ex.Message}");
            }

            return Task.CompletedTask;
        }

        #endregion

        public void Dispose()
        {
            _cancellationTokenSource?.Cancel();
            _testTimer?.Dispose();
        }
    }
}