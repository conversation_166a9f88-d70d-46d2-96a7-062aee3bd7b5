﻿using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.BLL
{
    public class LoginService
    {
        private readonly NLogHelper _logger;
        private readonly UserService _userService;
        private readonly AppDbContext _dbContext;
        private readonly ObservableGlobalState _observableGlobalState;
        public LoginService(NLogHelper logger,IUserService userService, IDbContext appDbContext,ObservableGlobalState globalState)
        {
            _logger = logger;
            _userService = (UserService)userService;
           _dbContext = (AppDbContext)appDbContext;
            _observableGlobalState = globalState;
        }

        /// <summary>
        /// 检查配置文件
        /// </summary>
        public bool CheckConfigFile()
        {
            bool result = true;
            try
            {
                string configFile = FileHelper.ConcatFile(GlobalConstants.BASEDIC, GlobalConstants.CONFIGNAME);
                if (FileHelper.FileExists(configFile))
                {
                    string content = FileHelper.ReadFile(configFile);
                    var config = JsonConvert.DeserializeObject<ConfigModel>(content);
                    if (config != null) _observableGlobalState.AppConfig = config.MapTo<ConfigModel,ObservableConfigModel>();
                    else _observableGlobalState.AppConfig = new ObservableConfigModel();
                }
                else
                {
                    _observableGlobalState.AppConfig = new ObservableConfigModel();
                    var temp = _observableGlobalState.AppConfig.MapTo<ObservableConfigModel,ConfigModel>();
                    FileHelper.WriteFile(configFile, JsonConvert.SerializeObject(temp,Formatting.Indented));
                }

            }
            catch (Exception ex)
            {
                _logger.LogInfo($"软件配置文件加载失败,原因:{ex}");
                throw new Exception("软件配置文件加载失败，重启软件，再次尝试！");
            }
            return result;
        }

        /// <summary>
        /// 检查DLL文件
        /// </summary>
        /// <returns></returns>
        public void CheckDLLFolder()
        {
            try
            {
                FileHelper.CreateFolder(GlobalConstants.DLLFOLDER);
            }
            catch (Exception ex)
            {
                _logger.LogInfo($"DLL文件检测失败,原因:{ex.Message}");
            }
        }

        public bool CheckDB()
        {
            bool result = true;
            try
            {
                string configPath = FileHelper.ConcatFile(GlobalConstants.PRODUCTFOLDER, GlobalConstants.REPORTDB);
                var temp = CheckConfigDB(configPath);
                
                if (!temp)
                {
                    _logger.LogInfo("未查询到配置数据文件，重新初始化...");
                    FileHelper.CreateFileWithoutWriting(configPath);
                    _dbContext.LoadDBConfig(configPath);
                    CreateAllTable();
                    _userService.AddSuperUser();
                    _logger.LogInfo("系统配置数据库初始化成功!");
                }else
                    _dbContext.LoadDBConfig(configPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                throw new Exception("系统配置数据库校验失败！");
            }
            return result;
        }

        public async Task<ObservableUserInfoModel> VerifyUser(string account,string password)
        {
           var result = await _userService.getFirstOrDefault(item=>item.Account == account && item.Password == password);
            return result.MapTo<UserModel,ObservableUserInfoModel>();
        }

        private void CreateAllTable()
        {
            _dbContext.CreateTable(types: typeof(UserModel));
            _dbContext.CreateTable(types: typeof(ProcessModel));
            _dbContext.CreateTable(types: typeof(ProductParamModel));
            _dbContext.CreateTable(types: typeof(BarcodeRuleModel));
            _dbContext.CreateTable(types: typeof(EquipmentModel));
            _dbContext.CreateTable(types: typeof(LightParamModel));
            _dbContext.CreateTable(types: typeof(MainCameraParamModel));
            _dbContext.CreateTable(types: typeof(FocusPointModel));
            _dbContext.CreateTable(types: typeof(ReportModel));
            _dbContext.CreateTable(types: typeof(CompPointModel));
            _dbContext.CreateTable(types: typeof(CompInfoModel));
        }

        private bool CheckConfigDB(string filePath)
        {
            FileHelper.CreateFolder(GlobalConstants.PRODUCTFOLDER);
            return FileHelper.FileExists(filePath);
        }
    }
}
