﻿using IPM.Vision.Model;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;

namespace IPM.Vision.Common
{
    public class RestHelper
    {
        private HttpClient _client;
        private static readonly string qrCode = "qrDetect";

        public RestHelper()
        {

        }

        public async Task<string> CallContourDetectAsync(BitmapImage videoStream)
        {
            if (_client == null) _client = new HttpClient();
            var url = $"http://*************:5000/{qrCode}";
            RestModel restModel = new RestModel()
            {
                Event_Id = Guid.NewGuid().ToString(),
                Event_Name = "service.event.qrDetect",
                file = videoStream,
            };
            using (var form = new MultipartFormDataContent())
            {
                // 创建一个新的 MemoryStream
                using (var ms = new MemoryStream())
                {
                    // 使用 JpegBitmapEncoder 将 BitmapImage 写入 MemoryStream
                    var encoder = new JpegBitmapEncoder();
                    encoder.Frames.Add(BitmapFrame.Create(videoStream));
                    encoder.Save(ms);
                    ms.Position = 0; // 重置流的位置

                    byte[] fileData = ms.ToArray();

                    // 添加 ByteArrayContent 到表单内容
                    var imageContent = new ByteArrayContent(fileData);
                    imageContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue("image/jpeg");
                    form.Add(imageContent, "file", "captured.jpg");
                    
                }

                var response = await _client.PostAsync(url, form);
                response.EnsureSuccessStatusCode();

                var responseString = await response.Content.ReadAsStringAsync();
                var json = JObject.Parse(responseString);
                var message = json["message"].ToString();
                return message;
            }
        }
    }
}
