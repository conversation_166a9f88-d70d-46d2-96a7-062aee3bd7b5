﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.CustomControls
{
    public class LightCtrlControlViewModel:ViewModelBase
    {
        private readonly ObservableGlobalState _globalState;
        private readonly LightParamService _lightParamService;
        private ObservableLightModel _lightModel;
        public LightCtrlControlViewModel(ObservableGlobalState globalState, ILightParamService lightParamService)
        {
            _globalState = globalState;
            _lightParamService = (LightParamService)lightParamService;
            _globalState.ProcessChangedEvent += _globalState_ProcessChangedEvent;
        }

        public ObservableLightModel LightModel
        {
            get => _lightModel;
            set => SetProperty(ref _lightModel, value);
        }

        private void _globalState_ProcessChangedEvent(ObservableProcessModel obj)
        {
            if(obj.LightModel != null)
            {
                LightModel = obj.LightModel;
            }else LightModel = new ObservableLightModel();
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            if(_globalState.CurrentProcessModel != null)
                LightModel = _globalState.CurrentProcessModel.LightModel;
            else
                LightModel = new ObservableLightModel();
        });

        public IRelayCommand UnLoadCommand => new RelayCommand(() =>
        {
            _globalState.ProcessChangedEvent -= _globalState_ProcessChangedEvent;
        });

        public PopupWindow ShowControl { get; set; }

        public IRelayCommand SaveCommand => new RelayCommand(async () => {
            if (!string.IsNullOrEmpty(LightModel.Id))
            {
                await _lightParamService.Update(LightModel.MapTo<ObservableLightModel, LightParamModel>());
            }
            if(ShowControl != null) ShowControl.Close();
        
        });

        public IRelayCommand<string> MainLightChangedCommand => new RelayCommand<string>((channel) =>
        {
            switch (channel)
            {
                case "1":
                    _lightParamService.SetLight(1, (byte)LightModel.LOneLuminance);
                    break;
                case "2":
                    _lightParamService.SetLight(2, (byte)LightModel.LTwoLuminance);
                    break;
                case "3":
                    _lightParamService.SetLight(3, (byte)LightModel.LThreeLuminance);
                    break;
                case "4":
                    _lightParamService.SetLight(4, (byte)LightModel.LFourLuminance);
                    break;
                case "5":
                    _lightParamService.SetLight(5, (byte)LightModel.LFiveLuminance);
                    break;
                case "6":
                    _lightParamService.SetLight(6, (byte)LightModel.LSixLuminance);
                    break;
                case "7":
                    _lightParamService.SetLight(7, (byte)LightModel.LSevenLuminance);
                    break;
                case "8":
                    _lightParamService.SetLight(8, (byte)LightModel.LEightLuminance);
                    break;
                case "9":
                    _lightParamService.SetLight(9, (byte)LightModel.LNineLuminance);
                    break;
                case "10":
                    _lightParamService.SetLight(10, (byte)LightModel.LTenLuminance);
                    break;
                case "11":
                    _lightParamService.SetLight(11, (byte)LightModel.LElevenLuminance);
                    break;
                case "12":
                    _lightParamService.SetLight(12, (byte)LightModel.LTwelveLuminance);
                    break;
                case "13":
                    _lightParamService.SetLight(13, (byte)LightModel.LThirteenLuminance);
                    break;
                case "14":
                    _lightParamService.SetLight(14, (byte)LightModel.LFourteenLuminance);
                    break;
                case "15":
                    _lightParamService.SetLight(15, (byte)LightModel.LFifteenLuminance);
                    break;
                case "16":
                    _lightParamService.SetLight(16, (byte)LightModel.LSixteenLuminance);
                    break;
            }
        });
    }
}
