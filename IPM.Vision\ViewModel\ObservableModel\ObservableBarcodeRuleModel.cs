﻿using IPM.Vision.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableBarcodeRuleModel:ViewModelBase
    {
        private string _id;
        private string _rule;
        private RuleType _ruleType;
        private string _ruleTypeName;
        private YesOrNo _isRemove;
        private string _isRemoveName;
        private int _sortNumber;

        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Rule
        {
            get => _rule;
            set => SetProperty(ref _rule, value);
        }

        public RuleType RuleType
        {
            get => _ruleType;
            set => SetProperty(ref _ruleType, value);
        }

        public YesOrNo IsRemove
        {
            get => _isRemove;
            set => SetProperty(ref _isRemove, value);
        }

        public int SortNumber
        {
            get=> _sortNumber; 
            set => SetProperty(ref _sortNumber, value);
        }

        public string RuleTypeName
        {
            get => _ruleTypeName;
            set => SetProperty(ref _ruleTypeName, value);
        }

        public string IsRemoveName
        {
            get => _isRemoveName;
            set => SetProperty(ref _isRemoveName, value);
        }
    }
}
