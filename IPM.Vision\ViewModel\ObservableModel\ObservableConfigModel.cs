﻿using CommunityToolkit.Mvvm.ComponentModel;
using IPM.Vision.Common;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableConfigModel : ViewModelBase
    {
        private string _logoPath = "pack://application:,,,/Assets/logo.png";
        public string LogoPath
        {
            get => _logoPath;
            set => SetProperty(ref _logoPath, value);
        }

        private string _workStation = string.Empty;
        public string WorkStation
        {
            get => _workStation;
            set => SetProperty(ref _workStation, value);
        }

        private ConnectEnum _scanType;
        public ConnectEnum ScanType
        {
            get => _scanType;
            set => SetProperty(ref _scanType, value);
        }

        private SerialPortEnum _scanPort;
        public SerialPortEnum ScanPort
        {
            get => _scanPort;
            set => SetProperty(ref _scanPort, value);
        }

        private OpenEnum _openOCR;
        public OpenEnum OpenOCR
        {
            get => _openOCR;
            set => SetProperty(ref _openOCR, value);
        }

        private ConnectEnum _lightType;
        public ConnectEnum LightType
        {
            get => _lightType;
            set => SetProperty(ref _lightType, value);
        }

        private SerialPortEnum _lightPort;
        public SerialPortEnum LightPort
        {
            get => _lightPort;
            set => SetProperty(ref _lightPort, value);
        }

        private CameraEnum _mainCameraType;
        public CameraEnum MainCameraType
        {
            get => _mainCameraType;
            set => SetProperty(ref _mainCameraType, value);
        }


        private string _mainCameraAddress = string.Empty;
        public string MainCameraAddress
        {
            get => _mainCameraAddress;
            set => SetProperty(ref _mainCameraAddress, value);
        }

        private OpenEnum _haveMarkCamera = OpenEnum.OPEN; // 修复：默认启用Mark相机功能，便于测试
        public OpenEnum HaveMarkCamera
        {
            get => _haveMarkCamera;
            set => SetProperty(ref _haveMarkCamera, value);
        }

        private string _markCameraAddress = "***********10";
        public string MarkCameraAddress
        {
            get => _markCameraAddress;
            set => SetProperty(ref _markCameraAddress, value);
        }

        private SerialPortEnum _markLightPort;
        public SerialPortEnum MarkLightPort
        {
            get => _markLightPort;
            set => SetProperty(ref _markLightPort, value);
        }

        private string _plcAddress = string.Empty;
        public string PlcAddress
        {
            get => _plcAddress;
            set => SetProperty(ref _plcAddress, value);
        }

        private SerialPortEnum _weighAddress;
        public SerialPortEnum WeighAddress
        {
            get => _weighAddress;
            set => SetProperty(ref _weighAddress, value);
        }

        private FolderEnum _saveType;
        public FolderEnum SaveType
        {
            get => _saveType;
            set => SetProperty(ref _saveType, value);
        }

        private string _saveFolder = "C://Images";
        public string SaveFolder
        {
            get => _saveFolder;
            set => SetProperty(ref _saveFolder, value);
        }

        private string _localAddress = "***********";
        public string LocalAddress
        {
            get => _localAddress;
            set => SetProperty(ref _localAddress, value);
        }

        private string _aiSource = "C://Images/source";
        public string AiSource
        {
            get => _aiSource;
            set => SetProperty(ref _aiSource, value);
        }

        public double _diffRotate = 0.00375;
        public double DiffRotate
        {
            get => _diffRotate;
            set => SetProperty(ref _diffRotate, value);
        }

        private string _aiResult = "C://AIImages//result";
        public string AiResult
        {
            get => _aiResult;
            set => SetProperty(ref _aiResult, value);
        }

        private string _saveFolderAddress = string.Empty;
        public string SaveFolderAddress
        {
            get => _saveFolderAddress;
            set => SetProperty(ref _saveFolderAddress, value);
        }

        private FolderNameTypeEnum _folderNameType;
        public FolderNameTypeEnum FolderNameType
        {
            get => _folderNameType;
            set => SetProperty(ref _folderNameType, value);
        }

        private ObservableCollection<ObservableCustomNameModel> _watermarkList;
        public ObservableCollection<ObservableCustomNameModel> WatermarkList
        {
            get => _watermarkList;
            set => SetProperty(ref _watermarkList, value);
        }

        private ObservableCollection<ObservableCustomNameModel> _pictureList;
        public ObservableCollection<ObservableCustomNameModel> PictureList
        {
            get => _pictureList;
            set => SetProperty(ref _pictureList, value);
        }

        private ObservableCollection<ObservableCustomNameModel> _folderNameList;
        public ObservableCollection<ObservableCustomNameModel> FolderNameList
        {
            get => _folderNameList;
            set => SetProperty(ref _folderNameList, value);
        }

        // ===== MES 输出配置 =====
        private bool _enableMESOutput;
        public bool EnableMESOutput
        {
            get => _enableMESOutput;
            set => SetProperty(ref _enableMESOutput, value);
        }

        private string _mesOutputFolder;
        public string MESOutputFolder
        {
            get => _mesOutputFolder;
            set => SetProperty(ref _mesOutputFolder, value);
        }
    }
}
