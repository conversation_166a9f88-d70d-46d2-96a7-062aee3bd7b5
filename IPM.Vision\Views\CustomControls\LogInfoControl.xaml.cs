﻿using IPM.Vision.Camera.Com;
using IPM.Vision.LEvents;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace IPM.Vision.Views.CustomControls
{
    /// <summary>
    /// LogInfoControl.xaml 的交互逻辑
    /// </summary>
    public partial class LogInfoControl : UserControl,IHObservable
    {
       

        public LogInfoControl()
        {
            InitializeComponent();
            this.DataContext = this;
        }

        public void UpdateAsync(HObservable observable, HEquipmentStatusArgs e)
        {
            
            
        }

    }
}
