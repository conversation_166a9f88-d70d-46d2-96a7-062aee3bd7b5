﻿<UserControl x:Class="IPM.Vision.Views.CanonControls.CanonEvfBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             DataContext="{Binding CanonEvfBoxViewModel, Source={StaticResource Locator}}"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:local="clr-namespace:IPM.Vision.Views.CanonControls"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
        <hc:EventTrigger EventName="Unloaded">
            <hc:EventToCommand Command="{Binding UnLoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="LightGray">
        <Canvas>
            <hc:Interaction.Triggers>
                <hc:EventTrigger EventName="Loaded">
                    <hc:EventToCommand Command="{Binding CanvasLoadedCommand}" PassEventArgsToCommand="True" />
                </hc:EventTrigger>
                <hc:EventTrigger EventName="MouseLeftButtonDown">
                    <hc:EventToCommand Command="{Binding MouseLeftButtonDownCommand}" PassEventArgsToCommand="True" />
                </hc:EventTrigger>
                <hc:EventTrigger EventName="MouseLeftButtonUp">
                    <hc:EventToCommand Command="{Binding MouseLeftButtonUpCommand}" PassEventArgsToCommand="True" />
                </hc:EventTrigger>
            </hc:Interaction.Triggers>
            <Image Source="{Binding CameraImage}"
                   Panel.ZIndex="1"
                   Stretch="UniformToFill"
                   Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}}"
                   Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}"
                   Visibility="{Binding IsConnect, Converter={StaticResource BoolToVisibilityConverter}}">
                <hc:Interaction.Triggers>
                    <hc:EventTrigger EventName="MouseDown">
                        <hc:EventToCommand Command="{Binding ModifyPointerCommand}" PassEventArgsToCommand="True"/>
                    </hc:EventTrigger>
                    <hc:EventTrigger EventName="Loaded">
                        <hc:EventToCommand Command="{Binding ImageLoadedCommand}" PassEventArgsToCommand="True"/>
                    </hc:EventTrigger>
                </hc:Interaction.Triggers>
            </Image>
            <Rectangle Panel.ZIndex="999"
                       Width="{Binding FocusPositions.Width,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                       Height="{Binding FocusPositions.Height,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                       Canvas.Left="{Binding FocusPositions.Left,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                       Canvas.Top="{Binding FocusPositions.Top,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                       Visibility="{Binding IsConnect,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged, Converter={StaticResource BoolToVisibilityConverter}}"
                       Stroke="{Binding FocusPositions.BorderColor,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
            <!-- 显示未连接的提示信息 -->
            <Border
                Visibility="{Binding IsConnect, Converter={StaticResource BoolToVisibilityConverter},ConverterParameter=True}"
                Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}}"
                Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}">
                <Button Content="主相机连接已断开，点击尝试重新连接..."
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Style="{StaticResource ButtonDashedDanger}"
                        FontSize="14"
                        Command="{Binding ReconnectCommand}"
                        Visibility="{Binding IsConnect, Converter={StaticResource BoolToVisibilityConverter},ConverterParameter=True}"/>
            </Border>

        </Canvas>
    </Border>
</UserControl>
