﻿using System;
using System.Collections.Generic;
using System.Diagnostics.Tracing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.LEvents
{
    public class HEquipmentStatusArgs:EventArgs
    {
        public HEventCode EventCode { get; set; } = HEventCode.SUCCESS;
        public ShowType StatusShowType { get; set; } = ShowType.LABEL;
        public SourceType SourceType { get; set; }
        public int EquipmentStatus { get; set; } = 0;
        public string EventMessage { get; set; }
    }
}
