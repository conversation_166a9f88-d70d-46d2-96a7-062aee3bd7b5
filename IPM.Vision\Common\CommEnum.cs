﻿using IPM.Vision.Model;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common
{
    public enum SerialPortEnum
    {
        NONE,
        COM1,
        COM2,
        COM3,
        COM4,
        COM5,
        COM6,
        COM7,
        COM8,
        COM9,
        COM10,
        COM11,
        COM12,
    }

    public enum ConnectEnum
    {
        [Description("串口通讯")]
        SERIAL,

        [Description("USB")]
        USB
    }

    public enum OpenEnum
    {
        [Description("开启")]
        OPEN,

        [Description("关闭")]
        CLOSE
    }

    public enum CameraEnum
    {
        [Description("Canon")]
        Canon = 0,

        [Description("OPT")]
        OPT = 1
    }

    public enum CameraTypeEnum
    {
        [Description("主相机")]
        Main = 0,
        [Description("副相机")]
        Child = 1
    }

    public enum ProcessTypeEnum
    {
        [Description("二维码识别")]
        QRCODE = 0,
        [Description("原点识别")]
        POINT = 1,
        [Description("拍照")]
        TAKEPICTURE = 2,
    }

    public enum FolderEnum
    {
        [Description("本地文件夹")]
        LOCAL,
        [Description("FTP")]
        FTP,
        [Description("远程共享文件夹")]
        ShareFolder
    }

    public enum PinTypeEnum
    {
        [Description("普通元件")]
        COMMON,
        [Description("目标元件")]
        TARGET,
        [Description("原点元件")]
        ROUND
    }

    public enum FolderNameTypeEnum
    {
        [Description("同级关系")]
        SAMELEVEL,

        [Description("层级关系")]
        OTHERLEVEL
    }

    public enum NameEnum
    {
        [Description("产品编号")]
        SERIALNUMBER,
        [Description("步骤名称")]
        STEPNAME,
        [Description("作业计划号")]
        ORDERNUMBER,
        [Description("产品代号")]
        PRODUCTNUMBER,
        [Description("产品名称")]
        PRODUCTNAME,
        [Description("重量")]
        WEIGHT,
        [Description("年")]
        YEAR,
        [Description("月")]
        MOUNTH,
        [Description("日")]
        DATA,
        [Description("时")]
        HOUR,
        [Description("分")]
        MINUTES,
        [Description("秒")]
        SECONDS,
        [Description("自定义字符")]
        CUSTOM,
    }

    public enum PictureType
    {
        [Description("单张")]
        SINGLE,
        [Description("焦点堆叠")]
        POINT,
        [Description("照片合并")]
        MERGE,
        [Description("照片拆分")]
        SPLIT
    }

    public enum RuleType
    {
        [Description("产品编号")]
        SN = 0,
        [Description("作业计划号")]
        ON = 1
    }

    public enum YesOrNo
    {
        [Description("是")]
        YSE = 1,
        [Description("否")]
        NO = 0
    }

    public enum PictureLayoutEnum
    {
        [Description("1X1")]
        ONE,
        [Description("1X2")]
        TWO,
        [Description("2X1")]
        THREE,
        [Description("2X2")]
        FOUR,
        [Description("3X3")]
        FIVE,

    }

    /// <summary>
    /// 引脚拍摄起点位置枚举
    /// </summary>
    public enum PinStartPositionEnum
    {
        [Description("左上角")]
        TopLeft = 0,
        [Description("右上角")]
        TopRight = 1,
        [Description("右下角")]
        BottomRight = 2,
        [Description("左下角")]
        BottomLeft = 3
    }

    /// <summary>
    /// 引脚拍摄方向枚举
    /// </summary>
    public enum PinDirectionEnum
    {
        [Description("顺时针")]
        Clockwise = 0,
        [Description("逆时针")]
        CounterClockwise = 1
    }




    public static class EnumHelper
    {
        public static string GetEnumDescription(Enum value)
        {
            var field = value.GetType().GetField(value.ToString());
            var attribute = (DescriptionAttribute)field.GetCustomAttributes(typeof(DescriptionAttribute), false).FirstOrDefault();

            return attribute?.Description ?? value.ToString();
        }
        public static List<EnumItem> GetEnumList<T>() where T : Enum
        {
            return Enum.GetValues(typeof(T))
                       .Cast<T>()
                       .Select(e => new EnumItem { Label = GetEnumDescription(e), Value = e })
                       .ToList();
        }

        public static T ParseEnum<T>(string value) where T : Enum
        {
            // Using Enum.Parse to convert the string to enum
            return (T)Enum.Parse(typeof(T), value, true); // true ignores case sensitivity
        }

    }


}
