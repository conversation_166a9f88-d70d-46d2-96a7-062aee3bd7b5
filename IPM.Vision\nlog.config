<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
			xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
			autoReload="true"
			internalLogLevel="Warn"
			internalLogFile="internal-nlog.txt">
	<targets>
		<target name="file" xsi:type="AsyncWrapper" queueLimit="5000" overflowAction="Discard">
			<target xsi:type="File" fileName="${basedir}/logs/${event-properties:filename}/${shortdate}.txt"
							layout="${longdate} ${level:uppercase=true} - ${message} ${event-context:item=Amount}"
							archiveAboveSize="1000000" maxArchiveFiles="10"/>
		</target>
		<target name="log_console" xsi:type="ColoredConsole" useDefaultRowHighlightingRules="true" layout="${longdate}|${level}|${logger}|${message} ${exception}">
			<highlight-row condition="level == LogLevel.Trace" foregroundColor="DarkGray" />
			<highlight-row condition="level == LogLevel.Debug" foregroundColor="Gray" />
			<highlight-row condition="level == LogLevel.Info" foregroundColor="White" />
			<highlight-row condition="level == LogLevel.Warn" foregroundColor="Yellow" />
			<highlight-row condition="level == LogLevel.Error" foregroundColor="Red" />
			<highlight-row condition="level == LogLevel.Fatal" foregroundColor="Magenta" backgroundColor="White" />
		</target>
	</targets>

	<rules>
		<logger name="*" minlevel="Info" writeTo="file"/>
		<logger name="*" minlevel="Trace" writeTo="log_console" />
	</rules>
</nlog>