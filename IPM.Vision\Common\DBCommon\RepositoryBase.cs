﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common.DBCommon
{
    public class RepositoryBase<T> : IBaseRepository<T> where T : class, new()
    {
        private readonly IDbContext context;

        public RepositoryBase(IDbContext dbContext) => this.context = dbContext;

        public async Task<bool> Add(T model)
        {
            return await ExecuteWithRetry(async () =>
            {
                int num = await this.context.Db.Insertable<T>(model).ExecuteCommandAsync();
                return num > 0;
            }, "Add");
        }

        public async Task<bool> AddRange(List<T> list)
        {
            int num = await this.context.Db.Insertable<T>(list).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> AddReturnIdentity(T model)
        {
            int num = await this.context.Db.Insertable<T>(model).ExecuteReturnIdentityAsync();
            return num > 0;
        }

        public async Task<T> AddReturnEntity(T model)
        {
            T obj = await this.context.Db.Insertable<T>(model).ExecuteReturnEntityAsync();
            return obj;
        }

        public async Task<bool> AddColumns(T model, params string[] columns)
        {
            int num = await this.context.Db.Insertable<T>(model).InsertColumns(columns).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> AddColumnsByIgnoreColumns(T model, params string[] IgnoreColumns)
        {
            int num = await this.context.Db.Insertable<T>(model).IgnoreColumns(IgnoreColumns).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> Delete<S>(S key)
        {
            int num = await this.context.Db.Deleteable<T>().In<S>(key).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> DeleteRange<S>(params S[] keys)
        {
            int num = await this.context.Db.Deleteable<T>().In<S>(keys).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> DeleteWhere(Expression<System.Func<T, bool>> where)
        {
            int num = await this.context.Db.Deleteable<T>().Where(where).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> Update(T model)
        {
            return await ExecuteWithRetry(async () =>
            {
                int num = await this.context.Db.Updateable<T>(model).ExecuteCommandAsync();
                return num > 0;
            }, "Update");
        }

        public async Task<bool> UpdateRange(List<T> list)
        {
            int num = await this.context.Db.Updateable<T>(list).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> Update(T model, Expression<System.Func<T, object>> expression)
        {
            int num = await this.context.Db.Updateable<T>(model).WhereColumns(expression).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> UpdateColumns(T model, params string[] columns)
        {
            int num = await this.context.Db.Updateable<T>(model).UpdateColumns(columns).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> UpdateColumns(T model, Expression<System.Func<T, object>> columns)
        {
            int num = await this.context.Db.Updateable<T>(model).UpdateColumns(columns).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> UpdateColumnsByIgnoreColumns(T model, params string[] columns)
        {
            int num = await this.context.Db.Updateable<T>(model).IgnoreColumns(columns).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> UpdateColumnsByIgnoreColumns(
          T model,
          Expression<System.Func<T, object>> columns)
        {
            int num = await this.context.Db.Updateable<T>(model).IgnoreColumns(columns).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> UpdateNotNullColumns(
          T model,
          bool ignoreAllNullColumns,
          bool isOffIdentity = false,
          bool ignoreAllDefaultValue = false)
        {
            int num = await this.context.Db.Updateable<T>().IgnoreColumns(true).ExecuteCommandAsync();
            return num > 0;
        }

        public async Task<bool> UpdateIF(T model, Dictionary<Expression<System.Func<T, object>>, bool> dic)
        {
            IUpdateable<T> able = this.context.Db.Updateable<T>(model);
            foreach (KeyValuePair<Expression<System.Func<T, object>>, bool> keyValuePair in dic)
            {
                KeyValuePair<Expression<System.Func<T, object>>, bool> item = keyValuePair;
                able.UpdateColumnsIF(item.Value, item.Key);
                item = new KeyValuePair<Expression<System.Func<T, object>>, bool>();
            }
            int num = await able.ExecuteCommandAsync();
            bool flag = num > 0;
            able = (IUpdateable<T>)null;
            return flag;
        }

        public async Task<List<T>> getAll(
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<T> listAsync = await this.context.Db.Queryable<T>().OrderByIF(isOrderBy, orderBy, orderByType).ToListAsync();
            return listAsync;
        }

        public async Task<List<T>> getTakeList(
          int num,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            const int maxRetries = 3;
            const int delayMs = 50;
            
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    List<T> listAsync = await this.context.Db.Queryable<T>()
                        .OrderByIF(isOrderBy, orderBy, orderByType)
                        .Take(num)
                        .ToListAsync();
                    return listAsync;
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("集合已修改") && attempt < maxRetries)
                {
                    // 集合修改异常，等待后重试
                    await Task.Delay(delayMs * attempt);
                    continue;
                }
                catch (Exception ex) when (attempt < maxRetries && IsRetryableException(ex))
                {
                    // 其他可重试的异常
                    await Task.Delay(delayMs * attempt);
                    continue;
                }
            }
            
            // 如果所有重试都失败，则抛出异常
            throw new InvalidOperationException($"getTakeList 方法在 {maxRetries} 次重试后仍然失败");
        }

        private static bool IsRetryableException(Exception ex)
        {
            // 判断是否是可重试的异常
            return ex is InvalidOperationException ||
                   ex is System.Data.Common.DbException ||
                   ex.Message.Contains("超时") ||
                   ex.Message.Contains("连接") ||
                   ex.Message.Contains("枚举") ||
                   ex.Message.Contains("绑定") ||
                   ex.Message.Contains("bind error") ||
                   ex.Message.Contains("Operator绑定") ||
                   ex.Message.Contains("OrderNumber绑定");
        }

        public async Task<List<T>> getPageList(
          int skip,
          int take,
          Expression<System.Func<T, bool>> whereExp,
          Expression<System.Func<T, object>> orderBy,
          OrderByType orderByType = OrderByType.Asc)
        {
            const int maxRetries = 3;
            const int delayMs = 50;
            
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    List<T> listAsync = await this.context.Db.Queryable<T>().Skip(skip).Take(take).OrderBy(orderBy, orderByType).ToListAsync();
                    return listAsync;
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("集合已修改") && attempt < maxRetries)
                {
                    // 集合修改异常，等待后重试
                    await Task.Delay(delayMs * attempt);
                    continue;
                }
                catch (Exception ex) when (attempt < maxRetries && IsRetryableException(ex))
                {
                    // 其他可重试的异常（包括绑定错误）
                    await Task.Delay(delayMs * attempt);
                    continue;
                }
            }
            
            // 如果所有重试都失败，则抛出异常
            throw new InvalidOperationException($"getPageList 方法在 {maxRetries} 次重试后仍然失败");
        }

        public async Task<List<T>> getTakeList(
          Expression<System.Func<T, bool>> where,
          int num,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            const int maxRetries = 3;
            const int delayMs = 50;
            
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    List<T> listAsync = await this.context.Db.Queryable<T>()
                        .Where(where)
                        .OrderByIF(isOrderBy, orderBy, orderByType)
                        .Take(num)
                        .ToListAsync();
                    return listAsync;
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("集合已修改") && attempt < maxRetries)
                {
                    // 集合修改异常，等待后重试
                    await Task.Delay(delayMs * attempt);
                    continue;
                }
                catch (Exception ex) when (attempt < maxRetries && IsRetryableException(ex))
                {
                    // 其他可重试的异常
                    await Task.Delay(delayMs * attempt);
                    continue;
                }
            }
            
            // 如果所有重试都失败，则抛出异常
            throw new InvalidOperationException($"getTakeList(带条件) 方法在 {maxRetries} 次重试后仍然失败");
        }

        public async Task<T> getByPrimaryKey(object pkValue)
        {
            T byPrimaryKey = await this.context.Db.Queryable<T>().InSingleAsync(pkValue);
            return byPrimaryKey;
        }

        public async Task<T> getFirstOrDefault(Expression<System.Func<T, bool>> where)
        {
            return await ExecuteWithRetry(async () =>
            {
                T firstOrDefault = await this.context.Db.Queryable<T>().FirstAsync(where);
                return firstOrDefault;
            }, "getFirstOrDefault");
        }

        public async Task<List<T>> getByIn<S>(
          List<S> list,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<T> listAsync = await this.context.Db.Queryable<T>().In<S>(list).OrderByIF(isOrderBy, orderBy, orderByType).ToListAsync();
            return listAsync;
        }

        public async Task<List<T>> getByIn<S>(
          Expression<System.Func<T, object>> column,
          List<S> list,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<T> listAsync = await this.context.Db.Queryable<T>().In<S>(column, list).OrderByIF(isOrderBy, orderBy, orderByType).ToListAsync();
            return listAsync;
        }

        public async Task<List<T>> getByNotIn<S>(
          List<S> list,
          object field,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<T> listAsync = await this.context.Db.Queryable<T>().Where((Expression<System.Func<T, bool>>)(t => !SqlFunc.ContainsArray<S>(list, field))).OrderByIF(isOrderBy, orderBy, orderByType).ToListAsync();
            return listAsync;
        }

        public async Task<List<T>> getByWhere(
          Expression<System.Func<T, bool>> where,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<T> listAsync = await this.context.Db.Queryable<T>().Where(where).OrderByIF(isOrderBy, orderBy, orderByType).ToListAsync();
            return listAsync;
        }

        public async Task<List<T>> getByWhereIF(
          bool isWhere,
          Expression<System.Func<T, bool>> where,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<T> listAsync = await this.context.Db.Queryable<T>().WhereIF(isWhere, where).OrderByIF(isOrderBy, orderBy, orderByType).ToListAsync();
            return listAsync;
        }

        public async Task<List<T>> getByWhereIF(
          Dictionary<Expression<System.Func<T, bool>>, bool> wheres,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            ISugarQueryable<T> able = this.context.Db.Queryable<T>();
            foreach (KeyValuePair<Expression<System.Func<T, bool>>, bool> where in wheres)
            {
                KeyValuePair<Expression<System.Func<T, bool>>, bool> item = where;
                able.WhereIF(item.Value, item.Key);
                item = new KeyValuePair<Expression<System.Func<T, bool>>, bool>();
            }
            List<T> listAsync = await able.OrderByIF(isOrderBy, orderBy, orderByType).ToListAsync();
            able = (ISugarQueryable<T>)null;
            return listAsync;
        }

        public async Task<List<T>> getByBetween(
          object value,
          object start,
          object end,
          bool isOrderBy = false,
          Expression<System.Func<T, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<T> listAsync = await this.context.Db.Queryable<T>().Where((Expression<System.Func<T, bool>>)(it => SqlFunc.Between(value, start, end))).OrderByIF(isOrderBy, orderBy, orderByType).ToListAsync();
            return listAsync;
        }

        public async Task<bool> getIsAny(Expression<System.Func<T, bool>> where)
        {
            bool isAny = await this.context.Db.Queryable<T>().AnyAsync(where);
            return isAny;
        }


        public async Task<object> getJoinList<T1, T2>(
          Expression<Func<T1, T2, JoinQueryInfos>> joinExp,
          Expression<Func<T1, T2, object>> selectExp,
          bool isWhere = false,
          Expression<Func<T1, T2, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<Func<T1, T2, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<object> listAsync = await this.context.Db.Queryable<T1, T2>(joinExp).WhereIF(isWhere, whereExp).OrderByIF(isOrderBy, orderBy, orderByType).Select<object>(selectExp).ToListAsync();
            return (object)listAsync;
        }

        public async Task<object> getJoinPageList<T1, T2>(
          int pageIndex,
          int pageSize,
          Expression<Func<T1, T2, JoinQueryInfos>> joinExp,
          Expression<Func<T1, T2, object>> selectExp,
          bool isWhere = false,
          Expression<Func<T1, T2, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<Func<T1, T2, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<object> pageListAsync = await this.context.Db.Queryable<T1, T2>(joinExp).WhereIF(isWhere, whereExp).OrderByIF(isOrderBy, orderBy, orderByType).Select<object>(selectExp).ToPageListAsync(pageIndex, pageSize);
            return (object)pageListAsync;
        }

        public async Task<object> getJoinList<T1, T2, T3>(
          Expression<Func<T1, T2, T3, JoinQueryInfos>> joinExp,
          Expression<Func<T1, T2, T3, object>> selectExp,
          bool isWhere = false,
          Expression<Func<T1, T2, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<Func<T1, T2, T3, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<object> listAsync = await this.context.Db.Queryable<T1, T2, T3>(joinExp).WhereIF(isWhere, whereExp).OrderByIF(isOrderBy, orderBy, orderByType).Select<object>(selectExp).ToListAsync();
            return (object)listAsync;
        }

        public async Task<object> getJoinList<T1, T2, T3>(
          int pageIndex,
          int pageSize,
          Expression<Func<T1, T2, T3, JoinQueryInfos>> joinExp,
          Expression<Func<T1, T2, T3, object>> selectExp,
          bool isWhere = false,
          Expression<Func<T1, T2, bool>> whereExp = null,
          bool isOrderBy = false,
          Expression<Func<T1, T2, T3, object>> orderBy = null,
          OrderByType orderByType = OrderByType.Asc)
        {
            List<object> pageListAsync = await this.context.Db.Queryable<T1, T2, T3>(joinExp).WhereIF(isWhere, whereExp).OrderByIF(isOrderBy, orderBy, orderByType).Select<object>(selectExp).ToPageListAsync(pageIndex, pageSize);
            return (object)pageListAsync;
        }

        public async Task<List<T>> getListBySql(string sql)
        {
            List<T> listAsync = await this.context.Db.SqlQueryable<T>(sql).ToListAsync();
            return listAsync;
        }

        public async Task<bool> ExecuteCommandSql(string sql, params SugarParameter[] parameters)
        {
            int num = await this.context.Db.Ado.ExecuteCommandAsync(sql, parameters);
            return num > 0;
        }

        public async Task<List<T>> getListBySqlQuery(string sql, params SugarParameter[] parameters)
        {
            List<T> listBySqlQuery = await this.context.Db.Ado.SqlQueryAsync<T>(sql, parameters);
            return listBySqlQuery;
        }

        public async Task<object> getScalar(string sql, params SugarParameter[] parameters)
        {
            object scalarAsync = await this.context.Db.Ado.GetScalarAsync(sql, parameters);
            return scalarAsync;
        }

        public async Task<DataTable> UseStoredProcedure(
          string procedureName,
          params SugarParameter[] parameters)
        {
            DataTable dataTableAsync = await this.context.Db.Ado.UseStoredProcedure().GetDataTableAsync(procedureName, parameters);
            return dataTableAsync;
        }

        public Task<DbResult<bool>> UseTran(Action action, Action<Exception> errorCallBack) => throw new NotImplementedException();

        public Task<DbResult<bool>> UseTran(Func<Task> action, Action<Exception> errorCallBack) => (Task<DbResult<bool>>)null;

        public Task<DbResult<S>> UseTran<S>(Func<S> func, Action<Exception> errorCallBack) => throw new NotImplementedException();

        public async Task<List<T>> getPageList<TResult>(int pageIndex, int pageSize, bool isWhere = false, Expression<Func<T, bool>> whereExp = null, bool isOrderBy = false, Expression<Func<T, object>> orderBy = null, OrderByType orderByType = OrderByType.Asc)
        {
            const int maxRetries = 3;
            const int delayMs = 50;
            
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    var pageListAsync = await this.context.Db.Queryable<T>().WhereIF(isWhere, whereExp).OrderByIF(isOrderBy, orderBy, orderByType).ToPageListAsync(pageIndex, pageSize);
                    return pageListAsync;
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("集合已修改") && attempt < maxRetries)
                {
                    // 集合修改异常，等待后重试
                    await Task.Delay(delayMs * attempt);
                    continue;
                }
                catch (Exception ex) when (attempt < maxRetries && IsRetryableException(ex))
                {
                    // 其他可重试的异常（包括绑定错误）
                    await Task.Delay(delayMs * attempt);
                    continue;
                }
            }
            
            // 如果所有重试都失败，则抛出异常
            throw new InvalidOperationException($"getPageList<TResult> 方法在 {maxRetries} 次重试后仍然失败");
        }

        /// <summary>
        /// 执行数据库操作并在失败时重试，专门处理SQLite并发访问问题
        /// </summary>
        /// <typeparam name="TResult">返回类型</typeparam>
        /// <param name="operation">要执行的数据库操作</param>
        /// <param name="operationName">操作名称，用于日志记录</param>
        /// <returns>操作结果</returns>
        private async Task<TResult> ExecuteWithRetry<TResult>(Func<Task<TResult>> operation, string operationName)
        {
            const int maxRetries = 5; // 增加重试次数
            const int baseDelayMs = 50;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    return await operation();
                }
                catch (Exception ex) when (IsSqliteRetryableException(ex) && attempt < maxRetries)
                {
                    // 计算延迟时间：指数退避 + 随机抖动
                    int delay = baseDelayMs * (int)Math.Pow(2, attempt - 1) + new Random().Next(0, 50);

                    // 记录重试日志
                    System.Diagnostics.Debug.WriteLine($"数据库操作 {operationName} 第{attempt}次重试，延迟{delay}ms，错误: {ex.Message}");

                    await Task.Delay(delay);
                    continue;
                }
                catch (Exception ex)
                {
                    // 非可重试异常或达到最大重试次数，记录错误并抛出
                    System.Diagnostics.Debug.WriteLine($"数据库操作 {operationName} 失败，尝试次数: {attempt}, 错误: {ex.Message}");
                    throw;
                }
            }

            throw new InvalidOperationException($"数据库操作 {operationName} 在 {maxRetries} 次重试后仍然失败");
        }

        /// <summary>
        /// 判断是否为SQLite可重试的异常
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>是否可重试</returns>
        private bool IsSqliteRetryableException(Exception ex)
        {
            if (ex == null) return false;

            string message = ex.Message?.ToLower() ?? "";

            // SQLite常见的可重试错误
            return message.Contains("database is locked") ||
                   message.Contains("database table is locked") ||
                   message.Contains("cannot start a transaction within a transaction") ||
                   message.Contains("disk i/o error") ||
                   message.Contains("database disk image is malformed") ||
                   message.Contains("busy") ||
                   message.Contains("timeout") ||
                   message.Contains("集合已修改") ||
                   ex is InvalidOperationException ||
                   ex is System.Data.Common.DbException;
        }
    }
}
