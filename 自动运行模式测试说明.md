# 自动运行模式逻辑测试工具使用说明

## 概述
自动运行模式逻辑测试工具是一个专门用于测试IPM.Vision.WPF应用程序自动运行逻辑的软件测试界面，无需任何硬件设备即可验证软件逻辑的正确性。

## 如何打开测试窗口

### 方法1：通过主界面按钮
1. 启动IPM.Vision.WPF应用程序
2. 进入主界面的视觉检测页面（VisionPage）
3. 在控制按钮区域找到"测试"按钮（齿轮图标）
4. 点击"测试"按钮即可打开测试窗口

### 方法2：通过代码调用
```csharp
// 在代码中直接创建测试窗口
var testWindow = new TestWindow();
testWindow.Show();
```

## 测试界面说明

### 左侧控制面板
- **测试场景选择**：选择不同的测试场景
- **模拟参数配置**：设置各种延迟和间隔参数
- **测试控制**：开始/停止测试按钮
- **状态显示**：显示当前测试状态和执行次数

### 右侧日志查看器
- **实时日志**：显示测试过程中的详细日志信息
- **自动滚动**：可选择是否自动滚动到最新日志
- **清空日志**：清空当前显示的日志内容

## 测试场景介绍

### 1. Normal（正常流程）
- **描述**：模拟正常的自动运行流程，包括设备状态监控、步骤切换、相机触发等
- **测试内容**：
  - 设备状态变化模拟（状态2→状态4→状态2）
  - Mark点识别流程
  - 相机拍照触发
  - 步骤自动切换
- **适用场景**：验证基本的自动运行逻辑

### 2. DeviceError（设备错误）
- **描述**：模拟设备错误情况，测试错误处理和恢复机制
- **测试内容**：
  - 设备状态异常模拟
  - 错误处理逻辑验证
  - 恢复机制测试
- **适用场景**：验证异常处理能力

### 3. FastStatusChange（快速状态变化）
- **描述**：模拟快速连续的设备状态变化，测试防抖机制
- **测试内容**：
  - 快速连续状态变化
  - 防抖机制验证
  - 状态稳定性测试
- **适用场景**：验证系统稳定性

### 4. MarkPointFailure（Mark点失败）
- **描述**：模拟Mark点识别失败，测试重试和错误处理逻辑
- **测试内容**：
  - Mark点识别失败模拟
  - 重试机制验证
  - 失败处理逻辑
- **适用场景**：验证Mark点相关逻辑

### 5. CameraTriggerTest（相机触发测试）
- **描述**：专门测试相机触发流程和引脚位置遍历
- **测试内容**：
  - 相机触发流程
  - 引脚位置遍历
  - 拍照流程验证
- **适用场景**：验证相机相关功能

## 参数配置说明

### 设备状态间隔（Device Status Interval）
- **默认值**：2000ms
- **说明**：模拟设备状态变化的时间间隔
- **建议范围**：1000-5000ms

### Mark点延迟（Mark Point Delay）
- **默认值**：1500ms
- **说明**：模拟Mark点识别过程的延迟时间
- **建议范围**：500-3000ms

### 拍照触发延迟（Photo Trigger Delay）
- **默认值**：800ms
- **说明**：模拟相机拍照触发的延迟时间
- **建议范围**：200-2000ms

### 模拟错误（Simulate Errors）
- **默认值**：false
- **说明**：是否在测试过程中随机模拟错误情况

### 详细日志（Enable Detailed Log）
- **默认值**：true
- **说明**：是否输出详细的调试日志信息

## 使用步骤

1. **打开测试窗口**
   - 通过主界面"测试"按钮打开

2. **选择测试场景**
   - 从下拉列表中选择要测试的场景
   - 查看场景描述了解测试内容

3. **配置参数**
   - 根据需要调整各项延迟参数
   - 选择是否启用错误模拟和详细日志

4. **开始测试**
   - 点击"开始测试"按钮
   - 观察右侧日志输出
   - 监控测试状态和执行次数

5. **停止测试**
   - 点击"停止测试"按钮结束测试
   - 或等待测试自动完成

6. **查看结果**
   - 通过日志查看测试过程和结果
   - 分析是否符合预期行为

## 注意事项

1. **无硬件依赖**：此工具完全基于软件模拟，不需要连接任何硬件设备

2. **测试环境**：建议在开发或测试环境中使用，避免在生产环境中运行

3. **日志分析**：注意观察日志中的时间戳和状态变化，确保逻辑正确

4. **参数调整**：可以通过调整参数来模拟不同的运行条件

5. **多次测试**：建议多次运行不同场景的测试以确保系统稳定性

## 故障排除

### 测试窗口无法打开
- 检查应用程序是否正常启动
- 确认依赖注入配置正确
- 查看应用程序日志是否有错误信息

### 测试无法开始
- 检查是否有其他测试正在运行
- 确认参数配置是否有效
- 查看错误日志获取详细信息

### 日志显示异常
- 检查日志组件是否正常工作
- 确认UI线程调度是否正常
- 重启应用程序重试

## 技术支持

如有问题，请联系开发团队或查看相关技术文档。
