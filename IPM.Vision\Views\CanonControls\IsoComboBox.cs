﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Views.CanonControls
{
    public class IsoComboBox : PropertyComboBox, IObserver
    {
        private EDSDK.EdsPropertyDesc _desc;
        public IsoComboBox()
        {
            if (this.items == null) items = new Dictionary<uint, string>();
            items.Add(0U, " AUTO");
            items.Add(64U, "50");
            items.Add(72U, "100");
            items.Add(75U, "125");
            items.Add(77U, "160");
            items.Add(80U, "200");
            items.Add(83U, "250");
            items.Add(85U, "320");
            items.Add(88U, "400");
            items.Add(91U, "500");
            items.Add(93U, "640");
            items.Add(96U, "800");
            items.Add(99U, "1000");
            items.Add(101U, "1250");
            items.Add(104U, "1600");
            items.Add(107U, "2000");
            items.Add(109U, "2500");
            items.Add(112U, "3200");
            items.Add(115U, "4000");
            items.Add(117U, "5000");
            items.Add(120U, "6400");
            items.Add(123U, "8000");
            items.Add(125U, "10000");
            items.Add(128U, "12800");
            items.Add(131U, "16000");
            items.Add(133U, "20000");
            items.Add(136U, "25600");
            items.Add(139U, "32000");
            items.Add(141U, "40000");
            items.Add(144U, "51200");
            items.Add(147U, "64000");
            items.Add(149U, "80000");
            items.Add(152U, "102400");
            items.Add(160U, "204800");
            items.Add(168U, "409600");
            items.Add(176U, "819200");
            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }

        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();

                if (propertyID == EDSDK.PropID_ISOSpeed)
                {
                    uint property = model.Iso;
                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            this.UpdateProperty(property);
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:
                            _desc = model.IsoDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            this.UpdateProperty(property);
                            break;
                    }
                }
            }
        }
    }
}
