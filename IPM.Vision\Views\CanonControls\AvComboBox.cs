﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class AvComboBox : PropertyComboBox, IObserver
    {
        private EDSDK.EdsPropertyDesc _desc;
        public AvComboBox()
        {
            if (this.items == null) items = new Dictionary<uint, string>();
            items.Add(0U, "AUTO");
            items.Add(8U, "F1.0");
            items.Add(11U, "F1.1");
            items.Add(12U, "F1.2");
            items.Add(13U, "F1.2");
            items.Add(16U, "F1.4");
            items.Add(19U, "F1.6");
            items.Add(20U, "F1.8");
            items.Add(21U, "F1.8");
            items.Add(24U, "F2.0");
            items.Add(27U, "F2.2");
            items.Add(28U, "F2.5");
            items.Add(29U, "F2.5");
            items.Add(32U, "F2.8");
            items.Add(35U, "F3.2");
            items.Add(36U, "F3.5");
            items.Add(37U, "F3.5");
            items.Add(40U, "F4.0");
            items.Add(43U, "F4.5");
            items.Add(44U, "F4.5");
            items.Add(45U, "F5.0");
            items.Add(48U, "F5.6");
            items.Add(51U, "F6.3");
            items.Add(52U, "F6.7");
            items.Add(53U, "F7.1");
            items.Add(56U, "F8.0");
            items.Add(59U, "F9.0");
            items.Add(60U, "F9.5");
            items.Add(61U, "F10");
            items.Add(64U, "F11");
            items.Add(67U, "F13");
            items.Add(68U, "F13");
            items.Add(69U, "F14");
            items.Add(72U, "F16");
            items.Add(75U, "F18");
            items.Add(76U, "F19");
            items.Add(77U, "F20");
            items.Add(80U, "F22");
            items.Add(83U, "F25");
            items.Add(84U, "F27");
            items.Add(85U, "F29");
            items.Add(88U, "F32");
            items.Add(91U, "F36");
            items.Add(92U, "F38");
            items.Add(93U, "F40");
            items.Add(96U, "F45");
            items.Add(99U, "F51");
            items.Add(100U, "F54");
            items.Add(101U, "F57");
            items.Add(104U, "F64");
            items.Add(107U, "F72");
            items.Add(108U, "F76");
            items.Add(109U, "F81");
            items.Add(112U, "F91");
            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }

        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();

                if (propertyID == EDSDK.PropID_Av)
                {
                    uint property = model.Av;

                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            this.UpdateProperty(property);
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:
                            _desc = model.AvDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            this.UpdateProperty(property);
                            break;
                    }
                }
            }
        }

        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = (KeyValuePair<uint, string>)e.AddedItems[0];
                CameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_AV, (IntPtr)selectedItem.Key));

            }

            base.OnSelectionChanged(e);
        }
    }
}
