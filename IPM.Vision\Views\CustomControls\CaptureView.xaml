﻿<UserControl x:Class="IPM.Vision.Views.CustomControls.CaptureView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.CustomControls"
             xmlns:controls="clr-namespace:IPM.Vision.Views.CustomControls"
             xmlns:canon="clr-namespace:IPM.Vision.Views.CanonControls"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             DataContext="{Binding CaptureViewModel,Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
        <hc:EventTrigger EventName="Unloaded">
            <hc:EventToCommand Command="{Binding UnLoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>

        <!--相机界面-->
        <Border Grid.Row="0" Margin="1">
            <Grid>
                <Border
                    Visibility="{Binding ShowMainCamera,Mode=TwoWay,
                    UpdateSourceTrigger=PropertyChanged,
                    Converter={StaticResource BoolToVisibilityConverter}}"
                    >
                    <canon:CanonEvfBox/>
                </Border>
                <Border
                    Visibility="{Binding ShowMainCamera,Mode=TwoWay,
                    UpdateSourceTrigger=PropertyChanged,
                    Converter={StaticResource BoolToVisibilityConverter},ConverterParameter=True}"
                    >
                    <controls:HKCameraControl/>
                </Border>

            </Grid>
        </Border>

        <Border Grid.Row="1" Margin="5 0 5 0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <hc:UniformSpacingPanel
                    Spacing="10"
                    Orientation="Horizontal"
                    Grid.Column="0"
                    Visibility="{Binding NeedSetting,Converter={StaticResource BoolToVisibilityConverter}}">
                    <hc:UniformSpacingPanel Margin="0 10 10 0" Orientation="Horizontal" Spacing="5" Grid.Column="0" VerticalAlignment="Center">
                        <TextBlock FontWeight="Bold" FontSize="22" Text="重量:"/>
                        <TextBlock  FontWeight="Bold" FontSize="22" TextAlignment="Center" Width="80"  Text="{Binding WeightData}"/>
                        <TextBlock  FontWeight="Bold" FontSize="22"  Text="克"/>
                    </hc:UniformSpacingPanel>
                    <Button
                        Style="{StaticResource ButtonSuccess}"
                        Height="45"
                        Width="85"
                        IsEnabled="{Binding IsRunning,Converter={StaticResource Boolean2BooleanReConverter}}"
                        Command="{Binding OpenCameraCommand}"
                        Visibility="{Binding ShowMainCamera,Mode=TwoWay,
                        UpdateSourceTrigger=PropertyChanged,Converter={StaticResource BoolToVisibilityConverter}}">
                        <Button.Content>
                            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="&#xf030;" FontSize="14" TextAlignment="Center" HorizontalAlignment="Center" FontFamily="{StaticResource FontAwesome}"/>
                                <TextBlock Text="相机设置" />
                            </hc:UniformSpacingPanel>
                        </Button.Content>
                    </Button>
                    <Button
                        Style="{StaticResource ButtonPrimary}"
                        Height="45"
                        Width="85"
                        IsEnabled="{Binding IsRunning,Converter={StaticResource Boolean2BooleanReConverter}}"
                        Command="{Binding LightControlCommand}">
                        <Button.Content>
                            <hc:UniformSpacingPanel Orientation="Vertical"  Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="&#xf0eb;" FontSize="14" TextAlignment="Center" HorizontalAlignment="Center" FontFamily="{StaticResource FontAwesome}"/>
                                <TextBlock Text="光源设置" />
                            </hc:UniformSpacingPanel>
                        </Button.Content>
                    </Button>
                    <Button
                        Style="{StaticResource ButtonInfo}"
                        Height="45"
                        Width="85"
                        IsEnabled="{Binding IsRunning,Converter={StaticResource Boolean2BooleanReConverter}}"
                        Command="{Binding OpenEquipmentCommand}">
                        <Button.Content>
                            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="&#xf185;" FontSize="14" TextAlignment="Center" HorizontalAlignment="Center" FontFamily="{StaticResource FontAwesome}"/>
                                <TextBlock Text="设备调整"/>
                            </hc:UniformSpacingPanel>
                        </Button.Content>
                    </Button>
                    <Button Style="{StaticResource ButtonDanger}" Height="45" Width="85" Command="{Binding ResetErrorCommand}">
                        <Button.Content>
                            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="&#xf071;" FontSize="14" TextAlignment="Center" HorizontalAlignment="Center" FontFamily="{StaticResource FontAwesome}"/>
                                <TextBlock Text="清除报警"/>
                            </hc:UniformSpacingPanel>
                        </Button.Content>
                    </Button>
                    <Button Style="{StaticResource ButtonWarning}" Height="45" Width="85"  Command="{Binding RestAllCommand}">
                        <Button.Content>
                            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="&#xf021;" FontSize="12" TextAlignment="Center" HorizontalAlignment="Center" FontFamily="{StaticResource FontAwesome}"/>
                                <TextBlock Text="重置设备"/>
                            </hc:UniformSpacingPanel>
                        </Button.Content>
                    </Button>
                    <Button Style="{StaticResource ButtonWarning}"
                            Height="45"
                            Width="85"
                            IsEnabled="{Binding IsRunning,Converter={StaticResource Boolean2BooleanReConverter}}"
                            Command="{Binding RestartWeight}">
                        <Button.Content>
                            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" HorizontalAlignment="Center">
                                <TextBlock Text="&#xf021;" FontSize="12" TextAlignment="Center" HorizontalAlignment="Center" FontFamily="{StaticResource FontAwesome}"/>
                                <TextBlock Text="重置秤盘"/>
                            </hc:UniformSpacingPanel>
                        </Button.Content>
                    </Button>
                </hc:UniformSpacingPanel>
                <Border
                    Grid.Column="1"
                    Visibility="{Binding ShowMainCamera,Mode=TwoWay,
                    UpdateSourceTrigger=PropertyChanged,Converter={StaticResource BoolToVisibilityConverter}}">
                    <hc:Interaction.Triggers>
                        <hc:EventTrigger EventName="Loaded">
                            <hc:EventToCommand Command="{Binding ProgressLoadedCommand}" PassEventArgsToCommand="True"/>
                        </hc:EventTrigger>
                    </hc:Interaction.Triggers>
                    <canon:LDownLoadProgress
                        Width="140"
                        Height="35"
                        Margin="0 0 20 0"
                        Style="{StaticResource ProgressBarInfo}"/>
                </Border>

                <hc:ButtonGroup  HorizontalAlignment="Right"  Height="45" Margin="0 2 0 0" Grid.Column="2">
                    <RadioButton Content="主相机"
                                 FontSize="16"
                                 Height="45"
                                 Width="85"
                                 IsEnabled="{Binding IsRunning,Converter={StaticResource Boolean2BooleanReConverter}}"
                                 IsChecked="{Binding ShowMainCamera,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <RadioButton
                        Content="副相机"
                        FontSize="16"
                        Height="45"
                        Width="85"
                        IsEnabled="{Binding IsRunning,Converter={StaticResource Boolean2BooleanReConverter}}"
                        IsChecked="{Binding ShowMainCamera,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged,Converter={StaticResource Boolean2BooleanReConverter}}"
                        Visibility="{Binding HaveChild,Converter={StaticResource BoolToVisibilityConverter}}"/>
                </hc:ButtonGroup>
            </Grid>
        </Border>
    </Grid>
</UserControl>
