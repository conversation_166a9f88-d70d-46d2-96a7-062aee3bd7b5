﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableReportModel:ViewModelBase
    {
        private long _id;
        public long Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _orderNumber;
        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value);
        }

        private string _serialNumber;
        public string SerialNumber
        {
            get => _serialNumber;
            set => SetProperty(ref _serialNumber, value);
        }

        private string _pictureName;
        public string PictureName
        {
            get => _pictureName;
            set => SetProperty(ref _pictureName, value);
        }

        private string _picturePath;
        public string PicturePath
        {
            get => _picturePath;
            set => SetProperty(ref _picturePath, value);
        }

        private string _productNumber;
        public string ProductNumber
        {
            get => _productNumber;
            set => SetProperty(ref _productNumber, value);
        }

        private string _processId;
        public string ProcessId
        {
            get => _processId;
            set => SetProperty(ref _processId, value);
        }

        private string _processName;
        public string ProcessName
        {
            get => _processName;
            set => SetProperty(ref _processName, value);
        }

        private string _weight;
        public string Weight
        {
            get => _weight;
            set => SetProperty(ref _weight, value); 
        }

        private string _createTime;
        public string CreateTime
        {
            get => _createTime;
            set => SetProperty(ref _createTime, value);
        }

        private string _operatorName;
        public string OperatorName
        {
            get => _operatorName;
            set => SetProperty(ref _operatorName, value);
        }

        private string _workStation;
        public string WorkStation
        {
            get => _workStation;
            set => SetProperty(ref _workStation, value);
        }

        private string _status;
        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        private string _checkFilePath;
        public string CheckFilePath
        {
            get => _checkFilePath;
            set => SetProperty(ref _checkFilePath, value);
        }

        private string _manualReviewResult;
        public string ManualReviewResult
        {
            get => _manualReviewResult;
            set => SetProperty(ref _manualReviewResult, value);
        }

        private DateTime? _manualReviewTime;
        public DateTime? ManualReviewTime
        {
            get => _manualReviewTime;
            set => SetProperty(ref _manualReviewTime, value);
        }

        private string _manualReviewOperator;
        public string ManualReviewOperator
        {
            get => _manualReviewOperator;
            set => SetProperty(ref _manualReviewOperator, value);
        }

        private string _manualReviewRemark;
        public string ManualReviewRemark
        {
            get => _manualReviewRemark;
            set => SetProperty(ref _manualReviewRemark, value);
        }

        private DateTime _checkTime;
        /// <summary>
        /// 检测时间
        /// </summary>
        public DateTime CheckTime
        {
            get => _checkTime;
            set => SetProperty(ref _checkTime, value);
        }
    }
}
