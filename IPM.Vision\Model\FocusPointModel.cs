﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("focus_point")]
    public class FocusPointModel:BasicModel
    {
        [SugarColumn(ColumnName = "process_para_id")]
        public string ProcessParaId { get; set; }

        [SugarColumn(ColumnName = "X")]
        public float PositionX {  get; set; }

        [SugarColumn(ColumnName = "Y")]
        public float PositionY { get; set; }

        [SugarColumn(ColumnName = "position_index")]
        public int PositionIndex {  get; set; }
    }
}
