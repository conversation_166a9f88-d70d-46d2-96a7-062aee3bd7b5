﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableFocusPointModel:ViewModelBase
    {
        private string _id;
        private string _processParaId;
        private float _positionX;
        private float _positionY;
        private int _positionIndex;

        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string ProcessParaId
        {
            get => _processParaId;
            set => SetProperty(ref _processParaId, value);
        }

        public float PositionX
        {
            get => _positionX;
            set => SetProperty(ref _positionX, value);
        }

        public float PositionY
        {
            get => _positionY; 
            set => SetProperty(ref _positionY, value);
        }

        public int PositionIndex
        {
            get => _positionIndex; 
            set => SetProperty(ref _positionIndex, value);
        }
    }
}
