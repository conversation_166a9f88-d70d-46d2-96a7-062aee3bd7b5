﻿using CommunityToolkit.Mvvm.Input;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using IPM.Vision.Camera.HKSDKLib;
using System.IO;
using System.Windows.Media.Imaging;
using IPM.Vision.Common;
using System.Windows.Input;
using System.Windows.Controls;
using IPM.Vision.ViewModel.ObservableModel;
using System.Windows.Media;

namespace IPM.Vision.ViewModel.CustomControls
{
    public class CanonEvfBoxViewModel:ViewModelBase,IObserver
    {
        private readonly MainCameraParaService _cameraService;
        private delegate void _Update(Observable from, CameraEvent e);
        private readonly Dispatcher _dispatcher;
        private readonly ObservableGlobalState _globalState;
        private readonly EquipmentService _equipmentService;
        private WriteableBitmap _cameraImage;
        private System.Windows.Controls.Image _imageControl;
        private Canvas _canvasControl;
        private ObservableFocusPosition _focusPosition;
        private readonly NLogHelper _logger;
        private bool _isConnect = false;
        private double xRate;
        private double yRate;
        private readonly RestHelper _restHelepr;


        public CanonEvfBoxViewModel(IMainCameraParaService mainCameraParaService,NLogHelper logger, ObservableGlobalState globalState, IEquipmentService equipmentService, RestHelper restHelepr)
        {
            _logger = logger;
            _cameraService = (MainCameraParaService)mainCameraParaService;
            _dispatcher = Application.Current.Dispatcher;
            _globalState = globalState;
            _equipmentService = (EquipmentService)equipmentService;
            _cameraService.MainCameraAddedEvent += _cameraService_MainCameraAddedEvent;
            _restHelepr = restHelepr;
        }

        private void _cameraService_MainCameraAddedEvent(CameraController obj)
        {
            _cameraService.AddSource(this);
            if(!IsConnect) _cameraService.StartEVF();
        }

        public ObservableFocusPosition FocusPositions
        {
            get => _focusPosition;
            set => SetProperty(ref _focusPosition, value);
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            _cameraService.AddSource(this);
            if (!IsConnect) _cameraService.StartEVF();
        });

        public bool IsConnect
        {
            get => _isConnect;
            set => SetProperty(ref _isConnect, value);
        }

        public WriteableBitmap CameraImage
        {
            get => _cameraImage;
            set
            {
                SetProperty(ref _cameraImage, value);
            }
        }


        public IRelayCommand<RoutedEventArgs> ImageLoadedCommand => new RelayCommand<RoutedEventArgs>((args) => {
            _imageControl = args.Source as System.Windows.Controls.Image;

        });

        public IRelayCommand<MouseButtonEventArgs> ModifyPointerCommand => new RelayCommand<MouseButtonEventArgs>((args) => {
            var position = args.GetPosition(_imageControl);
            double x = position.X * xRate;
            double y = position.Y * yRate;
        });

        #region Focus信息
        public IRelayCommand<RoutedEventArgs> CanvasLoadedCommand => new RelayCommand<RoutedEventArgs>((args) =>
        {
            _canvasControl = args.Source as Canvas;
            if (_canvasControl != null && FocusPositions == null)
            {
                FocusPositions = new ObservableFocusPosition()
                {
                    Width = 60,
                    Height = 60,
                    Left = _canvasControl.ActualWidth / 2 - 30,
                    Top = _canvasControl.ActualHeight / 2 - 30,
                };
            }
        });

        public IRelayCommand<MouseButtonEventArgs> MouseLeftButtonDownCommand => new RelayCommand<MouseButtonEventArgs>((args) =>
        {
            if (_canvasControl == null) return;
            System.Windows.Point mousePosition = args.GetPosition(_canvasControl);
            double left = mousePosition.X;
            double top = mousePosition.Y;
            // 限制左边界和上边界，确保不会超出
            if (left - FocusPositions.Width / 2 < 0)
                left = FocusPositions.Width / 2;

            if (top - FocusPositions.Height / 2 < 0)
                top = FocusPositions.Height / 2;

            // 限制右边界和下边界，确保不会超出
            if (left + FocusPositions.Width / 2 > _canvasControl.ActualWidth)
                left = _canvasControl.ActualWidth - FocusPositions.Width / 2;

            if (top + FocusPositions.Height / 2 > _canvasControl.ActualHeight)
                top = _canvasControl.ActualHeight - FocusPositions.Height / 2;

            // 更新 FocusPosition 的位置，确保矩形框的中心在鼠标点击位置
            FocusPositions.Top = top - FocusPositions.Height / 2;
            FocusPositions.Left = left - FocusPositions.Width / 2;
            if (_cameraService.MainController == null) return;
            int cameraFocusX = Convert.ToInt32(FocusPositions.Left * xRate) - 537;
            int cameraFocusY = Convert.ToInt32(FocusPositions.Top * yRate) - 309;
            _cameraService.MainController.GetModel().ZoomRect = new EDSDK.EdsRect() { x = cameraFocusX, y = cameraFocusY };
            _cameraService.SetFocusPosition();
            _cameraService.PressHalf();
        });

        public IRelayCommand MouseLeftButtonUpCommand => new RelayCommand(() =>
        {
            _cameraService.PressOFF();
        });
        #endregion

        public IRelayCommand UnLoadCommand => new RelayCommand(() =>
        {
            if (_cameraService.MainController == null) return;
            _cameraService.EndEVF();
            _cameraService.MainController.GetModel().Remove(this);
            //_cameraService.DisconnectCamera();
            //StopPressCamera();
        });

        public IRelayCommand ReconnectCommand => new RelayCommand(() =>
        {
            if (_equipmentService.IsConnect)
            {
                _equipmentService.ReStartCamera();
            }
            if (_cameraService.MainController == null) return;
            _cameraService.MainController.GetModel().Add(this);
            _cameraService.StartEVF();
        });



        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            if (!_dispatcher.CheckAccess())
            {
                _dispatcher.Invoke(new _Update(UpdateAsync), observable, e);
                return;
            }
            if (_cameraService.MainController == null) return;
            CameraEvent.Type eventType = e.GetEventType();
            var _model = (CanonCameraModel)observable;
            if (_model.Camera != _cameraService.MainController.GetModel().Camera) return;
            uint propertyID;
            switch (eventType)
            {
                case CameraEvent.Type.EVFDATA_CHANGED:
                    IntPtr evfDataSetPtr = e.GetArg();
                    EVFDataSet evfDataSet = (EVFDataSet)Marshal.PtrToStructure(evfDataSetPtr, typeof(EVFDataSet));
                    UpdateCameraImageAsync(evfDataSet);
                    propertyID = EDSDK.PropID_FocusInfo;
                    _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                    {
                        StatusShowType = LEvents.ShowType.ICON,
                        EventCode = LEvents.HEventCode.SUCCESS,
                        SourceType = LEvents.SourceType.CAMERA,
                        EquipmentStatus = 1
                    });
                    _cameraService.MainController.ActionPerformed(new ActionEvent(ActionEvent.Command.GET_PROPERTY, (IntPtr)propertyID));
                    _cameraService.MainController.ActionPerformed(new ActionEvent(ActionEvent.Command.DOWNLOAD_EVF, IntPtr.Zero));
                    break;
                case CameraEvent.Type.PROPERTY_CHANGED:
                    propertyID = (uint)e.GetArg();
                    if (propertyID == EDSDK.PropID_Evf_OutputDevice)
                    {
                        uint device = _model.EvfOutputDevice;
                        if (!IsConnect && (device & EDSDK.EvfOutputDevice_PC) != 0)
                        {
                            IsConnect = true;
                            _cameraService.MainController.ActionPerformed(new ActionEvent(ActionEvent.Command.DOWNLOAD_EVF, IntPtr.Zero));
                        }
                        if (IsConnect && (device & EDSDK.EvfOutputDevice_PC) == 0)
                        {
                            IsConnect = false;
                        }
                    }
                    if (propertyID == EDSDK.PropID_FocusInfo)
                    {
                        UpdateFocus();
                    }
                    break;
                case CameraEvent.Type.SHUT_DOWN:
                    IsConnect = false;
                    _cameraService.IsConnect = false;
                    _cameraService.RemoveAllSource();
                    _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                    {
                        EventCode = LEvents.HEventCode.ERROR,
                        SourceType = LEvents.SourceType.CAMERA,
                        StatusShowType = LEvents.ShowType.ALL,
                        EventMessage = "主相机已断开连接！",
                        EquipmentStatus = 3
                    });
                    break;
            }
        }

        private void UpdateFocus()
        {
            if (_cameraService.MainController == null) return;
            var t1 = _cameraService.MainController.GetModel().SizeJpegLarge.width;
            var t2 = _cameraService.MainController.GetModel().SizeJpegLarge.height;
            xRate = _cameraService.MainController.GetModel().SizeJpegLarge.width / _imageControl.ActualWidth;
            yRate = _cameraService.MainController.GetModel().SizeJpegLarge.height / _imageControl.ActualHeight;
            var temp = _cameraService.MainController.GetModel().FocusInfo;
            for (var i = 0; i < temp.pointNumber; i++)
            {
                temp.focusPoint[i].rect.width = (int)(temp.focusPoint[i].rect.width / xRate);
                temp.focusPoint[i].rect.height = (int)(temp.focusPoint[i].rect.height / yRate);
                if (temp.focusPoint[i].valid == 1)
                {
                    if ((temp.focusPoint[i].justFocus & 0x0f) == 1)
                    {
                        this.FocusPositions.BorderColor = Brushes.Green;
                        //_cameraService.Controller.GetModel().NotifyObservers(new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)0));
                    }
                    else if ((temp.focusPoint[i].justFocus & 0x0f) == 2)
                    {
                        this.FocusPositions.BorderColor = Brushes.Red;
                        //_cameraService.Controller.GetModel().NotifyObservers(new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)0x00008D01));
                    }
                    else if ((temp.focusPoint[i].justFocus & 0x0f) == 4)
                    {
                        this.FocusPositions.BorderColor = Brushes.Green;
                        //_cameraService.Controller.GetModel().NotifyObservers(new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)0));
                    }
                    else
                    {
                        FocusPositions.BorderColor = Brushes.White;
                    }


                }
                this.FocusPositions.Left = temp.focusPoint[i].rect.x / xRate ;
                this.FocusPositions.Top = temp.focusPoint[i].rect.y / yRate;
                this.FocusPositions.Width = temp.focusPoint[i].rect.width;
                this.FocusPositions.Height = temp.focusPoint[i].rect.height;
            }
        }

        private void UpdateCameraImageAsync(EVFDataSet evfDataSet)
        {
            if (evfDataSet.stream != IntPtr.Zero)
            {
                try
                {
                    IntPtr evfStream;
                    EDSDK.EdsGetPointer(evfDataSet.stream, out evfStream);

                    if (evfStream != IntPtr.Zero)
                    {
                        // 获取流的长度
                        EDSDK.EdsGetLength(evfDataSet.stream, out ulong streamLength);
                        byte[] jpegData = new byte[streamLength];

                        // 将非托管内存复制到托管数组
                        Marshal.Copy(evfStream, jpegData, 0, (int)streamLength);

                        // 使用MemoryStream加载JPEG图像
                        using (MemoryStream ms = new MemoryStream(jpegData))
                        {
                            BitmapImage bitmapImage = new BitmapImage();
                            bitmapImage.BeginInit();
                            bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                            bitmapImage.StreamSource = ms;

                            bitmapImage.EndInit();
                            bitmapImage.Freeze(); 

                            WriteableBitmap writeableBitmap = new WriteableBitmap(bitmapImage);
                            CameraImage = writeableBitmap;
                            if(_globalState.AppConfig.OpenOCR == OpenEnum.OPEN)  CheckQRCode(bitmapImage);
                            //var temp = await _restHelper.CallContourDetectAsync(bitmapImage);
                            //_logger.LogInformation(temp);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex);
                }
            }
        }

        private async void CheckQRCode(BitmapImage bitmapImage)
        {
            try
            {
               
                await Task.Factory.StartNew(async () =>
                {
                    var temp = await _restHelepr.CallContourDetectAsync(bitmapImage);
                    _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                    {
                        StatusShowType = LEvents.ShowType.LABEL,
                        EventCode = LEvents.HEventCode.SUCCESS,
                        EventMessage = temp
                    });
                });
            }
            catch (Exception ex) {

                await Task.Factory.StartNew(() =>
                {
                    _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                    {
                        StatusShowType = LEvents.ShowType.LABEL,
                        EventCode = LEvents.HEventCode.SUCCESS,
                        EventMessage = $"二维码识别失败，原因:[{ex.Message}]"
                    });
                });
            }
          
        }
    }
}
