using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace IPM.Vision.Comm.Converts
{
    /// <summary>
    /// 布尔值反转转换器
    /// </summary>
    public class BooleanInvertConverter : IValueConverter
    {
        /// <summary>
        /// 将布尔值反转（true变为false，false变为true）
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }

        /// <summary>
        /// 将布尔值反转（true变为false，false变为true）
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false;
        }
    }
} 