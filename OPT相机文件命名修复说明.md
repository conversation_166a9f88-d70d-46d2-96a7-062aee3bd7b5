# OPT相机文件命名修复说明

## 问题描述

用户要求OPT相机的照片文件夹命名和照片命名使用VisionPageViewModel中现有的 `CheckFolder()` 和 `CheckPictureName()` 方法，而不是使用自定义的命名方式。

## 修复方案

### 1. 删除自定义命名方法

**文件**: `IPM.Vision\BLL\OptVisionService.cs`

删除了以下自定义命名方法：
- `GetAutoTriggerFolderName()` - 自动触发文件夹名称生成
- `GetAutoTriggerFileName()` - 自动触发文件名称生成

### 2. 添加事件通信机制

**文件**: `IPM.Vision\BLL\OptVisionService.cs`

添加了新的事件定义：
```csharp
public event Action RequestAutoTriggerSave; // 请求自动触发保存事件
```

### 3. 修改自动触发逻辑

**文件**: `IPM.Vision\BLL\OptVisionService.cs`

修改了帧回调中的自动触发处理逻辑：

```csharp
// 修复前
string autoFolderName = GetAutoTriggerFolderName();
string autoFileName = GetAutoTriggerFileName();
EnqueueSaveImageTask(autoFolderName, autoFileName);

// 修复后
// 触发自动拍照事件，让VisionPageViewModel处理文件夹和文件名生成
RequestAutoTriggerSave?.Invoke();
```

### 4. 在VisionPageViewModel中处理事件

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`

#### 4.1 订阅事件
```csharp
// LoadCommand中添加
_optVisionService.RequestAutoTriggerSave += _optVisionService_RequestAutoTriggerSave;

// UnLoadCommand中添加
_optVisionService.RequestAutoTriggerSave -= _optVisionService_RequestAutoTriggerSave;
```

#### 4.2 事件处理方法
```csharp
private void _optVisionService_RequestAutoTriggerSave()
{
    try
    {
        // 使用现有的文件夹和文件名生成逻辑
        string folderName = CheckFolder();
        string pictureName = CheckPictureName();

        // 调用OPT相机的拍照方法
        _optVisionService.TakePicture(folderName, pictureName);

        NotifyLog($"📷 OPT相机自动触发保存 - 文件夹: {folderName}, 文件名: {pictureName}");
    }
    catch (Exception ex)
    {
        NotifyLog($"❌ OPT相机自动触发保存失败: {ex.Message}");
    }
}
```

## 现有的命名逻辑

### CheckFolder() 方法
```csharp
private string CheckFolder()
{
    string folderName = _state.AppConfig.SaveFolder;
    var temp = TranslateFolderName();
    folderName = Common.FileHelper.ConcatFile(folderName, temp);
    Common.FileHelper.CreateFolder(folderName);
    return folderName;
}
```

### CheckPictureName() 方法
```csharp
private string CheckPictureName()
{
    return TranslatePictureName();
}
```

这些方法会根据配置文件中的规则生成文件夹和文件名，支持：
- 自定义文本
- 日期时间（年、月、日、时、分、秒）
- 产品名称
- 步骤名称
- 序列号等

## 修复效果

1. **统一命名规则**: OPT相机现在使用与其他相机相同的文件夹和文件名生成规则
2. **配置化管理**: 文件夹和文件名的格式完全由配置文件控制，便于用户自定义
3. **保持一致性**: 所有相机的照片命名方式保持一致，便于管理和查找

## 工作流程

1. **硬件触发**: PLC发送触发信号给OPT相机
2. **帧回调**: OPT相机接收到帧数据，检测到是触发模式且队列为空
3. **请求命名**: 触发 `RequestAutoTriggerSave` 事件
4. **生成路径**: VisionPageViewModel接收事件，调用 `CheckFolder()` 和 `CheckPictureName()` 生成路径
5. **执行保存**: 调用 `_optVisionService.TakePicture()` 执行实际保存操作
6. **文件存储**: 照片按照配置的命名规则保存到指定位置

## 注意事项

1. 修改后OPT相机的自动触发保存会使用配置文件中定义的命名规则
2. 文件夹和文件名的格式可以通过修改配置文件进行调整
3. 保持了原有的高性能保存机制，只是改变了命名方式的获取方法
4. 事件驱动的设计确保了模块间的解耦，便于后续维护
