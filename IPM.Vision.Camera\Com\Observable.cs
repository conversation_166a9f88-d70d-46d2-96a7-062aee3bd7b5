﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.Com
{
    public abstract class Observable
    {
        private List<IObserver> observers = new List<IObserver>();

        public void Add(ref IObserver observer)
        {
            if (observers.IndexOf(observer) == -1)
            {
                observers.Add(observer);
            }
        }

        public void Add(IObserver observer)
        {
            if (observers.IndexOf(observer) == -1)
            {
                observers.Add(observer);
            }
        }

        public void Remove(ref IObserver observer)
        {
            observers.Remove(observer);
        }
        public void Remove(IObserver observer)
        {
            observers.Remove(observer);
        }

        public void RemoveAll()
        {
            observers.Clear();
        }

        public void NotifyObservers(CameraEvent e)
        {
            for (int i = 0; i < observers.Count; i++)
            {
                observers[i].UpdateAsync(this, e);
            }
        }
    }
}
