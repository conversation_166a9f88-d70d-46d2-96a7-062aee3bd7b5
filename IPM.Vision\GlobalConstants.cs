﻿using System;

namespace IPM.Vision
{
    public static class GlobalConstants
    {
        public static readonly string BASEDIC = AppDomain.CurrentDomain.BaseDirectory;
        public static readonly string CONFIGFOLDER = BASEDIC + "config";
        public static readonly string REPORTDB = "ipm.db";
        public static readonly string DLLFOLDER = BASEDIC + "DLL";
        public static readonly string PRODUCTFOLDER = BASEDIC + "data";
        public static readonly string CONFIGNAME = "app_config.lof";
        public static readonly string USERDATA =  "user.lof";
        public static readonly string MENUCONFIG = "menu.lof";
         public static readonly string OPCSUFFIX = "ns=4;i=";
        //  public static readonly string OPCSUFFIX = "ns=3;i=";
        public static readonly string OPCCONFIGNAME = "opc_config.lof";
    }
}
