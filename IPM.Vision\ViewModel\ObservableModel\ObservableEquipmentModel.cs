﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableEquipmentModel : ViewModelBase
    {
        private string _id;
        public string Id {
            get => _id;
            set => SetProperty(ref _id, value);
        }
        private string _paraName;
        public string ParamName
        {
            get => _paraName;
            set => SetProperty(ref _paraName, value);
        }

        private float _x;
        /// <summary>
        /// X轴
        /// </summary>
        public float X
        {
            get => _x;
            set => SetProperty(ref _x, value);
        }

        private float _y;
        /// <summary>
        /// Y轴
        /// </summary>
        public float Y
        {
            get => _y;
            set => SetProperty(ref _y, value);
        }

        private float _z;
        /// <summary>
        /// Z轴
        /// </summary>
        public float Z
        {
            get => _z;
            set => SetProperty(ref _z, value);
        }

        private float _t;
        /// <summary>
        /// 转盘旋转
        /// </summary>
        public float T
        {
            get => _t;
            set => SetProperty(ref _t, value);
        }

        private float _r;
        /// <summary>
        /// 相机角度
        /// </summary>
        public float R
        {
            get => _r;
            set => SetProperty(ref _r, value);
        }

        private float _o;
        /// <summary>
        /// 焦距角度
        /// </summary>
        public float O
        {
            get => _o;
            set => SetProperty(ref _o, value);
        }

        private string _operator;
        public string Operator
        {
            get => _operator;
            set => SetProperty(ref _operator, value);
        }

        public DateTime _createTime;
        public DateTime CreateTime
        {
            get => _createTime;
            set => SetProperty(ref _createTime, value);
        }
    }
}
