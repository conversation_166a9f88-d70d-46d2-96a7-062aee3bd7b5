﻿<UserControl
    x:Class="IPM.Vision.Views.Dialogs.CompInfoDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:IPM.Vision.Views.CustomControls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="980"
    d:DesignWidth="1420"
    DataContext="{Binding CompInfoDialogViewModel, Source={StaticResource Locator}}"
    mc:Ignorable="d">
    <Border
        Width="1480"
        Height="1000"
        Background="White"
        CornerRadius="5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="0"
                Background="#283643"
                CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText
                            Margin="10,0,0,0"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Foreground="White"
                            Text="{Binding Title}" />
                    </Border>
                    <Border Grid.Column="2">
                        <Button
                            Width="40"
                            Height="40"
                            Command="{Binding CloseCommand}"
                            Content="&#xf00d;"
                            Template="{StaticResource CloseTemplate}" />
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1" Padding="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="1.5*" />
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Border Grid.Row="0">
                                <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                                    <hc:NumericUpDown
                                        Grid.Column="0"
                                        Margin="5,0,0,0"
                                        hc:TitleElement.Title="速度"
                                        hc:TitleElement.TitlePlacement="Left"
                                        DecimalPlaces="2"
                                        Maximum="250"
                                        Minimum="0"
                                        Style="{StaticResource NumericUpDownExtend}"
                                        Value="{Binding CurrentData.MoveSpeed}" />
                                    <hc:ComboBox
                                        Width="220"
                                        hc:TitleElement.Title="起点位置"
                                        hc:TitleElement.TitlePlacement="Left"
                                        hc:TitleElement.TitleWidth="60"
                                        ItemsSource="{Binding PinStartPositionList}"
                                        DisplayMemberPath="Label"
                                        SelectedValuePath="Value"
                                        SelectedValue="{Binding CurrentData.PinStartPosition}" />
                                    <hc:ComboBox
                                        Width="220"
                                        hc:TitleElement.Title="拍摄方向"
                                        hc:TitleElement.TitlePlacement="Left"
                                        hc:TitleElement.TitleWidth="60"
                                        ItemsSource="{Binding PinDirectionList}"
                                        DisplayMemberPath="Label"
                                        SelectedValuePath="Value"
                                        SelectedValue="{Binding CurrentData.PinDirection}" />
                                </hc:UniformSpacingPanel>
                            </Border>
                            <Border
                                Grid.Row="2"
                                Margin="0,5,0,5"
                                BorderBrush="Gray"
                                BorderThickness="0.5"
                                CornerRadius="5">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="25" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Border
                                        Grid.Row="0"
                                        Background="Gray"
                                        CornerRadius="5 5 0 0">
                                        <hc:SimpleText
                                            Margin="10,0,0,0"
                                            VerticalAlignment="Center"
                                            Foreground="White"
                                            Text="Mark点" />
                                    </Border>
                                    <hc:UniformSpacingPanel
                                        Grid.Row="1"
                                        Margin="5"
                                        Orientation="Vertical"
                                        Spacing="10">
                                        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                                            <Button
                                                Command="{Binding GetCommand}"
                                                CommandParameter="5"
                                                Content="读取设备当前位置信息"
                                                Style="{StaticResource ButtonInfo}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="1" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </Button>
                                            <Button
                                                Command="{Binding SendCommand}"
                                                CommandParameter="5"
                                                Content="发送至设备"
                                                Style="{StaticResource ButtonInfo}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="1" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </Button>

                                            <CheckBox
                                                Content="是否拍照"
                                                Foreground="Black"
                                                IsChecked="{Binding CurrentData.IsLinked5, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="1" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </CheckBox>
                                        </hc:UniformSpacingPanel>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="X轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="440"
                                                Minimum="-385"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.MarkX}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="1" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Y轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="195"
                                                Minimum="-250"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.MarkY}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="1" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                        </Grid>
                                    </hc:UniformSpacingPanel>
                                </Grid>

                            </Border>
                            <Border
                                Grid.Row="3"
                                Margin="0,5,0,5"
                                BorderBrush="Gray"
                                BorderThickness="0.5"
                                CornerRadius="5">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="25" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Border
                                        Grid.Row="0"
                                        Background="Gray"
                                        CornerRadius="5 5 0 0">
                                        <hc:SimpleText
                                            Margin="10,0,0,0"
                                            VerticalAlignment="Center"
                                            Foreground="White"
                                            Text="第一边" />
                                    </Border>
                                    <hc:UniformSpacingPanel
                                        Grid.Row="1"
                                        Margin="5"
                                        Orientation="Vertical"
                                        Spacing="10">
                                        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                                            <Button
                                                Command="{Binding GetCommand}"
                                                CommandParameter="1"
                                                Content="读取设备当前位置信息"
                                                Style="{StaticResource ButtonInfo}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </Button>
                                            <Button
                                                Command="{Binding SendCommand}"
                                                CommandParameter="1"
                                                Content="发送至设备"
                                                Style="{StaticResource ButtonInfo}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </Button>

                                            <CheckBox
                                                Content="是否拍照"
                                                Foreground="Black"
                                                IsChecked="{Binding CurrentData.IsLinked1, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </CheckBox>
                                            <Button
                                                Command="{Binding GenerateDataCommand}"
                                                CommandParameter="1"
                                                Content="同步下一边数据"
                                                Style="{StaticResource ButtonPrimary}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </Button>
                                            <!--<Button
                                                Command="{Binding AutoMoveTestCommand}"
                                                CommandParameter="1"
                                                Content="连拍移动测试"
                                                Style="{StaticResource ButtonWarning}" />-->
                                        </hc:UniformSpacingPanel>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="X轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="440"
                                                Minimum="-385"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.X1}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Y轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="195"
                                                Minimum="-250"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Y1}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Z轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="10"
                                                Minimum="-125"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Z1}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                        </Grid>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="倾斜角度"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="60"
                                                Minimum="30"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.R1}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="旋转角度"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="360"
                                                Minimum="0"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.T1}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="X增量值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.X1More}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                        </Grid>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="PIN数量"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Pin1}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="步进值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Step1}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Y增量值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Y1More}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </hc:NumericUpDown>
                                        </Grid>
                                    </hc:UniformSpacingPanel>
                                </Grid>

                            </Border>
                            <Border
                                Grid.Row="4"
                                Margin="0,0,0,5"
                                BorderBrush="Gray"
                                BorderThickness="0.5"
                                CornerRadius="5">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="25" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Border
                                        Grid.Row="0"
                                        Background="Gray"
                                        CornerRadius="5 5 0 0">
                                        <hc:SimpleText
                                            Margin="10,0,0,0"
                                            VerticalAlignment="Center"
                                            Foreground="White"
                                            Text="第二边" />
                                    </Border>
                                    <hc:UniformSpacingPanel
                                        Grid.Row="1"
                                        Margin="5"
                                        Orientation="Vertical"
                                        Spacing="10">
                                        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                                            <Button
                                                Command="{Binding GetCommand}"
                                                CommandParameter="2"
                                                Content="读取设备当前位置信息"
                                                Style="{StaticResource ButtonInfo}" />
                                            <Button
                                                Command="{Binding SendCommand}"
                                                CommandParameter="2"
                                                Content="发送至设备"
                                                Style="{StaticResource ButtonInfo}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </Button>
                                            <CheckBox
                                                Content="是否拍照"
                                                Foreground="Black"
                                                IsChecked="{Binding CurrentData.IsLinked2, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                                            <Button
                                                Command="{Binding GenerateDataCommand}"
                                                CommandParameter="2"
                                                Content="同步下一边数据"
                                                Style="{StaticResource ButtonPrimary}" />
                                            <!--<Button
                                                Command="{Binding AutoMoveTestCommand}"
                                                CommandParameter="2"
                                                Content="连拍移动测试"
                                                Style="{StaticResource ButtonWarning}" />-->
                                        </hc:UniformSpacingPanel>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="X轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="440"
                                                Minimum="-385"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.X2}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Y轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="195"
                                                Minimum="-250"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Y2}" />
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Z轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="10"
                                                Minimum="-125"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Z2}" />
                                        </Grid>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="倾斜角度"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="60"
                                                Minimum="30"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.R2}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="旋转角度"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="360"
                                                Minimum="0"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.T2}" />
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="X增量值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.X2More}" />
                                        </Grid>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="PIN数量"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Pin2}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="步进值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Step2}" />
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Y增量值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Y2More}" />
                                        </Grid>
                                    </hc:UniformSpacingPanel>
                                </Grid>
                            </Border>
                            <Border
                                Grid.Row="5"
                                Margin="0,0,0,5"
                                BorderBrush="Gray"
                                BorderThickness="0.5"
                                CornerRadius="5">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="25" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Border
                                        Grid.Row="0"
                                        Background="Gray"
                                        CornerRadius="5 5 0 0">
                                        <hc:SimpleText
                                            Margin="10,0,0,0"
                                            VerticalAlignment="Center"
                                            Foreground="White"
                                            Text="第三边" />
                                    </Border>
                                    <hc:UniformSpacingPanel
                                        Grid.Row="1"
                                        Margin="5"
                                        Orientation="Vertical"
                                        Spacing="10">
                                        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                                            <Button
                                                Command="{Binding GetCommand}"
                                                CommandParameter="3"
                                                Content="读取设备当前位置信息"
                                                Style="{StaticResource ButtonInfo}" />
                                            <Button
                                                Command="{Binding SendCommand}"
                                                CommandParameter="3"
                                                Content="发送至设备"
                                                Style="{StaticResource ButtonInfo}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </Button>
                                            <CheckBox
                                                Content="是否拍照"
                                                Foreground="Black"
                                                IsChecked="{Binding CurrentData.IsLinked3, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                                            <Button
                                                Command="{Binding GenerateDataCommand}"
                                                CommandParameter="3"
                                                Content="同步下一边数据"
                                                Style="{StaticResource ButtonPrimary}" />
                                            <!--<Button
                                                Command="{Binding AutoMoveTestCommand}"
                                                CommandParameter="3"
                                                Content="连拍移动测试"
                                                Style="{StaticResource ButtonWarning}" />-->
                                        </hc:UniformSpacingPanel>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="X轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="440"
                                                Minimum="-385"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.X3}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Y轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="195"
                                                Minimum="-250"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Y3}" />
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Z轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="10"
                                                Minimum="-125"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Z3}" />
                                        </Grid>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="倾斜角度"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="60"
                                                Minimum="30"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.R3}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="旋转角度"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="360"
                                                Minimum="0"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.T3}" />
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="X增量值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.X3More}" />
                                        </Grid>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="PIN数量"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Pin3}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="步进值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Step3}" />
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Y增量值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Y3More}" />
                                        </Grid>
                                    </hc:UniformSpacingPanel>
                                </Grid>
                            </Border>
                            <Border
                                Grid.Row="6"
                                BorderBrush="Gray"
                                BorderThickness="0.5"
                                CornerRadius="5">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="25" />
                                        <RowDefinition Height="*" />
                                    </Grid.RowDefinitions>
                                    <Border
                                        Grid.Row="0"
                                        Background="Gray"
                                        CornerRadius="5 5 0 0">
                                        <hc:SimpleText
                                            Margin="10,0,0,0"
                                            VerticalAlignment="Center"
                                            Foreground="White"
                                            Text="第四边" />
                                    </Border>
                                    <hc:UniformSpacingPanel
                                        Grid.Row="1"
                                        Margin="5"
                                        Orientation="Vertical"
                                        Spacing="10">
                                        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                                            <Button
                                                Command="{Binding GetCommand}"
                                                CommandParameter="4"
                                                Content="读取设备当前位置信息"
                                                Style="{StaticResource ButtonInfo}" />
                                            <Button
                                                Command="{Binding SendCommand}"
                                                CommandParameter="4"
                                                Content="发送至设备"
                                                Style="{StaticResource ButtonInfo}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="GotFocus">
                                                        <hc:EventToCommand Command="{Binding FocusCommand}" CommandParameter="2" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                            </Button>
                                            <CheckBox
                                                Content="是否拍照"
                                                Foreground="Black"
                                                IsChecked="{Binding CurrentData.IsLinked4, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                                            <!--<Button
                                                Command="{Binding AutoMoveTestCommand}"
                                                CommandParameter="4"
                                                Content="连拍移动测试"
                                                Style="{StaticResource ButtonWarning}" />-->
                                        </hc:UniformSpacingPanel>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="X轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="440"
                                                Minimum="-385"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.X4}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Y轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="195"
                                                Minimum="-250"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Y4}" />
                                            <hc:NumericUpDown
                                                Grid.Column="2"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="Z轴"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="10"
                                                Minimum="-125"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Z4}" />
                                        </Grid>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="倾斜角度"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="60"
                                                Minimum="30"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.R4}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="旋转角度"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Maximum="360"
                                                Minimum="0"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.T4}" />
                                        </Grid>
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <hc:NumericUpDown
                                                Grid.Column="0"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="PIN数量"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Pin4}" />
                                            <hc:NumericUpDown
                                                Grid.Column="1"
                                                Margin="5,0,0,0"
                                                hc:TitleElement.Title="步进值"
                                                hc:TitleElement.TitlePlacement="Left"
                                                hc:TitleElement.TitleWidth="60"
                                                DecimalPlaces="2"
                                                Style="{StaticResource NumericUpDownExtend}"
                                                Value="{Binding CurrentData.Step4}" />
                                        </Grid>
                                    </hc:UniformSpacingPanel>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>
                    <Border
                        Grid.Column="1"
                        Margin="5"
                        Visibility="{Binding ShowMainCamera, Converter={StaticResource BoolToVisibilityConverter}}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <hc:UniformSpacingPanel
                                Grid.Row="0"
                                Orientation="Horizontal"
                                Spacing="10">
                                <Button
                                    Width="100"
                                    Height="30"
                                    Command="{Binding ChangeCommand}"
                                    CommandParameter="0"
                                    Content="垂直翻转"
                                    Style="{StaticResource ButtonPrimary}" />
                                <Button
                                    Width="100"
                                    Height="30"
                                    Command="{Binding ChangeCommand}"
                                    CommandParameter="1"
                                    Content="水平翻转"
                                    Style="{StaticResource ButtonPrimary}" />
                                <Button
                                    Width="100"
                                    Height="30"
                                    Command="{Binding ChangeCommand}"
                                    CommandParameter="2"
                                    Content="顺时针90度"
                                    Style="{StaticResource ButtonPrimary}" />
                                <Button
                                    Width="100"
                                    Height="30"
                                    Command="{Binding ChangeCommand}"
                                    CommandParameter="3"
                                    Content="逆时针90度"
                                    Style="{StaticResource ButtonPrimary}" />
                                <Button
                                    Width="100"
                                    Height="30"
                                    Command="{Binding ChangeCommand}"
                                    CommandParameter="4"
                                    Content="清除设备报警"
                                    Style="{StaticResource ButtonDanger}" />
                            </hc:UniformSpacingPanel>
                            <Border Grid.Row="1" Margin="0,10,0,0">
                                <controls:OPTCameraControl />
                            </Border>
                        </Grid>
                    </Border>
                    <Border
                        Grid.Column="1"
                        Margin="5"
                        Visibility="{Binding ShowMainCamera, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=True}">
                        <controls:HKCameraControl />
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="2" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Button
                        Width="320"
                        Height="45"
                        Command="{Binding SaveCommand}"
                        Content="生成步骤信息"
                        Style="{StaticResource ButtonPrimary}" />
                    <Button
                        Grid.Column="1"
                        Width="320"
                        Height="45"
                        Command="{Binding CloseCommand}"
                        Content="取消生成"
                        Style="{StaticResource ButtonDanger}" />
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
