﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class OpenSessionCommand : BasicCommand
    {
        public OpenSessionCommand(ref CanonCameraModel model) : base(ref model)
        {
        }

        public override bool Execute()
        {
            uint num = 0u;
            //num = EDSDK.EdsSetPropertyData(_model.Camera, 16777216u, 158860912, 4, 16777240u);
            //num = EDSDK.EdsSetPropertyData(_model.Camera, 16777216u, 472995419, 4, 16778334u);
            num = EDSDK.EdsOpenSession(_model.Camera);
            bool flag = false;
            num = EDSDK.EdsSetPropertyData(_model.Camera, 11u, 0, 4, 2u);
            if (num == 0)
            {
                num = EDSDK.EdsSendStatusCommand(_model.Camera, 0u, 0);
            }

            if (num == 0)
            {
                flag = true;
            }

            if (num == 0)
            {
                EDSDK.EdsCapacity inCapacity = default(EDSDK.EdsCapacity);
                inCapacity.NumberOfFreeClusters = int.MaxValue;
                inCapacity.BytesPerSector = 4096;
                inCapacity.Reset = 1;
                num = EDSDK.EdsSetCapacity(_model.Camera, inCapacity);
            }

            if (flag)
            {
                num = EDSDK.EdsSendStatusCommand(_model.Camera, 1u, 0);
            }

            if (num != 0)
            {
                CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                _model.NotifyObservers(e);
            }

            return true;
        }
    }
}
