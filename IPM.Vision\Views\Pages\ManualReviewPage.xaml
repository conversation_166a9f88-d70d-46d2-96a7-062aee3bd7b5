<Page x:Class="IPM.Vision.Views.Pages.ManualReviewPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:IPM.Vision.Views.Pages"
      DataContext="{Binding ManualReviewPageViewModel,Source={StaticResource Locator}}"
      mc:Ignorable="d"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      d:DesignHeight="450" d:DesignWidth="1200">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
            
        <!-- 搜索条件 -->
        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10" Grid.Row="0">
            <hc:TextBox
                hc:InfoElement.TitlePlacement="Left"
                hc:InfoElement.Title="作业计划号"
                FontSize="14"
                Text="{Binding SearchModel.OrderNumber}"
                Height="35"
                Width="280" />
            <hc:TextBox
                hc:InfoElement.TitlePlacement="Left"
                hc:InfoElement.Title="序列号"
                FontSize="14"
                Text="{Binding SearchModel.SerialNumber}"
                Height="35"
                Width="280" />
            <hc:TextBox
                hc:InfoElement.TitlePlacement="Left"
                hc:InfoElement.Title="产品编号"
                FontSize="14"
                Text="{Binding SearchModel.ProductNumber}"
                Height="35"
                Width="280" />
            <Button Style="{StaticResource ButtonPrimary}" Content="查询" Height="35" Width="120" FontSize="16" Command="{Binding SearchCommand}"/>
            <Button Style="{StaticResource ButtonWarning}" Content="重置" Height="35" Width="120" FontSize="16" Command="{Binding ResetCommand}"/>
        </hc:UniformSpacingPanel>

            <!-- 主要内容区域 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧数据表格 -->
                <hc:TabControl Grid.Column="0" Style="{StaticResource TabControlBaseStyle}">
                    <hc:TabItem Header="复判管理">
                        <DataGrid
                            Style="{StaticResource DataGridBaseStyle}"
                            AutoGenerateColumns="False"
                            hc:DataGridAttach.ShowSelectAllButton="True"
                            ItemsSource="{Binding DataList}"
                            SelectedItem="{Binding CurrentSelect, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                            RowHeaderWidth="60"
                            HeadersVisibility="All"
                            hc:DataGridAttach.CanUnselectAllWithBlankArea="True"
                            hc:DataGridAttach.ShowRowNumber="True"
                            hc:DataGridAttach.ApplyDefaultStyle="True"
                            RowDetailsVisibilityMode="Collapsed"
                            CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                            ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}">
                            <DataGrid.Columns>
                                <DataGridTextColumn
                                    Binding="{Binding OrderNumber}"
                                    Header="作业计划号"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True"/>
                                <DataGridTextColumn
                                    Binding="{Binding SerialNumber}"
                                    Header="序列号"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True"/>
                                <DataGridTextColumn
                                    Binding="{Binding ProductNumber}"
                                    Header="产品编号"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True"/>
                                <DataGridTextColumn
                                    Binding="{Binding ProcessName}"
                                    Header="步骤名称"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True"/>
                                <DataGridTextColumn
                                    Binding="{Binding Status, Converter={StaticResource StatusTextConvert}}"
                                    Header="检测结果"
                                    Width="*"
                                    CanUserSort="False"
                                    IsReadOnly="True">
                                    <DataGridTextColumn.CellStyle>
                                        <Style TargetType="DataGridCell" BasedOn="{StaticResource LDataGridTextCenterColumnStyle}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Status}" Value="FAIL">
                                                    <Setter Property="Foreground" Value="Red"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="NACK">
                                                    <Setter Property="Foreground" Value="Red"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="NG">
                                                    <Setter Property="Foreground" Value="Red"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Status}" Value="WAITING">
                                                    <Setter Property="Foreground" Value="Orange"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGridTextColumn.CellStyle>
                                </DataGridTextColumn>
                                <DataGridTextColumn
                                    Binding="{Binding CreateTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}"
                                    Header="创建时间"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True"/>
                                <DataGridTextColumn
                                    Binding="{Binding Operator}"
                                    Header="操作员"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True"/>
                                <!-- **新增：复判结果显示列** -->
                                <DataGridTextColumn
                                    Binding="{Binding ManualReviewResult, Converter={StaticResource ManualReviewResultConverter}}"
                                    Header="复判结果"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True">
                                    <DataGridTextColumn.CellStyle>
                                        <Style TargetType="DataGridCell" BasedOn="{StaticResource LDataGridTextCenterColumnStyle}">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding ManualReviewResult}" Value="PASS">
                                                    <Setter Property="Foreground" Value="Green"/>
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding ManualReviewResult}" Value="FAIL">
                                                    <Setter Property="Foreground" Value="Red"/>
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </DataGridTextColumn.CellStyle>
                                </DataGridTextColumn>
                                <!-- **新增：复判时间显示列** -->
                                <DataGridTextColumn
                                    Binding="{Binding ManualReviewTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}"
                                    Header="复判时间"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True"/>
                                <!-- **新增：复判操作员显示列** -->
                                <DataGridTextColumn
                                    Binding="{Binding ManualReviewOperator}"
                                    Header="复判操作员"
                                    CanUserSort="False"
                                    Width="*"
                                    IsReadOnly="True"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </hc:TabItem>
                </hc:TabControl>

                <!-- 右侧图片预览和操作区域 -->
                <Border Grid.Column="1" Margin="10,0,0,0" BorderBrush="Gray" BorderThickness="1" CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 标题 -->
                        <Border Grid.Row="0" Background="#283643" CornerRadius="5 5 0 0">
                            <TextBlock Text="复判操作" 
                                     Foreground="White" 
                                     FontSize="16" 
                                     FontWeight="Bold"
                                     VerticalAlignment="Center"
                                     HorizontalAlignment="Center"/>
                        </Border>

                        <!-- 图片预览区域 -->
                        <Grid Grid.Row="1" Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <!-- 原始图片 -->
                            <TextBlock Grid.Row="0" Text="原始图片：" FontWeight="Bold" Margin="0,0,0,5"/>
                            <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" CornerRadius="3" Background="White">
                                <Image Source="{Binding CurrentSelect.PicturePath, IsAsync=True, Converter={StaticResource UriToBitmapConverter}}" 
                                       Stretch="Uniform" 
                                       Margin="5">
                                    <Image.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Command="{Binding PreviewCommand}" Header="查看原图" CommandParameter="{Binding}"/>
                                            <MenuItem Command="{Binding OpenPathCommand}" Header="打开路径" CommandParameter="{Binding}"/>
                                        </ContextMenu>
                                    </Image.ContextMenu>
                                </Image>
                            </Border>

                            <!-- 检测结果图片 -->
                            <TextBlock Grid.Row="2" Text="检测结果图片：" FontWeight="Bold" Margin="0,10,0,5"/>
                            <Border Grid.Row="3" BorderBrush="Gray" BorderThickness="1" CornerRadius="3" Background="White">
                                <Image Source="{Binding CurrentSelect.CheckFilePath, IsAsync=True, Converter={StaticResource UriToBitmapConverter}}" 
                                       Stretch="Uniform" 
                                       Margin="5">
                                    <Image.ContextMenu>
                                        <ContextMenu>
                                            <MenuItem Command="{Binding PreviewCheckCommand}" Header="查看检测图" CommandParameter="{Binding}"/>
                                        </ContextMenu>
                                    </Image.ContextMenu>
                                </Image>
                            </Border>
                        </Grid>

                        <!-- 信息显示区域 -->
                        <StackPanel Grid.Row="2" Margin="10">
                            <TextBlock Text="检测信息：" FontWeight="Bold" Margin="0,0,0,5"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                    <RowDefinition/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="产品编号：" Margin="0,2"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentSelect.ProductNumber}" Margin="5,2"/>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="序列号：" Margin="0,2"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding CurrentSelect.SerialNumber}" Margin="5,2"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="检测结果：" Margin="0,2"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CurrentSelect.Status}" Margin="5,2" Foreground="Red" FontWeight="Bold"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="检测时间：" Margin="0,2"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding CurrentSelect.CreateTime}" Margin="5,2"/>
                            </Grid>
                        </StackPanel>

                        <!-- **新增：复判信息显示** -->
                        <StackPanel Grid.Row="2" Margin="10" Orientation="Vertical">
                            <TextBlock Text="复判信息" FontWeight="Bold" FontSize="14" Margin="0,0,0,10" HorizontalAlignment="Center"/>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBlock Grid.Row="0" Grid.Column="0" Text="复判结果：" Margin="0,2"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentSelect.ManualReviewResult, Converter={StaticResource ManualReviewResultConverter}}" Margin="5,2">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding CurrentSelect.ManualReviewResult}" Value="PASS">
                                                    <Setter Property="Foreground" Value="Green"/>
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CurrentSelect.ManualReviewResult}" Value="FAIL">
                                                    <Setter Property="Foreground" Value="Red"/>
                                                    <Setter Property="FontWeight" Value="Bold"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding CurrentSelect.ManualReviewResult}" Value="{x:Null}">
                                                    <Setter Property="Foreground" Value="Gray"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>

                                <TextBlock Grid.Row="1" Grid.Column="0" Text="复判时间：" Margin="0,2"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding CurrentSelect.ManualReviewTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}" Margin="5,2"/>

                                <TextBlock Grid.Row="2" Grid.Column="0" Text="复判操作员：" Margin="0,2"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CurrentSelect.ManualReviewOperator}" Margin="5,2"/>

                                <TextBlock Grid.Row="3" Grid.Column="0" Text="复判备注：" Margin="0,2"/>
                                <TextBlock Grid.Row="3" Grid.Column="1" Text="{Binding CurrentSelect.ManualReviewRemark}" Margin="5,2" TextWrapping="Wrap"/>
                            </Grid>
                        </StackPanel>

                        <!-- 复判操作按钮 -->
                        <StackPanel Grid.Row="4" Margin="10" Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Style="{StaticResource ButtonSuccess}" 
                                    Content="复判合格" 
                                    Width="100" 
                                    Height="40" 
                                    Margin="5"
                                    FontSize="14"
                                    Command="{Binding PassCommand}"
                                    IsEnabled="{Binding CurrentSelect, Converter={StaticResource Object2BooleanConverter}}"/>
                            <Button Style="{StaticResource ButtonDanger}" 
                                    Content="复判不合格" 
                                    Width="100" 
                                    Height="40" 
                                    Margin="5"
                                    FontSize="14"
                                    Command="{Binding FailCommand}"
                                    IsEnabled="{Binding CurrentSelect, Converter={StaticResource Object2BooleanConverter}}"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>

            <!-- 分页控件 -->
            <Border Grid.Row="2" Margin="0 10 10 5">
                <hc:Pagination
                    HorizontalAlignment="Right"
                    DataCountPerPage="{Binding SearchModel.PageSize}"
                    MaxPageCount="{Binding SearchModel.PageCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    PageIndex="{Binding SearchModel.PageIndex}">
                    <hc:Interaction.Triggers>
                        <hc:EventTrigger EventName="PageUpdated">
                            <hc:EventToCommand Command="{Binding PageChangedCommand}" PassEventArgsToCommand="True" />
                        </hc:EventTrigger>
                    </hc:Interaction.Triggers>
                </hc:Pagination>
            </Border>

            <!-- 无数据提示 -->
            <Border Grid.Row="1">
                <TextBlock
                    FontSize="22"
                    Visibility="{Binding DataList.Count,Converter={StaticResource NullToVisibilityConverter}}"
                    Text="暂无复判数据"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"/>
            </Border>
        </Grid>
    </Page> 