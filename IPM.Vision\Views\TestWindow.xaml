﻿<Window x:Class="IPM.Vision.Views.TestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:IPM.Vision.Views"
        mc:Ignorable="d"
        xmlns:hc="https://handyorg.github.io/handycontrol"

        Title="自动运行模式逻辑测试" Height="700" Width="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#2C3E50" Padding="10">
            <TextBlock Text="自动运行模式逻辑测试工具"
                       Foreground="White"
                       FontSize="16"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1.5*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧控制面板 -->
            <hc:UniformSpacingPanel Grid.Column="0" Orientation="Vertical" Spacing="15" Margin="10">

                <!-- 测试场景选择 -->
                <GroupBox Header="测试场景">
                    <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" Margin="10">
                        <ComboBox SelectedItem="{Binding SelectedScenario}"
                                  ItemsSource="{Binding TestScenarios}"
                                  Height="30"
                                  FontSize="14"/>
                        <TextBlock Text="{Binding ScenarioDescription}"
                                   Foreground="Gray"
                                   FontSize="12"
                                   TextWrapping="Wrap"/>
                    </hc:UniformSpacingPanel>
                </GroupBox>

                <!-- 模拟参数设置 -->
                <GroupBox Header="模拟参数">
                    <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" Margin="10">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="设备状态间隔(ms):" Width="120" VerticalAlignment="Center"/>
                            <TextBox Text="{Binding DeviceStatusInterval}" Width="100" Height="25"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="Mark点识别延迟(ms):" Width="120" VerticalAlignment="Center"/>
                            <TextBox Text="{Binding MarkPointDelay}" Width="100" Height="25"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="拍照触发延迟(ms):" Width="120" VerticalAlignment="Center"/>
                            <TextBox Text="{Binding PhotoTriggerDelay}" Width="100" Height="25"/>
                        </StackPanel>
                        <CheckBox Content="模拟设备错误" IsChecked="{Binding SimulateErrors}"/>
                        <CheckBox Content="启用详细日志" IsChecked="{Binding EnableDetailedLog}"/>
                    </hc:UniformSpacingPanel>
                </GroupBox>

                <!-- 测试控制 -->
                <GroupBox Header="测试控制">
                    <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" Margin="10">
                        <Button Content="开始测试"
                                Command="{Binding StartTestCommand}"
                                Height="40"
                                Background="#27ae60"
                                Foreground="White"
                                FontWeight="Bold"
                                IsEnabled="{Binding CanStartTest}"/>
                        <Button Content="停止测试"
                                Command="{Binding StopTestCommand}"
                                Height="40"
                                Background="#e74c3c"
                                Foreground="White"
                                FontWeight="Bold"
                                IsEnabled="{Binding IsTestRunning}"/>
                        <Button Content="清空日志"
                                Command="{Binding ClearLogCommand}"
                                Height="30"
                                Background="#95a5a6"
                                Foreground="White"/>

                        <!-- 快速测试按钮 -->
                        <Separator Margin="0,10"/>
                        <TextBlock Text="快速测试:" FontWeight="Bold" Margin="0,5"/>
                        <Button Content="测试设备状态"
                                Command="{Binding TestDeviceStatusCommand}"
                                Height="30"
                                Background="#3498db"
                                Foreground="White"/>
                        <Button Content="测试相机回调"
                                Command="{Binding TestCameraCallbackCommand}"
                                Height="30"
                                Background="#9b59b6"
                                Foreground="White"/>
                        <Button Content="测试设备参数"
                                Command="{Binding TestDeviceParametersCommand}"
                                Height="30"
                                Background="#e67e22"
                                Foreground="White"/>
                        <Button Content="测试并发事件"
                                Command="{Binding TestConcurrentEventsCommand}"
                                Height="30"
                                Background="#e74c3c"
                                Foreground="White"/>
                    </hc:UniformSpacingPanel>
                </GroupBox>

                <!-- 测试状态 -->
                <GroupBox Header="测试状态">
                    <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" Margin="10">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="当前状态:" Width="80" VerticalAlignment="Center"/>
                            <Ellipse Width="12" Height="12" VerticalAlignment="Center" Margin="5,0">
                                <Ellipse.Style>
                                    <Style TargetType="Ellipse">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsTestRunning}" Value="False">
                                                <Setter Property="Fill" Value="Red"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding IsTestRunning}" Value="True">
                                                <Setter Property="Fill" Value="Green"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Ellipse.Style>
                            </Ellipse>
                            <TextBlock Text="{Binding TestStatus}" VerticalAlignment="Center"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="当前步骤:" Width="80" VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding CurrentStep}" VerticalAlignment="Center"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="设备状态:" Width="80" VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding DeviceStatus}" VerticalAlignment="Center"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="执行次数:" Width="80" VerticalAlignment="Center"/>
                            <TextBlock Text="{Binding ExecutionCount}" VerticalAlignment="Center"/>
                        </StackPanel>
                    </hc:UniformSpacingPanel>
                </GroupBox>
            </hc:UniformSpacingPanel>

            <!-- 右侧日志区域 -->
            <GroupBox Grid.Column="1" Header="测试日志" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                        <TextBox Text="{Binding LogContent}"
                                 IsReadOnly="True"
                                 TextWrapping="Wrap"
                                 FontFamily="Consolas"
                                 FontSize="12"
                                 Background="#1e1e1e"
                                 Foreground="#d4d4d4"
                                 BorderThickness="0"
                                 Padding="10"/>
                    </ScrollViewer>

                    <CheckBox Grid.Row="1"
                              Content="自动滚动到底部"
                              IsChecked="{Binding AutoScrollToBottom}"
                              Margin="5"/>
                </Grid>
            </GroupBox>
        </Grid>
    </Grid>
</Window>
