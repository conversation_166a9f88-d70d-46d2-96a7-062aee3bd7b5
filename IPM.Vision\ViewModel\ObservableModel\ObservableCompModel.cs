﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using IPM.Vision.Common;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableCompModel:ViewModelBase
    {

        private string _id;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private bool _isLinked1 = true;
        public bool IsLinked1
        {
            get => _isLinked1;
            set => SetProperty(ref _isLinked1, value);
        }

        private bool _isLinked5 = true;
        public bool IsLinked5
        {
            get => _isLinked5;
            set => SetProperty(ref _isLinked5, value);
        }

        private bool _isLinked2 = true;
        public bool IsLinked2
        {
            get => _isLinked2;
            set => SetProperty(ref _isLinked2, value);
        }

        private bool _isLinked3 = true;
        public bool IsLinked3
        {
            get => _isLinked3;
            set => SetProperty(ref _isLinked3, value);
        }

        private bool _isLinked4 = true;
        public bool IsLinked4
        {
            get => _isLinked4;
            set => SetProperty(ref _isLinked4, value);
        }

        private bool _isInsert = false;
        public bool IsInsert
        {
            get => _isInsert;
            set => SetProperty(ref _isInsert, value);
        }

        private string _productId;
        public string ProductId
        {
            get => _productId;
            set => SetProperty(ref _productId, value);  
        }

        private float _x1;
        public float X1
        {
            get => _x1;
            set
            {
                
                SetProperty(ref _x1, value);
            }
        }
        private float _y1;
        public float Y1
        {
            get => _y1;
            set => SetProperty(ref _y1, value);
        }

        private float _z1;
        public float Z1
        {
            get => _z1; 
            set => SetProperty(ref _z1, value);
        }

        private float _r1;
       
        public float R1
        {
            get => _r1;
            set => SetProperty(ref _r1, value);
        }

        private float _t1;

        public float T1
        {
            get => _t1;
            set => SetProperty(ref _t1, value);
        }

        private int _pin1;
        public int Pin1
        {
            get => _pin1;
            set => SetProperty(ref _pin1, value);
        }

        private float _step1;
        public float Step1
        {
            get => _step1;
            set => SetProperty(ref _step1, value);
        }

        private float _x2;
        public float X2
        {
            get => _x2;
            set
            {
                SetProperty(ref _x2, value);
            }
        }
        private float _y2;
        public float Y2
        {
            get => _y2;
            set => SetProperty(ref _y2, value);
        }

        private float _z2;
        public float Z2
        {
            get => _z2;
            set => SetProperty(ref _z2, value);
        }

        private float _r2;

        public float R2
        {
            get => _r2;
            set => SetProperty(ref _r2, value);
        }

        private float _t2;

        public float T2
        {
            get => _t2;
            set => SetProperty(ref _t2, value);
        }

        private int _pin2;
        public int Pin2
        {
            get => _pin2;
            set => SetProperty(ref _pin2, value);
        }

        private float _step2;
        public float Step2
        {
            get => _step2;
            set => SetProperty(ref _step2, value);
        }

        private float _x3;
        public float X3
        {
            get => _x3;
            set => SetProperty(ref _x3, value);
        }
        private float _y3;
        public float Y3
        {
            get => _y3;
            set => SetProperty(ref _y3, value);
        }

        private float _z3;
        public float Z3
        {
            get => _z3;
            set => SetProperty(ref _z3, value);
        }

        private float _r3;

        public float R3
        {
            get => _r3;
            set => SetProperty(ref _r3, value);
        }

        private float _t3;

        public float T3
        {
            get => _t3;
            set => SetProperty(ref _t3, value);
        }

        private int _pin3;
        public int Pin3
        {
            get => _pin3;
            set => SetProperty(ref _pin3, value);
        }

        private float _step3;
        public float Step3
        {
            get => _step3;
            set => SetProperty(ref _step3, value);
        }

        private float _x4;
        public float X4
        {
            get => _x4;
            set => SetProperty(ref _x4, value);
        }
        private float _y4;
        public float Y4
        {
            get => _y4;
            set => SetProperty(ref _y4, value);
        }

        private float _z4;
        public float Z4
        {
            get => _z4;
            set => SetProperty(ref _z4, value);
        }

        private float _r4;

        public float R4
        {
            get => _r4;
            set => SetProperty(ref _r4, value);
        }

        private float _t4;

        public float T4
        {
            get => _t4;
            set => SetProperty(ref _t4, value);
        }

        private int _pin4;
        public int Pin4
        {
            get => _pin4;
            set => SetProperty(ref _pin4, value);
        }

        private float _step4;
        public float Step4
        {
            get => _step4;
            set => SetProperty(ref _step4, value);
        }
        private float _t2Rotation;
        public float T2Rotation
        {
            get => _t2Rotation;
            set => SetProperty(ref _t2Rotation,value);
        }

        private float _t3Rotation;
        public float T3Rotation
        {
            get => _t3Rotation;
            set => SetProperty(ref _t3Rotation,value);
        }

        private float _t4Rotation;
        public float T4Rotation
        {
            get=> _t4Rotation;
            set => SetProperty(ref _t4Rotation, value);
        }

        private bool _isReserver = true;
        public bool IsReserver
        {
            get => _isReserver;
            set => SetProperty(ref _isReserver, value);
        }

        private float _reserverNumber;

        public float ReserverNumber
        {
            get => _reserverNumber;
            set => SetProperty(ref _reserverNumber, value);
        }
        private float _x1More;
        public float X1More
        {
            get => _x1More;
            set => SetProperty(ref _x1More, value);
        }

        private float _y1More;
        public float Y1More
        {
            get => _y1More;
            set => SetProperty(ref _y1More, value);
        }

        private float _x2More;
        public float X2More
        {
            get => _x2More;
            set => SetProperty(ref _x2More, value);
        }

        private float _y2More;
        public float Y2More
        {
            get => _y2More;
            set => SetProperty(ref _y2More, value);
        }

        private float _x3More;
        public float X3More
        {
            get => _x3More;
            set => SetProperty(ref _x3More, value);
        }

        private float _y3More;
        public float Y3More
        {
            get => _y3More;
            set => SetProperty(ref _y3More, value);
        }

        private float _x4More;
        public float X4More
        {
            get => _x4More;
            set => SetProperty(ref _x4More, value);
        }

        private float _y4More;
        public float Y4More
        {
            get => _y4More;
            set => SetProperty(ref _y4More, value);
        }

        private bool _isAutoTake = true;
        public bool IsAutoTake
        {
            get => _isAutoTake;
            set => SetProperty(ref _isAutoTake, value);
        }

        private float _moveSpeed = 250;
        public float MoveSpeed
        {
            get => _moveSpeed;
            set => SetProperty(ref _moveSpeed, value);
        }

        private float _aceSpeed = 62.5F;
        public float AceSpeed
        {
            get => _aceSpeed;
            set => SetProperty(ref _aceSpeed, value);
        }

        private float _delayTime = 0;
        public float DelayTime
        {
            get => _delayTime;
            set => SetProperty(ref _delayTime, value);
        }

        private float markX = 0;
        public float MarkX
        {
            get => markX;
            set => SetProperty(ref markX, value);
        }
        private float markY = 0;
        public float MarkY
        {
            get => markY;
            set => SetProperty(ref markY, value);
        }

        /// <summary>
        /// 引脚拍摄起点位置
        /// </summary>
        private PinStartPositionEnum _pinStartPosition = PinStartPositionEnum.TopLeft;
        public PinStartPositionEnum PinStartPosition
        {
            get => _pinStartPosition;
            set => SetProperty(ref _pinStartPosition, value);
        }

        /// <summary>
        /// 引脚拍摄方向
        /// </summary>
        private PinDirectionEnum _pinDirection = PinDirectionEnum.CounterClockwise;
        public PinDirectionEnum PinDirection
        {
            get => _pinDirection;
            set => SetProperty(ref _pinDirection, value);
        }


    }
}
