﻿using IPM.Vision.Common.DBCommon;
using IPM.Vision.DAL.Interfaces;
using IPM.Vision.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.DAL
{
    public class CompRepository : RepositoryBase<CompPointModel>, ICompRepository
    {
        public CompRepository(IDbContext dbContext) : base(dbContext)
        {
        }
    }
}
