﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;
using System.IO;
using IPM.Vision.Model;

namespace IPM.Vision.BLL
{
    internal class MESHelper
    {
        /// <summary>
        /// Generate MES detection result XML data
        /// </summary>
        /// <param name="reportModel">Detection result data</param>
        /// <param name="productModel">Product information</param>
        /// <param name="outputPath">Output path</param>
        /// <returns>Generated XML file path</returns>
        public static string GenerateMESXml(ReportModel reportModel, ProductParamModel productModel, string outputPath)
        {
            try
            {
                // Ensure output directory exists
                if (!Directory.Exists(outputPath))
                {
                    Directory.CreateDirectory(outputPath);
                }

                // Create XML document
                var doc = new XDocument(
                    new XDeclaration("1.0", "utf-8", "yes"),
                    new XElement("TestResult",
                        new XElement("TestItem",
                            new XAttribute("Name", productModel?.ProductName ?? ""),
                            new XAttribute("Step", reportModel?.ProcessName ?? ""),
                            new XElement("Parameter",
                                new XAttribute("Name", "DetectionResult"),
                                new XAttribute("Unit", ""),
                                new XElement("Group",
                                    new XElement("TestCondition",
                                        new XElement("Description", $"WorkStation:{reportModel?.WorkStation ?? ""}")
                                    ),
                                    new XElement("TheoreticalValue",
                                        new XElement("LowerLimit", "PASS"),
                                        new XElement("UpperLimit", "PASS")
                                    ),
                                    new XElement("ActualValue",
                                        new XElement("Description", 
                                            new XAttribute("Name", "DetectionStatus")),
                                        new XElement("Value", reportModel?.Status ?? "FAIL"),
                                        new XElement("Qualified", GetQualityResult(reportModel?.Status))
                                    )
                                )
                            ),
                            new XElement("Parameter",
                                new XAttribute("Name", "ProductInfo"),
                                new XAttribute("Unit", ""),
                                new XElement("Group",
                                    new XElement("TestCondition",
                                        new XElement("Description", "Product Basic Information")
                                    ),
                                    new XElement("TheoreticalValue",
                                        new XElement("LowerLimit", ""),
                                        new XElement("UpperLimit", "")
                                    ),
                                    new XElement("ActualValue",
                                        new XElement("Description", 
                                            new XAttribute("Name", "ProductModel")),
                                        new XElement("Value", productModel?.ProductNumber ?? ""),
                                        new XElement("Qualified", "PASS")
                                    )
                                )
                            ),
                            new XElement("Parameter",
                                new XAttribute("Name", "DetectionImage"),
                                new XAttribute("Unit", ""),
                                new XElement("Group",
                                    new XElement("TestCondition",
                                        new XElement("Description", "Detection Image Path")
                                    ),
                                    new XElement("TheoreticalValue",
                                        new XElement("LowerLimit", ""),
                                        new XElement("UpperLimit", "")
                                    ),
                                    new XElement("ActualValue",
                                        new XElement("Description", 
                                            new XAttribute("Name", "ImagePath")),
                                        new XElement("Value", reportModel?.PicturePath ?? ""),
                                        new XElement("Qualified", "PASS")
                                    )
                                )
                            ),
                            new XElement("Parameter",
                                new XAttribute("Name", "DetectionTime"),
                                new XAttribute("Unit", ""),
                                new XElement("Group",
                                    new XElement("TestCondition",
                                        new XElement("Description", "Detection Completion Time")
                                    ),
                                    new XElement("TheoreticalValue",
                                        new XElement("LowerLimit", ""),
                                        new XElement("UpperLimit", "")
                                    ),
                                    new XElement("ActualValue",
                                        new XElement("Description", 
                                            new XAttribute("Name", "DetectionTime")),
                                        new XElement("Value", reportModel?.CheckTime.ToString("yyyy-MM-dd HH:mm:ss") ?? ""),
                                        new XElement("Qualified", "PASS")
                                    )
                                )
                            )
                        )
                    )
                );

                // Generate file name
                string fileName = $"MES_{reportModel?.SerialNumber ?? "Unknown"}_{DateTime.Now:yyyyMMdd_HHmmss}.xml";
                string filePath = Path.Combine(outputPath, fileName);

                // Save XML file
                doc.Save(filePath);

                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to generate MES XML file: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get quality result based on detection status
        /// </summary>
        /// <param name="status">Detection status</param>
        /// <returns>Quality result</returns>
        private static string GetQualityResult(string status)
        {
            return status?.ToUpper() == "PASS" ? "PASS" : "FAIL";
        }

        /// <summary>
        /// Batch generate MES XML data
        /// </summary>
        /// <param name="reportModels">Detection result data list</param>
        /// <param name="productModel">Product information</param>
        /// <param name="outputPath">Output path</param>
        /// <returns>Generated XML file path list</returns>
        public static List<string> GenerateBatchMESXml(List<ReportModel> reportModels, ProductParamModel productModel, string outputPath)
        {
            var filePaths = new List<string>();
            
            foreach (var reportModel in reportModels)
            {
                try
                {
                    string filePath = GenerateMESXml(reportModel, productModel, outputPath);
                    filePaths.Add(filePath);
                }
                catch (Exception ex)
                {
                    // Log error but continue processing other data
                    Console.WriteLine($"Failed to generate MES XML - SerialNumber: {reportModel?.SerialNumber}, Error: {ex.Message}");
                }
            }

            return filePaths;
        }
    }
}
