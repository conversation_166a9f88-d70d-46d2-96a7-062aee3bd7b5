﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common.OPCHelper
{
    public delegate void ConnectedEventHandler(object sender, OpcEventArgs e);
    public delegate void ConnectEventHandler(object sender, OpcEventArgs e);
    public delegate void ErrorEventHandler(object sender, OpcEventArgs e);
    public delegate void InfoEventHandler(object sender, OpcEventArgs e);
    public delegate void WarningEventHandler(object sender, OpcEventArgs e);
}
