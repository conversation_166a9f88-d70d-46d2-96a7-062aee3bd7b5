﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class DriveLensCommand:BasicCommand
    {
        private uint _parameter;

        public DriveLensCommand(ref CanonCameraModel model, uint parameter)
            : base(ref model)
        {
            _parameter = parameter;
        }

        public override bool Execute()
        {
            uint num = EDSDK.EdsSendCommand(_model.Camera, 259u, (int)_parameter);
            switch (num)
            {
                case 129u:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e);
                        return true;
                    }
                default:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e);
                        break;
                    }
                case 0u:
                    break;
            }

            return true;
        }
    }
}
