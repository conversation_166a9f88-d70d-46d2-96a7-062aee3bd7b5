﻿using CommunityToolkit.Mvvm.Input;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Common.OPCHelper;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace IPM.Vision.ViewModel
{
    public class MainWindowViewModel:ViewModelBase
    {
        private readonly NLogHelper _logger;
        private readonly ObservableGlobalState _globalState;
        private Page _currentPage;
        private readonly MainCameraParaService _mainCameraParaService;
        private readonly LightParamService _lightParamService;
        private readonly HKVisionService _hkVisionService;
        private readonly EquipmentService _equipmentService;
        private readonly OptVisionService _optVisionService;
        private readonly GlobalCameraManager _globalCameraManager; // 添加全局相机管理器
        private readonly CheckService _checkService; // 添加检测服务
        
        // 软件关闭控制
        private bool _canClose = true;
        private string _closeBlockReason = string.Empty;
        
        /// <summary>
        /// 是否可以关闭软件
        /// </summary>
        public bool CanClose
        {
            get => _canClose;
            private set => SetProperty(ref _canClose, value);
        }
        
        /// <summary>
        /// 关闭阻止的原因
        /// </summary>
        public string CloseBlockReason
        {
            get => _closeBlockReason;
            private set => SetProperty(ref _closeBlockReason, value);
        }
        
        /// <summary>
        /// 全局相机管理器，提供给其他ViewModel使用
        /// </summary>
        public GlobalCameraManager GlobalCameraManager => _globalCameraManager;
        
        public MainWindowViewModel(NLogHelper logger,
            ObservableGlobalState globalState,
            HKVisionService hkVisionService,
            IEquipmentService equipmentService,
            IMainCameraParaService mainCameraParaService,
            ILightParamService lightParamService,
            OptVisionService optVisionService,
            GlobalCameraManager globalCameraManager, // 注入全局相机管理器
            CheckService checkService) // 注入检测服务
        {
            _logger = logger;
            _globalState = globalState;
            _globalState.NotifyProgressStatus += _globalState_NotifyProgressStatus;
            _equipmentService = (EquipmentService)equipmentService;
            _mainCameraParaService = (MainCameraParaService)mainCameraParaService;
            _globalState.ConfigChanged += _globalState_ConfigChanged;
            _hkVisionService = hkVisionService;
            _lightParamService = (LightParamService)lightParamService;
            _optVisionService = optVisionService;
            _globalCameraManager = globalCameraManager; // 保存全局相机管理器引用
            _checkService = checkService; // 保存检测服务引用

            // 订阅裁剪状态变化事件
            _globalState.CroppingStatusChanged += OnCroppingStatusChanged;

            // 订阅全局相机管理器的状态变化
            GlobalCameraManager.ConnectionStatusChanged += OnCameraConnectionStatusChanged;
        }

        private void _globalState_NotifyProgressStatus(bool obj)
        {
            IsRunning = obj;
        }

        private bool _isRunning =false;
        public bool IsRunning
        {
            get => _isRunning;
            set => SetProperty(ref _isRunning, value);
        }

        private void _globalState_ConfigChanged(ObservableConfigModel obj)
        {
            ObConfigModel = obj;
        }
        
        /// <summary>
        /// 处理裁剪状态变化事件
        /// </summary>
        /// <param name="hasCroppingTasks">是否有裁剪任务</param>
        private void OnCroppingStatusChanged(bool hasCroppingTasks)
        {
            if (hasCroppingTasks)
            {
                CanClose = false;
                CloseBlockReason = $"有 {_globalState.WaitingCropCount} 张照片正在等待或处理裁剪，请等待处理完成后再关闭软件";
                _logger.LogInfo($"软件关闭被阻止: {CloseBlockReason}");
            }
            else
            {
                CanClose = true;
                CloseBlockReason = string.Empty;
                _logger.LogInfo("裁剪任务已完成，软件可以正常关闭");
            }
        }

        /// <summary>
        /// 处理相机连接状态变化事件
        /// </summary>
        /// <param name="statusMessage">状态消息</param>
        private void OnCameraConnectionStatusChanged(string statusMessage)
        {
            _logger.LogInfo($"相机连接状态变化: {statusMessage}");
        }

        private ObservableUserInfoModel _userInfoModel;
        public ObservableUserInfoModel UserInfoModel
        {
            get => _userInfoModel;
            set => SetProperty(ref _userInfoModel, value);
        }

        private ObservableConfigModel _config;
        public ObservableConfigModel ObConfigModel
        {
            get => _config;
            set => SetProperty(ref _config, value);
        }

        public Page CurrentPage
        {
            get => _currentPage;
            set => SetProperty(ref _currentPage, value);
        }
        
        /// <summary>
        /// 全局状态，用于访问裁剪任务状态
        /// </summary>
        public ObservableGlobalState GlobalState => _globalState;
        
        /// <summary>
        /// 检查是否可以关闭窗口
        /// </summary>
        /// <returns>true表示可以关闭，false表示不能关闭</returns>
        public bool CheckCanCloseWindow()
        {
            try
            {
                // 检查是否正在运行
                if (IsRunning)
                {
                    HandyControl.Controls.MessageBox.Error("程序自动运行中，无法关闭软件！");
                    return false;
                }
                
                // 检查是否可以关闭（裁剪任务检查）
                if (!CanClose)
                {
                    string message = !string.IsNullOrEmpty(CloseBlockReason) 
                        ? CloseBlockReason 
                        : "当前有任务正在处理，无法关闭软件！";
                        
                    HandyControl.Controls.MessageBox.Warning(message, "无法关闭");
                    return false;
                }
                
                // 显示确认对话框
                var result = HandyControl.Controls.MessageBox.Show(
                    "确认关闭软件？", 
                    "温馨提示", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Information);
                
                return result == MessageBoxResult.Yes;
            }
            catch (Exception ex)
            {
                _logger.LogError($"窗口关闭检查异常: {ex.Message}");
                // 出现异常时允许关闭，避免程序无法退出
                return true;
            }
        }

        public IRelayCommand LoadCommand => new RelayCommand(async () =>
        {
            ObConfigModel = _globalState.AppConfig;
            if (string.IsNullOrEmpty(ObConfigModel.WorkStation)) ObConfigModel.WorkStation = "未设置";
            UserInfoModel = _globalState.LoginUser;
            _globalState.MenuChangedEvent += _globalState_MenuChangedEvent;

            _logger.LogInfo("🚀 开始优化的设备连接初始化序列...");
            _logger.LogInfo("   策略：PLC优先连接 → 并行初始化其他设备");

            // **优化策略：PLC优先连接（最重要的设备）**
            var plcTask = Task.Run(async () =>
            {
                try
                {
                    await ConnectOtherDevicesAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ PLC设备连接异常: {ex.Message}");
                }
            });

            // 创建并行任务列表
            var initializationTasks = new List<Task> { plcTask };

            // 任务2: 检测服务启动（可以并行启动）
            var serviceTask = Task.Run(() =>
            {
                try
                {
                    _logger.LogInfo("🔍 开始异步启动检测服务...");
                    StartCheckService();
                    _logger.LogInfo("✅ 检测服务启动任务完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ 检测服务启动异常: {ex.Message}");
                }
            });
            initializationTasks.Add(serviceTask);

            // 任务3: 相机连接（独立执行，不影响其他设备）
            var cameraTask = Task.Run(async () =>
            {
                try
                {
                    _logger.LogInfo("📷 开始异步初始化相机连接...");
                    await InitializeGlobalCameraConnectionsAsync();

                    // 输出相机连接诊断信息
                    GlobalCameraManager.DiagnosteCameraStatus();
                    _logger.LogInfo("✅ 相机连接任务完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ 相机初始化异常: {ex.Message}");
                }
            });
            initializationTasks.Add(cameraTask);

            // 等待所有任务完成（或超时）
            try
            {
                _logger.LogInfo("⏳ 等待所有设备初始化完成...");
                await Task.WhenAll(initializationTasks);
                _logger.LogInfo("🎉 所有设备初始化任务已完成");
                
                // **新增：显示设备连接状态摘要**
                await DisplayDeviceConnectionSummaryAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError($"⚠️ 部分设备初始化任务异常: {ex.Message}");
                
                // 即使有异常也显示连接状态摘要
                await DisplayDeviceConnectionSummaryAsync();
            }
        });

        /// <summary>
        /// 手动重新连接PLC（用于界面按钮调用）
        /// </summary>
        public async Task<bool> ManualReconnectPlcAsync()
        {
            try
            {
                _logger.LogInfo("🔄 用户手动触发PLC重连...");

                string plcAddress = _globalState.AppConfig?.PlcAddress;
                if (string.IsNullOrEmpty(plcAddress))
                {
                    _logger.LogError("❌ PLC地址未配置，无法重连");
                    return false;
                }

                bool result = await ConnectPlcWithRetryAsync(plcAddress);
                
                if (result)
                {
                    _logger.LogInfo("✅ 手动PLC重连成功");
                    await DisplayDeviceConnectionSummaryAsync();
                }
                else
                {
                    _logger.LogError("❌ 手动PLC重连失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 手动PLC重连异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 使用全局相机管理器初始化所有相机连接
        /// </summary>
        private async Task InitializeGlobalCameraConnectionsAsync()
        {
            try
            {
                _logger.LogInfo("=== 开始全局相机连接初始化 ===");
                
                // 使用全局相机管理器初始化所有相机
                await GlobalCameraManager.InitializeCamerasAsync();
                
                _logger.LogInfo($"=== 全局相机连接初始化完成 - OPT: {(GlobalCameraManager.IsOptCameraConnected ? "已连接" : "未连接")}, HK: {(GlobalCameraManager.IsHkCameraConnected ? "已连接" : "未连接")} ===");
            }
            catch (Exception ex)
            {
                _logger.LogError($"全局相机连接初始化异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 连接其他设备（PLC、光源等）
        /// </summary>
        private void ConnectOtherDevices()
        {
            Task.Factory.StartNew(() =>
            {
                try
                {
                    // 检查PLC配置
                    string plcAddress = _globalState.AppConfig?.PlcAddress;
                    if (string.IsNullOrEmpty(plcAddress))
                    {
                        _logger.LogInfo("⚠️  PLC地址未配置，跳过PLC连接");
                        return;
                    }
                    // 连接PLC
                    _equipmentService.ConnectPlc();
                    // 连接光源（如果需要）
                    // _lightParamService.ConnectLight();

                    _logger.LogInfo("🔧 设备连接初始化完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ 连接设备异常: {ex.Message}");
                    _logger.LogError($"   异常详情: {ex}");
                }
            });
        }

        /// <summary>
        /// 异步连接其他设备（PLC、光源等）- 增强重试机制和连接验证
        /// </summary>
        private async Task ConnectOtherDevicesAsync()
        {
            await Task.Run(async () =>
            {
                try
                {
                    // 检查PLC配置
                    string plcAddress = _globalState.AppConfig?.PlcAddress;
                    if (string.IsNullOrEmpty(plcAddress))
                    {
                        _logger.LogInfo("⚠️  PLC地址未配置，跳过PLC连接");
                        return;
                    }

                    _logger.LogInfo($"🔗 开始连接PLC设备: {plcAddress}");

                    // **增强的PLC连接 - 带重试机制**
                    bool plcConnected = await ConnectPlcWithRetryAsync(plcAddress);
                    
                    if (plcConnected)
                    {
                        _logger.LogInfo("✅ PLC设备连接成功");
                    }
                    else
                    {
                        _logger.LogError("❌ PLC设备连接失败（已重试多次）");
                    }

                    // 连接光源（如果需要）
                    // await ConnectLightWithRetryAsync();

                    _logger.LogInfo("🔧 设备连接初始化完成");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ 连接设备异常: {ex.Message}");
                    _logger.LogError($"   异常详情: {ex}");
                }
            });
        }

        /// <summary>
        /// 带重试机制的PLC连接方法
        /// </summary>
        /// <param name="plcAddress">PLC地址</param>
        /// <returns>连接是否成功</returns>
        private async Task<bool> ConnectPlcWithRetryAsync(string plcAddress)
        {
            const int maxRetries = 3;
            const int connectionTimeoutSeconds = 20; // 增加超时时间，给数据验证更多时间
            const int retryDelayMs = 3000; // 增加重试间隔，给设备更多启动时间

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    _logger.LogInfo($"🔄 PLC连接尝试 {attempt}/{maxRetries} - 地址: {plcAddress}");

                    // 检查是否已经连接（避免重复连接）
                    if (_equipmentService.IsConnect)
                    {
                        _logger.LogInfo("✅ PLC已经连接，跳过连接步骤");
                        return true;
                    }

                    // 发起连接
                    _equipmentService.ConnectPlc();

                    // **关键改进：等待连接建立并验证状态**
                    _logger.LogInfo($"⏳ 等待PLC连接建立（超时: {connectionTimeoutSeconds}秒）...");
                    bool connectionEstablished = await WaitForPlcConnectionAsync(connectionTimeoutSeconds);

                    if (connectionEstablished)
                    {
                        // **三次验证：确保连接真正可用**
                        _logger.LogInfo("✅ 阶段1和2验证通过，开始最终验证...");
                        
                        // **额外稳定时间**
                        await Task.Delay(2000); // 等待2秒让连接和数据通道完全稳定
                        
                        // **最终验证：基础连接状态 + 数据通信能力**
                        if (_equipmentService.IsConnect)
                        {
                            _logger.LogInfo($"🎉 PLC连接成功验证 - 尝试次数: {attempt}");
                            return true;
                        }
                        else
                        {
                            _logger.LogInfo($"⚠️ PLC最终连接状态验证失败 - 尝试 {attempt}");
                        }
                    }
                    else
                    {
                        _logger.LogInfo($"⏰ PLC连接或数据验证超时 - 尝试 {attempt}");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ PLC连接尝试 {attempt} 异常: {ex.Message}");
                    
                    // 记录详细的异常信息用于诊断
                    if (ex.InnerException != null)
                    {
                        _logger.LogError($"   内部异常: {ex.InnerException.Message}");
                    }
                }

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries)
                {
                    _logger.LogInfo($"🔄 等待 {retryDelayMs}ms 后重试PLC连接...");
                    await Task.Delay(retryDelayMs);
                    
                    // **重试前清理连接状态**
                    try
                    {
                        if (_equipmentService.IsConnect)
                        {
                            _logger.LogInfo("🧹 清理旧的PLC连接状态");
                            _equipmentService.DisconnectPLC();
                            await Task.Delay(1000); // 等待断开完成
                        }
                    }
                    catch (Exception cleanupEx)
                    {
                        _logger.LogError($"清理PLC连接状态异常: {cleanupEx.Message}");
                    }
                }
            }

            _logger.LogError($"💥 PLC连接失败 - 已尝试 {maxRetries} 次，建议检查：");
            _logger.LogError($"   1. PLC设备是否开机且网络可达: {plcAddress}");
            _logger.LogError($"   2. OPC UA服务是否正常运行");
            _logger.LogError($"   3. 网络防火墙设置");
            _logger.LogError($"   4. PLC设备负载情况");

            return false;
        }

        /// <summary>
        /// 等待PLC连接建立并验证数据通信
        /// **关键改进：不仅检查连接状态，还要验证能否读取设备状态点位数据**
        /// </summary>
        /// <param name="timeoutSeconds">超时时间（秒）</param>
        /// <returns>连接是否在超时时间内建立且数据通信正常</returns>
        private async Task<bool> WaitForPlcConnectionAsync(int timeoutSeconds)
        {
            var timeout = TimeSpan.FromSeconds(timeoutSeconds);
            var startTime = DateTime.Now;
            var checkInterval = TimeSpan.FromMilliseconds(500); // 每500ms检查一次
            bool basicConnectionVerified = false;
            bool dataCommVerified = false;

            _logger.LogInfo("🔍 PLC连接验证阶段：");
            _logger.LogInfo("   阶段1: 基础连接状态检查");
            _logger.LogInfo("   阶段2: 设备状态点位数据读取验证");

            while (DateTime.Now - startTime < timeout)
            {
                try
                {
                    // **阶段1: 检查基础连接状态**
                    if (!basicConnectionVerified && _equipmentService.IsConnect)
                    {
                        basicConnectionVerified = true;
                        _logger.LogInfo("✅ 阶段1完成: PLC基础连接已建立");
                        _logger.LogInfo("🔄 进入阶段2: 验证设备状态点位数据通信...");
                    }

                    // **阶段2: 验证数据通信（最关键的验证）**
                    if (basicConnectionVerified && !dataCommVerified)
                    {
                        try
                        {
                            // **核心验证：尝试读取设备状态点位数据**
                            bool canReadDeviceData = await TestDeviceDataCommunicationAsync();
                            
                            if (canReadDeviceData)
                            {
                                dataCommVerified = true;
                                _logger.LogInfo("🎉 阶段2完成: 设备状态点位数据读取成功");
                                _logger.LogInfo("✅ PLC连接验证全部通过 - 连接完全可用");
                                return true;
                            }
                            else
                            {
                                var elapsed = DateTime.Now - startTime;
                                _logger.LogInfo($"⏳ 设备数据读取尚未成功，继续等待... (已等待: {elapsed.TotalSeconds:F1}秒)");
                            }
                        }
                        catch (Exception dataEx)
                        {
                            var elapsed = DateTime.Now - startTime;
                            _logger.LogInfo($"🔄 设备数据读取测试异常: {dataEx.Message} (已等待: {elapsed.TotalSeconds:F1}秒)");
                        }
                    }

                    // 短暂等待后继续检查
                    await Task.Delay(checkInterval);
                }
                catch (Exception ex)
                {
                    _logger.LogError($"PLC连接检查异常: {ex.Message}");
                    await Task.Delay(checkInterval);
                }
            }

            // 超时后的状态分析
            _logger.LogInfo($"⏰ PLC连接验证超时（{timeoutSeconds}秒）");
            _logger.LogInfo($"   基础连接状态: {(basicConnectionVerified ? "✅ 成功" : "❌ 失败")}");
            _logger.LogInfo($"   数据通信验证: {(dataCommVerified ? "✅ 成功" : "❌ 失败")}");
            
            if (basicConnectionVerified && !dataCommVerified)
            {
                _logger.LogInfo("🔧 诊断建议: OPC UA连接已建立但无法读取设备数据，可能原因：");
                _logger.LogInfo("   1. 设备状态点位配置错误或未配置");
                _logger.LogInfo("   2. PLC程序未运行或状态异常");
                _logger.LogInfo("   3. OPC UA节点地址映射错误");
                _logger.LogInfo("   4. 设备权限或安全设置问题");
            }

            return false;
        }

        /// <summary>
        /// 测试设备数据通信是否正常
        /// **通过实际读取设备状态点位来验证通信是否可用**
        /// </summary>
        /// <returns>数据通信是否正常</returns>
        private async Task<bool> TestDeviceDataCommunicationAsync()
        {
            const int maxTestAttempts = 3;
            const int testDelayMs = 1000;

            _logger.LogInfo("🔍 开始设备数据通信测试...");

            for (int attempt = 1; attempt <= maxTestAttempts; attempt++)
            {
                try
                {
                    _logger.LogInfo($"📊 数据通信测试尝试 {attempt}/{maxTestAttempts}");

                    // **关键改进：给OPC UA连接更多时间稳定**
                    if (attempt == 1)
                    {
                        _logger.LogInfo("⏳ 首次测试前等待3秒，让OPC UA连接完全稳定...");
                        await Task.Delay(3000);
                    }

                    // **验证OPC UA连接状态**
                    if (!_equipmentService.IsConnect)
                    {
                        _logger.LogInfo($"❌ 测试 {attempt}: OPC UA连接状态为False");
                        
                        if (attempt < maxTestAttempts)
                        {
                            await Task.Delay(testDelayMs);
                            continue;
                        }
                        return false;
                    }

                    // **尝试读取设备状态数据**
                    _logger.LogInfo($"🔄 测试 {attempt}: 开始读取设备状态点位...");
                    var deviceStatus = await GetCurrentDeviceStatusAsync();
                    
                    if (deviceStatus.HasValue)
                    {
                        var statusValue = deviceStatus.Value;
                        var statusDesc = GetDeviceStatusDescription(statusValue);
                        
                        _logger.LogInfo($"✅ 数据通信测试成功: 读取到设备状态 {statusValue} ({statusDesc})");
                        _logger.LogInfo($"🎉 设备状态点位数据读取验证通过 - 尝试次数: {attempt}");
                        return true;
                    }
                    else
                    {
                        _logger.LogInfo($"⚠️ 测试 {attempt}: 设备状态读取返回空值");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInfo($"❌ 测试 {attempt}: 数据通信测试异常 - {ex.Message}");
                    
                    // 记录异常类型用于诊断
                    if (ex is NullReferenceException)
                    {
                        _logger.LogInfo($"   空引用异常，可能OPC UA客户端尚未完全初始化");
                    }
                    else if (ex is ObjectDisposedException)
                    {
                        _logger.LogInfo($"   对象已释放异常，需要重新建立连接");
                    }
                    else if (ex is InvalidOperationException)
                    {
                        _logger.LogInfo($"   操作状态异常，客户端可能正在连接中");
                    }
                }

                // 非最后一次尝试时等待后重试
                if (attempt < maxTestAttempts)
                {
                    _logger.LogInfo($"⏳ 等待 {testDelayMs}ms 后重试数据通信测试...");
                    await Task.Delay(testDelayMs);
                }
            }

            _logger.LogInfo($"❌ 数据通信测试失败 - 已尝试 {maxTestAttempts} 次");
            _logger.LogInfo("🔧 可能原因：");
            _logger.LogInfo("   1. OPC UA连接建立但设备状态点位配置错误");
            _logger.LogInfo("   2. PLC程序未运行或状态点位未正确映射");
            _logger.LogInfo("   3. 设备响应延迟较大，需要更长等待时间");
            _logger.LogInfo("   4. 网络通信不稳定或有间歇性问题");

            return false;
        }

        /// <summary>
        /// 获取当前设备状态 - 增强防护版本
        /// **直接调用设备服务的状态读取方法**
        /// </summary>
        /// <returns>设备状态值，如果读取失败返回null</returns>
        private async Task<int?> GetCurrentDeviceStatusAsync()
        {
            try
            {
                // **预检查：确保设备服务可用**
                if (_equipmentService == null)
                {
                    _logger.LogInfo("❌ 设备服务为空，无法读取状态");
                    return null;
                }

                if (!_equipmentService.IsConnect)
                {
                    _logger.LogInfo("❌ 设备服务未连接，无法读取状态");
                    return null;
                }

                _logger.LogInfo("🔄 开始调用设备服务读取状态...");

                // **使用超时控制的Task.Run调用**
                var status = await Task.Run(() =>
                {
                    try
                    {
                        var result = _equipmentService.ReadStatus();
                        _logger.LogInfo($"📊 设备服务返回状态: {result}");
                        return result;
                    }
                    catch (NullReferenceException ex)
                    {
                        _logger.LogInfo($"💥 设备状态读取空引用异常: {ex.Message}");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogInfo($"❌ 设备状态读取异常: {ex.Message}");
                        throw;
                    }
                });

                _logger.LogInfo($"✅ 设备状态读取成功: {status}");
                return status;
            }
            catch (NullReferenceException ex)
            {
                _logger.LogInfo($"💥 读取设备状态时发生空引用异常: {ex.Message}");
                _logger.LogInfo("   这通常表示OPC UA客户端尚未完全初始化");
                return null;
            }
            catch (ObjectDisposedException ex)
            {
                _logger.LogInfo($"🗑️ 读取设备状态时对象已释放: {ex.Message}");
                _logger.LogInfo("   OPC UA客户端可能已被释放，需要重新连接");
                return null;
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogInfo($"⚠️ 读取设备状态时操作无效: {ex.Message}");
                _logger.LogInfo("   OPC UA客户端可能正在连接过程中");
                return null;
            }
            catch (TimeoutException ex)
            {
                _logger.LogInfo($"⏰ 读取设备状态超时: {ex.Message}");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogInfo($"❌ 读取设备状态发生未知异常: {ex.GetType().Name} - {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取设备状态描述
        /// </summary>
        /// <param name="status">状态值</param>
        /// <returns>状态描述</returns>
        private string GetDeviceStatusDescription(int status)
        {
            switch (status)
            {
                case 1:
                    return "待机";
                case 2:
                    return "运行";
                case 3:
                    return "暂停";
                case 4:
                    return "错误";
                default:
                    return "未知";
            }
        }

        /// <summary>
        /// 显示设备连接状态摘要
        /// </summary>
        private async Task DisplayDeviceConnectionSummaryAsync()
        {
            await Task.Run(async () =>
            {
                try
                {
                    _logger.LogInfo("📊 === 设备连接状态摘要 ===");
                    
                    // PLC连接状态（增强版 - 包括数据通信验证）
                    bool plcConnected = _equipmentService?.IsConnect ?? false;
                    string plcAddress = _globalState.AppConfig?.PlcAddress ?? "未配置";
                    
                    // 验证PLC数据通信状态
                    string plcDataStatus = "数据通信未验证";
                    if (plcConnected)
                    {
                        try
                        {
                            var deviceStatus = await GetCurrentDeviceStatusAsync();
                            plcDataStatus = deviceStatus.HasValue ? 
                                $"数据通信正常(状态:{deviceStatus.Value})" : 
                                "数据通信异常";
                        }
                        catch (Exception ex)
                        {
                            plcDataStatus = $"数据通信异常({ex.Message})";
                        }
                    }
                    
                    _logger.LogInfo($"   🔗 PLC设备: {(plcConnected ? "✅ 已连接" : "❌ 未连接")} ({plcAddress})");
                    if (plcConnected)
                    {
                        _logger.LogInfo($"      📊 数据状态: {plcDataStatus}");
                    }
                    
                    // 相机连接状态
                    bool optConnected = GlobalCameraManager?.IsOptCameraConnected ?? false;
                    bool hkConnected = GlobalCameraManager?.IsHkCameraConnected ?? false;
                    string optAddress = _globalState.AppConfig?.MainCameraAddress ?? "未配置";
                    string hkAddress = _globalState.AppConfig?.MarkCameraAddress ?? "未配置";
                    
                    _logger.LogInfo($"   📷 OPT相机: {(optConnected ? "✅ 已连接" : "❌ 未连接")} ({optAddress})");
                    _logger.LogInfo($"   📷 海康相机: {(hkConnected ? "✅ 已连接" : "❌ 未连接")} ({hkAddress})");
                    
                    // 光源连接状态（如果有）
                    try
                    {
                        bool lightConnected = _lightParamService?.IsConnect ?? false;
                        string lightPort = _globalState.AppConfig?.LightPort.ToString() ?? "未配置";
                        _logger.LogInfo($"   💡 光源设备: {(lightConnected ? "✅ 已连接" : "❌ 未连接")} (端口: {lightPort})");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogInfo($"   💡 光源设备: ❓ 状态未知 ({ex.Message})");
                    }
                    
                    // 总体状态评估
                    int connectedCount = 0;
                    int totalDevices = 0;
                    
                    // 统计关键设备连接情况
                    if (!string.IsNullOrEmpty(plcAddress) && plcAddress != "未配置")
                    {
                        totalDevices++;
                        if (plcConnected) connectedCount++;
                    }
                    
                    if (!string.IsNullOrEmpty(optAddress) && optAddress != "未配置")
                    {
                        totalDevices++;
                        if (optConnected) connectedCount++;
                    }
                    
                    if (!string.IsNullOrEmpty(hkAddress) && hkAddress != "未配置")
                    {
                        totalDevices++;
                        if (hkConnected) connectedCount++;
                    }
                    
                    string statusIcon = connectedCount == totalDevices ? "🎉" : (connectedCount > 0 ? "⚠️" : "❌");
                    _logger.LogInfo($"   {statusIcon} 设备连接率: {connectedCount}/{totalDevices} ({(totalDevices > 0 ? (connectedCount * 100 / totalDevices) : 0)}%)");
                    
                    _logger.LogInfo("📊 ========================");
                    
                    // 如果PLC未连接，提供故障排除建议
                    if (!plcConnected && !string.IsNullOrEmpty(plcAddress) && plcAddress != "未配置")
                    {
                        _logger.LogInfo("🔧 PLC连接故障排除建议：");
                        _logger.LogInfo("   1. 确认PLC设备已开机且运行正常");
                        _logger.LogInfo("   2. 检查网络连接和IP地址配置");
                        _logger.LogInfo("   3. 验证OPC UA服务是否启用");
                        _logger.LogInfo("   4. 检查防火墙和网络安全设置");
                        _logger.LogInfo("   5. 确认设备状态点位(EMCStatus)配置正确");
                        _logger.LogInfo("   6. 尝试重启软件或设备");
                        _logger.LogInfo("💡 提示：现在PLC连接验证包含设备状态点位数据读取");
                        _logger.LogInfo("   只有能成功读取到设备状态数据才算连接成功");
                    }
                    else if (plcConnected && plcDataStatus.Contains("异常"))
                    {
                        _logger.LogInfo("⚠️ PLC连接正常但数据通信异常：");
                        _logger.LogInfo("   1. 检查OPC节点配置文件(opc_config.lof)");
                        _logger.LogInfo("   2. 验证EMCStatus点位地址是否正确");
                        _logger.LogInfo("   3. 确认PLC程序中状态点位已正确映射");
                        _logger.LogInfo("   4. 检查OPC UA用户权限和安全设置");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError($"显示设备连接状态摘要异常: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 启动检测服务定时任务
        /// </summary>
        private void StartCheckService()
        {
            try
            {
                _logger.LogInfo("🔍 启动检测服务定时任务...");
                _checkService.StartGetData();
                _logger.LogInfo("✅ 检测服务定时任务启动成功");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 启动检测服务定时任务失败: {ex.Message}");
                _logger.LogError($"   异常详情: {ex}");
            }
        }

        private void _globalState_MenuChangedEvent(MenuModel menu)
        {
            if (menu != null) 
            {
                GetContainer(menu);
                
                // 根据页面类型管理相机流状态
                HandlePageCameraStreams(menu);
            }
        }

        /// <summary>
        /// 根据页面类型管理相机流状态
        /// </summary>
        /// <param name="menu">菜单信息</param>
        private async void HandlePageCameraStreams(MenuModel menu)
        {
            try
            {
                // 判断是否为相机相关页面
                bool isCameraPage = IsCameraRelatedPage(menu.MenuPath);
                
                if (isCameraPage)
                {
                    _logger.LogInfo($"🔄 切换到相机页面: {menu.MenuPath}，开始恢复相机流...");

                    // 切换到相机页面时，检查并恢复相机连接
                    await EnsureCameraConnectionsAsync();

                    // **增加延迟，确保连接检查完成**
                    await Task.Delay(500);

                    // 根据配置启动相应的相机流
                    // 主相机流启动（根据配置的主相机类型）
                    if (_globalState.AppConfig.MainCameraType == CameraEnum.OPT)
                    {
                        _logger.LogInfo($"🔄 启动主相机流 (OPT)...");
                        await GlobalCameraManager.StartOptStreamAsync();
                        _logger.LogInfo($"✅ 主相机流 (OPT) 启动完成, IP: {_globalState.AppConfig.MainCameraAddress}");
                    }
                    else if (_globalState.AppConfig.MainCameraType == CameraEnum.Canon)
                    {
                        // Canon相机流启动逻辑
                        _logger.LogInfo($"主相机类型为Canon，IP: {_globalState.AppConfig.MainCameraAddress}");
                    }

                    // **平衡优化：减少页面切换延迟但保持必要的稳定性**
                    _logger.LogInfo("🔄 开始恢复海康相机流...");

                    // **优化：减少等待时间**
                    await Task.Delay(500); // 从1000ms减少到500ms

                    // 启动海康相机流
                    await GlobalCameraManager.StartHkStreamAsync();

                    // **优化：减少验证等待时间**
                    await Task.Delay(300); // 从500ms减少到300ms
                    bool hkStreamStatus = GlobalCameraManager.IsHkCameraConnected;
                    _logger.LogInfo($"✅ 海康相机流恢复完成 - 连接状态: {(hkStreamStatus ? "正常" : "异常")}, IP: {_globalState.AppConfig.MarkCameraAddress}");

                    if (!hkStreamStatus)
                    {
                        _logger.LogInfo("⚠️ 海康相机流恢复失败，尝试重新启动...");
                        await Task.Delay(500); // 从1000ms减少到500ms
                        await GlobalCameraManager.StartHkStreamAsync();
                    }
                }
                else
                {
                    _logger.LogInfo($"🔄 切换到非相机页面: {menu.MenuPath}，开始暂停相机流...");

                    // 暂停所有相机流（但保持连接）
                    _logger.LogInfo("🛑 暂停OPT相机流...");
                    GlobalCameraManager.PauseOptStream();

                    _logger.LogInfo("🛑 暂停海康相机流...");
                    GlobalCameraManager.PauseHkStream();

                    _logger.LogInfo("✅ 所有相机流已暂停（连接保持）");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"处理页面相机流切换异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 确保相机连接状态，如果配置有变化则自动重连
        /// </summary>
        private async Task EnsureCameraConnectionsAsync()
        {
            try
            {
                _logger.LogInfo("检查相机连接状态和配置变化...");
                
                // 检查并恢复相机连接
                await GlobalCameraManager.CheckAndRecoverConnectionsAsync();
                
                _logger.LogInfo("相机连接状态检查完成");
            }
            catch (Exception ex)
            {
                _logger.LogError($"确保相机连接异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否为相机相关页面
        /// </summary>
        /// <param name="menuPath">菜单路径</param>
        /// <returns>是否为相机相关页面</returns>
        private bool IsCameraRelatedPage(string menuPath)
        {
            if (string.IsNullOrEmpty(menuPath)) return false;
            
            // 定义相机相关页面列表
            var cameraPages = new[]
            {
                "VisionPage",           // 视觉检测页面
                "CameraControl",        // 相机控制页面
                "LightControl",         // 光源控制页面（通常也需要相机预览）
                "EquipmentControl"      // 设备控制页面（可能需要相机预览）
            };
            
            return cameraPages.Any(page => menuPath.Contains(page));
        }

        public void DisconnectOPC()
        {
            _equipmentService.DisconnectPLC();
            // 注意：不在这里释放相机资源，因为现在使用全局管理器
        }

        public IRelayCommand CloseCommand => new RelayCommand(async () => {
            // 使用统一的关闭检查方法
            if (CheckCanCloseWindow())
            {
                await SafeShutdownAsync();
            }
        });

        /// <summary>
        /// 安全关闭软件，释放所有资源
        /// </summary>
        private async Task SafeShutdownAsync()
        {
            try
            {
                _logger.LogInfo("开始安全关闭软件...");
                
                // 首先停止所有正在运行的任务
                try
                {
                    _globalState.IsRunning = false;
                    _logger.LogInfo("已停止所有运行任务");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"停止运行任务异常: {ex.Message}");
                }

                // 停止检测服务定时任务
                try
                {
                    _logger.LogInfo("正在停止检测服务定时任务...");
                    _checkService.StopListener();
                    _logger.LogInfo("检测服务定时任务已停止");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"停止检测服务定时任务异常: {ex.Message}");
                }
                
                // 释放单实例互斥量
                try
                {
                    LoginWindowViewModel.GlobalReleaseMutex();
                    _logger.LogInfo("单实例互斥量已释放");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"释放互斥量异常: {ex.Message}");
                }
                
                // 设置总体超时时间
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(15)))
                {
                    var shutdownTasks = new List<Task>();
                    
                    // 使用全局相机管理器释放所有相机资源（最重要）
                    shutdownTasks.Add(Task.Run(() =>
                    {
                        try
                        {
                            _logger.LogInfo("正在通过全局管理器释放相机资源...");
                            GlobalCameraManager.ReleaseAllCameraResources();
                            _logger.LogInfo("全局相机资源已释放");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"释放全局相机资源异常: {ex.Message}");
                        }
                    }, cts.Token));
                    
                    // 关闭光源
                    if (_lightParamService?.IsConnect == true)
                    {
                        shutdownTasks.Add(Task.Run(() =>
                        {
                            try
                            {
                                _logger.LogInfo("正在关闭光源...");
                                _lightParamService.CloseAllLight();
                                Thread.Sleep(200);
                                _lightParamService.DisconnectLight();
                                _logger.LogInfo("光源已关闭");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"关闭光源异常: {ex.Message}");
                            }
                        }, cts.Token));
                    }
                    
                    // 断开PLC连接
                    if (_equipmentService?.IsConnect == true)
                    {
                        shutdownTasks.Add(Task.Run(() =>
                        {
                            try
                            {
                                _logger.LogInfo("正在断开PLC连接...");
                                _equipmentService.DisconnectPLC();
                                _logger.LogInfo("PLC连接已断开");
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError($"断开PLC连接异常: {ex.Message}");
                            }
                        }, cts.Token));
                    }
                    
                    // 等待所有关闭任务完成或超时
                    try
                    {
                        var timeoutTask = Task.Delay(12000, cts.Token);
                        var allTasksTask = Task.WhenAll(shutdownTasks);
                        
                        var completedTask = await Task.WhenAny(allTasksTask, timeoutTask);
                        if (completedTask == allTasksTask)
                        {
                            _logger.LogInfo("所有资源释放完成");
                        }
                        else
                        {
                            _logger.LogInfo("部分资源释放超时，将强制关闭软件");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"资源释放过程中发生异常: {ex.Message}");
                    }
                }
                
                _logger.LogInfo("开始关闭应用程序");
                
                // 在UI线程上执行最终关闭
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        Application.Current.Shutdown();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"应用程序关闭异常: {ex.Message}");
                        Environment.Exit(0);
                    }
                });
                
                // 如果程序执行到这里还没退出，等待2秒后强制退出
                await Task.Delay(2000);
                _logger.LogError("程序未能正常退出，执行强制退出");
                Environment.Exit(1);
            }
            catch (Exception ex)
            {
                _logger.LogError($"软件关闭过程中发生严重异常: {ex.Message}");
                
                // 显示错误信息并强制退出
                try
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        HandyControl.Controls.MessageBox.Error($"软件关闭异常: {ex.Message}\n将强制退出程序");
                    });
                }
                catch { }
                
                Environment.Exit(1);
            }
        }

        public IRelayCommand<Window> MinCommand => new RelayCommand<Window>((param) => {

            if (param != null)
            {
                param.WindowState = WindowState.Minimized;
            }
        });

        /// <summary>
        /// 强制关闭命令 - 紧急情况下使用
        /// </summary>
        public IRelayCommand ForceCloseCommand => new RelayCommand(() =>
        {
            var result = HandyControl.Controls.MessageBox.Show("确认强制关闭软件？这将跳过资源清理步骤。", "强制关闭", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                _logger.LogInfo("用户请求强制关闭软件");
                // 释放互斥量
                LoginWindowViewModel.GlobalReleaseMutex();
                Environment.Exit(1);
            }
        });

        private void GetContainer(MenuModel menu)
        {
            CurrentPage = null;
            Type type = Type.GetType($"IPM.Vision.Views.Pages.{menu.MenuPath}");
            ConstructorInfo constructorInfo = type.GetConstructor(System.Type.EmptyTypes);
            CurrentPage = (Page)constructorInfo.Invoke(null);
        }

        

    }
}
