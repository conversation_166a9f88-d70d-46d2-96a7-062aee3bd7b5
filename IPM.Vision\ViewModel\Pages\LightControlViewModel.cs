﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.Dialogs;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.Dialogs;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace IPM.Vision.ViewModel.Pages
{
    public class LightControlViewModel: ViewModelBase
    {
        private bool _isLoading = false;
        private readonly NLogHelper _logger;
        private readonly ProcessParaService _processParaService;
        private readonly LightParamService _lightService;
        private string _paraName;
        private ObservableCollection<ObservableLightModel> _dataList;

        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public ObservableCollection<ObservableLightModel> DataList
        {
            get => _dataList;
            set => SetProperty(ref _dataList, value);
        }

        public string ParamName
        {
            get => _paraName;
            set => SetProperty(ref _paraName, value);
        }

        public LightControlViewModel(NLogHelper logger, IProcessParaService processParaService, ILightParamService lightService)
        {
            _logger = logger;
            _processParaService = (ProcessParaService)processParaService;
            _lightService = (LightParamService)lightService;
        }

        public IRelayCommand AddParamCommand => new RelayCommand(async () =>
        {
            var result = await Dialog.Show<LightCtrlDialog>()
             .Initialize<LightCtrlDialogViewModel>(vm =>
             {
                 vm.Title = "新增光源参数";
                 vm.LightModel = new ObservableLightModel();
                 vm.IsInsert = true;
             })
             .GetResultAsync<bool>();
            SearchEvent();
        });

        public IRelayCommand<ObservableLightModel> ModifyParamCommand => new RelayCommand<ObservableLightModel>(async (model) =>
        {
            var result = await Dialog.Show<LightCtrlDialog>()
             .Initialize<LightCtrlDialogViewModel>(vm =>
             {
                 vm.Title = "修改光源参数";
                 vm.IsInsert = false;
                 vm.LightModel = model;
             })
             .GetResultAsync<bool>();
            SearchEvent();
        });

        public IRelayCommand<ObservableLightModel> DeleteParamCommand => new RelayCommand<ObservableLightModel>(async (model) =>
        {
            var temp = await _processParaService.getIsAny(item => item.LightParamId == model.Id);
            if (temp)
            {
                HandyControl.Controls.MessageBox.Error("无法删除该参数，有步骤在使用！");
                return;
            }
            var tempResult = HandyControl.Controls.MessageBox.Show($"确认删除参数信息？", "提示", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (tempResult == MessageBoxResult.Yes)
            {
                var deleteResult = await _lightService.Delete(model.Id);
                if (deleteResult) SearchEvent();
            }
        });

        public IRelayCommand SearchCommand => new RelayCommand(() =>
        {
            SearchEvent();
        });

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            SearchEvent();
        });

        private async void SearchEvent()
        {
            IsLoading = true;
            try
            {
                var temp = await _lightService.getByWhereIF(!string.IsNullOrEmpty(ParamName), item => item.ParamName.Contains(ParamName));
                DataList = temp.MapTo<List<LightParamModel>, ObservableCollection<ObservableLightModel>>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }
            finally
            {
                IsLoading = false;
            }
        }
    }
}
