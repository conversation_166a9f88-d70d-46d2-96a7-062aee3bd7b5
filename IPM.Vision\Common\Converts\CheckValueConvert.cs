﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;

namespace IPM.Vision.Common.Converts
{
    public class CheckValueConvert : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value.ToString() == "OK") return "Green";
            if (value.ToString() == "NG") return "Red";
            return "Black";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 检测状态文本转换器：将数据库状态值转换为用户友好的显示文本
    /// </summary>
    public class StatusTextConvert : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return "未知";

            string status = value.ToString().ToUpper();
            switch (status)
            {
                case "WAITING":
                    return "待检测";
                case "NG":
                    return "NG";
                case "PASS":
                    return "PASS";
                case "FAIL":
                    return "FAIL";
                case "NACK":
                    return "NACK";
                case "OK":
                    return "OK";
                default:
                    return status; // 返回原始值
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// 复判结果转换器：将PASS/FAIL转换为合格/不合格的中文显示
    /// </summary>
    public class ManualReviewResultConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null || string.IsNullOrEmpty(value.ToString()))
                return "未复判";

            string result = value.ToString().ToUpper();
            switch (result)
            {
                case "PASS":
                    return "合格";
                case "FAIL":
                    return "不合格";
                default:
                    return "未复判";
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
