﻿using CommunityToolkit.Mvvm.ComponentModel;
using IPM.Vision.Common;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public partial class ObservableProcessModel : ViewModelBase
    {
        private string _id;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _paraName;
        public string ParamName
        {
            get => _paraName;
            set => SetProperty(ref _paraName, value);
        }

        private int _processNumber = 1;
        public int ProcessNumber
        {
            get => _processNumber;
            set => SetProperty(ref _processNumber, value);
        }

        private PictureType _takeType = PictureType.SINGLE;
        public PictureType TakeType
        {
            get => _takeType;
            set => SetProperty(ref _takeType, value);
        }

        private PictureLayoutEnum _pictureLayout;
        public PictureLayoutEnum PictureLayout
        {
            get => _pictureLayout;
            set => SetProperty(ref _pictureLayout, value);
        }

        private ProcessTypeEnum _processType = ProcessTypeEnum.TAKEPICTURE;

        public ProcessTypeEnum ProcessType
        {
            get => _processType;
            set => SetProperty(ref _processType, value);
        }

        private CameraTypeEnum _cameraType = CameraTypeEnum.Main;
        public CameraTypeEnum CameraType
        {
            get => _cameraType;
            set => SetProperty(ref _cameraType, value);
        }

        private string _cameraTypeName = "主相机";
        public string CameraTypeName
        {
            get => _cameraTypeName;
            set => SetProperty(ref _cameraTypeName, value);
        }

        private int _takeTime = 1;
        public int TakeTime
        {
            get => _takeTime;
            set => SetProperty(ref _takeTime, value);
        }

        private string _equipmentName;
        public string EquipmentName
        {
            get => _equipmentName;
            set => SetProperty(ref _equipmentName, value);
        }

        private string _equipmentParaId;
        public string EquipmentParaId
        {
            get => _equipmentParaId;
            set => SetProperty(ref _equipmentParaId, value);
        }

        private string _cameraParaName;
        public string CameraParaName
        {
            get => _cameraParaName;
            set => SetProperty(ref _cameraParaName, value);
        }

        private string _mainCameraId;
        public string MainCameraId
        {
            get => _mainCameraId;
            set => SetProperty(ref _mainCameraId, value);
        }

        public string _lightParaName;

        public string LightParaName
        {
            get => _lightParaName;
            set => SetProperty(ref _lightParaName, value);
        }

        private string _lightParamId;
        public string LightParamId
        {
            get => _lightParamId;
            set => SetProperty(ref _lightParamId, value);
        }

        private ObservableMainCameraParaModel _mainCameraPara;
        public ObservableMainCameraParaModel MainCameraPara
        {
            get => _mainCameraPara;
            set => SetProperty(ref _mainCameraPara, value);
        }

        private ObservableLightModel _lightModel;
        public ObservableLightModel LightModel
        {
            get => _lightModel;
            set => SetProperty(ref _lightModel, value);
        }

        public ObservableEquipmentModel _equipmentPara;
        public ObservableEquipmentModel EquipmentPara
        {
            get => _equipmentPara;
            set => SetProperty(ref _equipmentPara, value);
        }

        private bool _status = false;
        public bool Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        private ObservableCollection<ObservablePinPosition> _pinPositions;
        /// <summary>
        /// 关联的pin脚坐标列表
        /// </summary>
        public ObservableCollection<ObservablePinPosition> PinPositions
        {
            get => _pinPositions;
            set => SetProperty(ref _pinPositions, value);
        }

        private int _pinStartIndex;
        /// <summary>
        /// pin脚起始索引
        /// </summary>
        public int PinStartIndex
        {
            get => _pinStartIndex;
            set => SetProperty(ref _pinStartIndex, value);
        }

        private int _pinCount;
        /// <summary>
        /// 该process包含的pin脚数量
        /// </summary>
        public int PinCount
        {
            get => _pinCount;
            set => SetProperty(ref _pinCount, value);
        }


    }

}
