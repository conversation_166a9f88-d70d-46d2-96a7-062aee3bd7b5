﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("main_camera_para")]
    public class MainCameraParamModel:BasicModel
    {
        [SugarColumn(ColumnName = "para_name")]
        public string ParamName { get; set; }

        [SugarColumn(ColumnName = "ae", IsNullable = true)]
        public uint Ae { get; set; }

        [SugarColumn(ColumnName = "drive_mode", IsNullable = true)]
        public uint DriveMode { get; set; }

        [SugarColumn(ColumnName = "av", IsNullable = true)]
        public uint Av { get; set; }

        [SugarColumn(ColumnName = "evf_af", IsNullable = true)]
        public uint EvfAfMode { get; set; }

        [SugarColumn(ColumnName = "af_mode", IsNullable = true)]
        public uint AfMode { get; set; }

        [SugarColumn(ColumnName = "evf_af_switch", IsNullable = true)]
        public uint EvfAfSwitch { get; set; }

        [SugarColumn(ColumnName = "white_balance", IsNullable = true)]
        public uint WhiteBalance { get; set; }

        [SugarColumn(ColumnName = "tv", IsNullable = true)]
        public uint Tv { get; set; }

        [SugarColumn(ColumnName = "exposure", IsNullable = true)]
        public uint Exposure { get; set; }

        [SugarColumn(ColumnName = "iso", IsNullable = true)]
        public uint Iso { get; set; }


        [SugarColumn(ColumnName = "picture_style", IsNullable = true)]
        public uint PictureStyle { get; set; }

        [SugarColumn(ColumnName = "hdr", IsNullable = true)]
        public uint Hdr { get; set; }

        [SugarColumn(ColumnName = "hdr_style", IsNullable = true)]
        public uint HdrStyle { get; set; }

        [SugarColumn(ColumnName = "open_auto_focus_len", IsNullable = true)]
        public bool AutoFocusLenOpened { get; set; }

        [SugarColumn(ColumnName = "open_zoom_len", IsNullable = true)]
        public bool ZoomLenOpened { get; set; }

        [SugarColumn(ColumnName = "zoom_number", IsNullable = true)]
        public uint ZoomNumber { get; set; }


        [SugarColumn(ColumnName = "image_quality", IsNullable = true)]
        public uint ImageQuality { get; set; }

        [SugarColumn(ColumnName = "operator")]
        public string Operator { get; set; }

        

        [SugarColumn(ColumnName = "create_time")]
        public DateTime CreateTime { get; set; }
    }
}
