﻿using IPM.Vision.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    public class ConfigModel
    {
        public string LogoPath { get; set; } = "pack://application:,,,/Assets/logo.png";
        public string WorkStation { get; set; } = string.Empty;
        public ConnectEnum ScanType { get; set; }
        public SerialPortEnum ScanPort { get; set; }
        public OpenEnum OpenOCR { get; set; } = 0;
        public ConnectEnum LightType { get; set; }
        public SerialPortEnum LightPort { get; set; }
        public CameraEnum MainCameraType { get; set; } = 0;
        public string MainCameraAddress { get; set; } = string.Empty;
        public OpenEnum HaveMarkCamera { get; set; } = OpenEnum.CLOSE;
        public string MarkCameraAddress { get; set; } = string.Empty;
        public SerialPortEnum MarkLightPort { get; set; }
        public string PLCAddress { get; set; } = string.Empty;
        public SerialPortEnum WeighAddress { get; set; }
        public FolderEnum SaveType { get; set; }
        public string SaveFolder { get; set; } = string.Empty;
        public string SaveFolderAddress { get; set; } = string.Empty;
        public FolderNameTypeEnum FolderNameType { get; set; } = 0;

        public string LocalAddress { get; set; } = "***********";

        public string AiSource { get; set; } = string.Empty;

        public double DiffRotate { get; set; } = 0.003375;

        public string AiResult { get; set; } = string.Empty;

        public List<CustomNameModel> WatermarkList { get; set; } = new List<CustomNameModel>();
        public List<CustomNameModel> PictureList { get; set; } = new List<CustomNameModel>();
        public List<CustomNameModel> FolderNameList { get; set; } = new List<CustomNameModel>();

        public string WaterColor { get; set; } = string.Empty;
        public int WaterSize { get; set; } = 22;
        public int WaterX { get; set; }
        public int WaterY { get; set; }

        // ===== MES 输出配置 =====
        /// <summary>
        /// 是否开启 MES XML 输出
        /// </summary>
        public bool EnableMESOutput { get; set; } = false;

        /// <summary>
        /// MES XML 输出文件夹，默认 exe 同级 mesdata
        /// </summary>
        public string MESOutputFolder { get; set; } = string.Empty;
    }
}
