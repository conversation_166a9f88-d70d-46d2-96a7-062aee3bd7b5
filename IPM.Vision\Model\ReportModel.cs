﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("report")]
    public class ReportModel
    {
        [SugarColumn(ColumnName = "id", IsPrimaryKey = true, IsIdentity = true, ColumnDataType = "INTEGER")]
        public long Id { get; set; }
        
        [SugarColumn(ColumnName = "order_number", IsNullable = true, ColumnDataType = "TEXT")]
        public string OrderNumber { get; set; }

        [SugarColumn(ColumnName = "serial_number", IsNullable = true, ColumnDataType = "TEXT")]
        public string SerialNumber { get; set; }

        [SugarColumn(ColumnName = "picture_name", ColumnDataType = "TEXT")]
        public string PictureName { get; set; }

        [SugarColumn(ColumnName = "picture_path", ColumnDataType = "TEXT")]
        public string PicturePath { get; set; }

        [SugarColumn(ColumnName = "product_number", IsNullable = true, ColumnDataType = "TEXT")]
        public string ProductNumber { get; set; }

        [SugarColumn(ColumnName = "weight", IsNullable = true, ColumnDataType = "TEXT")]
        public string Weight { get; set; }

        [SugarColumn(ColumnName = "process_id", IsNullable = true, ColumnDataType = "TEXT")]
        public string ProcessId { get; set; }

        [SugarColumn(ColumnName = "process_name", IsNullable = true, ColumnDataType = "TEXT")]
        public string ProcessName { get; set; }

        [SugarColumn(ColumnName = "create_time", ColumnDataType = "DATETIME")]
        public DateTime CreateTime { get; set; }

        [SugarColumn(ColumnName = "operator", ColumnDataType = "TEXT")]
        public string OperatorName { get; set; }

        [SugarColumn(ColumnName = "check_path", ColumnDataType = "TEXT")]
        public string CheckFilePath { get; set; }

        [SugarColumn(ColumnName = "work_station", IsNullable = true, ColumnDataType = "TEXT")]
        public string WorkStation { get; set; }

        [SugarColumn(ColumnName = "status", ColumnDataType = "TEXT")]
        public string Status { get; set; }

        [SugarColumn(ColumnName = "check_time", ColumnDataType = "DATETIME")]
        public DateTime CheckTime { get; set; }

        /// <summary>
        /// 人工复判结果：PASS-合格，FAIL-不合格，null-未复判
        /// </summary>
        [SugarColumn(ColumnName = "manual_review_result", IsNullable = true, ColumnDataType = "TEXT")]
        public string ManualReviewResult { get; set; }

        /// <summary>
        /// 人工复判时间
        /// </summary>
        [SugarColumn(ColumnName = "manual_review_time", IsNullable = true, ColumnDataType = "DATETIME")]
        public DateTime? ManualReviewTime { get; set; }

        /// <summary>
        /// 人工复判操作员
        /// </summary>
        [SugarColumn(ColumnName = "manual_review_operator", IsNullable = true, ColumnDataType = "TEXT")]
        public string ManualReviewOperator { get; set; }

        /// <summary>
        /// 复判备注
        /// </summary>
        [SugarColumn(ColumnName = "manual_review_remark", IsNullable = true, ColumnDataType = "TEXT")]
        public string ManualReviewRemark { get; set; }
    }
}
