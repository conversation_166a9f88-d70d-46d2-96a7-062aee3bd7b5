﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Camera.Com;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.CustomControls;
using IPM.Vision.Views.Dialogs;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class ProcessManageDialogViewModel : ViewModelBase, IDialogResultable<bool>
    {
        private bool _result = false;
        private string _title = string.Empty;
        private bool _isInsert = false;
        private NLogHelper _logger;
        private ObservableProcessModel _processModel;
        private readonly ObservableGlobalState _globalState;
        private readonly EquipmentService _equipmentService;
        private readonly MainCameraParaService _mainCameraParaService;
        private readonly LightParamService _lightParamService;
        private readonly ProcessParaService _processParaService;
        private ObservableCollection<ObservableEquipmentModel> _equipmentModels;
        private ObservableCollection<ObservableMainCameraParaModel> _mainCameraParaModels;
        private ObservableCollection<ObservableLightModel> _lightModels;
        public event Action<ObservableProcessModel> SaveSucceEvent;
        private float _stepNumber = 0.01F;


        public ProcessManageDialogViewModel(NLogHelper logger,
            IEquipmentService equipmentService,
            IMainCameraParaService mainCameraParaService,
            IProcessParaService processParaService,
            ObservableGlobalState globalState,
            ILightParamService lightParamService)
        {
            _logger = logger;
            _processParaService = (ProcessParaService)processParaService;
            _equipmentService = (EquipmentService)equipmentService;
            _mainCameraParaService = (MainCameraParaService)mainCameraParaService;
            _lightParamService = (LightParamService)lightParamService;
            _globalState = globalState;
        }

        public bool Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public float StepNumber
        {
            get => _stepNumber;
            set => SetProperty(ref _stepNumber, value);
        }

        public ObservableCollection<ObservableEquipmentModel> ObservableEquipmentModels
        {
            get => _equipmentModels;
            set => SetProperty(ref _equipmentModels, value);
        }

        public ObservableCollection<ObservableMainCameraParaModel> MainCameraParaModels
        {
            get => _mainCameraParaModels;
            set => SetProperty(ref _mainCameraParaModels, value);
        }

        public ObservableCollection<ObservableLightModel> LightModels
        {
            get => _lightModels;
            set => SetProperty(ref _lightModels, value);
        }

        public bool IsInsert
        {
            get => _isInsert;
            set => SetProperty(ref _isInsert, value);
        }

        public ObservableProcessModel CurrentProcessModel
        {
            get => _processModel; 
            set => SetProperty(ref _processModel, value);
        }
        private ObservableMainCameraParaModel _currentCameraPara;
        public ObservableMainCameraParaModel CurrentCameraParaModel
        {
            get => _currentCameraPara;
            set => SetProperty(ref _currentCameraPara, value);
        }

        private ObservableLightModel _lightParaModel;
        public ObservableLightModel LightParaModel
        {
            get => _lightParaModel;
            set => SetProperty(ref _lightParaModel, value);
        }

        private ObservableEquipmentModel _equipmentModel;
        public ObservableEquipmentModel EquipmentModel
        {
            get => _equipmentModel;
            set
            {
                if (value == null) return;
                SetProperty(ref _equipmentModel, value);
            }
        }

        public List<EnumItem> CameraTypes { get => Common.EnumHelper.GetEnumList<CameraTypeEnum>(); }
        public List<EnumItem> ProcessTypes { get => Common.EnumHelper.GetEnumList<ProcessTypeEnum>(); }
        public List<EnumItem> TakePictureTypes { get => Common.EnumHelper.GetEnumList<PictureType>(); }
        public List<EnumItem> PictureLayoutTypes { get => Common.EnumHelper.GetEnumList<PictureLayoutEnum>(); }

        public Action CloseAction { get; set; }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            RefreshEquipment();
            //RefreshLightModelAsync();
            //RefreshCameraParamModelAsync();
            if (IsInsert)
            {
                LightParaModel = new ObservableLightModel();
                CurrentCameraParaModel = new ObservableMainCameraParaModel();
                EquipmentModel = new ObservableEquipmentModel();
            }
            else
            {
                GetEquipment(CurrentProcessModel.EquipmentParaId);
                //GetLightModelAsync(CurrentProcessModel.LightParamId);
                //GetCameraParamModelAsync(CurrentProcessModel.MainCameraId);
            }
        });

        private void GetEquipment(string id)
        {
            if(string.IsNullOrEmpty(id)) EquipmentModel = new ObservableEquipmentModel();
            EquipmentModel = ObservableEquipmentModels.Where(item=>item.Id == id).FirstOrDefault();
        }

        private void GetLightModelAsync(string id)
        {
            if (string.IsNullOrEmpty(id)) LightParaModel = new ObservableLightModel();
            LightParaModel = LightModels.Where(item=>item.Id == id).FirstOrDefault();
        }

        private void GetCameraParamModelAsync(string id)
        {
            if (string.IsNullOrEmpty(id)) CurrentCameraParaModel = new ObservableMainCameraParaModel();
            CurrentCameraParaModel = MainCameraParaModels.Where(item=>item.Id == id).FirstOrDefault();
        }
        private async void RefreshEquipment()
        {
            var temp = await _equipmentService.getAll();
            ObservableEquipmentModels = temp.MapTo<List<EquipmentModel>, ObservableCollection<ObservableEquipmentModel>>();
        }

        private async void RefreshLightModelAsync()
        {
            var temp = await _lightParamService.getAll();
            LightModels = temp.MapTo<List<LightParamModel>, ObservableCollection<ObservableLightModel>>();
        }

        private async void RefreshCameraParamModelAsync()
        {
            var temp = await _mainCameraParaService.getAll();
            MainCameraParaModels = temp.MapTo<List<MainCameraParamModel>, ObservableCollection<ObservableMainCameraParaModel>>();
        }

        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            Result = false;
            CloseAction?.Invoke();
        });

        public IRelayCommand SaveCommand => new RelayCommand(async () =>
        {
            var temp4 = CurrentProcessModel.MapTo<ObservableProcessModel, ProcessModel>();
            //var temp1 = CurrentCameraParaModel.MapTo<ObservableMainCameraParaModel,MainCameraParamModel>();
            if (EquipmentModel != null)
            {
                var temp2 = EquipmentModel.MapTo<ObservableEquipmentModel, EquipmentModel>();

                if (string.IsNullOrEmpty(temp2.ParamName))
                {
                    HandyControl.Controls.MessageBox.Error("设备参数名不能为空！");
                    return;
                }
                if (IsInsert)
                {
                    var t2 = await _equipmentService.getIsAny(item => item.ParamName == temp2.ParamName);
                    if (t2)
                    {
                        Result = await _equipmentService.Update(temp2);
                    }
                    else
                    {
                        temp2.Id = Guid.NewGuid().ToString();
                        temp2.CreateTime = DateTime.Now;
                        temp2.Operator = _globalState.LoginUser.Account;
                        Result = await _equipmentService.Add(temp2);
                    }

                }
                else
                    Result = await _equipmentService.Update(temp2);
                temp4.EquipmentParaId = temp2.Id;

            }
            if (IsInsert)
            {
                if (string.IsNullOrEmpty(temp4.ParamName))
                {
                    HandyControl.Controls.MessageBox.Error("步骤参数名不能为空！");
                    return;
                }
                SaveSucceEvent?.Invoke(temp4.MapTo<ProcessModel, ObservableProcessModel>());
            }
            else Result = await _processParaService.Update(temp4);
            if (Result)
            {
                CloseAction?.Invoke();
            }
            else
            {
                HandyControl.Controls.MessageBox.Error("步骤信息更新失败！");
            }
            
        });
        public IRelayCommand AddEquipmentCommand => new RelayCommand(async () =>
        {
            var result = await Dialog.Show<EquipmentCtrlDialog>()
             .Initialize<EquipmentCtrlDialogViewModel>(vm =>
             {
                 vm.Title = "新增设备参数";
                 vm.EquipmentModel = new ObservableEquipmentModel();
                 vm.IsInsert = true;
             })
             .GetResultAsync<bool>();
            if (result) RefreshEquipment();
        });

        public IRelayCommand AddLightCommand => new RelayCommand( () =>
        {
            
            //if (result) RefreshLightModelAsync();
        });

        public IRelayCommand<object> CameraChangedCommand => new RelayCommand<object>((args) =>
        {
            //SelectionChangedEventArgs
            //var temp = args.Source as HandyControl.Controls.ComboBox;
            //if(temp != null) {
        });

        public IRelayCommand AddNewCommand => new RelayCommand(async () =>
        {
            try
            {
                EquipmentModel.Id = Guid.NewGuid().ToString();
                var temp = EquipmentModel.MapTo<ObservableEquipmentModel, EquipmentModel>();
                await _equipmentService.Add(temp);
            }
            catch
            {
                HandyControl.Controls.MessageBox.Error("步骤信息更新失败！");
            }
            
        });

        public IRelayCommand AddCameraCommand => new RelayCommand(async () =>
        {
            var result = await Dialog.Show<CameraCtrlDialog>()
             .Initialize<CameraCtrlDialogViewModel>(vm =>
             {
                 vm.Title = "新增相机参数";
                 vm.IsInsert = true;
                 vm.CameraParamModel = new ObservableMainCameraParaModel();
             })
             .GetResultAsync<bool>();
            if (result) RefreshCameraParamModelAsync();
        });

        public IRelayCommand<RoutedEventArgs> CameraCtrlLoadCommand => new RelayCommand<RoutedEventArgs>((args) => {
            var temp = args.Source as UniformSpacingPanel;
            if (temp != null) {
                var child = temp.Children;
                if (child.Count > 0)
                {
                    foreach (var item in child)
                    {
                        var observable = item as IObserver;
                        if(observable != null) _mainCameraParaService.AddSource(observable);
                    }
                }
            }
            _mainCameraParaService.GetAllPropertyDesc();
            //if (IsInsert) _mainCameraParaService.GetAllProperty();

        });


        public IRelayCommand<string> ResetSingleCommand => new RelayCommand<string>((args) =>
        {
            switch (args)
            {
                case "XY":
                    _equipmentService.ReSetXY();
                    EquipmentModel.X = 0;
                    EquipmentModel.Y = 0;
                    break;
                case "Z":
                    _equipmentService.ReSetZ();
                    EquipmentModel.Z = 0;
                    break;
                case "O":
                    _equipmentService.ReSetO();
                    EquipmentModel.O = 0;
                    break;
                case "R":
                    _equipmentService.ReSetR();
                    EquipmentModel.R = 0;
                    break;
                case "T":
                    _equipmentService.ReSetT();
                    EquipmentModel.T = 0;
                    break;
            }
        });

        public IRelayCommand<string> MoveCommand => new RelayCommand<string>((args) =>
        {
            MoveEvent(args);
        });

        private void MoveEvent(string args)
        {
            switch (args)
            {
                case "XY":
                    //_equipmentService.SetXY(EquipmentModel.X, EquipmentModel.Y);

                    _equipmentService.WriteXYArray(new float[] { EquipmentModel.X }, new float[] { EquipmentModel.Y }, 1);
                    break;
                case "Z":
                    _equipmentService.SetZAsync(EquipmentModel.Z);
                    break;
                case "O":
                    _equipmentService.SetO(EquipmentModel.O);
                    break;
                case "R":
                    _equipmentService.SetR(EquipmentModel.R);
                    break;
                case "T":
                    _equipmentService.SetT(EquipmentModel.T);
                    break;
            }
        }

        public IRelayCommand ResetErrorCommand => new RelayCommand(() =>
        {
            _equipmentService.ResetError();
        });

        public IRelayCommand RestAllCommand => new RelayCommand(() =>
        {
            _equipmentService.ResetAll();
        });

        public IRelayCommand<KeyEventArgs> XYKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("XY");
            }
        });
        public IRelayCommand<KeyEventArgs> ZKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("Z");
            }
        });
        public IRelayCommand<KeyEventArgs> RKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("R");
            }
        });
        public IRelayCommand<KeyEventArgs> TKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("T");
            }
        });
        public IRelayCommand<KeyEventArgs> OKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("O");
            }
        });

        public IRelayCommand<string> LeftCommand => new RelayCommand<string>((e) => {

            switch (e)
            {
                case "X":
                    var tempX = EquipmentModel.X - StepNumber;
                    if (tempX >= -430)
                    {
                        EquipmentModel.X = tempX;
                    }
                    else
                        EquipmentModel.X = -430;
                    MoveEvent("XY");
                    break;
                case "Y":
                    var tempY = EquipmentModel.Y - StepNumber;
                    if (tempY >= -460)
                    {
                        EquipmentModel.Y = tempY;
                    }
                    else
                        EquipmentModel.Y = -460;
                    MoveEvent("XY");
                    break;
                case "Z":
                    var tempZ = EquipmentModel.Z - StepNumber;
                    if (tempZ >= -70)
                    {
                        EquipmentModel.Z = tempZ;
                    }
                    else
                        EquipmentModel.Z = -70;
                    MoveEvent("Z");
                    break;
                case "R":
                    var tempR = EquipmentModel.R - StepNumber;
                    if (tempR >= 15)
                    {
                        EquipmentModel.R = tempR;
                    }
                    else
                        EquipmentModel.R = 15;
                    MoveEvent("R");
                    break;
                case "T":
                    var tempT = EquipmentModel.T - StepNumber;
                    if (tempT >= 0)
                    {
                        EquipmentModel.T = tempT;
                    }
                    else
                        EquipmentModel.T = 0;
                    MoveEvent("T");
                    break;
            }
            
        
        });


        public IRelayCommand<string> RightCommand => new RelayCommand<string>((e) => {
            switch (e)
            {
                case "X":
                    var tempX = EquipmentModel.X + StepNumber;
                    if (tempX <= 270)
                    {
                        EquipmentModel.X = tempX;
                    }else
                        EquipmentModel.X = 270;
                    MoveEvent("XY");
                    break;
                case "Y":
                    var tempY = EquipmentModel.Y + StepNumber;
                    if (tempY <= 150)
                    {
                        EquipmentModel.Y = tempY;

                    }
                    else
                    {
                        EquipmentModel.Y = 150;
                    }
                    MoveEvent("XY");
                    break;
                case "Z":
                    var tempZ = EquipmentModel.Z + StepNumber;
                    if (tempZ <= 10)
                    {
                        EquipmentModel.Z = tempZ;

                    }
                    else
                        EquipmentModel.Z = 10;
                    MoveEvent("Z");
                    break;
                case "R":
                    var tempR = EquipmentModel.R + StepNumber;
                    if (tempR <= 50)
                    {
                        EquipmentModel.R = tempR;
                    }
                    else
                        EquipmentModel.R = 50;
                    MoveEvent("R");
                    break;
                case "T":
                    var tempT = EquipmentModel.T + StepNumber;
                    if (tempT <= 360)
                    {
                        EquipmentModel.T = tempT;

                    }
                    else
                    {
                        EquipmentModel.T = 360;
                    }

                    MoveEvent("T");
                    break;
            }

        });

        public IRelayCommand PositionCommand => new RelayCommand(() =>
        {
            MoveEvent("T");
            MoveEvent("XY");
            MoveEvent("Z");
            MoveEvent("O");
            
        });

        public IRelayCommand ReadPositionCommand => new RelayCommand(() =>
        {
            EquipmentModel.X = _equipmentService.ReadX();
            EquipmentModel.Y = _equipmentService.ReadY();
            EquipmentModel.R = _equipmentService.ReadR();
            EquipmentModel.Z = _equipmentService.ReadZ();
            EquipmentModel.T = _equipmentService.ReadT();
        });
    }
}
