﻿using IPM.Vision.Model;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.BLL
{
    public class APIServices
    {
        private readonly HttpClient _client;
        private const string BaseUrl = "http://localhost:5000"; // 基础URL，根据实际情况替换

        public APIServices()
        {
            _client = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(10) // 设置超时时间为10分钟
            };
        }

        private async Task<T> PostAsync<T>(string url, Dictionary<string, string> data)
        {
            try
            {
                var content = new FormUrlEncodedContent(data);
                HttpResponseMessage response = await _client.PostAsync(BaseUrl + url, content);
                
                // 获取响应内容用于错误诊断
                string responseContent = await response.Content.ReadAsStringAsync();
                
                if (!response.IsSuccessStatusCode)
                {
                    // 提供更详细的错误信息
                    throw new Exception($"请求失败: 响应状态码不指示成功: {(int)response.StatusCode} ({response.StatusCode}). " +
                                      $"请求URL: {BaseUrl + url}. " +
                                      $"响应内容: {responseContent}");
                }

                return JsonConvert.DeserializeObject<T>(responseContent);
            }
            catch (HttpRequestException httpEx)
            {
                throw new Exception($"网络请求异常: {httpEx.Message}. 请检查AI服务是否正在运行在 {BaseUrl}");
            }
            catch (TaskCanceledException tcEx)
            {
                throw new Exception($"请求超时: {tcEx.Message}. AI服务响应时间过长");
            }
            catch (JsonException jsonEx)
            {
                throw new Exception($"JSON解析异常: {jsonEx.Message}. AI服务返回的数据格式不正确");
            }
            catch (Exception ex)
            {
                throw new Exception($"请求失败: {ex.Message}");
            }
        }

        // 二维码识别接口
        public async Task<QrDetectResponse> QrDetectAsync(string imagePath)
        {

            var resuestData = new Dictionary<string, string>()
            {
                {"Event_Id",Guid.NewGuid().ToString() },
                {"Event_Name", "service.event.qrDetect"},
                {"image_path",imagePath }
            };

            return await PostAsync<QrDetectResponse>("/qrDetect", resuestData);
        }

        /// <summary>
        ///截图
        /// </summary>
        /// <param name="inpath"></param>
        /// <param name="outpath"></param>
        /// <returns></returns>
        public async Task<CropImageModel> CropImageAsync(string inpath,string outpath)
        {

            var resuestData = new Dictionary<string, string>()
            {
                {"Event_Id",Guid.NewGuid().ToString() },
                {"Event_Name", "service.event.cropImage"},
                {"image_path",inpath },
                {"output_path",outpath }
            };

            return await PostAsync<CropImageModel>("/cropImage", resuestData);
        }

        // 原点识别
        public async Task<MarkModel> FetchMarkPoint(string imagePath)
        {

            var resuestData = new Dictionary<string, string>()
            {
                {"Event_Id",Guid.NewGuid().ToString() },
                {"Event_Name", "service.event.markDetection"},
                {"image_path",imagePath }
            };

            return await PostAsync<MarkModel>("/markDetection", resuestData);
        }
    }
}
