﻿using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class PropertyComboBox : ComboBox
    {
        private CameraController _controller;
        private ActionSource _actionSource;
        public void SetActionSource(ActionSource actionSource) { _actionSource = actionSource; }
        public ActionSource ActionSource { get => _actionSource; }
        public Dictionary<uint, string> items;
        private delegate void _UpdateProperty(uint value);
        private delegate void _UpdatePropertyDesc(ref EDSDK.EdsPropertyDesc desc);

        public CameraController CameraController { get => _controller; }

        public void SetCameraController(CameraController controller)
        {
            if (controller == null) return;
            _controller = controller;
        }

        protected void UpdateProperty(uint value)
        {
            if (!this.Dispatcher.CheckAccess())
            {
                this.Dispatcher.Invoke(new _UpdateProperty(UpdateProperty), value);
                return;
            }


        }

        protected void UpdatePropertyDesc(ref EDSDK.EdsPropertyDesc desc)
        {
            if (!this.Dispatcher.CheckAccess())
            {
                this.Dispatcher.Invoke(new _UpdatePropertyDesc(UpdatePropertyDesc), desc);
                return;
            }
            this.IsEnabled = (desc.NumElements != 0);
            Dictionary<uint, string> result = new Dictionary<uint, string>();
            if (desc.PropDesc == null) return;
            foreach (var item in desc.PropDesc)
            {
                if (items.ContainsKey((uint)item) && !result.ContainsKey((uint)item)) result.Add((uint)item, items[(uint)item]);
            }
            this.ItemsSource = result;
        }

    }
}
