﻿using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.ViewModel.Pages;
using Microsoft.Extensions.Options;
using Opc.Ua;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.BLL
{
    public class ReportService : BaseService<ReportModel>, IReportService
    {
        public ReportService(IBaseRepository<ReportModel> baseRepository) : base(baseRepository)
        {
        }


        public async Task<ObservableCollection<ObservableReportModel>> TopData(int number)
        {
            var result = await getTakeList(number, true, item => item.CreateTime, SqlSugar.OrderByType.Desc);
            return result.MapTo<List<ReportModel>, ObservableCollection<ObservableReportModel>>();
        }

        public async Task<int> CountAsync(ReportSearchModel options)
        {
            var other = await getByWhere(item => item.OrderNumber.Contains(options.OrderNumber) 
            && item.SerialNumber.Contains(options.SerialNumber) 
            && item.ProductNumber.Contains(options.ProductNumber)
            && item.CreateTime >= options.BeginTime 
            && item.CreateTime <= options.EndTime);
            return other.Count();
        }

        public async Task<ObservableCollection<ObservableReportModel>> GetDataByPage(ReportSearchModel model)
        {
            var temp = await getPageList<ReportModel>(model.PageIndex, model.PageSize,true,
                        item => item.OrderNumber.Contains(model.OrderNumber) 
                        && item.SerialNumber.Contains(model.SerialNumber)
                        && item.ProductNumber.Contains(model.ProductNumber)
                        && item.CreateTime >= model.BeginTime && item.CreateTime <= model.EndTime,
                        true,
                        item => item.CreateTime,SqlSugar.OrderByType.Desc);
            return temp.MapTo<List<ReportModel>,ObservableCollection<ObservableReportModel>>();
        }

        /// <summary>
        /// 获取复判数据（查询不合格的数据，包括已复判和未复判的）
        /// </summary>
        public async Task<ObservableCollection<ObservableReportModel>> GetManualReviewData(ManualReviewSearchModel model)
        {
            var temp = await getPageList<ReportModel>(model.PageIndex, model.PageSize, true,
                        item => item.OrderNumber.Contains(model.OrderNumber)
                        && item.SerialNumber.Contains(model.SerialNumber)
                        && item.ProductNumber.Contains(model.ProductNumber)
                        && (item.Status == "FAIL" || item.Status == "NACK" || item.Status == "NG" || item.Status == "WAITING"),  // 不合格或待检测的数据
                        // **修复：移除未复判的限制，显示所有不合格的数据（包括已复判和未复判的）**
                        true,
                        item => item.CreateTime, SqlSugar.OrderByType.Desc);
            return temp.MapTo<List<ReportModel>, ObservableCollection<ObservableReportModel>>();
        }

        /// <summary>
        /// 获取复判数据的总数（包括已复判和未复判的）
        /// </summary>
        public async Task<int> CountManualReviewAsync(ManualReviewSearchModel options)
        {
            var other = await getByWhere(item => item.OrderNumber.Contains(options.OrderNumber)
                        && item.SerialNumber.Contains(options.SerialNumber)
                        && item.ProductNumber.Contains(options.ProductNumber)
                        && (item.Status == "FAIL" || item.Status == "NACK" || item.Status == "NG" || item.Status == "WAITING"));  // 不合格或待检测的数据
                        // **修复：移除未复判的限制，统计所有不合格的数据**
            return other.Count();
        }

        /// <summary>
        /// 更新复判结果
        /// </summary>
        public async Task<bool> UpdateManualReviewResult(long reportId, string result, string remark, string operatorName)
        {
            try
            {
                var report = await getFirstOrDefault(x => x.Id == reportId);
                if (report != null)
                {
                    report.ManualReviewResult = result;
                    report.ManualReviewTime = DateTime.Now;
                    report.ManualReviewOperator = operatorName;
                    report.ManualReviewRemark = remark;
                    await Update(report);
                    return true;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
