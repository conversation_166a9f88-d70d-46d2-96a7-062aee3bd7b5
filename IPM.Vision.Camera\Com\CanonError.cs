﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.Com
{
    public static class CanonError
    {
        public static string getType(uint code)
        {
            switch (code)
            {
                case (uint)ErrorType.EDS_ERR_DEVICE_BUSY:
                    return ErrorType.EDS_ERR_DEVICE_BUSY.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_CF_GATE_CHANGED:
                    return ErrorType.EDS_ERR_DEVICE_CF_GATE_CHANGED.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_DIAL_CHANGED:
                    return ErrorType.EDS_ERR_DEVICE_DIAL_CHANGED.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_DISK_ERROR:
                    return ErrorType.EDS_ERR_DEVICE_DISK_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_EMERGENCY:
                    return ErrorType.EDS_ERR_DEVICE_EMERGENCY.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_INTERNAL_ERROR:
                    return ErrorType.EDS_ERR_DEVICE_INTERNAL_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_INVALID:
                    return ErrorType.EDS_ERR_DEVICE_INVALID.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_INVALID_PARAMETER:
                    return ErrorType.EDS_ERR_DEVICE_INVALID_PARAMETER.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_MEMORY_FULL:
                    return ErrorType.EDS_ERR_DEVICE_MEMORY_FULL.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_NOT_FOUND:
                    return ErrorType.EDS_ERR_DEVICE_NOT_FOUND.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_NOT_INSTALLED:
                    return ErrorType.EDS_ERR_DEVICE_NOT_INSTALLED.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_NOT_RELEASED:
                    return ErrorType.EDS_ERR_DEVICE_NOT_RELEASED.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_NO_DISK:
                    return ErrorType.EDS_ERR_DEVICE_NO_DISK.GetDescription();
                case (uint)ErrorType.EDS_ERR_DEVICE_STAY_AWAKE:
                    return ErrorType.EDS_ERR_DEVICE_STAY_AWAKE.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_ALREADY_EXISTS:
                    return ErrorType.EDS_ERR_FILE_ALREADY_EXISTS.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_CLOSE_ERROR:
                    return ErrorType.EDS_ERR_FILE_CLOSE_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_DATA_CORRUPT:
                    return ErrorType.EDS_ERR_FILE_DATA_CORRUPT.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_DISK_FULL_ERROR:
                    return ErrorType.EDS_ERR_FILE_DISK_FULL_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_FORMAT_UNRECOGNIZED:
                    return ErrorType.EDS_ERR_FILE_FORMAT_UNRECOGNIZED.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_IO_ERROR:
                    return ErrorType.EDS_ERR_FILE_IO_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_NAMING_NA:
                    return ErrorType.EDS_ERR_FILE_NAMING_NA.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_NOT_FOUND:
                    return ErrorType.EDS_ERR_FILE_NOT_FOUND.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_OPEN_ERROR:
                    return ErrorType.EDS_ERR_FILE_OPEN_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_PERMISSION_ERROR:
                    return ErrorType.EDS_ERR_FILE_PERMISSION_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_READ_ERROR:
                    return ErrorType.EDS_ERR_FILE_READ_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_SEEK_ERROR:
                    return ErrorType.EDS_ERR_FILE_SEEK_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_TELL_ERROR:
                    return ErrorType.EDS_ERR_FILE_TELL_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_TOO_MANY_OPEN:
                    return ErrorType.EDS_ERR_FILE_TOO_MANY_OPEN.GetDescription();
                case (uint)ErrorType.EDS_ERR_FILE_WRITE_ERROR:
                    return ErrorType.EDS_ERR_FILE_WRITE_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_INCOMPATIBLE_VERSION:
                    return ErrorType.EDS_ERR_INCOMPATIBLE_VERSION.GetDescription();
                case (uint)ErrorType.EDS_ERR_INTERNAL_ERROR:
                    return ErrorType.EDS_ERR_INTERNAL_ERROR.GetDescription();
                case (uint)ErrorType.EDS_ERR_LAST_GENERIC_ERROR_PLUS_ONE:
                    return ErrorType.EDS_ERR_LAST_GENERIC_ERROR_PLUS_ONE.GetDescription();
                case (uint)ErrorType.EDS_ERR_MEM_ALLOC_FAILED:
                    return ErrorType.EDS_ERR_MEM_ALLOC_FAILED.GetDescription();
                case (uint)ErrorType.EDS_ERR_MEM_FREE_FAILED:
                    return ErrorType.EDS_ERR_MEM_FREE_FAILED.GetDescription();
                case (uint)ErrorType.EDS_ERR_MISSING_SUBCOMPONENT:
                    return ErrorType.EDS_ERR_MISSING_SUBCOMPONENT.GetDescription();
                case (uint)ErrorType.EDS_ERR_NOT_SUPPORTED:
                    return ErrorType.EDS_ERR_NOT_SUPPORTED.GetDescription();
                case (uint)ErrorType.EDS_ERR_OPERATION_CANCELLED:
                    return ErrorType.EDS_ERR_OPERATION_CANCELLED.GetDescription();
                //case (uint)ErrorType.EDS_ERR_PROTECTION_VIOLATION:
                //    return ErrorType.EDS_ERR_PROTECTION_VIOLATION.GetDescription();
                case (uint)ErrorType.EDS_ERR_SELECTION_UNAVAILABLE:
                    return ErrorType.EDS_ERR_SELECTION_UNAVAILABLE.GetDescription();
                case (uint)ErrorType.EDS_ERR_TAKE_PICTURE_AF_NG:
                    return ErrorType.EDS_ERR_TAKE_PICTURE_AF_NG.GetDescription();
                case (uint)ErrorType.EDS_ERR_TAKE_PICTURE_CARD_NG:
                    return ErrorType.EDS_ERR_TAKE_PICTURE_CARD_NG.GetDescription();
                case (uint)ErrorType.EDS_ERR_TAKE_PICTURE_CARD_PROTECT_NG:
                    return ErrorType.EDS_ERR_TAKE_PICTURE_CARD_PROTECT_NG.GetDescription();
                case (uint)ErrorType.EDS_ERR_TAKE_PICTURE_MIRROR_UP_NG:
                    return ErrorType.EDS_ERR_TAKE_PICTURE_MIRROR_UP_NG.GetDescription();
                case (uint)ErrorType.EDS_ERR_TAKE_PICTURE_NO_CARD_NG:
                    return ErrorType.EDS_ERR_TAKE_PICTURE_NO_CARD_NG.GetDescription();
                case (uint)ErrorType.EDS_ERR_TAKE_PICTURE_RESERVED:
                    return ErrorType.EDS_ERR_TAKE_PICTURE_RESERVED.GetDescription();
                case (uint)ErrorType.EDS_ERR_TAKE_PICTURE_SENSOR_CLEANING_NG:
                    return ErrorType.EDS_ERR_TAKE_PICTURE_SENSOR_CLEANING_NG.GetDescription();
                case (uint)ErrorType.EDS_ERR_TAKE_PICTURE_SILENCE_NG:
                    return ErrorType.EDS_ERR_TAKE_PICTURE_SILENCE_NG.GetDescription();
                case (uint)ErrorType.EDS_ERR_UNEXPECTED_EXCEPTION:
                    return ErrorType.EDS_ERR_UNEXPECTED_EXCEPTION.GetDescription();
                case (uint)ErrorType.EDS_ERR_UNIMPLEMENTED:
                    return ErrorType.EDS_ERR_UNIMPLEMENTED.GetDescription();
                default:
                    return string.Empty;
            }
        }
    }

    public enum ErrorType
    {
        [Description("未实现")]
        EDS_ERR_UNIMPLEMENTED = 1,
        [Description("内部错误")]
        EDS_ERR_INTERNAL_ERROR = 2,
        [Description("内部分配错误")]
        EDS_ERR_MEM_ALLOC_FAILED = 3,
        [Description("内存释放错误")]
        EDS_ERR_MEM_FREE_FAILED = 4,
        [Description("操作取消")]
        EDS_ERR_OPERATION_CANCELLED = 5,
        [Description("版本错误")]
        EDS_ERR_INCOMPATIBLE_VERSION = 6,
        [Description("不支持")]
        EDS_ERR_NOT_SUPPORTED = 7,
        [Description("意想不到的异常")]
        EDS_ERR_UNEXPECTED_EXCEPTION = 8,
        [Description("失踪的子组件")]
        EDS_ERR_MISSING_SUBCOMPONENT = 10,
        [Description("选择不可用")]
        EDS_ERR_SELECTION_UNAVAILABLE = 11,
        [Description("IO错误")]
        EDS_ERR_FILE_IO_ERROR = 32,
        [Description("打开文件过多")]
        EDS_ERR_FILE_TOO_MANY_OPEN = 33,
        [Description("文件不存在")]
        EDS_ERR_FILE_NOT_FOUND = 34,
        [Description("打开错误")]
        EDS_ERR_FILE_OPEN_ERROR = 35,
        [Description("关闭错误")]
        EDS_ERR_FILE_CLOSE_ERROR = 36,
        [Description("错误寻找")]
        EDS_ERR_FILE_SEEK_ERROR = 37,
        [Description("错误显示")]
        EDS_ERR_FILE_TELL_ERROR = 38,
        [Description("错误读取")]
        EDS_ERR_FILE_READ_ERROR = 39,
        [Description("错误写入")]
        EDS_ERR_FILE_WRITE_ERROR = 40,
        [Description("权限错误")]
        EDS_ERR_FILE_PERMISSION_ERROR = 41,
        [Description("磁盘已满")]
        EDS_ERR_FILE_DISK_FULL_ERROR = 42,
        [Description("文件已存在")]
        EDS_ERR_FILE_ALREADY_EXISTS = 43,
        [Description("格式错误")]
        EDS_ERR_FILE_FORMAT_UNRECOGNIZED = 44,
        [Description("数据无效")]
        EDS_ERR_FILE_DATA_CORRUPT = 45,
        [Description("文件命名错误")]
        EDS_ERR_FILE_NAMING_NA = 46,
        [Description("未找到相机")]
        EDS_ERR_DEVICE_NOT_FOUND = 128,
        [Description("相机忙碌")]
        EDS_ERR_DEVICE_BUSY = 129,
        [Description("相机错误")]
        EDS_ERR_DEVICE_INVALID = 130,
        [Description("DEVICE_EMERGENCY")]
        EDS_ERR_DEVICE_EMERGENCY = 131,
        [Description("内存已满")]
        EDS_ERR_DEVICE_MEMORY_FULL = 132,
        [Description("内部错误")]
        EDS_ERR_DEVICE_INTERNAL_ERROR = 133,
        [Description("参数错误")]
        EDS_ERR_DEVICE_INVALID_PARAMETER = 134,
        [Description("没有磁盘")]
        EDS_ERR_DEVICE_NO_DISK = 135,
        [Description("磁盘错误")]
        EDS_ERR_DEVICE_DISK_ERROR = 136,
        [Description("CFGATE已改变")]
        EDS_ERR_DEVICE_CF_GATE_CHANGED = 137,
        [Description("仪表盘已改变")]
        EDS_ERR_DEVICE_DIAL_CHANGED = 138,
        [Description("设备未安装")]
        EDS_ERR_DEVICE_NOT_INSTALLED = 139,
        [Description("设备处于唤醒模式")]
        EDS_ERR_DEVICE_STAY_AWAKE = 140,
        [Description("设备未释放")]
        EDS_ERR_DEVICE_NOT_RELEASED = 141,
        [Description("对焦失败")]
        EDS_ERR_TAKE_PICTURE_AF_NG = 36097,
        [Description("照片保存失败")]
        EDS_ERR_TAKE_PICTURE_RESERVED = 36098,
        [Description("反射失败")]
        EDS_ERR_TAKE_PICTURE_MIRROR_UP_NG = 36099,
        [Description("传感器刷新失败")]
        EDS_ERR_TAKE_PICTURE_SENSOR_CLEANING_NG = 36100,
        [Description("当前正在执行静默操作")]
        EDS_ERR_TAKE_PICTURE_SILENCE_NG = 36101,
        [Description("内存卡不存在")]
        EDS_ERR_TAKE_PICTURE_NO_CARD_NG = 36102,
        [Description("写卡失败")]
        EDS_ERR_TAKE_PICTURE_CARD_NG = 36103,
        [Description("内存卡处于保护状态")]
        EDS_ERR_TAKE_PICTURE_CARD_PROTECT_NG = 36104,
        [Description("文件命名错误")]
        EDS_ERR_LAST_GENERIC_ERROR_PLUS_ONE = 245
    }
}
