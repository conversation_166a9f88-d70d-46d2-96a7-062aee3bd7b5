﻿<Page x:Class="IPM.Vision.Views.Pages.ReportPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:IPM.Vision.Views.Pages"
      DataContext="{Binding ReportPageViewModel,Source={StaticResource Locator}}"
      mc:Ignorable="d"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      d:DesignHeight="450" d:DesignWidth="1200">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10" Grid.Row="0">
                <hc:TextBox
                    hc:InfoElement.TitlePlacement="Left"
                    hc:InfoElement.Title="作业计划号"
                    FontSize="14"
                    Text="{Binding ReportSearchModel.OrderNumber}"
                    Height="35"
                    Width="280" />
                <hc:TextBox
                    hc:InfoElement.TitlePlacement="Left"
                    hc:InfoElement.Title="序列号"
                    FontSize="14"
                    Text="{Binding ReportSearchModel.SerialNumber}"
                    Height="35"
                    Width="280" />
                <hc:TextBox
                    hc:InfoElement.TitlePlacement="Left"
                    hc:InfoElement.Title="产品编号"
                    FontSize="14"
                    Text="{Binding ReportSearchModel.ProductNumber}"
                    Height="35"
                    Width="280" />
                <hc:DatePicker
                    hc:InfoElement.ShowClearButton="True"
                    Width="250"
                    FontSize="14"
                    Height="35"
                    SelectedDate="{Binding ReportSearchModel.BeginTime}"
                    hc:InfoElement.TitlePlacement="Left"
                    hc:InfoElement.Title="开始时间"
                    Style="{StaticResource DatePickerExtend}"/>
                <hc:DatePicker
                    hc:InfoElement.ShowClearButton="True"
                    Width="250"
                    FontSize="14"
                    Height="35"
                    SelectedDate="{Binding ReportSearchModel.EndTime}"
                    hc:InfoElement.TitlePlacement="Left"
                    hc:InfoElement.Title="结束时间"
                    Style="{StaticResource DatePickerExtend}"/>
                <Button Style="{StaticResource ButtonPrimary}" Content="查询" Height="35" Width="120" FontSize="16" Command="{Binding SearchCommand}"/>
                <Button Style="{StaticResource ButtonWarning}" Content="重置" Height="35" Width="120" FontSize="16" Command="{Binding ResetCommand}"/>
            </hc:UniformSpacingPanel>

            <hc:TabControl Grid.Row="1" Style="{StaticResource TabControlBaseStyle}">
                <hc:TabItem Header="表格">
                    <DataGrid
                        Style="{StaticResource DataGridBaseStyle}"
                        AutoGenerateColumns="False"
                        hc:DataGridAttach.ShowSelectAllButton="True"
                        ItemsSource="{Binding DataList}"
                        RowHeaderWidth="60"
                        HeadersVisibility="All"
                        hc:DataGridAttach.CanUnselectAllWithBlankArea="True"
                        hc:DataGridAttach.ShowRowNumber="True"
                        hc:DataGridAttach.ApplyDefaultStyle="True"
                        RowDetailsVisibilityMode="Collapsed"
                        CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                        ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Binding="{Binding OrderNumber}"
                                Header="作业计划号"
                                CanUserSort="False"
                                Width="*"
                                IsReadOnly="True"/>
                            <DataGridTextColumn
                                Binding="{Binding SerialNumber}"
                                Header="产品编号"
                                CanUserSort="False"
                                Width="*"
                                IsReadOnly="True"/>
                            <DataGridTextColumn
                                Binding="{Binding ProductNumber}"
                                Header="产品代号"
                                CanUserSort="False"
                                Width="*"
                                IsReadOnly="True"/>
                            <DataGridTextColumn
                                Binding="{Binding ProcessName}"
                                Header="步骤名称"
                                CanUserSort="False"
                                Width="*"
                                IsReadOnly="True"/>
                            <DataGridTextColumn
                                Binding="{Binding WorkStation}"
                                Header="工位"
                                Width="*"
                                CanUserSort="False"
                                IsReadOnly="True"/>
                            <DataGridTextColumn
                                Binding="{Binding Status, Converter={StaticResource StatusTextConvert}}"
                                Header="检测结果"
                                Width="*"
                                CanUserSort="False"
                                IsReadOnly="True"/>
                            <DataGridTextColumn
                                Binding="{Binding PictureName}"
                                Header="照片名称"
                                CanUserSort="False"
                                Width="*"
                                IsReadOnly="True"/>
                            <DataGridTemplateColumn
                                Header="照片地址"
                                CanUserSort="False"
                                Width="220"
                                IsReadOnly="True">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button
                                            Command="{Binding DataContext.OpenDirectoryCommand, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                            CommandParameter="{Binding PicturePath}"
                                            Content="{Binding PicturePath}"
                                            Cursor="Hand"
                                            Style="{StaticResource ButtonCustom}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn
                                Header="检测照片地址"
                                CanUserSort="False"
                                Width="220"
                                IsReadOnly="True">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button
                                            Command="{Binding DataContext.OpenDirectoryCommand, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                            CommandParameter="{Binding CheckPath}"
                                            Content="{Binding CheckPath}"
                                            Cursor="Hand"
                                            Style="{StaticResource ButtonCustom}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn
                                Binding="{Binding CreateTime, StringFormat=\{0:yyyy-MM-dd HH:mm:ss\}}"
                                Header="创建时间"
                                CanUserSort="False"
                                Width="*"
                                IsReadOnly="True"/>
                            <DataGridTextColumn
                                Binding="{Binding OperatorName}"
                                Header="操作员"
                                CanUserSort="False"
                                Width="*"
                                IsReadOnly="True"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </hc:TabItem>

                <hc:TabItem Header="缩略图">
                    <Border Grid.Row="1" Margin="2" x:Name="picture_container">
                        <ListBox
                            Width="{Binding ElementName=picture_container, Path=ActualWidth}"
                            ItemsSource="{Binding DataList}"
                            ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                            ScrollViewer.VerticalScrollBarVisibility="Auto"
                            SelectedItem="{Binding CurrentSelect,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource LListBoxStyle}">
                            <ListBox.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Command="{Binding PreviewCommand}" Header="查看图片" CommandParameter="{Binding}"/>
                                    <MenuItem Command="{Binding OpenPathCommand}" Header="打开路径"  CommandParameter="{Binding}"/>
                                </ContextMenu>
                            </ListBox.ContextMenu>
                            <ListBox.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <hc:UniformSpacingPanel
                                        Width="{Binding ElementName=picture_container, Path=ActualWidth}"
                                        Orientation="Horizontal"
                                        Spacing="10" ChildWrapping="Wrap"/>
                                </ItemsPanelTemplate>
                            </ListBox.ItemsPanel>
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <Grid Margin="0 15 0 15">
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="*" />
                                            <RowDefinition Height="Auto" />
                                        </Grid.RowDefinitions>
                                        <Image
                                            Grid.Row="0"
                                            Width="220"
                                            Height="180"
                                            Source="{Binding PicturePath,IsAsync=True,Converter={StaticResource UriToBitmapConverter}}"
                                            Stretch="Fill" />
                                        <hc:UniformSpacingPanel
                                            Grid.Row="1"
                                            Margin="0,10,0,0"
                                            Orientation="Vertical"
                                            HorizontalAlignment="Left"
                                            Spacing="5">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="65"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="工单号:" Grid.Column="0" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding OrderNumber,Converter={StaticResource NullToStringConvert},ConverterParameter=未录入}" Grid.Column="1"/>
                                            </Grid>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="65"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="序列号:" Grid.Column="0" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding SerialNumber,Converter={StaticResource NullToStringConvert},ConverterParameter=未录入}" Grid.Column="1"/>
                                            </Grid>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="65"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="产品编号:" Grid.Column="0" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding ProductNumber,Converter={StaticResource NullToStringConvert},ConverterParameter=未录入}" Grid.Column="1"/>
                                            </Grid>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="65"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="照片名称:" Grid.Column="0" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding PictureName,Converter={StaticResource NullToStringConvert},ConverterParameter=未录入}" Grid.Column="1"/>
                                            </Grid>
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="65"/>
                                                    <ColumnDefinition Width="*"/>
                                                </Grid.ColumnDefinitions>
                                                <TextBlock Text="拍摄时间:" Grid.Column="0" FontWeight="Bold"/>
                                                <TextBlock Text="{Binding CreateTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" Grid.Column="1"/>
                                            </Grid>
                                        </hc:UniformSpacingPanel>
                                    </Grid>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>
                    </Border>
                </hc:TabItem>
            </hc:TabControl>

            <Border Grid.Row="2" Margin="0 10 10 5">
                <hc:Pagination
                    HorizontalAlignment="Right"
                    DataCountPerPage="{Binding ReportSearchModel.PageSize}"
                    MaxPageCount="{Binding ReportSearchModel.PageCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                    PageIndex="{Binding ReportSearchModel.PageIndex}">
                    <hc:Interaction.Triggers>
                        <hc:EventTrigger EventName="PageUpdated">
                            <hc:EventToCommand Command="{Binding PageChangedCommand}" PassEventArgsToCommand="True" />
                        </hc:EventTrigger>
                    </hc:Interaction.Triggers>
                </hc:Pagination>
            </Border>
            <Border Grid.Row="1">
                <TextBlock
                    FontSize="22"
                    Visibility="{Binding DataList.Count,Converter={StaticResource NullToVisibilityConverter}}"
                    Text="暂未查询到数据"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"/>
            </Border>




        </Grid>
    </Border>
</Page>
