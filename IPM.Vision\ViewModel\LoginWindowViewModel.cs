﻿using CommunityToolkit.Mvvm.Input;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;

namespace IPM.Vision.ViewModel
{
    public class LoginWindowViewModel:ViewModelBase, IDisposable
    {
        private readonly LoginService _loginService;
        private readonly NLogHelper _logger;
        private readonly ObservableGlobalState _globalState;
        private ObservableConfigModel _obConfig;
        private ObservableUserModel _loginUser;
        private readonly MainCameraParaService _mainCameraParaService;
        private readonly HKVisionService _hkVisionService;
        private string _message = string.Empty;
        private bool _isLoaded = true;
        
        // 单实例控制
        private static Mutex _mutex = null;
        private const string MutexName = "IPM.Vision.SingleInstance.Mutex";
        private bool _isFirstInstance = false;

        /// <summary>
        /// 静态方法：获取当前互斥量状态
        /// </summary>
        public static bool HasMutex => _mutex != null;

        /// <summary>
        /// 静态方法：全局释放互斥量
        /// </summary>
        public static void GlobalReleaseMutex()
        {
            try
            {
                if (_mutex != null)
                {
                    _mutex.ReleaseMutex();
                    _mutex.Dispose();
                    _mutex = null;
                }
            }
            catch (Exception)
            {
                // 静默处理异常，避免在程序关闭时出现问题
            }
        }

        public LoginWindowViewModel(LoginService loginService,
            NLogHelper logger,
            ObservableGlobalState globalState,
            HKVisionService hKVisionService,
            IMainCameraParaService mainCameraParaService)
        {
            _loginService = loginService;
            _logger = logger;
            _globalState = globalState;
            _mainCameraParaService = (MainCameraParaService)mainCameraParaService;
            _hkVisionService = hKVisionService;
        }

        public ObservableConfigModel ObConfig { get => _obConfig; set => SetProperty(ref _obConfig, value); }
        public ObservableUserModel LoginUser
        {
            get => _loginUser;
            set => SetProperty(ref _loginUser, value);
        }
        public string Message { get => _message; set => SetProperty(ref _message, value); }
        public bool IsLoaded { get => _isLoaded; set => SetProperty(ref _isLoaded, value); }

        /// <summary>
        ///  界面加载
        /// </summary>
        public IRelayCommand LoadedCommand => new RelayCommand(() =>
        {
            // 首先检查是否为第一个实例
            if (!CheckSingleInstance())
            {
                Application.Current.Dispatcher.Invoke(() => {
                    HandyControl.Controls.MessageBox.Show("程序已经在运行中，请勿重复启动！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    Application.Current.Shutdown();
                });
                return;
            }
            
            IsLoaded = false;
            UpdateMessage("软件完整性自检中，请稍等...");
            Task.Factory.StartNew(() => {
                try
                {
                    UpdateMessage("开始校验配置文件...");
                    IsLoaded = _loginService.CheckConfigFile();
                    if (IsLoaded) ObConfig = _globalState.AppConfig;
                    UpdateMessage("开始校验DLL文件完整性...");
                    _loginService.CheckDLLFolder();
                    UpdateMessage("开始校验数据库...");
                    IsLoaded = _loginService.CheckDB();
                    UpdateMessage("开始尝试连接设备...");
                    UpdateMessage("检测相机是否连通...");
                    UpdateMessage("软件自检成功！");
                    LoadUserData();
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() => {
                        HandyControl.Controls.MessageBox.Show(ex.Message, "初始化致命错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        ReleaseMutex();
                        Application.Current.Shutdown();
                    });
                }

            });

        });



        /// <summary>
        /// 加载登录用户
        /// </summary>
        private void LoadUserData()
        {
            try
            {
                if (!FileHelper.FileIsExists(FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER, GlobalConstants.USERDATA))) LoginUser = new ObservableUserModel();
                string content = FileHelper.ReadFile(FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER, GlobalConstants.USERDATA));
                LoginUser = JsonConvert.DeserializeObject<ObservableUserModel>(content);
            }
            catch (Exception ex)
            {
                LoginUser = new ObservableUserModel();
                _logger.LogError(ex);
            }

        }

        public IRelayCommand CloseCommand => new RelayCommand(() => {
            Application.Current.Dispatcher.Invoke(() => {
                var temp = HandyControl.Controls.MessageBox.Show("确认关闭软件？", "温馨提示", MessageBoxButton.YesNo, MessageBoxImage.Information);
                if (temp == MessageBoxResult.Yes)
                {
                    ReleaseMutex();
                    Application.Current.Shutdown();
                }
            });
        });

        public IRelayCommand LoginCommand => new RelayCommand(async () => {
            if (string.IsNullOrEmpty(LoginUser.Account) || string.IsNullOrEmpty(LoginUser.Password)) return;
            var result = await _loginService.VerifyUser(LoginUser.Account, LoginUser.Password);
            if (result == null)
            {
                HandyControl.Controls.MessageBox.Show("用户名或密码错误！", "温馨提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }
            _globalState.LoginUser = result;
            if ((bool)LoginUser.IsRemeber) FileHelper.WriteFile(FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER,GlobalConstants.USERDATA), JsonConvert.SerializeObject(LoginUser));
            
            _logger.LogInfo($"用户 {result.Account} 登录成功");
            OpenMainWindow();
            CloseCurrentWindow();
        });

        /// <summary>
        /// 测试单实例状态命令 - 调试用
        /// </summary>
        public IRelayCommand TestSingleInstanceCommand => new RelayCommand(() =>
        {
            var status = HasMutex ? "已获取互斥量" : "未获取互斥量";
            HandyControl.Controls.MessageBox.Show($"当前单实例状态: {status}", "调试信息", MessageBoxButton.OK, MessageBoxImage.Information);
        });

        private void OpenMainWindow()
        {
            var mainWindow = new MainWindow();
            mainWindow.Show();
        }

        private void CloseCurrentWindow()
        {
            foreach (Window window in Application.Current.Windows)
            {
                if (window.DataContext == this)
                {
                    window.Close();
                    break;
                }
            }
        }


        private void UpdateMessage(string message)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                Message = message;
            });
        }

        /// <summary>
        /// 检查是否为单一实例
        /// </summary>
        /// <returns>true表示是第一个实例，false表示已有实例在运行</returns>
        private bool CheckSingleInstance()
        {
            try
            {
                if (_mutex == null)
                {
                    // 创建一个命名互斥量
                    _mutex = new Mutex(true, MutexName, out _isFirstInstance);
                    
                    if (!_isFirstInstance)
                    {
                        // 如果不是第一个实例，尝试激活已存在的窗口
                        ActivateExistingWindow();
                        return false;
                    }
                    
                    _logger.LogInfo("程序启动 - 单实例检查通过");
                    return true;
                }
                return _isFirstInstance;
            }
            catch (Exception ex)
            {
                _logger.LogError($"单实例检查异常: {ex.Message}");
                return true; // 出现异常时允许启动，避免阻止正常使用
            }
        }

        /// <summary>
        /// 尝试激活已存在的程序窗口
        /// </summary>
        private void ActivateExistingWindow()
        {
            try
            {
                var currentProcessName = Process.GetCurrentProcess().ProcessName;
                var processes = Process.GetProcessesByName(currentProcessName);
                
                foreach (var process in processes)
                {
                    if (process.Id != Process.GetCurrentProcess().Id && process.MainWindowHandle != IntPtr.Zero)
                    {
                        // 激活已存在的窗口
                        ShowWindow(process.MainWindowHandle, SW_RESTORE);
                        SetForegroundWindow(process.MainWindowHandle);
                        _logger.LogInfo("已激活现有程序窗口");
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"激活现有窗口异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放互斥量
        /// </summary>
        private void ReleaseMutex()
        {
            try
            {
                if (_mutex != null && _isFirstInstance)
                {
                    _mutex.ReleaseMutex();
                    _mutex.Dispose();
                    _mutex = null;
                    _logger.LogInfo("互斥量已释放");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"释放互斥量异常: {ex.Message}");
            }
        }

        // Windows API 声明
        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        private const int SW_RESTORE = 9;

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            GlobalReleaseMutex();
        }
    }
}
