﻿<UserControl x:Class="IPM.Vision.Views.Dialogs.ProcessManageDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
             xmlns:canon="clr-namespace:IPM.Vision.Views.CanonControls"
             xmlns:control="clr-namespace:IPM.Vision.Views.CustomControls"
             xmlns:converts="clr-namespace:IPM.Vision.Common.Converts"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             DataContext="{Binding ProcessManageDialogViewModel, Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="660" d:DesignWidth="1320">
    <UserControl.Resources>
        <ResourceDictionary>
            <converts:CameraTypeConvert x:Key="CameraTypeConvert"/>
        </ResourceDictionary>
    </UserControl.Resources>
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="White" CornerRadius="5" Width="1200" Height="860">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Background="#283643" CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText Text="{Binding Title}" Foreground="White" FontSize="18" VerticalAlignment="Center" Margin="10 0 0 0"/>
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Template="{StaticResource CloseTemplate}"
                            Width="40"
                            Height="40"
                            Content="&#xf00d;"
                            Command="{Binding CloseCommand}"/>
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1.5*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!--设备-->
                    <Border Grid.Column="0" Margin="5 0 5 0">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Border Grid.Row="0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <hc:UniformSpacingPanel Orientation="Vertical" Grid.Column="0" Spacing="5">
                                        <hc:TextBox
                                            Text="{Binding CurrentProcessModel.ParamName,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                                            FontSize="14"
                                            hc:TitleElement.Title="步骤名称"
                                            hc:TitleElement.TitlePlacement="Left" />
                                        <hc:ComboBox
                                            FontSize="14"
                                            ItemsSource="{Binding CameraTypes}"
                                            SelectedValue="{Binding CurrentProcessModel.CameraType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                                            DisplayMemberPath="Label"
                                            SelectedValuePath="Value"
                                            hc:TitleElement.Title="使用相机"
                                            hc:TitleElement.TitlePlacement="Left"/>

                                    </hc:UniformSpacingPanel>
                                    <hc:UniformSpacingPanel Margin="10 0 0 0" Orientation="Vertical" Grid.Column="1" Spacing="5">
                                        <hc:NumericUpDown
                                            FontSize="14"
                                            IsReadOnly="True"
                                            Style="{StaticResource NumericUpDownExtend}"
                                            hc:TitleElement.Title="步骤编号"
                                            Value="{Binding CurrentProcessModel.ProcessNumber,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                                            hc:TitleElement.TitlePlacement="Left"/>
                                        <hc:ComboBox
                                            FontSize="14"
                                            hc:TitleElement.Title="步骤类型"
                                            SelectedValue="{Binding CurrentProcessModel.ProcessType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                                            ItemsSource="{Binding ProcessTypes}"
                                            DisplayMemberPath="Label"
                                            SelectedValuePath="Value"
                                            hc:TitleElement.TitlePlacement="Left"/>

                                    </hc:UniformSpacingPanel>
                                </Grid>
                            </Border>

                            <Border Grid.Row="1" Margin="0 10 0 0" BorderBrush="Gray" BorderThickness="0.5" CornerRadius="5">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="30"/>
                                        <RowDefinition Height="*"/>
                                    </Grid.RowDefinitions>
                                    <Border Grid.Row="0" Background="Gray" CornerRadius="5 5 0 0">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <hc:SimpleText Grid.Column="0" Text="实时画面" FontSize="14" VerticalAlignment="Center" Foreground="White" Margin="5 0 0 0"/>
                                            <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10" Grid.Column="1" VerticalAlignment="Center" HorizontalAlignment="Right" Margin="0 0 10 0">
                                                <hc:SimpleText  Text="当前相机:" FontSize="14" VerticalAlignment="Center" Foreground="White" />
                                                <hc:SimpleText Text="{Binding CurrentProcessModel.CameraType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged,Converter={StaticResource CameraTypeConvert}}"
                                                               FontSize="14"
                                                               VerticalAlignment="Center"
                                                               Foreground="LightGreen" />
                                            </hc:UniformSpacingPanel>
                                        </Grid>
                                    </Border>
                                    <Border Grid.Row="1">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <Border Grid.Column="0"
                                                    Visibility="{Binding CurrentProcessModel.CameraType,Mode=TwoWay,
                                                    UpdateSourceTrigger=PropertyChanged,
                                                    Converter={StaticResource EnumToVisibleConvert},ConverterParameter=Main}">
                                                <control:OPTCameraControl/>
                                            </Border>
                                            <Border Grid.Column="0"
                                                    Visibility="{Binding CurrentProcessModel.CameraType,Mode=TwoWay,
                                                    UpdateSourceTrigger=PropertyChanged,
                                                    Converter={StaticResource EnumToVisibleConvert},ConverterParameter=Child}">
                                                <control:HKCameraControl/>
                                            </Border>
                                            <Border
                                                Grid.Column="1"
                                                Width="80"
                                                Padding="4"
                                                Visibility="{Binding CurrentProcessModel.TakeType,Mode=TwoWay,
                                                UpdateSourceTrigger=PropertyChanged,
                                                Converter={StaticResource EnumToVisibleConvert},ConverterParameter=POINT}">
                                                <ListBox />
                                            </Border>
                                        </Grid>
                                    </Border>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>

                    <Border Grid.Column="1"  BorderBrush="Gray" BorderThickness="0.5" CornerRadius="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="30"/>
                                <RowDefinition Height="40"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>
                            <Border Grid.Row="0" Background="Gray" CornerRadius="5 5 0 0">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <hc:SimpleText Text="设备参数" FontSize="14" VerticalAlignment="Center" Foreground="White" Grid.Column="0" Margin="5 0 0 0"/>
                                </Grid>
                            </Border>

                            <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10" Grid.Row="1" Margin="5 5 0 0">
                                <Button Content="清除报警" Style="{StaticResource ButtonDanger}" Command="{Binding ResetErrorCommand}"/>
                                <Button Content="重置设备" Style="{StaticResource ButtonWarning}" Command="{Binding ResetCommand}"/>
                                <Button Content="设备定位" Style="{StaticResource ButtonInfo}" Command="{Binding PositionCommand}"/>
                                <Button Content="读取当前定位" Style="{StaticResource ButtonInfo}" Command="{Binding ReadPositionCommand}"/>
                                <hc:NumericUpDown Value="{Binding StepNumber,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Style="{StaticResource NumericUpDownExtend}" FontSize="16" Width="120" DecimalPlaces="2" hc:TitleElement.TitlePlacement="Left" hc:TitleElement.Title="步进"/>
                            </hc:UniformSpacingPanel>

                            <Border Grid.Row="2" Padding="5">
                                <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" >
                                    <Grid >
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <hc:ComboBox Grid.Column="0"
                                                     hc:TitleElement.Title="参数名称"
                                                     ItemsSource="{Binding ObservableEquipmentModels}"
                                                     DisplayMemberPath="ParamName"
                                                     SelectedValuePath="Id"
                                                     IsEditable="True"
                                                     SelectedValue="{Binding EquipmentModel.Id}"
                                                     SelectedItem="{Binding EquipmentModel}"
                                                     IsEnabled="{Binding IsInsert,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                                                     Text="{Binding EquipmentModel.ParamName}"
                                                     FontSize="14"/>
                                    </Grid>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <hc:UniformSpacingPanel Orientation="Vertical" Grid.Column="0">
                                            <!--X轴-->
                                            <hc:UniformSpacingPanel Orientation="Vertical">

                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Button Content="&#xf104;" Grid.Column="0" FontSize="22" FontFamily="{StaticResource FontAwesome}" Style="{StaticResource ButtonWarning}" Height="50" Command="{Binding LeftCommand}" CommandParameter="X" />
                                                    <hc:NumericUpDown
                                                        Margin="4 0"
                                                        Grid.Column="1"
                                                        hc:InfoElement.Title="X轴"
                                                        Maximum="440"
                                                        Minimum="-385"
                                                        DecimalPlaces="2"
                                                        Style="{StaticResource NumericUpDownExtend}"
                                                        Value="{Binding EquipmentModel.X,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                        <hc:Interaction.Triggers>
                                                            <hc:EventTrigger EventName="KeyDown">
                                                                <hc:EventToCommand Command="{Binding XYKeyDownCommand}" PassEventArgsToCommand="True" />
                                                            </hc:EventTrigger>
                                                        </hc:Interaction.Triggers>
                                                    </hc:NumericUpDown>
                                                    <Button Content="&#xf105;" Grid.Column="2" FontSize="22" FontFamily="{StaticResource FontAwesome}" Style="{StaticResource ButtonWarning}" Height="50"  Command="{Binding RightCommand}" CommandParameter="X" />
                                                </Grid>

                                                <hc:PreviewSlider   Maximum="440"
                                                                  Minimum="-385"
                                                                  VerticalAlignment="Bottom"
                                                                  IsSnapToTickEnabled="True"
                                                                  Value="{Binding EquipmentModel.X,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                    <hc:Interaction.Triggers>
                                                        <hc:EventTrigger EventName="LostMouseCapture">
                                                            <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="XY" />
                                                        </hc:EventTrigger>
                                                    </hc:Interaction.Triggers>
                                                    <hc:PreviewSlider.PreviewContent>
                                                        <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                                                    </hc:PreviewSlider.PreviewContent>
                                                </hc:PreviewSlider>
                                            </hc:UniformSpacingPanel>

                                            <!--Y轴-->
                                            <hc:UniformSpacingPanel Orientation="Vertical">

                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>
                                                    <Button Content="&#xf104;" Grid.Column="0" FontSize="22" FontFamily="{StaticResource FontAwesome}" Style="{StaticResource ButtonWarning}" Height="50" Command="{Binding LeftCommand}" CommandParameter="Y" />
                                                    <hc:NumericUpDown
                                                        Grid.Column="1"
                                                        Margin="4 0"
                                                        hc:InfoElement.Title="Y轴"
                                                        Maximum="195"
                                                        Minimum="-250"
                                                        DecimalPlaces="2"
                                                        Style="{StaticResource NumericUpDownExtend}"
                                                        Value="{Binding EquipmentModel.Y,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                        <hc:Interaction.Triggers>
                                                            <hc:EventTrigger EventName="KeyDown">
                                                                <hc:EventToCommand Command="{Binding XYKeyDownCommand}" PassEventArgsToCommand="True" />
                                                            </hc:EventTrigger>
                                                        </hc:Interaction.Triggers>
                                                    </hc:NumericUpDown>
                                                    <Button Content="&#xf105;" Grid.Column="2" FontSize="22" FontFamily="{StaticResource FontAwesome}" Style="{StaticResource ButtonWarning}" Height="50"  Command="{Binding RightCommand}" CommandParameter="Y" />

                                                </Grid>

                                                <hc:PreviewSlider  Maximum="195"
                                                                  Minimum="-250"
                                                                  VerticalAlignment="Bottom"
                                                                  IsSnapToTickEnabled="True"
                                                                  Value="{Binding EquipmentModel.Y,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                    <hc:Interaction.Triggers>
                                                        <hc:EventTrigger EventName="LostMouseCapture">
                                                            <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="XY" />
                                                        </hc:EventTrigger>
                                                    </hc:Interaction.Triggers>
                                                    <hc:PreviewSlider.PreviewContent>
                                                        <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                                                    </hc:PreviewSlider.PreviewContent>
                                                </hc:PreviewSlider>
                                            </hc:UniformSpacingPanel>
                                        </hc:UniformSpacingPanel>
                                        <Button Content="重置XY轴"
                                                Command="{Binding ResetSingleCommand}"
                                                CommandParameter="XY"
                                                Margin="10 0 0 0"
                                                Width="100"
                                                Height="120"
                                                Style="{StaticResource ButtonInfo}"
                                                Grid.Column="1"/>
                                    </Grid>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <!--Z轴-->

                                        <hc:UniformSpacingPanel Orientation="Vertical" Grid.Column="0">

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <Button Content="&#xf104;" Grid.Column="0" FontSize="22" FontFamily="{StaticResource FontAwesome}" Style="{StaticResource ButtonWarning}" Height="50" Command="{Binding LeftCommand}" CommandParameter="Z" />

                                                <hc:NumericUpDown
                                                    hc:InfoElement.Title="Z轴"
                                                    Grid.Column="1"
                                                    Margin="4 0"
                                                    Maximum="10"
                                                    Minimum="-70"
                                                    DecimalPlaces="2"
                                                    Style="{StaticResource NumericUpDownExtend}"
                                                    Value="{Binding EquipmentModel.Z,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                    <hc:Interaction.Triggers>
                                                        <hc:EventTrigger EventName="LostMouseCapture">
                                                            <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="Z" />
                                                        </hc:EventTrigger>
                                                    </hc:Interaction.Triggers>
                                                </hc:NumericUpDown>
                                                <Button Content="&#xf105;" Grid.Column="2" FontSize="22" FontFamily="{StaticResource FontAwesome}" Style="{StaticResource ButtonWarning}" Height="50"  Command="{Binding RightCommand}" CommandParameter="Z" />

                                            </Grid>

                                            <hc:PreviewSlider  Maximum="10"
                                                              Minimum="-125"
                                                              VerticalAlignment="Bottom"
                                                              IsSnapToTickEnabled="True"
                                                              Value="{Binding EquipmentModel.Z,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="LostMouseCapture">
                                                        <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="Z" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                                <hc:PreviewSlider.PreviewContent>
                                                    <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                                                </hc:PreviewSlider.PreviewContent>
                                            </hc:PreviewSlider>
                                        </hc:UniformSpacingPanel>
                                        <Button Content="重置Z轴"
                                                Command="{Binding ResetSingleCommand}"
                                                CommandParameter="Z"
                                                Margin="10 0 0 0"
                                                Width="100"
                                                Height="60"
                                                Style="{StaticResource ButtonInfo}"
                                                Grid.Column="1"/>
                                    </Grid>


                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <!--相机角度-->
                                        <hc:UniformSpacingPanel Orientation="Vertical" Grid.Column="0">

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <Button Content="&#xf104;" Grid.Column="0" FontSize="22" FontFamily="{StaticResource FontAwesome}" Style="{StaticResource ButtonWarning}" Height="50" Command="{Binding LeftCommand}" CommandParameter="R" />
                                                <hc:NumericUpDown
                                                    Grid.Column="1"
                                                    Margin="4 0"
                                                    hc:InfoElement.Title="相机角度"
                                                    Maximum="60"
                                                    Minimum="30"
                                                    DecimalPlaces="2"
                                                    Style="{StaticResource NumericUpDownExtend}"
                                                    Value="{Binding EquipmentModel.R,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                    <hc:Interaction.Triggers>
                                                        <hc:EventTrigger EventName="KeyDown">
                                                            <hc:EventToCommand Command="{Binding RKeyDownCommand}" PassEventArgsToCommand="True" />
                                                        </hc:EventTrigger>
                                                    </hc:Interaction.Triggers>
                                                </hc:NumericUpDown>
                                                <Button Content="&#xf105;"
                                                        Grid.Column="2"
                                                        FontSize="22"
                                                        FontFamily="{StaticResource FontAwesome}"
                                                        Style="{StaticResource ButtonWarning}" Height="50"
                                                        Command="{Binding RightCommand}" CommandParameter="R" />
                                            </Grid>

                                            <hc:PreviewSlider  Maximum="60"
                                                              Minimum="30"
                                                              VerticalAlignment="Bottom"
                                                              IsSnapToTickEnabled="True"
                                                              Value="{Binding EquipmentModel.R,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="LostMouseCapture">
                                                        <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="R" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                                <hc:PreviewSlider.PreviewContent>
                                                    <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                                                </hc:PreviewSlider.PreviewContent>
                                            </hc:PreviewSlider>
                                        </hc:UniformSpacingPanel>
                                        <Button Content="重置相机"
                                                Margin="10 0 0 0"
                                                Width="100"
                                                Height="60"
                                                Command="{Binding ResetSingleCommand}"
                                                CommandParameter="R"
                                                Style="{StaticResource ButtonInfo}"
                                                Grid.Column="1"/>
                                    </Grid>

                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <!--转盘角度-->
                                        <hc:UniformSpacingPanel Orientation="Vertical" Grid.Column="0">

                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>
                                                <Button Content="&#xf104;" Grid.Column="0"
                                                        FontSize="22" FontFamily="{StaticResource FontAwesome}" Style="{StaticResource ButtonWarning}" Height="50"  Command="{Binding LeftCommand}" CommandParameter="T" />
                                                <hc:NumericUpDown
                                                    hc:InfoElement.Title="水平旋转角度"
                                                    Maximum="360"
                                                    Minimum="0"
                                                    Grid.Column="1"
                                                    Margin="4 0"
                                                    DecimalPlaces="2"
                                                    Style="{StaticResource NumericUpDownExtend}"
                                                    Value="{Binding EquipmentModel.T,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                    <hc:Interaction.Triggers>
                                                        <hc:EventTrigger EventName="KeyDown">
                                                            <hc:EventToCommand Command="{Binding TKeyDownCommand}" PassEventArgsToCommand="True" />
                                                        </hc:EventTrigger>
                                                    </hc:Interaction.Triggers>
                                                </hc:NumericUpDown>
                                                <Button
                                                    Content="&#xf105;"
                                                    Grid.Column="2"
                                                    FontSize="22"
                                                    FontFamily="{StaticResource FontAwesome}"
                                                    Style="{StaticResource ButtonWarning}"
                                                    Height="50"  Command="{Binding RightCommand}"
                                                    CommandParameter="T" />
                                            </Grid>

                                            <hc:PreviewSlider  Maximum="360"
                                                              Minimum="0"
                                                              VerticalAlignment="Bottom"
                                                              IsSnapToTickEnabled="True"
                                                              Value="{Binding EquipmentModel.T,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                                <hc:Interaction.Triggers>
                                                    <hc:EventTrigger EventName="LostMouseCapture">
                                                        <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="T" />
                                                    </hc:EventTrigger>
                                                </hc:Interaction.Triggers>
                                                <hc:PreviewSlider.PreviewContent>
                                                    <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                                                </hc:PreviewSlider.PreviewContent>
                                            </hc:PreviewSlider>
                                        </hc:UniformSpacingPanel>
                                        <Button Content="重置转盘" Margin="10 0 0 0" Width="100" Height="60" Style="{StaticResource ButtonInfo}" Grid.Column="1" Command="{Binding ResetSingleCommand}"
                                                CommandParameter="T"/>
                                    </Grid>
                                </hc:UniformSpacingPanel>
                            </Border>
                        </Grid>
                    </Border>

                </Grid>
            </Border>

            <Grid Grid.Row="2" Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Button Grid.Column="0" Style="{StaticResource ButtonPrimary}" Width="320" Height="40" Content="保存" Command="{Binding SaveCommand}"/>
                <Button Grid.Column="1" Content="取消" Width="320" Height="40" Command="{Binding CloseCommand}"/>
            </Grid>
        </Grid>
    </Border>
</UserControl>
