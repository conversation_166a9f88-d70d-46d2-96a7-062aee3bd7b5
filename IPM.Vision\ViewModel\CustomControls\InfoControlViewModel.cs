﻿using CommunityToolkit.Mvvm.Input;
using IPM.Vision.Common;
using IPM.Vision.LEvents;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.CustomControls;
using Org.BouncyCastle.Utilities;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

namespace IPM.Vision.ViewModel.CustomControls
{
    public class InfoControlViewModel:ViewModelBase, IHObservable
    {
        private readonly ObservableGlobalState _globalState;
        private readonly NLogHelper _logger;
        public event HNotifyInfoDelegate HNotifyInfoEvent;
        private Dispatcher _dispatcher = Application.Current.Dispatcher;
        public delegate void _UpdateAsync(HObservable observable, HEquipmentStatusArgs e);
        private Brush _mainCameraStatus = Brushes.Gray;
        private Brush _lightStatus = Brushes.Gray;
        private Brush _equipmentStatus = Brushes.Gray;

        public ObservableCollection<ObservableLogModel> Logs { get; set; } = new ObservableCollection<ObservableLogModel>();

        public InfoControlViewModel(ObservableGlobalState globalState,NLogHelper logger)
        {
            _globalState = globalState;
            _logger = logger;
            _globalState.HObservable.Add(this);
        }

        public Brush MainCameraStatus
        {
            get => _mainCameraStatus;
            set => SetProperty(ref _mainCameraStatus, value);
        }

        public Brush LightStatus
        {
            get => _lightStatus;
            set => SetProperty(ref _lightStatus, value);
        }

        public Brush EquipmentStatus
        {
            get => _equipmentStatus;
            set => SetProperty(ref _equipmentStatus, value);
        }


        public void UpdateAsync(HObservable observable, HEquipmentStatusArgs e)
        {
            if (!_dispatcher.CheckAccess())
            {
                _dispatcher.Invoke(new _UpdateAsync(UpdateAsync), observable, e);
                return;
            }
            
            switch (e.StatusShowType)
            {
                case ShowType.ICON:
                    ShowLabel(e);
                    break;
                case ShowType.LABEL:
                    ShowMessage(e);
                    break;
                case ShowType.ALL:
                    ShowLabel(e);
                    ShowMessage(e);
                    break;
            }   

        }

        private Brush ConvertStatus(int status)
        {
            var result = Brushes.Gray;
            switch (status)
            {
                case 0:
                    result = Brushes.Gray;  // 停止状态
                    break;
                case 1:
                    result = Brushes.Green;  // 运行中
                    break;
                case 2:
                    result = Brushes.Yellow;  // 完成
                    break;
                case 3:
                    result = Brushes.Gray;  // 待机
                    break;
                case 4:
                    result = Brushes.Red;  // 错误
                    break;
                default:
                    result = Brushes.Gray;  // 未知状态
                    break;
            }
            return result;
        }

        private void ShowLabel(HEquipmentStatusArgs e)
        {
            switch (e.SourceType)
            {
                case SourceType.CAMERA:
                    MainCameraStatus = ConvertStatus(e.EquipmentStatus);
                    break;
                case SourceType.LIGHT:
                    LightStatus = ConvertStatus(e.EquipmentStatus);
                    break;
                case SourceType.PLC:
                    EquipmentStatus = ConvertStatus(e.EquipmentStatus);
                    break;
            }
        }

        private void ShowMessage(HEquipmentStatusArgs e)
        {
            if (string.IsNullOrEmpty(e.EventMessage)) return;
            string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            string formattedMessage = $"{timestamp}: {e.EventMessage}";
            ObservableLogModel _log = new ObservableLogModel()
            {
                Message = formattedMessage,
            };
            switch (e.EventCode)
            {
                case HEventCode.SUCCESS:
                    _log.Color = Brushes.Black;
                    break;
                case HEventCode.ERROR:
                    _log.Color = Brushes.Red;
                    break;
                case HEventCode.WARNING:
                    break;
            }
            _logger.LogInfo(e.EventMessage);
            Logs.Insert(0, _log);
            if (Logs.Count > 200) Logs.RemoveAt(Logs.Count - 1);
        }
    }
}
