﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class DownloadEvfCommand : BasicCommand
    {
        public DownloadEvfCommand(ref CanonCameraModel  model)
            : base(ref model)
        {
        }

        public override bool Execute()
        {
            uint num = 0u;
            IntPtr outEvfImageRef = IntPtr.Zero;
            IntPtr outStream = IntPtr.Zero;
            ulong inBufferSize = 2097152uL;
            IntPtr intPtr = IntPtr.Zero;
            IntPtr zero = IntPtr.Zero;
            if ((_model.EvfOutputDevice & 2) == 0)
            {
                return true;
            }

            num = EDSDK.EdsCreateMemoryStream(inBufferSize, out outStream);
            if (num == 0)
            {
                num = EDSDK.EdsCreateEvfImageRef(outStream, out outEvfImageRef);
            }

            if (num == 0)
            {
                num = EDSDK.EdsDownloadEvfImage(_model.Camera, outEvfImageRef);
            }

            if (num == 0)
            {
                EVFDataSet structure = default(EVFDataSet);
                structure.stream = outStream;
                EDSDK.EdsGetPropertyData(outEvfImageRef, 1287u, 0, out structure.zoom);
                EDSDK.EdsGetPropertyData(outEvfImageRef, 1291u, 0, out structure.imagePosition);
                EDSDK.EdsGetPropertyData(outEvfImageRef, 1345u, 0, out structure.zoomRect);
                EDSDK.EdsGetPropertyData(outEvfImageRef, 1344u, 0, out structure.sizeJpegLarge);
                EDSDK.EdsGetPropertyData(outEvfImageRef, 16778566u, 0, out structure.visibleRect);
                _model.SizeJpegLarge = structure.sizeJpegLarge;
                _model.SetPropertyRect(1345u, structure.zoomRect);
                _model.SetPropertyRect(16778566u, structure.visibleRect);
                int cb = Marshal.SizeOf(structure);
                intPtr = Marshal.AllocHGlobal(cb);
                Marshal.StructureToPtr(structure, intPtr, fDeleteOld: false);
            }

            if (num == 0)
            {
                CameraEvent e = new CameraEvent(CameraEvent.Type.EVFDATA_CHANGED, intPtr);
                _model.NotifyObservers(e);
            }

            if (num == 0 && _model.RollPitch == 0)
            {
                EDSDK.EdsCameraPos outPropertyData = default(EDSDK.EdsCameraPos);
                num = EDSDK.EdsGetPropertyData(outEvfImageRef, 16778564u, 0, out outPropertyData);
                if (num == 0)
                {
                    int cb2 = Marshal.SizeOf(outPropertyData);
                    zero = Marshal.AllocHGlobal(cb2);
                    Marshal.StructureToPtr(outPropertyData, zero, fDeleteOld: false);
                    CameraEvent e2 = new CameraEvent(CameraEvent.Type.ANGLEINFO, zero);
                    _model.NotifyObservers(e2);
                }
            }

            if (outStream != IntPtr.Zero)
            {
                EDSDK.EdsRelease(outStream);
                outStream = IntPtr.Zero;
            }

            if (outEvfImageRef != IntPtr.Zero)
            {
                EDSDK.EdsRelease(outEvfImageRef);
                outEvfImageRef = IntPtr.Zero;
            }

            if (intPtr != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(intPtr);
            }

            switch (num)
            {
                case 41218u:
                    return false;
                case 129u:
                    {
                        CameraEvent e4 = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e4);
                        return false;
                    }
                default:
                    {
                        CameraEvent e3 = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e3);
                        break;
                    }
                case 0u:
                    break;
            }

            return true;
        }
    }
}
