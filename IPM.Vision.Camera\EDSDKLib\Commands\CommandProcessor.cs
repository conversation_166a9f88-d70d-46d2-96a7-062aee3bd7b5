﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class CommandProcessor
    {
        private ConcurrentQueue<BasicCommand> _commandQueue = new ConcurrentQueue<BasicCommand>();

        private bool _running = false;

        private Task _task = null;

        public async void Start()
        {
            _running = true;
            _task = await Task.Factory.StartNew((Func<Task>)async delegate
            {
                try
                {
                    while (_running)
                    {
                        await Task.Delay(1);
                        BasicCommand command = null;
                        _commandQueue.TryDequeue(out command);
                        if (command != null && !command.Execute())
                        {
                            await Task.Delay(500);
                            _commandQueue.Enqueue(command);
                        }
                    }
                }
                catch (Exception)
                {
                }
            }, TaskCreationOptions.LongRunning);
        }

        public void Stop()
        {
            _running = false;
            
            try
            {
                if(_task != null ) 
                      _task.Wait();
            }
            catch (AggregateException)
            {
            }
            if (_task != null)
                _task.Dispose();
        }

        public void PostCommand(BasicCommand command)
        {
            _commandQueue.Enqueue(command);
        }
    }
}
