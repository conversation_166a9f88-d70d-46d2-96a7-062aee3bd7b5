﻿using IPM.Vision.Common;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("comp_point")]
    public class CompPointModel:BasicModel
    {
        [SugarColumn(ColumnName = "product_param_id")]
        public string ProductParamId { get; set; }

        [SugarColumn(ColumnName = "comp_name")]
        public string CompName { get; set; }

        [SugarColumn(ColumnName = "comp_width", IsNullable = true)]
        public double CompWidth { get;set; }

        [SugarColumn(ColumnName = "comp_height", IsNullable = true)]
        public double CompHeight { get; set; }

        [SugarColumn(ColumnName = "comp_x", IsNullable = true)]
        public double CompX { get; set; }

        [SugarColumn(ColumnName = "comp_y", IsNullable = true)]
        public double CompY { get; set; }

        [SugarColumn(ColumnName = "picture_x", IsNullable = true)]
        public double PictureX { get; set; }

        [SugarColumn(ColumnName = "picture_y", IsNullable = true)]
        public double PictureY { get; set; }

        [SugarColumn(ColumnName = "reel_x", IsNullable = true)]
        public double ReelX { get; set; }

        [SugarColumn(ColumnName = "reel_y",IsNullable = true)]
        public double ReelY { get; set; }

        [SugarColumn(ColumnName = "ref_number", IsNullable = true)]
        public string RefNumber { get; set; }

        [SugarColumn(ColumnName = "pin_number", IsNullable = true)]
        public string PinNumber { get; set; }
        [SugarColumn(ColumnName = "x_pin_number", IsNullable = true)]
        public int XPinNumber {  get; set; }

        [SugarColumn(ColumnName = "y_pin_number", IsNullable = true)]
        public int YPinNumber { get; set; }

        [SugarColumn(ColumnName = "pin_fetch", IsNullable = true)]
        public double PinFetch { get; set; }

        [SugarColumn(ColumnName = "pin_type")]
        public PinTypeEnum PinType { get; set; }
    }
}
