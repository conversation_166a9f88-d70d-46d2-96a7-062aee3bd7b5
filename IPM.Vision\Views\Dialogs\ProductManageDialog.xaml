﻿<UserControl
    x:Class="IPM.Vision.Views.Dialogs.ProductManageDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:dd="urn:gong-wpf-dragdrop"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="860"
    d:DesignWidth="1420"
    DataContext="{Binding ProductManageDialogViewModel, Source={StaticResource Locator}}"
    mc:Ignorable="d">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}" />
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border
        Width="1510"
        Height="960"
        Background="White"
        CornerRadius="5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="0"
                Background="#283643"
                CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText
                            Margin="10,0,0,0"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Foreground="White"
                            Text="{Binding Title}" />
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Width="40"
                            Height="40"
                            Command="{Binding CloseCommand}"
                            Content="&#xf00d;"
                            Template="{StaticResource CloseTemplate}" />
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <hc:UniformSpacingPanel
                            Grid.Column="0"
                            Margin="0,0,5,0"
                            Orientation="Vertical"
                            Spacing="5">
                            <hc:TextBox
                                hc:InfoElement.Necessary="True"
                                hc:TitleElement.Title="产品代号"
                                IsEnabled="{Binding IsInsert}"
                                Text="{Binding ProductModel.ProductNumber, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                            <!--<hc:TextBox hc:TitleElement.Title="检测模型"
                            hc:InfoElement.Necessary="True"
                            IsEnabled="{Binding IsInsert}"
                            hc:InfoElement.Placeholder="请选择检测模型"
                            Text="{Binding ProductModel.CheckModel,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            />-->
                        </hc:UniformSpacingPanel>
                        <hc:UniformSpacingPanel
                            Grid.Column="1"
                            Margin="5,0,0,0"
                            Orientation="Vertical"
                            Spacing="5">
                            <hc:TextBox hc:TitleElement.Title="产品名称" Text="{Binding ProductModel.ProductName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                        </hc:UniformSpacingPanel>
                    </Grid>
                    <Grid Grid.Row="2" Margin="5">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <hc:UniformSpacingPanel
                            Grid.Row="0"
                            Margin="0,0,0,5"
                            Orientation="Horizontal"
                            Spacing="10">
                            <Button
                                Command="{Binding GenerateProceessCommand}"
                                Content="&#xf067; 生成步骤"
                                FontFamily="{StaticResource FontAwesome}"
                                FontSize="14"
                                Style="{StaticResource ButtonPrimary}" />
                            <Button
                                Command="{Binding AddNewProcessAndOtherCommand}"
                                Content="&#xf067; 添加新步骤和新设备参数"
                                FontFamily="{StaticResource FontAwesome}"
                                FontSize="14"
                                Style="{StaticResource ButtonPrimary}" />
                            <Button
                                Command="{Binding ImportCommand}"
                                Content="&#xf067; 导入编程"
                                FontFamily="{StaticResource FontAwesome}"
                                FontSize="14"
                                Style="{StaticResource ButtonSuccess}" />
                        </hc:UniformSpacingPanel>

                        <DataGrid
                            Grid.Row="1"
                            dd:DragDrop.DropHandler="{Binding}"
                            dd:DragDrop.IsDragSource="True"
                            dd:DragDrop.IsDropTarget="True"
                            dd:DragDrop.UseDefaultDragAdorner="True"
                            hc:DataGridAttach.CanUnselectAllWithBlankArea="True"
                            hc:DataGridAttach.ShowRowNumber="False"
                            hc:DataGridAttach.ShowSelectAllButton="False"
                            AutoGenerateColumns="False"
                            CanUserReorderColumns="False"
                            CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                            ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}"
                            ItemsSource="{Binding ProcessModels}"
                            RowHeaderWidth="60"
                            Style="{StaticResource DataGridBaseStyle}">
                            <DataGrid.Columns>
                                <DataGridTextColumn
                                    Width="80"
                                    Binding="{Binding ProcessNumber}"
                                    CanUserSort="False"
                                    Header="步骤序号"
                                    IsReadOnly="True" />
                                <DataGridTemplateColumn Width="*" Header="步骤名称">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <hc:TextBox Width="220" Text="{Binding ParamName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Width="*" Header="步骤类型">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <hc:ComboBox
                                                Width="220"
                                                DisplayMemberPath="Label"
                                                ItemsSource="{Binding DataContext.ProcessTypes, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                SelectedValue="{Binding ProcessType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                SelectedValuePath="Value" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Width="*" Header="使用相机">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <hc:ComboBox
                                                Width="220"
                                                DisplayMemberPath="Label"
                                                ItemsSource="{Binding DataContext.CameraTypes, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                SelectedValue="{Binding CameraType, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                SelectedValuePath="Value" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Width="*" Header="设备参数">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <hc:ComboBox
                                                Width="220"
                                                DisplayMemberPath="ParamName"
                                                ItemsSource="{Binding DataContext.ObservableEquipmentModels, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                SelectedValue="{Binding EquipmentParaId, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                SelectedValuePath="Id" />
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Width="120" Header="操作">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Button
                                                    Command="{Binding DataContext.ModifyProcessCommand, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"
                                                    Content="&#xf044;"
                                                    FontFamily="{StaticResource FontAwesome}"
                                                    Style="{StaticResource ButtonInfo}" />
                                                <Button
                                                    Margin="5,0,0,0"
                                                    Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                    CommandParameter="{Binding}"
                                                    Content="&#xf1f8;"
                                                    FontFamily="{StaticResource FontAwesome}"
                                                    Style="{StaticResource ButtonDanger}"
                                                    Visibility="{Binding IsSupper, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=True}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                    <Grid Grid.Row="3" Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Button
                            Grid.Column="0"
                            Width="320"
                            Height="40"
                            Command="{Binding SaveCommand}"
                            Content="保存"
                            Style="{StaticResource ButtonPrimary}" />
                        <Button
                            Grid.Column="1"
                            Width="320"
                            Height="40"
                            Command="{Binding CloseCommand}"
                            Content="取消" />
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
