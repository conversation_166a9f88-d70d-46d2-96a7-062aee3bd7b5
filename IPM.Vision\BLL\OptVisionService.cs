﻿using IPM.Vision.BLL.interfaces;
using IPM.Vision.Camera.Com;
using IPM.Vision.Common;
using IPM.Vision.LEvents;
using IPM.Vision.ViewModel.ObservableModel;
using OPTSDK_Net;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using static OPTSDK_Net.OPTDefine;

namespace IPM.Vision.BLL
{
    /// <summary>
    /// OPT相机服务类 - 负责OPT相机的连接、采集和实时画面显示
    /// 
    /// 实时画面显示工作流程：
    /// 1. RefreshCamera() -> 枚举并连接相机设备
    /// 2. ConnectCameraWithRetry() -> 创建句柄、打开设备、注册回调、启动采集
    /// 3. GrapCallBack() -> 相机帧回调，接收原始帧数据并缓存
    /// 4. ShowGrapAsync() -> 循环处理缓存帧，转换为BitmapSource并触发UI更新
    /// 5. ConvertFrameToBitmapSource() -> 像素格式转换，生成可显示的位图
    /// 6. UpdateGrappingBitmapSourceEvent -> 通知ViewModel更新UI显示
    /// 
    /// 关键修复点：
    /// - 确保连接成功后调用ShowGrapAsync启动显示循环
    /// - 优化帧回调处理，避免内存泄漏和时序问题
    /// - 增强错误处理和状态同步机制
    /// </summary>
    public class OptVisionService
    {
        private readonly NLogHelper _logger;
        private readonly ObservableGlobalState _globalState;
        private MyCamera _camera;
        public event Action NewCameraAddEvent;
        public event Action CameraDisConnectedEvent;
        private readonly EquipmentService _equipmentService;
        public event Action<WriteableBitmap> UpdateGrappingEvent;
        public event Action<BitmapSource> UpdateGrappingBitmapSourceEvent;
        public event Action<PictureModel> UpdatePictureModelEvent;
        public event Action FrameUpdateTrrigerEvent;
        public event Action RequestAutoTriggerSave; // 请求自动触发保存事件
        public event Func<int> GetActualPinIndex; // 获取当前实际引脚编号的委托
        public event Func<string> GetSerialNumber; // 获取用户输入的序列号的委托
        public event Func<string> GetOrderNumber; // 获取用户输入的订单号的委托
        public event Func<bool> ShouldSaveCurrentFrame; // 检查当前步骤是否需要保存OPT照片的委托

        private static OPT_FrameCallBack frameCallBack;
        private bool m_bStartGrab = false;
        private List<OPTDefine.OPT_Frame> m_frameList = new List<OPTDefine.OPT_Frame>(); // 图像缓存列表
        private object _lockObj = new object(); // 锁对象
        private OPT_Frame _currentFrame;
        private OPT_Frame _displayFrame; // 用于显示的帧
        private BitmapSource _currentDisplayBitmap; // 当前显示的位图
        private object _displayLockObj = new object(); // 显示锁对象
        private uint _deviceCount = 0;
        private bool _isConnect = false;
        // 移除了角度相关字段，旋转处理将在图片存储后进行
        // 简化状态控制，移除复杂的锁机制
        private volatile bool _isOperating = false;
        
        // 添加帧计数器用于调试
        private int _frameCount = 0;
        // ================= 共享缓冲区 / 位图 =================
        private IntPtr _convertBuffer = IntPtr.Zero; // SDK像素转换输出缓冲区
        private int _convertBufferSize = 0;          // 缓冲区大小
        private byte[] _rgbBuffer;                   // 托管RGB缓冲区
        private WriteableBitmap _sharedBitmap;       // 共用WriteableBitmap实例
        private int _bitmapWidth = 0;
        private int _bitmapHeight = 0;
        
        // ================= 统一帧处理队列（FIFO先进先出） =================
        private readonly ConcurrentQueue<UnifiedFrameTask> _unifiedFrameQueue = new ConcurrentQueue<UnifiedFrameTask>(); // 统一帧任务队列
        private readonly AutoResetEvent _frameEvent = new AutoResetEvent(false); // 帧到达信号
        private Task _unifiedProcessingTask; // 统一处理线程
        private CancellationTokenSource _unifiedCts = new CancellationTokenSource();
        private bool _isUnifiedProcessing = false;

        // 触发模式控制
        private bool _isTriggerMode = false; // true: 硬触发模式；false: 连续采集模式
        private DateTime _triggerModeSetTime = DateTime.MinValue; // 记录触发模式设置时间
        private readonly TimeSpan _triggerModeStabilizeDelay = TimeSpan.FromMilliseconds(100); // **修复：减少延迟到100ms，确保触发帧不被跳过**
        private int _triggerFrameCounter = 0; // **新增：触发帧计数器，用于识别真正的触发帧**
        
        // **文件命名计数器**
        private int _currentPinIndex = 0; // 当前引脚编号

        /// <summary>
        /// 标记当前是否处于硬触发模式（自动拍照阶段）
        /// </summary>
        public bool IsTriggerMode
        {
            get => _isTriggerMode;
            set
            {
                _isTriggerMode = value;
                if (value)
                {
                    _triggerModeSetTime = DateTime.Now;
                    _triggerFrameCounter = 0; // **修复：重置触发帧计数器**
                    AddLog($"触发模式已设置，稳定延迟: {_triggerModeStabilizeDelay.TotalMilliseconds}ms，重置帧计数器");
                }
                else
                {
                    AddLog($"触发模式已关闭，共处理触发帧: {_triggerFrameCounter}");
                }
            }
        }
        
        public OptVisionService(NLogHelper logger,ObservableGlobalState globalState, IEquipmentService equipmentService)
        {
            _logger = logger;
            _globalState = globalState;
            _equipmentService = (EquipmentService)equipmentService;
            // 移除了角度监听注册，旋转处理将在图片存储后进行

            // 启动统一帧处理任务（FIFO先进先出）
            StartUnifiedFrameProcessing();
        }

        // 移除了角度监听，旋转处理将在图片存储后进行

        private int _optResult = OPTDefine.OPT_OK;
        public int OptResult
        {
            get => _optResult;
            set
            {
                _optResult = value;
            }
        }

        public bool IsConnect { get => _isConnect; }

        public uint GetDeviceCount { get => _deviceCount; }

        public void InitSDK()
        {
            if(_camera == null) _camera = new MyCamera();
        }

        /// <summary>
        /// 根据官方demo重构的相机连接方法，解决初次连接问题
        /// **重要修复：改为通过设备兼容性测试识别OPT相机，不再依赖IP地址**
        /// 
        /// 设备识别策略：
        /// 1. 枚举所有GigE接口设备
        /// 2. 逐个尝试用OPT SDK创建句柄和打开设备
        /// 3. 只有OPT设备能成功通过OPT SDK的操作
        /// 4. 海康设备会在OPT SDK操作中失败，从而被过滤掉
        /// </summary>
        public void RefreshCamera()
        {
            if (_isOperating)
            {
                AddLog("相机操作正在进行中，跳过刷新请求", false);
                return;
            }
            
            _isOperating = true;
            
            try
            {
                AddLog("开始连接OPT相机设备...");
                
                // 1. 确保完全清理当前连接
                CompleteCleanup();
                
                // 2. 强制等待，确保SDK状态稳定
                Thread.Sleep(1000);
                
                // **关键修复：只枚举一次设备，避免与海康相机重复探测**
                // 3. 枚举设备（只枚举GigE接口，因为OPT和海康都是网线连接）
                OPTDefine.OPT_DeviceList deviceList = new OPTDefine.OPT_DeviceList();
                
                // **重要修复：只枚举GigE接口，因为OPT和海康相机都是网线连接**
                AddLog("正在枚举GigE接口的OPT相机设备...");
                OPTDefine.OPT_EInterfaceType interfaceTp = OPTDefine.OPT_EInterfaceType.interfaceTypeGige;
                
                OptResult = MyCamera.OPT_EnumDevices(ref deviceList, (uint)interfaceTp);
                if (OptResult != OPTDefine.OPT_OK)
                {
                    AddLog($"枚举GigE设备失败，错误码: {OptResult}", true);
                    return;
                }
                
                if (deviceList.nDevNum <= 0)
                {
                    AddLog("未发现任何GigE相机设备", true);
                    return;
                }
                
                AddLog($"发现 {deviceList.nDevNum} 个GigE设备，开始筛选OPT相机...");
                
                _deviceCount = deviceList.nDevNum;
                
                if (_deviceCount < 1)
                {
                    AddLog("未检测到OPT相机设备", true, ShowType.ALL, 3);
                    return;
                }
                
                // **关键修复：通过设备名称前缀查找OPT相机**
                int cameraIndex = FindOptCameraByModelName(deviceList);
                if (cameraIndex < 0)
                {
                    AddLog("未找到OPT开头的相机设备", true, ShowType.ALL, 3);
                    return;
                }
                
                AddLog($"选择OPT相机设备，索引: {cameraIndex}");
                
                // 5. 尝试连接相机（带重试机制，但减少重试次数避免长时间占用）
                if (!ConnectCameraWithRetry(cameraIndex))
                {
                    AddLog("相机连接失败，已尝试所有恢复方法", true, ShowType.ALL, 3);
                    // **关键修复：连接失败时发送状态指示器更新**
                    AddLog("OPT相机连接失败", true, ShowType.ICON, 4);
                    return;
                }
                
                AddLog($"相机采集已启动，连接完成！", show: ShowType.ALL);
                AddLog($"采集状态 - StartGrab: {m_bStartGrab}, IsConnect: {_isConnect}, 回调已注册: {(frameCallBack != null)}");
                
                // **关键修复：连接成功后立即诊断设备，确保连接的是OPT设备**
                DiagnoseCurrentDevice();
                
                // **关键修复：专门发送状态指示器更新事件，确保UI显示绿色状态**
                AddLog("OPT相机连接成功", false, ShowType.ICON, 1);
                
                NewCameraAddEvent?.Invoke();
            }
            catch (Exception ex)
            {
                AddLog($"相机连接异常: {ex.Message}", true, ShowType.ALL, 3);
                // **关键修复：连接异常时发送状态指示器更新**
                AddLog("OPT相机连接异常", true, ShowType.ICON, 4);
                _isConnect = false;
                CompleteCleanup();
            }
            finally
            {
                _isOperating = false;
            }
        }

        /// <summary>
        /// 通过IP地址和OPT SDK兼容性测试查找真正的OPT相机
        /// **关键修复：基于配置文件IP地址的精确设备识别，如果IP获取失败则回退到兼容性测试**
        ///
        /// 识别策略：
        /// 1. 优先通过IP地址匹配配置文件中的主相机IP
        /// 2. 如果无法获取IP地址，则通过OPT SDK兼容性测试验证
        /// 3. 绝对禁止连接Mark相机IP对应的设备
        /// </summary>
        /// <param name="deviceList">设备列表</param>
        /// <returns>OPT相机的索引，如果未找到返回-1</returns>
        private int FindOptCameraByModelName(OPTDefine.OPT_DeviceList deviceList)
        {
            try
            {
                AddLog("开始通过IP地址和SDK兼容性双重筛选真正的OPT设备...");

                // **获取配置文件中的IP地址**
                string mainCameraIP = _globalState?.AppConfig?.MainCameraAddress ?? "";
                string markCameraIP = _globalState?.AppConfig?.MarkCameraAddress ?? "";

                AddLog($"📋 配置信息:");
                AddLog($"  - 主相机IP (应为OPT): {mainCameraIP}");
                AddLog($"  - Mark相机IP (应为海康): {markCameraIP}");
                AddLog($"  - 主相机类型: {_globalState?.AppConfig?.MainCameraType}");

                bool useIPMatching = !string.IsNullOrEmpty(mainCameraIP);
                if (!useIPMatching)
                {
                    AddLog("⚠️ 主相机IP地址未配置，将使用兼容性测试模式");
                }

                // **关键策略：遍历所有GigE设备，通过IP地址精确匹配**
                for (int i = 0; i < deviceList.nDevNum; i++)
                {
                    AddLog($"测试设备 {i + 1}/{deviceList.nDevNum}...");

                    MyCamera testCamera = null;
                    bool isOptDevice = false;
                    string deviceIP = "未知IP";

                    try
                    {
                        testCamera = new MyCamera();

                        // **步骤1：创建设备句柄以获取设备信息**
                        AddLog($"  步骤1: 创建设备句柄获取设备信息...");
                        int createResult = testCamera.OPT_CreateHandle(OPTDefine.OPT_ECreateHandleMode.modeByIndex, i, "");

                        if (createResult != OPTDefine.OPT_OK)
                        {
                            AddLog($"  ❌ 设备 {i} 创建句柄失败 (错误码: {createResult}) - 跳过");
                            continue;
                        }

                        // **步骤2：先尝试打开设备进行兼容性测试**
                        AddLog($"  步骤2: OPT SDK兼容性测试 - 打开设备...");
                        int openResult = testCamera.OPT_Open();

                        if (openResult != OPTDefine.OPT_OK)
                        {
                            AddLog($"  ❌ 设备 {i} 打开失败 (错误码: {openResult}) - 可能不是OPT设备");
                            testCamera.OPT_DestroyHandle();
                            continue;
                        }

                        AddLog($"  ✅ 设备 {i} 打开成功，开始获取IP地址...");

                        // **步骤3：设备打开后获取IP地址**
                        AddLog($"  步骤3: 获取设备IP地址...");
                        bool ipMatched = false;
                        try
                        {
                            // 尝试通过OPT SDK获取设备IP地址（使用整数方式）
                            long ipValue = 0;
                            int ipIntResult = testCamera.OPT_GetIntFeatureValue("GevCurrentIPAddress", ref ipValue);
                            if (ipIntResult == OPTDefine.OPT_OK)
                            {
                                // 将整数IP转换为字符串格式
                                deviceIP = $"{(ipValue >> 24) & 0xFF}.{(ipValue >> 16) & 0xFF}.{(ipValue >> 8) & 0xFF}.{ipValue & 0xFF}";
                                AddLog($"  📋 设备 {i} IP地址: {deviceIP}");

                                // **步骤4：IP地址匹配验证**
                                AddLog($"  步骤4: IP地址匹配验证...");

                                // **关键过滤：绝对禁止Mark相机IP**
                                if (!string.IsNullOrEmpty(markCameraIP) && deviceIP.Equals(markCameraIP, StringComparison.OrdinalIgnoreCase))
                                {
                                    AddLog($"  🚫 绝对禁止：设备IP {deviceIP} 匹配Mark相机IP，强制跳过");
                                    testCamera.OPT_Close();
                                    testCamera.OPT_DestroyHandle();
                                    continue;
                                }

                                // **关键匹配：只连接主相机IP对应的设备**
                                if (!string.IsNullOrEmpty(mainCameraIP) && !deviceIP.Equals(mainCameraIP, StringComparison.OrdinalIgnoreCase))
                                {
                                    AddLog($"  ❌ 设备IP {deviceIP} 不匹配主相机IP {mainCameraIP}，跳过");
                                    testCamera.OPT_Close();
                                    testCamera.OPT_DestroyHandle();
                                    continue;
                                }

                                AddLog($"  ✅ 设备IP {deviceIP} 匹配主相机IP {mainCameraIP}");
                                ipMatched = true;
                            }
                            else
                            {
                                AddLog($"  ⚠️ 无法获取设备IP地址，错误码: {ipIntResult}");
                                deviceIP = $"设备_{i}";

                                if (useIPMatching)
                                {
                                    AddLog($"  ❌ IP匹配模式下无法获取设备IP，跳过此设备");
                                    testCamera.OPT_Close();
                                    testCamera.OPT_DestroyHandle();
                                    continue;
                                }
                                else
                                {
                                    AddLog($"  ⚠️ 兼容性测试模式，继续进行OPT特有参数测试");
                                    ipMatched = true;
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            AddLog($"  ⚠️ 获取设备IP地址异常: {ex.Message}");
                            deviceIP = $"设备_{i}";

                            if (useIPMatching)
                            {
                                AddLog($"  ❌ IP匹配模式下获取IP异常，跳过此设备");
                                testCamera.OPT_Close();
                                testCamera.OPT_DestroyHandle();
                                continue;
                            }
                            else
                            {
                                AddLog($"  ⚠️ 兼容性测试模式，继续进行OPT特有参数测试");
                                ipMatched = true;
                            }
                        }

                        if (!ipMatched)
                        {
                            continue; // IP不匹配，跳过此设备
                        }

                        // **步骤5：测试OPT特有参数访问**
                        AddLog($"  步骤5: 测试OPT特有参数访问...");
                        try
                        {
                            // 尝试读取曝光时间参数（OPT相机特有）
                            double exposureTime = 0;
                            int expResult = testCamera.OPT_GetDoubleFeatureValue("ExposureTime", ref exposureTime);

                            if (expResult == OPTDefine.OPT_OK)
                            {
                                AddLog($"  ✅ 设备 {i} (IP: {deviceIP}) 成功读取OPT参数 (曝光时间: {exposureTime}μs)");
                                isOptDevice = true; // 通过所有测试，确认是OPT设备
                            }
                            else
                            {
                                AddLog($"  ❌ 设备 {i} (IP: {deviceIP}) 无法读取OPT参数 (错误码: {expResult})");
                            }
                        }
                        catch (Exception ex)
                        {
                            AddLog($"  ❌ 设备 {i} (IP: {deviceIP}) 参数访问异常: {ex.Message}");
                        }

                        // **安全关闭测试设备**
                        try
                        {
                            testCamera.OPT_Close();
                            testCamera.OPT_DestroyHandle();
                        }
                        catch (Exception ex)
                        {
                            AddLog($"  关闭测试设备异常: {ex.Message}", false);
                        }

                        // 如果通过所有测试，找到了OPT设备
                        if (isOptDevice)
                        {
                            AddLog($"🎯 找到并验证真正的OPT设备: IP {deviceIP} (索引: {i})");
                            return i;
                        }
                        else
                        {
                            AddLog($"  ❌ 设备 {i} (IP: {deviceIP}) 未通过完整的OPT兼容性测试");
                        }
                    }
                    catch (Exception ex)
                    {
                        AddLog($"  ❌ 设备 {i} (IP: {deviceIP}) 测试过程异常: {ex.Message}");

                        // 确保清理测试设备
                        try
                        {
                            if (testCamera != null)
                            {
                                testCamera.OPT_Close();
                                testCamera.OPT_DestroyHandle();
                            }
                        }
                        catch { }
                    }
                    finally
                    {
                        // 设备间测试间隔，避免冲突
                        System.Threading.Thread.Sleep(500);
                    }
                }

                AddLog($"❌ 未找到任何通过IP地址和兼容性双重筛选的OPT设备", true);
                AddLog($"筛选结果分析：", true);
                AddLog($"  1. 主相机IP {mainCameraIP} 对应的设备可能不是OPT相机", true);
                AddLog($"  2. OPT相机IP地址配置可能不正确", true);
                AddLog($"  3. OPT相机存在但无法正常通信", true);
                AddLog($"  4. 建议检查配置文件中的主相机IP地址设置", true);

                return -1; // 未找到OPT设备
            }
            catch (Exception ex)
            {
                AddLog($"OPT设备筛选过程异常: {ex.Message}", true);
                return -1;
            }
        }
        
        /// <summary>
        /// 启动统一帧处理任务 - FIFO先进先出处理所有帧
        /// **关键改进：单一队列，统一处理显示和保存，确保不丢帧**
        /// </summary>
        private void StartUnifiedFrameProcessing()
        {
            if (_isUnifiedProcessing)
                return;
            
            _isUnifiedProcessing = true;
            _unifiedCts = new CancellationTokenSource();
            
            _unifiedProcessingTask = Task.Run(async () =>
            {
                try
                {
                    AddLog("📋 统一帧处理线程启动 - FIFO先进先出模式", false);
                    int processedFrameCount = 0;
                    
                    while (_isUnifiedProcessing && !_unifiedCts.Token.IsCancellationRequested)
                    {
                        try
                        {
                            // 等待帧到达信号或超时
                            if (_unifiedFrameQueue.IsEmpty)
                            {
                                _frameEvent.WaitOne(5); // 5ms超时，确保响应性
                            continue;
                            }
                        
                            // FIFO方式取出帧任务
                            if (_unifiedFrameQueue.TryDequeue(out UnifiedFrameTask frameTask))
                            {
                                processedFrameCount++;
                                await ProcessUnifiedFrameTask(frameTask);
                                
                                // 定期GC，但频率降低
                                if (processedFrameCount % 300 == 0)
                                {
                                    GC.Collect();
                                    GC.WaitForPendingFinalizers();
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError($"[UnifiedProcessing] 帧处理异常: {ex.Message}");
                        }
                    }
                    
                    AddLog($"📋 统一帧处理线程已停止，共处理帧数: {processedFrameCount}", false);
                }
                catch (OperationCanceledException)
                {
                    AddLog("📋 统一帧处理线程被取消", false);
                }
                catch (Exception ex)
                {
                    AddLog($"📋 统一帧处理线程异常: {ex.Message}", true);
                }
                finally
                {
                    _isUnifiedProcessing = false;
                }
            });
        }
        
        /// <summary>
        /// 停止统一帧处理任务
        /// </summary>
        private void StopUnifiedFrameProcessing()
        {
            _isUnifiedProcessing = false;
            _unifiedCts?.Cancel();
            _frameEvent.Set(); // 唤醒等待线程
        }

        #region 统一帧处理核心方法
        
        /// <summary>
        /// 处理统一帧任务 - FIFO先进先出的核心处理逻辑
        /// **关键改进：在一个方法中同时处理显示和保存，确保顺序一致**
        /// </summary>
        /// <param name="frameTask">帧任务</param>
        /// <returns></returns>
        private async Task ProcessUnifiedFrameTask(UnifiedFrameTask frameTask)
        {
            var startTime = DateTime.Now;
            BitmapSource convertedBitmap = null;
            
            try
            {
                                 // **步骤1：转换帧数据为BitmapSource（显示和保存共用）**
                 var frameData = frameTask.FrameData;
                 convertedBitmap = await Task.Run(() => ConvertFrameToIndependentBitmapSource(ref frameData));
                
                if (convertedBitmap == null)
                {
                    _logger.LogError($"[FIFO] 帧转换失败 - 序号: {frameTask.FrameSequence}");
                    return;
                }
                
                // 确保位图冻结，可跨线程使用
                if (!convertedBitmap.IsFrozen)
                {
                    convertedBitmap.Freeze();
                }

                // **步骤2：处理显示（如果需要）**
                if (frameTask.NeedDisplay)
                    UpdateDisplayImage(convertedBitmap);

                // **步骤3：处理保存（如果需要）**
                if (frameTask.NeedSave)
                 {
                    // **修复：优先使用外部传入的文件名和文件夹，确保引脚编号正确**
                    string actualFolderName = !string.IsNullOrEmpty(frameTask.FolderName) ? frameTask.FolderName : GenerateFolderName();
                    string actualFileName = !string.IsNullOrEmpty(frameTask.FileName) ? frameTask.FileName : GenerateFileName();

                    // **调试日志：显示文件名来源**
                    if (!string.IsNullOrEmpty(frameTask.FileName))
                    {
                        AddLog($"🔢 使用外部传入的文件名: {actualFileName} (正确的引脚编号)");
                    }
                    else
                    {
                        AddLog($"⚠️ 使用内部生成的文件名: {actualFileName} (可能引脚编号不正确)");
                    }
                    
                    bool saveSuccess = await Task.Run(() => SaveBitmapSourceToFile(convertedBitmap, actualFolderName, actualFileName));
                    
                    var elapsed = (DateTime.Now - startTime).TotalMilliseconds;

                    // 自动运行模式的保存日志
                    _logger.LogInfo($"[FIFO-AutoRun] 自动运行帧处理完成 - 序号: {frameTask.FrameSequence}, 文件: {actualFileName}, 耗时: {elapsed:F1}ms, 保存: {(saveSuccess ? "成功" : "失败")}");
                    AddLog($"📸 自动保存: {actualFileName} - {(saveSuccess ? "成功" : "失败")}");
                    
                    if (elapsed > 8)
                    {
                        _logger.LogInfo($"[FIFO] 帧处理耗时过长 - 序号: {frameTask.FrameSequence}, 耗时: {elapsed:F1}ms");
                    }
                }
                else if (frameTask.NeedDisplay)
                {
                    // 仅显示帧的处理
                    var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                    if (elapsed > 5)
                    {
                        _logger.LogInfo($"[FIFO-Display] 显示帧处理耗时: {elapsed:F1}ms");
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                _logger.LogError($"[FIFO] 统一帧处理异常 - 序号: {frameTask.FrameSequence}, 耗时: {elapsed:F1}ms, 异常: {ex.Message}");
                            }
                            finally
                            {
                 // **步骤4：清理帧数据**
                 var frameDataToRelease = frameTask.FrameData;
                 if (frameDataToRelease.pData != IntPtr.Zero)
                                {
                     ReleaseFrameCopy(ref frameDataToRelease);
                }
             }
        }

        /// <summary>
        /// 清空统一帧队列并释放内存
        /// </summary>
        public void ClearUnifiedFrameQueue()
        {
            try
            {
                int clearedCount = 0;
                                 while (_unifiedFrameQueue.TryDequeue(out UnifiedFrameTask task))
                {
                     var frameDataToRelease = task.FrameData;
                     if (frameDataToRelease.pData != IntPtr.Zero)
                    {
                         ReleaseFrameCopy(ref frameDataToRelease);
                    }
                     clearedCount++;
                 }
                AddLog($"📋 统一帧队列已清空，清理了{clearedCount}个任务", false);
            }
            catch (Exception ex)
            {
                AddLog($"清空统一帧队列异常: {ex.Message}", true);
            }
        }
        
        #endregion
        
        /// <summary>
        /// 清除所有未处理的拍照任务队列（用于强制停止时清理）
        /// </summary>
        public void ClearPendingTasks()
        {
            try
            {
                // 直接清空统一帧队列
                ClearUnifiedFrameQueue();

                AddLog("OPT相机统一帧队列已清空，切换为实时显示模式");
            }
            catch (Exception ex)
            {
                AddLog($"清除OPT相机统一帧队列时异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 获取当前队列状态信息（用于监控高频触发下的队列健康状况）
        /// </summary>
        /// <returns>队列状态信息</returns>
        public string GetQueueStatus()
        {
            try
            {
                int unifiedQueueCount = _unifiedFrameQueue.Count;
                bool isProcessing = _isUnifiedProcessing;

                return $"统一帧队列:{unifiedQueueCount}, 处理中:{isProcessing}, 触发模式:{_isTriggerMode}";
            }
            catch
            {
                return "队列状态获取失败";
            }
        }

        /// <summary>
        /// 检查队列健康状况，防止高频触发导致的积压
        /// </summary>
        public void CheckQueueHealth()
        {
            try
            {
                int queueCount = _unifiedFrameQueue.Count;

                if (queueCount > 30)
                {
                    _logger.LogInfo($"[QueueHealth] 统一帧队列积压严重: {queueCount}个任务");
                    AddLog($"⚠️ 统一队列积压警告: {queueCount}个任务待处理", true);
                }
                else if (queueCount > 10)
                {
                    _logger.LogInfo($"[QueueHealth] 统一帧队列轻微积压: {queueCount}个任务");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"检查统一队列健康状况异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清空帧处理队列并释放帧内存（停止流程时调用）
        /// **已重构：使用统一帧队列替代原来的帧队列**
        /// </summary>
        public void ClearFrameQueue()
        {
            try
            {
                // 直接调用统一帧队列清理方法
                ClearUnifiedFrameQueue();
            }
            catch (Exception ex)
            {
                AddLog($"清空帧队列异常: {ex.Message}", true);
            }
        }
        


        /// <summary>
        /// 将保存图像任务添加到队列 - 已重构为使用统一FIFO队列
        /// **关键改进：不再需要预先捕获帧，而是等待下一个帧到达时直接处理**
        /// </summary>
        private void EnqueueSaveImageTask(string folderName, string fileName)
        {
            // 在硬触发（自动拍照）模式下，记录触发信息，便于排查照片丢失问题
            if (_isTriggerMode)
            {
                _logger.LogInfo($"[Trigger] 触发统一队列任务 -> Folder: {folderName}, File: {fileName}");

                // 检查队列长度，防止积压过多任务
                if (_unifiedFrameQueue.Count > 50) // 设置队列上限
                {
                    _logger.LogInfo($"[Trigger] 统一队列任务过多({_unifiedFrameQueue.Count})，可能存在处理瓶颈");
                }
            }

            // **关键改进：不再在这里创建任务，而是标记下一个帧需要保存**
            // 这将在GrapCallBack中的帧到达时处理，确保使用最新的帧数据
            AddLog($"📋 已标记下一帧需要保存: {fileName}");
        }

        // 移除了未实现的图像处理、分析和压缩方法，只保留存储功能
        
        /// <summary>
        /// 异步保存图像 - 跨线程修复版本，支持原始帧数据处理
        /// **关键修复：解决PLC触发模式下的跨线程访问问题**
        /// </summary>
        private async Task SaveImageAsync(CameraTask task)
        {
            var startTime = DateTime.Now;
            string folderName = task.FolderName;
            string fileName = task.FileName;

            try
            {
                BitmapSource bitmapToSave = null;

                // **优先级1：使用原始帧数据进行安全转换（PLC触发模式主要路径）**
                if (task.FrameData.HasValue)
                {
                    _logger.LogInfo($"[PLCTrigger] 使用原始帧数据进行转换: {fileName}");
                    
                    try
                    {
                        var frameData = task.FrameData.Value;
                        // 在后台线程中安全转换，避免跨线程问题
                        bitmapToSave = await Task.Run(() => ConvertFrameToIndependentBitmapSource(ref frameData)).ConfigureAwait(false);
                        
                        if (bitmapToSave != null)
                        {
                            // 确保位图被冻结，可以跨线程访问
                            if (!bitmapToSave.IsFrozen)
                            {
                                bitmapToSave.Freeze();
                            }
                            _logger.LogInfo($"[PLCTrigger] 原始帧数据转换成功: {fileName}");
                        }
                        else
                        {
                            _logger.LogError($"[PLCTrigger] 原始帧数据转换失败: {fileName}");
                        }
                        
                        // 释放原始帧数据
                        var frameToRelease = frameData;
                        if (frameToRelease.pData != IntPtr.Zero)
                        {
                            ReleaseFrameCopy(ref frameToRelease);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"[PLCTrigger] 原始帧数据转换异常: {ex.Message}, 文件: {fileName}");
                    }
                }
                // **优先级2：使用已转换的图像数据（连续模式主要路径）**
                else if (task.ImageData != null)
                {
                    bitmapToSave = task.ImageData;
                    _logger.LogInfo($"[ContinuousMode] 使用预转换图像数据: {fileName}");
                }

                // **执行保存操作**
                if (bitmapToSave != null)
                {
                    await Task.Run(() => SaveBitmapSourceToFile(bitmapToSave, folderName, fileName)).ConfigureAwait(false);

                    var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                    if (elapsed > 8) // 10ms触发，保存应在8ms内完成
                    {
                        _logger.LogInfo($"[Save] 保存耗时过长: {elapsed:F1}ms, 文件: {fileName}");
                    }
                    else
                    {
                        _logger.LogInfo($"[Save] 保存完成: {elapsed:F1}ms, 文件: {fileName}");
                    }
                    return;
                }

                // **回退方案：跳过显示位图回退，直接失败**
                // 原因：显示位图可能也有跨线程访问问题，PLC触发模式下应该依赖原始帧数据
                _logger.LogError($"[Fallback] 任务无有效数据，PLC触发模式下不使用显示位图回退: {fileName}");

                // **最终失败**
                _logger.LogError($"[SaveFailed] 保存失败，无任何可用图像数据: {fileName}");
                AddLog($"PLC触发保存失败，无有效帧数据 - {fileName}", true);
            }
            catch (Exception ex)
            {
                var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                _logger.LogError($"[SaveException] 保存异常: {ex.Message}, 耗时: {elapsed:F1}ms, 文件: {fileName}");
                AddLog($"PLC触发保存异常 - {fileName}: {ex.Message}", true);
            }
        }
        /// <summary>
        /// 完全清理资源，确保干净的状态
        /// **重要修复：简化清理逻辑，避免与海康相机SDK冲突**
        /// </summary>
        private void CompleteCleanup()
        {
            try
            {
                // 停止采集
                m_bStartGrab = false;
                
                if (_camera != null)
                {
                    try
                    {
                        _camera.OPT_Close(); // 修改为正确的API调用
                    }
                    catch { }
                    
                    try
                    {
                        _camera.OPT_DestroyHandle();
                    }
                    catch { }
                }
                
                // 清理帧缓存
                ClearFrameBuffers();

                // 释放像素转换缓冲区
                if (_convertBuffer != IntPtr.Zero)
                {
                    Marshal.FreeHGlobal(_convertBuffer);
                    _convertBuffer = IntPtr.Zero;
                    _convertBufferSize = 0;
                }
                _rgbBuffer = null;
                _sharedBitmap = null;
                
                // 重置连接状态
                _isConnect = false;
                
                // **修复：移除强制释放所有设备的调用，避免重复枚举和冲突**
                // ForceReleaseAllOptDevices(); // 注释掉，避免与海康相机冲突
                
                // 重新创建相机对象
                _camera = new MyCamera();
                
                // 重新注册回调
                frameCallBack = new OPT_FrameCallBack(GrapCallBack);
            }
            catch (Exception ex)
            {
                AddLog($"清理资源异常: {ex.Message}", true);
            }
        }

        private void ClearFrameBuffers()
        {
            try
            {
                AddLog("开始清理OPT相机帧缓存...", false);
                
                lock (_lockObj)
                {
                    // **关键修复：先停止所有帧处理活动**
                    // 清理帧列表 - 优化版本，不需要创建副本
                    for (int i = m_frameList.Count - 1; i >= 0; i--)
                    {
                        try
                        {
                            var frame = m_frameList[i];
                            if (frame.pData != IntPtr.Zero && _camera != null)
                            {
                                _camera.OPT_ReleaseFrame(ref frame);
                            }
                        }
                        catch (Exception ex)
                        {
                            AddLog($"释放帧 {i} 时异常: {ex.Message}", false);
                        }
                    }
                    
                    m_frameList.Clear();
                    
                    // **关键修复：清理当前帧引用**
                    if (_currentFrame.pData != IntPtr.Zero && _camera != null)
                    {
                        try
                        {
                            _camera.OPT_ReleaseFrame(ref _currentFrame);
                        }
                        catch (Exception ex)
                        {
                            AddLog($"释放当前帧时异常: {ex.Message}", false);
                        }
                    }
                    
                    _currentFrame = new OPT_Frame();
                }
                
                lock (_displayLockObj)
                {
                    // **关键修复：清理显示相关的资源**
                    if (_displayFrame.pData != IntPtr.Zero && _camera != null)
                    {
                        try
                        {
                            _camera.OPT_ReleaseFrame(ref _displayFrame);
                        }
                        catch (Exception ex)
                        {
                            AddLog($"释放显示帧时异常: {ex.Message}", false);
                        }
                    }
                    
                    _displayFrame = new OPT_Frame();
                    
                    // **关键修复：显式清空位图引用，帮助GC回收大对象**
                    _currentDisplayBitmap = null;
                }
                
                // **关键修复：重置帧计数器**
                _frameCount = 0;
                
                // **关键修复：强制垃圾回收，清理大量图像内存**
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect(); // 二次回收，确保清理完成
                
                AddLog("OPT相机帧缓存清理完成", false);
            }
            catch (Exception ex)
            {
                AddLog($"清理帧缓存时发生异常: {ex.Message}", true);
            }
        }

        // **移除FindTargetCamera方法，简化逻辑避免复杂设备查找**
        // private int FindTargetCamera(OPTDefine.OPT_DeviceList deviceList) 方法已移除
        // 现在直接使用第一个可用设备，避免设备IP解析导致的冲突

        private void GrapCallBack(ref OPTDefine.OPT_Frame frame, IntPtr pUser)
        {
            try
            {
                // **关键修复：验证帧数据有效性**
                if (frame.pData == IntPtr.Zero || frame.frameInfo.size <= 0)
                {
                    return; // 跳过无效帧
                }

                // **关键修复：检查采集状态，确保只处理有效帧**
                if (!m_bStartGrab || !_isConnect)
                {
                    return; // 如果已停止采集，直接返回
                }

                // **关键修复：增加帧计数器用于统计**
                _frameCount++;

                // **PLC硬件触发逻辑：在触发模式下，每个回调帧都是PLC硬件触发的结果**
                // **修正逻辑：检查系统是否在自动运行，并且当前步骤是否需要保存OPT照片**
                bool isAutoRunning = _globalState?.IsRunning ?? false;
                bool shouldSaveFrame = false;

                // **关键修复：只有在需要保存的步骤才保存OPT照片**
                if (isAutoRunning)
                {
                    shouldSaveFrame = ShouldSaveCurrentFrame?.Invoke() ?? false;
                }

                try
                {
                    // **PLC硬件触发模式：帧到达时根据当前步骤决定是否保存**
                    var unifiedTask = new UnifiedFrameTask
                    {
                        FrameData = CreateFrameCopy(ref frame),
                        NeedDisplay = true,                    // 所有帧都需要显示
                        NeedSave = shouldSaveFrame,            // 只有在拍照步骤才保存
                        FolderName = "",                       // 将通过RequestAutoTriggerSave事件获取
                        FileName = "",                         // 将通过RequestAutoTriggerSave事件获取
                        IsTriggerFrame = shouldSaveFrame,      // 只有需要保存的帧才是有效触发帧
                        FrameSequence = _frameCount,
                        Timestamp = DateTime.Now
                    };

                    // **PLC硬件触发：只有在需要保存时才触发事件获取文件名**
                    if (shouldSaveFrame)
                    {
                        RequestAutoTriggerSave?.Invoke();
                    }

                    // 将统一任务加入FIFO队列
                    _unifiedFrameQueue.Enqueue(unifiedTask);
                        _frameEvent.Set();

                    if (shouldSaveFrame)
                    {
                        _triggerFrameCounter++;
                        _logger.LogInfo($"[AutoRun] 自动运行模式帧 #{_triggerFrameCounter}, 总帧计数: {_frameCount} - 需要保存");
                    }
                    else if (isAutoRunning)
                    {
                        _logger.LogInfo($"[AutoRun] 自动运行模式帧，总帧计数: {_frameCount} - 当前步骤不需要保存OPT照片");
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"帧处理异常: {ex.Message}", true);
                }

                // 定期触发 GC（降低频率）
                if (_frameCount % 600 == 0)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
            }
            catch (Exception ex)
            {
                AddLog($"帧回调处理异常: {ex.Message}", true);
            }
        }

        
        
        public void CloseTrigger()
        {
            try
            {
                var result = _camera.OPT_SetEnumFeatureSymbol("TriggerMode", "Off");
                if (result == OPTDefine.OPT_OK)
                {
                    AddLog("OPT相机触发模式已关闭");
                    // 重置触发模式相关状态
                    _triggerModeSetTime = DateTime.MinValue;
                }
                else
                {
                    AddLog($"关闭触发模式失败，错误码: {result}", true);
                }
            }
            catch (Exception ex)
            {
                AddLog($"关闭触发模式异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 设置线触发配置（用于PLC硬件触发）- 修复版本，确保帧缓存正确更新
        /// </summary>
        /// <returns>触发模式配置是否成功</returns>
        public bool setLineTriggerConf()
        {
            try
            {
                AddLog("正在设置OPT相机线触发模式...");

                // **关键修复：清空所有帧缓存，确保不会使用旧帧**
                AddLog("清空连续采集的残留帧和缓存...");
                ClearFrameQueue();
                
                // **修复：清空当前显示帧，防止使用旧的显示内容**
                lock (_displayLockObj)
                {
                    _currentDisplayBitmap = null;
                }
                
                // **修复：重置帧计数器，从新开始计数**
                _frameCount = 0;
                _triggerFrameCounter = 0;

                // **修复：减少等待时间，避免过长延迟**
                AddLog("等待实时画面完全停止...");
                Thread.Sleep(100); // 减少到100ms

                // 启用触发模式
                var result = _camera.OPT_SetEnumFeatureSymbol("TriggerMode", "On");
                if (result != OPTDefine.OPT_OK)
                {
                    AddLog($"启用触发模式失败，错误码: {result} ({GetOptErrorDescription(result)})", true);
                    return false;
                }

                // 设置触发选择器为帧开始
                result = _camera.OPT_SetEnumFeatureSymbol("TriggerSelector", "FrameStart");
                if (result != OPTDefine.OPT_OK)
                {
                    AddLog($"设置触发选择器失败，错误码: {result} ({GetOptErrorDescription(result)})", true);
                    return false;
                }

                // 设置触发源为Line1（硬件触发）
                result = _camera.OPT_SetEnumFeatureSymbol("TriggerSource", "Line1");
                if (result != OPTDefine.OPT_OK)
                {
                    AddLog($"设置触发源失败，错误码: {result} ({GetOptErrorDescription(result)})", true);
                    return false;
                }

                // **新增：设置触发激活方式为上升沿**
                result = _camera.OPT_SetEnumFeatureSymbol("TriggerActivation", "RisingEdge");
                if (result != OPTDefine.OPT_OK)
                {
                    AddLog($"设置触发激活方式失败，错误码: {result} ({GetOptErrorDescription(result)})", false); // 这个不是严重错误
                }

                // **关键修复：等待触发模式设置生效**
                Thread.Sleep(50);

                AddLog("✅ OPT相机线触发模式设置完成");
                AddLog($"📊 触发模式状态 - 帧计数器已重置，等待硬件触发信号");
                
                return true;
            }
            catch (Exception ex)
            {
                AddLog($"设置线触发配置时异常: {ex.Message}", true);
                return false;
            }
        }

        /// <summary>
        /// 设置软触发配置（用于TestWindow手动触发测试）
        /// 与线触发的区别是TriggerSource设置为Software而不是Line1
        /// </summary>
        public void SetSoftwareTriggerConf()
        {
            try
            {
                AddLog("正在设置OPT相机软触发模式...");
                
                // 启用触发模式
                var result = _camera.OPT_SetEnumFeatureSymbol("TriggerMode", "On");
                if (result != OPTDefine.OPT_OK)
                {
                    AddLog($"启用触发模式失败，错误码: {result}", true);
                    return;
                }
                
                // 设置触发选择器为帧开始
                result = _camera.OPT_SetEnumFeatureSymbol("TriggerSelector", "FrameStart");
                if (result != OPTDefine.OPT_OK)
                {
                    AddLog($"设置触发选择器失败，错误码: {result}", true);
                    return;
                }

                // 设置触发源为软件触发
                result = _camera.OPT_SetEnumFeatureSymbol("TriggerSource", "Software");
                if (result != OPTDefine.OPT_OK)
                {
                    AddLog($"设置触发源为Software失败，错误码: {result}", true);
                    return;
                }
                
                // 设置触发激活方式（软触发模式下此参数可能不重要，但保持一致性）
                result = _camera.OPT_SetEnumFeatureSymbol("TriggerActivation", "RisingEdge");
                if (result != OPTDefine.OPT_OK)
                {
                    AddLog($"设置触发激活方式失败，错误码: {result}", true);
                    return;
                }
                
                
                
                AddLog("OPT相机软触发模式设置成功");
            }
            catch (Exception ex)
            {
                AddLog($"设置OPT相机软触发配置时异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 执行软触发命令 - 手动触发相机拍摄一帧
        /// 用于TestWindow中的手动测试功能，模拟软件触发拍照
        /// 
        /// 使用说明：
        /// 1. 调用此方法前需要先调用SetSoftwareTriggerConf()设置软触发模式
        /// 2. 确保相机已连接并处于采集状态（m_bStartGrab = true）
        /// 3. 此方法会立即触发一次拍照，照片通过现有的帧回调机制处理
        /// </summary>
        /// <returns>触发是否成功</returns>
        public bool ExecuteSoftwareTrigger()
        {
            try
            {
                // 检查相机连接状态
                if (!_isConnect || !m_bStartGrab)
                {
                    AddLog("OPT相机未连接或未开始采集，无法执行软触发", true);
                    return false;
                }
                
                AddLog("执行OPT相机软触发命令...");
                
                // 使用OPT SDK的命令执行接口发送软触发命令
                // "TriggerSoftware"是GigE Vision标准中的软触发命令名称
                int result = _camera.OPT_ExecuteCommandFeature("TriggerSoftware");
                
                if (result == OPTDefine.OPT_OK)
                {
                    AddLog("OPT相机软触发执行成功，等待拍照回调...");
                    return true;
                }
                else
                {
                    AddLog($"OPT相机软触发执行失败，错误码: {result} ({GetOptErrorDescription(result)})", true);
                    return false;
                }
            }
            catch (Exception ex)
            {
                AddLog($"执行OPT相机软触发时异常: {ex.Message}", true);
                return false;
            }
        }

        /// <summary>
        /// 软触发拍照方法 - 专门用于TestWindow测试
        /// 结合软触发和保存功能，直接触发并保存照片
        /// </summary>
        /// <param name="folderName">保存文件夹</param>
        /// <param name="pictureName">照片名称</param>
        /// <returns>是否成功触发</returns>
        public bool SoftwareTriggerAndSave(string folderName, string pictureName)
        {
            try
            {
                AddLog($"开始软触发拍照并保存: {pictureName}");
                
                // 首先执行软触发
                if (!ExecuteSoftwareTrigger())
                {
                    AddLog("软触发失败，取消拍照保存", true);
                    return false;
                }
                
                // 将保存任务加入队列（通过帧回调处理）
                EnqueueSaveImageTask(folderName, pictureName);
                
                AddLog($"软触发拍照任务已启动: {pictureName}");
                return true;
            }
            catch (Exception ex)
            {
                AddLog($"软触发拍照时异常: {ex.Message}", true);
                return false;
            }
        }

        /// <summary>
        /// 更新PLC硬件触发任务的文件夹和文件名
        /// **PLC触发流程：PLC触发相机 → 相机拍摄 → 回调产生任务 → 设置保存路径**
        /// </summary>
        /// <param name="folderName">保存文件夹</param>
        /// <param name="fileName">文件名</param>
        /// <returns>是否成功更新</returns>
        public bool UpdateHardwareTriggerTask(string folderName, string fileName)
        {
            try
            {
                // **重构：查找统一队列中最新的PLC触发任务**
                var updatedTasks = new List<UnifiedFrameTask>();
                bool found = false;
                int updatedCount = 0;

                // 将队列中的任务全部取出，更新需要的任务，然后重新入队
                while (_unifiedFrameQueue.TryDequeue(out UnifiedFrameTask task))
                {
                    if (task.IsTriggerFrame && task.NeedSave && 
                        string.IsNullOrEmpty(task.FolderName) && 
                        string.IsNullOrEmpty(task.FileName))
                {
                        // 更新PLC触发任务的保存信息
                        task.FolderName = folderName;
                        task.FileName = fileName;
                        found = true;
                        updatedCount++;
                    }
                    updatedTasks.Add(task);
                }

                // 将所有任务重新入队（保持FIFO顺序）
                foreach (var task in updatedTasks)
                    {
                    _unifiedFrameQueue.Enqueue(task);
                    }

                if (found)
                {
                    _logger.LogInfo($"[PLCTriggerTask] PLC触发任务已更新 - 文件夹: {folderName}, 文件名: {fileName}, 更新数量: {updatedCount}, 队列长度: {_unifiedFrameQueue.Count}");
                    return true;
                }
                else
                {
                    _logger.LogInfo($"[PLCTriggerTask] 未找到需要更新的PLC触发任务 - 队列长度: {_unifiedFrameQueue.Count}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"[PLCTriggerTask] 更新PLC触发任务异常: {ex.Message}");
                return false;
            }
        }



      
        

        /// <summary>
        /// 设置曝光
        /// </summary>
        /// <param name="param"></param>
        public void SetExporse(double param)
        {
            OptResult = _camera.OPT_SetDoubleFeatureValue("ExposureTime", param);
        }

        /// <summary>
        /// 后台线程安全的帧转换方法 - 专门用于解决跨线程访问问题
        /// **关键特性：完全独立于UI线程，不使用任何共享的WPF对象**
        /// </summary>
        /// <param name="frame">输入帧</param>
        /// <returns>转换后的BitmapSource（已冻结，可跨线程访问）</returns>
        private BitmapSource ConvertFrameToIndependentBitmapSource(ref OPT_Frame frame)
        {
            try
            {
                // **详细的帧数据有效性检查**
                if (frame.pData == IntPtr.Zero)
                {
                    _logger.LogError($"帧数据指针无效: pData = IntPtr.Zero");
                    return null;
                }

                if (frame.frameInfo.size <= 0)
                {
                    _logger.LogError($"帧数据大小无效: size = {frame.frameInfo.size}");
                    return null;
                }

                if (_camera == null)
                {
                    _logger.LogError($"相机对象为空，无法进行像素转换");
                    return null;
                }

                int width = (int)frame.frameInfo.width;
                int height = (int)frame.frameInfo.height;
                int rgbSize = width * height * 3;

             

                // 基础检查
                if (rgbSize <= 0 || rgbSize > 200 * 1024 * 1024)
                {
                    _logger.LogError($"帧数据大小异常 - 宽度: {width}, 高度: {height}, 总大小: {rgbSize} bytes");
                    return null;
                }

                // **关键改进：使用局部内存分配，避免共享资源**
                IntPtr convertBuffer = IntPtr.Zero;
                byte[] rgbBuffer = null;

                try
                {
                    // 分配独立的转换缓冲区
                    convertBuffer = Marshal.AllocHGlobal(rgbSize);
                    rgbBuffer = new byte[rgbSize];

                    // ----------- 像素格式转换 -----------
                    OPTDefine.OPT_PixelConvertParam cvt = new OPTDefine.OPT_PixelConvertParam
                    {
                        nWidth = frame.frameInfo.width,
                        nHeight = frame.frameInfo.height,
                        ePixelFormat = frame.frameInfo.pixelFormat,
                        pSrcData = frame.pData,
                        nSrcDataLen = frame.frameInfo.size,
                        nPaddingX = frame.frameInfo.paddingX,
                        nPaddingY = frame.frameInfo.paddingY,
                        eBayerDemosaic = OPTDefine.OPT_EBayerDemosaic.demosaicNearestNeighbor,
                        eDstPixelFormat = OPTDefine.OPT_EPixelType.gvspPixelRGB8,
                        pDstBuf = convertBuffer,
                        nDstBufSize = (uint)rgbSize
                    };

                    int ret = _camera.OPT_PixelConvert(ref cvt);
                    if (ret != OPTDefine.OPT_OK)
                    {
                        _logger.LogError($"像素转换失败详情:");
                        _logger.LogError($"  - 错误码: {ret} ({GetOptErrorDescription(ret)})");
                        _logger.LogError($"  - 源格式: {frame.frameInfo.pixelFormat}");
                        _logger.LogError($"  - 图像尺寸: {width}x{height}");
                        _logger.LogError($"  - 源数据大小: {frame.frameInfo.size} bytes");
                        _logger.LogError($"  - 目标缓冲区大小: {rgbSize} bytes");
                        _logger.LogError($"  - 相机对象状态: {(_camera != null ? "有效" : "无效")}");
                        return null;
                    }

                    // 复制到托管缓冲区
                    Marshal.Copy(convertBuffer, rgbBuffer, 0, rgbSize);

                    // **关键改进：创建完全独立的BitmapSource，不依赖共享对象**
                    var bitmapSource = BitmapSource.Create(
                        width, height,
                        96, 96, // DPI
                        PixelFormats.Rgb24,
                        null, // 调色板
                        rgbBuffer,
                        width * 3 // stride
                    );

                    // 冻结BitmapSource，使其可以跨线程访问
                    bitmapSource.Freeze();

                    return bitmapSource;
                }
                finally
                {
                    // 清理局部资源
                    if (convertBuffer != IntPtr.Zero)
                    {
                        Marshal.FreeHGlobal(convertBuffer);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"独立帧转换异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 使用官方OPT SDK将任意像素格式转换为RGB24的BitmapSource
        /// 优化版本 - 重点解决内存管理问题，特别针对高分辨率相机（如26MP）
        /// </summary>
        /// <param name="frame">输入帧</param>
        /// <returns>转换后的BitmapSource</returns>
        private BitmapSource ConvertFrameToRgbBitmapSource(ref OPT_Frame frame)
        {
            try
            {
                int width = (int)frame.frameInfo.width;
                int height = (int)frame.frameInfo.height;
                int rgbSize = width * height * 3;

                // 针对高分辨率相机的优化检查
                if (rgbSize <= 0 || rgbSize > 200 * 1024 * 1024)
                {
                    AddLog($"帧数据大小检查 - 宽度: {width}, 高度: {height}, 总大小: {rgbSize} bytes", true);
                    AddLog($"帧格式信息 - 像素格式: {frame.frameInfo.pixelFormat}, 原始大小: {frame.frameInfo.size} bytes", true);
                    
                    // 对于超大数据，建议使用ROI或降低分辨率
                    if (rgbSize > 200 * 1024 * 1024)
                    {
                        AddLog($"警告：图像数据过大({rgbSize/1024/1024}MB)，建议设置ROI或使用Mono8格式", true);
                    }
                    return null;
                }


                // ----------- 初始化或调整缓冲区 -----------
                if (_convertBufferSize < rgbSize)
                {
                    // 释放旧缓冲
                    if (_convertBuffer != IntPtr.Zero)
                    {
                        Marshal.FreeHGlobal(_convertBuffer);
                        _convertBuffer = IntPtr.Zero;
                    }

                    // 为大图像分配内存
                    try
                    {
                        _convertBuffer = Marshal.AllocHGlobal(rgbSize);
                        _convertBufferSize = rgbSize;
                        AddLog($"分配转换缓冲区: {rgbSize/1024/1024}MB", false);
                    }
                    catch (OutOfMemoryException)
                    {
                        AddLog($"内存不足，无法分配 {rgbSize/1024/1024}MB 缓冲区", true);
                        return null;
                    }
                }

                if (_rgbBuffer == null || _rgbBuffer.Length < rgbSize)
                {
                    try
                    {
                        _rgbBuffer = new byte[rgbSize];
                    }
                    catch (OutOfMemoryException)
                    {
                        AddLog($"内存不足，无法分配 {rgbSize/1024/1024}MB 托管缓冲区", true);
                        return null;
                    }
                }

                if (_sharedBitmap == null || width != _bitmapWidth || height != _bitmapHeight)
                {
                    _bitmapWidth = width;
                    _bitmapHeight = height;
                    try
                    {
                        _sharedBitmap = new WriteableBitmap(width, height, 96, 96, PixelFormats.Rgb24, null);
                        AddLog($"创建WriteableBitmap: {width}x{height}", false);
                    }
                    catch (OutOfMemoryException)
                    {
                        AddLog($"内存不足，无法创建 {width}x{height} 位图", true);
                        return null;
                    }
                }

                // ----------- 像素格式转换 -----------
                OPTDefine.OPT_PixelConvertParam cvt = new OPTDefine.OPT_PixelConvertParam
                {
                    nWidth = frame.frameInfo.width,
                    nHeight = frame.frameInfo.height,
                    ePixelFormat = frame.frameInfo.pixelFormat,
                    pSrcData = frame.pData,
                    nSrcDataLen = frame.frameInfo.size,
                    nPaddingX = frame.frameInfo.paddingX,
                    nPaddingY = frame.frameInfo.paddingY,
                    eBayerDemosaic = OPTDefine.OPT_EBayerDemosaic.demosaicNearestNeighbor,
                    eDstPixelFormat = OPTDefine.OPT_EPixelType.gvspPixelRGB8,
                    pDstBuf = _convertBuffer,
                    nDstBufSize = (uint)_convertBufferSize
                };

                int ret = _camera.OPT_PixelConvert(ref cvt);
                if (ret != OPTDefine.OPT_OK)
                {
                    AddLog($"像素转换失败: {ret} - 格式: {frame.frameInfo.pixelFormat}", true);
                    return null;
                }

                // ----------- 复制到托管缓冲区 -----------
                Marshal.Copy(_convertBuffer, _rgbBuffer, 0, rgbSize);

                // ----------- 写入 WriteableBitmap -----------
                _sharedBitmap.Lock();
                Marshal.Copy(_rgbBuffer, 0, _sharedBitmap.BackBuffer, rgbSize);
                _sharedBitmap.AddDirtyRect(new Int32Rect(0, 0, width, height));
                _sharedBitmap.Unlock();

                // 返回一个冻结的浅克隆，避免 UI 线程跨线程访问异常，同时保持 _sharedBitmap 可写
                var frameForUi = _sharedBitmap.Clone();
                frameForUi.Freeze();
                return frameForUi;
            }
            catch (Exception ex)
            {
                AddLog($"ConvertFrameToRgbBitmapSource异常: {ex.Message}", true);
                return null;
            }
        }
        
        /// <summary>
        /// 显示更新方法 - 负责实时推送每一帧到页面显示
        /// **触发模式优化：确保每一帧都能实时推送给页面，同时避免性能影响**
        /// </summary>
        private void UpdateDisplayImage(BitmapSource bitmap)
        {
            try
            {
                if (bitmap == null) return;

                // **关键修复：立即冻结位图，避免后续修改导致的内存问题**
                if (!bitmap.IsFrozen)
                {
                    bitmap.Freeze();
                }

                // **关键修复：更新当前显示位图前，先清理旧的引用**
                BitmapSource oldBitmap = null;
                lock (_displayLockObj)
                {
                    oldBitmap = _currentDisplayBitmap;
                    _currentDisplayBitmap = bitmap;
                }

                // **关键修复：清理旧位图引用，帮助GC回收**
                if (oldBitmap != null)
                {
                    oldBitmap = null; // 显式清空引用
                }
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // 这里会将每一帧实时推送给页面显示
                        UpdateGrappingBitmapSourceEvent?.Invoke(bitmap);
                    }
                    catch (Exception ex)
                    {
                        // 只在UI更新异常时记录日志，避免正常情况下的日志干扰
                        _logger.LogError($"[Display] UI更新异常: {ex.Message}");
                    }
                }), System.Windows.Threading.DispatcherPriority.Render); // 使用Render优先级确保显示流畅
            }
            catch (Exception ex)
            {
                _logger.LogError($"[Display] UpdateDisplayImage异常: {ex.Message}");
            }
        }


        public void Release()
        {
            try
            {
                AddLog("开始释放OPT相机资源...");
                _isOperating = true;
                
                // 使用简化的清理方法
                CompleteCleanup();
                
                // 停止统一帧处理线程
                StopUnifiedFrameProcessing();

                // 清空统一帧队列
                ClearUnifiedFrameQueue();
                
                // 触发断开事件
                try
                {
                    CameraDisConnectedEvent?.Invoke();
                    AddLog("OPT相机断开事件已触发");
                }
                catch (Exception ex)
                {
                    AddLog($"触发OPT相机断开事件异常: {ex.Message}", false);
                }
                
                AddLog("OPT相机资源释放完成");
            }
            catch (Exception ex)
            {
                AddLog($"释放OPT相机资源时发生异常: {ex.Message}", true);
            }
            finally
            {
                _isOperating = false;
            }
        }

        
        /// <summary>
        /// 开启新线程处理拍照
        /// </summary>
        /// <param name="folderName"></param>
        /// <param name="pictureName"></param>
        public void TakePicture(string folderName, string pictureName)
        {
            // 将拍照任务添加到队列
            EnqueueSaveImageTask(folderName, pictureName);
        }

        private  bool SaveToBitmap(ref OPTDefine.OPT_Frame frame , string folderName,string pictureName)
        {
            Bitmap bitmap = null;
            
            if (!ConvertToBitmap(ref frame, ref bitmap))
            {
                return false;
            }
            try
            {
                string imageName = Camera.Com.FileHelper.GenerateFileName(folderName, pictureName + ".bmp");
                string fullPath = Common.FileHelper.ConcatFile(folderName, imageName);
                bitmap.Save(fullPath);
                bitmap.Dispose();
                UpdatePictureModelEvent?.Invoke(new PictureModel() { FileName = imageName, FileFullPath = fullPath });
                _logger.LogInfo($"[Save] 拍摄成功 -> {fullPath}");
                AddLog($"拍摄成功！照片名称:{imageName}");
            }
            catch (Exception exception)
            {
                _logger.LogError($"[SaveError] 拍摄失败 -> {pictureName}, {exception}");
                AddLog($"拍摄失败！照片名称:{pictureName}，失败原因:{exception}");
                if (bitmap != null)
                {
                    bitmap.Dispose();
                }
                return false;
            }
            return true;
        }

        private bool ConvertToBitmap(ref OPTDefine.OPT_Frame frame, ref Bitmap bitmap)
        {
            IntPtr pDstRGB;
            BitmapData bmpData;
            Rectangle bitmapRect = new Rectangle();

            //转目标内存 彩色
            var ImgSize = (int)frame.frameInfo.width * (int)frame.frameInfo.height * 3;

            //当内存申请失败，返回false
            try
            {
                pDstRGB = Marshal.AllocHGlobal(ImgSize);
            }
            catch
            {
                return false;
            }
            if (pDstRGB == IntPtr.Zero)
            {
                return false;
            }

            OPTDefine.OPT_PixelConvertParam stPixelConvertParam = new OPTDefine.OPT_PixelConvertParam();
            int res = OPTDefine.OPT_OK;
            // 转码参数
            stPixelConvertParam.nWidth = frame.frameInfo.width;
            stPixelConvertParam.nHeight = frame.frameInfo.height;
            stPixelConvertParam.ePixelFormat = frame.frameInfo.pixelFormat;
            stPixelConvertParam.pSrcData = frame.pData;
            stPixelConvertParam.nSrcDataLen = frame.frameInfo.size;
            stPixelConvertParam.nPaddingX = frame.frameInfo.paddingX;
            stPixelConvertParam.nPaddingY = frame.frameInfo.paddingY;
            stPixelConvertParam.eBayerDemosaic = OPTDefine.OPT_EBayerDemosaic.demosaicNearestNeighbor;
            stPixelConvertParam.eDstPixelFormat = OPTDefine.OPT_EPixelType.gvspPixelBGR8;
            stPixelConvertParam.pDstBuf = pDstRGB;
            stPixelConvertParam.nDstBufSize = (uint)ImgSize;

            res = _camera.OPT_PixelConvert(ref stPixelConvertParam);
            if (res != OPTDefine.OPT_OK)
            {
                Console.WriteLine("image convert to BGR failed!");
                return false;
            }
            // 用BGR24数据生成Bitmap
            bitmap = new Bitmap((int)frame.frameInfo.width, (int)frame.frameInfo.height, System.Drawing.Imaging.PixelFormat.Format24bppRgb);

            bitmapRect.Height = bitmap.Height;
            bitmapRect.Width = bitmap.Width;
            bmpData = bitmap.LockBits(bitmapRect, ImageLockMode.ReadWrite, bitmap.PixelFormat);
            CopyMemory(bmpData.Scan0, pDstRGB, bmpData.Stride * bitmap.Height);
            bitmap.UnlockBits(bmpData);

            Marshal.FreeHGlobal(pDstRGB);

            return true;
        }

        /// <summary>
        /// 指针之间进行数据拷贝
        /// </summary>
        /// <param name="pDst">目标地址</param>
        /// <param name="pSrc">源地址</param>
        /// <param name="len">拷贝数据长度</param>
        [DllImport("Kernel32.dll", EntryPoint = "RtlMoveMemory", CharSet = CharSet.Ansi)]
        internal static extern void CopyMemory(IntPtr pDst, IntPtr pSrc, int len);


        public object ByteToStruct(Byte[] bytes, Type type)
        {
            int size = Marshal.SizeOf(type);
            if (size > bytes.Length)
            {
                return null;
            }
            IntPtr structPtr = Marshal.AllocHGlobal(size);
            Marshal.Copy(bytes, 0, structPtr, size);
            object obj = Marshal.PtrToStructure(structPtr, type);
            Marshal.FreeHGlobal(structPtr);
            return obj;
        }

        #region 日志
        private async void AddLog(string message, bool IsError = false, ShowType show = ShowType.LABEL,int status = 1)
        {
            await Task.Factory.StartNew(() => {
                _globalState.HObservable.NotifyObservers(new HEquipmentStatusArgs()
                {
                    EventMessage = message,
                    StatusShowType = show,
                    EquipmentStatus = status,
                    SourceType = SourceType.CAMERA,
                    EventCode = IsError ? HEventCode.ERROR : HEventCode.SUCCESS
                });
            });
            
        }
        #endregion

        #region 帧同步处理
        /// <summary>
        /// 创建帧的深拷贝，避免引用已释放的内存
        /// </summary>
        /// <param name="sourceFrame"></param>
        /// <returns></returns>
        private OPT_Frame CreateFrameCopy(ref OPT_Frame sourceFrame)
        {
            OPT_Frame copyFrame = new OPT_Frame();
            copyFrame.frameInfo = sourceFrame.frameInfo;
            
            // 为图像数据分配新的内存
            int dataSize = (int)sourceFrame.frameInfo.size;
            copyFrame.pData = Marshal.AllocHGlobal(dataSize);
            
            // 复制图像数据
            CopyMemory(copyFrame.pData, sourceFrame.pData, dataSize);
            
            return copyFrame;
        }

        /// <summary>
        /// 释放帧拷贝的内存
        /// </summary>
        /// <param name="frame"></param>
        private void ReleaseFrameCopy(ref OPT_Frame frame)
        {
            if (frame.pData != IntPtr.Zero)
            {
                Marshal.FreeHGlobal(frame.pData);
                frame.pData = IntPtr.Zero;
            }
        }

        /// <summary>
        /// 强制释放所有可能的设备句柄（增强版）
        /// </summary>
        private void ForceReleaseAllOptDevices()
        {
            try
            {
                AddLog("尝试强制释放所有OPT设备连接...");

                // 1. 首先尝试停止所有可能的采集
                try
                {
                    if (_camera != null)
                    {
                        _camera.OPT_StopGrabbing();
                        Thread.Sleep(500);
                    }
                }
                catch { }

                // 2. 枚举所有设备并尝试释放
                OPTDefine.OPT_DeviceList deviceList = new OPTDefine.OPT_DeviceList();
                OPTDefine.OPT_EInterfaceType interfaceTp = OPTDefine.OPT_EInterfaceType.interfaceTypeGige;

                int result = MyCamera.OPT_EnumDevices(ref deviceList, (uint)interfaceTp);
                if (result == OPTDefine.OPT_OK)
                {
                    AddLog($"发现 {deviceList.nDevNum} 个OPT设备，开始强制释放...");

                    for (int i = 0; i < deviceList.nDevNum; i++)
                    {
                        try
                        {
                            var tempCamera = new MyCamera();

                            // 尝试创建句柄
                            int createResult = tempCamera.OPT_CreateHandle(OPTDefine.OPT_ECreateHandleMode.modeByIndex, i, "");
                            if (createResult == OPTDefine.OPT_OK)
                            {
                                // 尝试停止采集
                                try { tempCamera.OPT_StopGrabbing(); } catch { }
                                Thread.Sleep(100);

                                // 尝试关闭设备
                                try { tempCamera.OPT_Close(); } catch { }
                                Thread.Sleep(100);

                                // 销毁句柄
                                try { tempCamera.OPT_DestroyHandle(); } catch { }
                            }

                            tempCamera = null;
                            AddLog($"设备 {i} 强制释放完成");
                        }
                        catch (Exception ex)
                        {
                            AddLog($"释放设备 {i} 异常: {ex.Message}", false);
                        }
                    }

                    // 3. 额外等待时间，确保设备完全释放
                    AddLog("等待设备状态完全重置...");
                    Thread.Sleep(2000);

                    AddLog("强制设备释放完成");
                }
                else
                {
                    AddLog($"枚举设备失败，错误码: {result}，尝试其他释放方法...");

                    // 4. 如果枚举失败，尝试直接释放当前相机
                    try
                    {
                        if (_camera != null)
                        {
                            _camera.OPT_StopGrabbing();
                            _camera.OPT_Close();
                            _camera.OPT_DestroyHandle();
                        }
                    }
                    catch { }
                }

                // 5. 最后的清理工作
                try
                {
                    // 强制垃圾回收，释放可能的托管资源
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }
                catch { }
            }
            catch (Exception ex)
            {
                AddLog($"强制设备释放异常: {ex.Message}", false);
            }
        }

        /// <summary>
        /// 将BitmapSource保存为文件 - 10ms高频触发优化版本
        /// **关键优化：针对10ms触发间隔进行极致性能优化**
        /// </summary>
        private bool SaveBitmapSourceToFile(BitmapSource bitmapSource, string folderName, string fileName)
        {
            var saveStartTime = DateTime.Now;

            try
            {
                if (bitmapSource == null)
                {
                    _logger.LogError("[HighFreq] 保存失败：位图为空");
                    return false;
                }

                // **10ms优化：预先创建目录，避免每次检查**
                // 假设目录已存在，只在失败时创建（减少IO操作）
                string filePath = Path.Combine(folderName, $"{fileName}.jpg");

                try
                {
                    // **跨线程修复：直接使用传入的BitmapSource，它应该已经被冻结**
                    // 如果BitmapSource没有被冻结，说明调用方有问题，记录错误但继续尝试保存
                    if (!bitmapSource.IsFrozen)
                    {
                        _logger.LogInfo($"[HighFreq] 警告：BitmapSource未冻结，可能存在跨线程风险: {fileName}");
                    }

                    // **10ms优化：使用高性能的文件写入方式**
                    using (var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None,
                                                     bufferSize: 65536, // 64KB缓冲区，提高写入性能
                                                     useAsync: false))   // 同步写入，避免异步开销
                    {
                        var encoder = new JpegBitmapEncoder();
                        // **10ms优化：设置JPEG质量为85，平衡文件大小和编码速度**
                        encoder.QualityLevel = 85;
                        encoder.Frames.Add(BitmapFrame.Create(bitmapSource));
                        encoder.Save(stream);
                        stream.Flush(); // 确保数据写入磁盘
                    }
                }
                catch (DirectoryNotFoundException)
                {
                    // 目录不存在时才创建，减少不必要的检查
                    Directory.CreateDirectory(folderName);

                    // **跨线程修复：直接使用传入的BitmapSource，它应该已经被冻结**
                    if (!bitmapSource.IsFrozen)
                    {
                        _logger.LogInfo($"[HighFreq] 警告：重试时BitmapSource未冻结: {fileName}");
                    }

                    // 重试保存
                    using (var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, 65536, false))
                    {
                        var encoder = new JpegBitmapEncoder();
                        encoder.QualityLevel = 85;
                        encoder.Frames.Add(BitmapFrame.Create(bitmapSource));
                        encoder.Save(stream);
                        stream.Flush();
                    }
                }

                var saveElapsed = (DateTime.Now - saveStartTime).TotalMilliseconds;

                // **10ms优化：简化日志记录，减少字符串操作**
                _logger.LogInfo($"[HighFreq] 保存完成: {saveElapsed:F1}ms, {fileName}");

                // **10ms优化：只在保存耗时过长时记录详细日志**
                if (saveElapsed > 6) // 10ms触发，保存应在6ms内完成，留4ms给其他处理
                {
                    AddLog($"[警告] 保存耗时过长: {saveElapsed:F1}ms - {fileName}", false);
                }

                // **10ms优化：异步通知上层，避免阻塞保存流程**
                Task.Run(() =>
                {
                    try
                    {
                        UpdatePictureModelEvent?.Invoke(new PictureModel() { FileName = $"{fileName}.jpg", FileFullPath = filePath });
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"[HighFreq] 通知上层异常: {ex.Message}");
                    }
                });

                return true;
            }
            catch (Exception ex)
            {
                var saveElapsed = (DateTime.Now - saveStartTime).TotalMilliseconds;
                _logger.LogError($"[HighFreq] 保存异常: {ex.Message}, 耗时: {saveElapsed:F1}ms, 文件: {fileName}");
                AddLog($"10ms高频触发：保存异常 - {fileName}: {ex.Message}", true);
                return false;
            }
        }
        #endregion

        /// <summary>
        /// 针对高分辨率相机（如26MP）的基础优化配置
        /// 主要优化流缓冲区和曝光参数，确保高分辨率图像稳定采集
        /// </summary>
        public void OptimizeForHighResolutionCamera()
        {
            try
            {
                if (!_isConnect || _camera == null)
                {
                    AddLog("相机未连接，无法进行优化配置", true);
                    return;
                }

                AddLog("开始配置高分辨率相机优化参数...");

                // 增加帧数据缓存个数，防止高分辨率图像丢帧
                try
                {
                    var result = _camera.OPT_SetBufferCount(16);
                    if (result == OPTDefine.OPT_OK)
                    {
                        AddLog("已增加帧数据缓存为16个，确保高分辨率图像稳定传输", false);
                    }
                    else
                    {
                        AddLog($"设置帧数据缓存失败，错误码: {result} ({GetOptErrorDescription(result)})", false);
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"设置缓冲区异常: {ex.Message}", false);
                }

                // 检查当前曝光时间
                try
                {
                    double currentExposure = 0;
                    var result = _camera.OPT_GetDoubleFeatureValue("ExposureTime", ref currentExposure);
                    if (result == OPTDefine.OPT_OK)
                    {
                        AddLog($"当前曝光时间: {currentExposure}μs", false);
                        
                        // 如果曝光时间过长，建议调整
                        if (currentExposure > 100000) // 100ms
                        {
                            AddLog("建议调整曝光时间以提高采集效率", false);
                        }
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"获取曝光时间异常: {ex.Message}", false);
                }

                AddLog("高分辨率相机优化配置完成", false);
            }
            catch (Exception ex)
            {
                AddLog($"高分辨率相机优化配置异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 带重试机制的相机连接方法
        /// **重要修复：减少重试次数，避免与海康相机长时间冲突**
        /// </summary>
        /// <param name="cameraIndex">相机索引</param>
        /// <returns>连接是否成功</returns>
        private bool ConnectCameraWithRetry(int cameraIndex)
        {
            int retryCount = 0;
            const int MAX_RETRIES = 3; // **修复：减少重试次数，避免长时间冲突**

            while (retryCount < MAX_RETRIES)
            {
                try
                {
                    AddLog($"尝试连接相机 (第{retryCount + 1}次尝试)...");

                    // **修复：重试前进行轻量级清理，避免过度操作**
                    if (retryCount > 0)
                    {
                        AddLog("重试前进行轻量级设备清理...");
                        
                        // **简化清理：只清理当前相机对象，不强制释放所有设备**
                        try
                        {
                            if (_camera != null)
                            {
                                _camera.OPT_StopGrabbing();
                                _camera.OPT_Close();
                                _camera.OPT_DestroyHandle();
                            }
                        }
                        catch { }

                        // 重新创建相机对象
                        _camera = new MyCamera();
                        frameCallBack = new OPT_FrameCallBack(GrapCallBack);

                        // **修复：减少等待时间，避免长时间占用**
                        int waitTime = Math.Min(2000, 500 * (retryCount + 1)); // 最多等待2秒
                        AddLog($"等待设备释放完成... ({waitTime}ms)");
                        Thread.Sleep(waitTime);
                    }

                    // 创建相机句柄 - 根据SDK文档，第二个参数是int类型
                    OptResult = _camera.OPT_CreateHandle(OPTDefine.OPT_ECreateHandleMode.modeByIndex, cameraIndex, "");
                    if (OptResult != OPTDefine.OPT_OK)
                    {
                        AddLog($"创建相机句柄失败，错误码: {OptResult} ({GetOptErrorDescription(OptResult)})", true);
                        retryCount++;
                        continue;
                    }

                    // 打开相机设备
                    OptResult = _camera.OPT_Open();
                    if (OptResult != OPTDefine.OPT_OK)
                    {
                        AddLog($"打开相机设备失败，错误码: {OptResult} ({GetOptErrorDescription(OptResult)})", true);

                        // 清理失败的句柄
                        try { _camera.OPT_DestroyHandle(); } catch { }

                        retryCount++;
                        continue;
                    }
                    
                    // **关键修复：启动采集和实时显示流程**
                    // 首先注册帧回调并启动采集
                    frameCallBack = new OPTDefine.OPT_FrameCallBack(GrapCallBack);
                    OptResult = _camera.OPT_AttachGrabbing(frameCallBack, IntPtr.Zero);
                    if (OptResult != OPTDefine.OPT_OK)
                    {
                        AddLog($"注册帧回调失败，错误码: {OptResult}", true);
                        retryCount++;
                        Thread.Sleep(500); // **修复：减少等待时间**
                        continue;
                    }
                    
                    // 开始采集
                    OptResult = _camera.OPT_StartGrabbing();
                    if (OptResult != OPTDefine.OPT_OK)
                    {
                        AddLog($"开始采集失败，错误码: {OptResult}", true);
                        retryCount++;
                        Thread.Sleep(500); // **修复：减少等待时间**
                        continue;
                    }
                    
                    // **关键修复：设置状态并启动显示循环**
                    m_bStartGrab = true;
                    _isConnect = true;
                    
                    // 关闭触发模式，使用连续采集
                    if (!_isTriggerMode) CloseTrigger();
                    
                    // 针对高分辨率相机进行优化配置
                    OptimizeForHighResolutionCamera();
                    
                    // 实时显示由 StartFrameProcessing 内部线程负责
                    
                    AddLog("相机采集和实时显示已启动", false);
                    
                    return true;
                }
                catch (Exception ex)
                {
                    AddLog($"连接异常: {ex.Message}", true);
                    retryCount++;
                    Thread.Sleep(500); // **修复：减少等待时间**
                }
            }
            
            return false;
        }

        /// <summary>
        /// 获取OPT错误码的详细描述
        /// </summary>
        /// <param name="errorCode">错误码</param>
        /// <returns>错误描述</returns>
        private string GetOptErrorDescription(int errorCode)
        {
            switch (errorCode)
            {
                case OPTDefine.OPT_OK:
                    return "成功";
                case -101:
                    return "设备被占用或连接失败";
                case -102:
                    return "设备未找到";
                case -103:
                    return "设备已断开";
                case -104:
                    return "无效的设备句柄";
                case -105:
                    return "设备初始化失败";
                case -106:
                    return "内存分配失败";
                case -107:
                    return "参数错误";
                case -108:
                    return "超时";
                case -109:
                    return "数据格式错误";
                case -110:
                    return "功能不支持";
                default:
                    return $"未知错误码: {errorCode}";
            }
        }

        /// <summary>
        /// 诊断OPT相机连接问题并提供解决方案
        /// **新增功能：全面诊断设备占用和冲突问题**
        /// </summary>
        public void DiagnoseOptCameraIssues()
        {
            try
            {
                AddLog("=== OPT相机连接问题诊断开始 ===");
                
                // 1. 检查SDK状态
                AddLog("🔍 步骤1：检查OPT SDK状态");
                if (_camera == null)
                {
                    AddLog("  - SDK未初始化，正在初始化...");
                    InitSDK();
                }
                else
                {
                    AddLog("  - SDK已初始化 ✅");
                }
                
                // 2. 枚举设备并检查占用情况
                AddLog("🔍 步骤2：枚举设备并检查占用情况");
                OPTDefine.OPT_DeviceList deviceList = new OPTDefine.OPT_DeviceList();
                OPTDefine.OPT_EInterfaceType interfaceTp = OPTDefine.OPT_EInterfaceType.interfaceTypeGige;
                
                int enumResult = MyCamera.OPT_EnumDevices(ref deviceList, (uint)interfaceTp);
                if (enumResult == OPTDefine.OPT_OK)
                {
                    AddLog($"  - 发现 {deviceList.nDevNum} 个OPT GigE设备");
                    
                    if (deviceList.nDevNum > 0)
                    {
                        // 3. 尝试逐个设备检查占用状态
                        AddLog("🔍 步骤3：检查设备占用状态");
                        for (int i = 0; i < deviceList.nDevNum; i++)
                        {
                            TestDeviceAvailability(i);
                        }
                    }
                    else
                    {
                        AddLog("  ❌ 未发现任何OPT设备");
                        AddLog("  🔧 建议：检查网络连接和设备电源");
                    }
                }
                else
                {
                    AddLog($"  ❌ 设备枚举失败，错误码: {enumResult} ({GetOptErrorDescription(enumResult)})");
                }
                
                // 4. 检查网络配置
                AddLog("🔍 步骤4：检查网络配置");
                string targetIP = _globalState?.AppConfig?.MainCameraAddress ?? "未配置";
                AddLog($"  - 目标相机IP: {targetIP}");
                if (targetIP != "未配置")
                {
                    // 这里可以添加ping测试等网络诊断
                    AddLog($"  - 建议：使用ping命令测试连接: ping {targetIP}");
                }
                
                // 5. 提供解决方案
                AddLog("🔧 解决方案建议：");
                AddLog("  1. 确保没有其他程序（包括海康客户端）占用OPT相机");
                AddLog("  2. 重启OPT相机设备（断电重启）");
                AddLog("  3. 检查网络连接和IP配置");
                AddLog("  4. 以管理员权限运行程序");
                AddLog("  5. 重启计算机释放所有设备句柄");
                AddLog("  6. 检查防火墙是否阻止了GigE连接");
                
                AddLog("=== OPT相机连接问题诊断完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"诊断过程异常: {ex.Message}", true);
            }
        }
        
        /// <summary>
        /// 测试指定索引设备的可用性
        /// </summary>
        private void TestDeviceAvailability(int deviceIndex)
        {
            try
            {
                AddLog($"  🔍 测试设备 {deviceIndex}...");
                
                var testCamera = new MyCamera();
                
                // 尝试创建句柄
                int createResult = testCamera.OPT_CreateHandle(OPTDefine.OPT_ECreateHandleMode.modeByIndex, deviceIndex, "");
                if (createResult == OPTDefine.OPT_OK)
                {
                    AddLog($"    - 句柄创建成功 ✅");
                    
                    // 尝试打开设备
                    int openResult = testCamera.OPT_Open();
                    if (openResult == OPTDefine.OPT_OK)
                    {
                        AddLog($"    - 设备打开成功 ✅ (设备可用)");
                        
                        // 立即关闭设备
                        testCamera.OPT_Close();
                        AddLog($"    - 设备已安全关闭");
                    }
                    else
                    {
                        AddLog($"    - 设备打开失败 ❌ 错误码: {openResult} ({GetOptErrorDescription(openResult)})");
                        if (openResult == -101)
                        {
                            AddLog($"    - 💡 设备被占用，可能被其他程序使用");
                        }
                    }
                    
                    // 清理句柄
                    testCamera.OPT_DestroyHandle();
                }
                else
                {
                    AddLog($"    - 句柄创建失败 ❌ 错误码: {createResult} ({GetOptErrorDescription(createResult)})");
                }
                
                testCamera = null;
            }
            catch (Exception ex)
            {
                AddLog($"    - 测试设备 {deviceIndex} 异常: {ex.Message}", false);
            }
        }

        /// <summary>
        /// 诊断当前连接的设备信息
        /// **新增功能：帮助识别当前连接的是否为真正的OPT设备**
        /// </summary>
        public void DiagnoseCurrentDevice()
        {
            try
            {
                AddLog("=== 开始诊断当前连接的设备 ===");
                
                if (!_isConnect || _camera == null)
                {
                    AddLog("当前没有连接的设备", true);
                    return;
                }
                
                // 尝试获取设备信息
                try
                {
                    OPTDefine.OPT_DeviceInfo deviceInfo = new OPTDefine.OPT_DeviceInfo();
                    int infoResult = _camera.OPT_GetDeviceInfo(ref deviceInfo);
                    
                    if (infoResult == OPTDefine.OPT_OK)
                    {
                        AddLog("✅ 成功获取设备信息：");
                        AddLog($"  - 设备类型: {deviceInfo.nCameraType}");
                        AddLog($"  - 接口类型: {deviceInfo.nInterfaceType}");
                        
                        // 尝试读取一些OPT特有的参数
                        try
                        {
                            double exposureTime = 0;
                            int expResult = _camera.OPT_GetDoubleFeatureValue("ExposureTime", ref exposureTime);
                            if (expResult == OPTDefine.OPT_OK)
                            {
                                AddLog($"  - 曝光时间: {exposureTime}μs ✅ OPT设备特征");
                            }
                            else
                            {
                                AddLog($"  - 曝光时间获取失败，错误码: {expResult}");
                            }
                        }
                        catch (Exception ex)
                        {
                            AddLog($"  - 曝光参数读取异常: {ex.Message}");
                        }
                        
                        // 尝试读取其他参数
                        try
                        {
                            double gain = 0;
                            int gainResult = _camera.OPT_GetDoubleFeatureValue("Gain", ref gain);
                            if (gainResult == OPTDefine.OPT_OK)
                            {
                                AddLog($"  - 增益: {gain} ✅ OPT设备特征");
                            }
                        }
                        catch (Exception ex)
                        {
                            AddLog($"  - 增益参数读取异常: {ex.Message}");
                        }
                    }
                    else
                    {
                        AddLog($"❌ 获取设备信息失败，错误码: {infoResult}");
                        AddLog("这可能表示连接的不是OPT设备！", true);
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"❌ 设备信息诊断异常: {ex.Message}", true);
                    AddLog("这强烈表示连接的不是OPT设备！", true);
                }
                
                AddLog("=== 设备诊断完成 ===");
            }
            catch (Exception ex)
            {
                AddLog($"诊断过程异常: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 获取PLC硬件触发模式详细状态信息 - 用于诊断触发问题
        /// </summary>
        /// <returns>触发模式状态信息</returns>
        public string GetTriggerModeStatus()
        {
            try
            {
                var sb = new StringBuilder();
                sb.AppendLine("=== OPT相机PLC硬件触发状态 ===");
                sb.AppendLine($"触发模式: {(_isTriggerMode ? "硬件触发模式" : "连续采集模式")}");
                sb.AppendLine($"相机连接: {(_isConnect ? "已连接" : "未连接")}");
                sb.AppendLine($"采集状态: {(m_bStartGrab ? "采集中" : "已停止")}");
                sb.AppendLine($"PLC触发帧计数: {_triggerFrameCounter}");
                sb.AppendLine($"总帧计数: {_frameCount}");
                
                if (_isTriggerMode)
                {
                    var timeSinceSet = DateTime.Now - _triggerModeSetTime;
                    sb.AppendLine($"触发模式设置时间: {_triggerModeSetTime:HH:mm:ss.fff}");
                    sb.AppendLine($"距离设置已过: {timeSinceSet.TotalMilliseconds:F0}ms");
                    sb.AppendLine($"稳定延迟: {_triggerModeStabilizeDelay.TotalMilliseconds}ms");
                    sb.AppendLine($"是否过稳定期: {(timeSinceSet >= _triggerModeStabilizeDelay ? "是" : "否")}");
                    sb.AppendLine("");
                    sb.AppendLine("PLC硬件触发工作原理:");
                    sb.AppendLine("1. PLC发送硬件触发信号给相机");
                    sb.AppendLine("2. 相机接收信号后拍摄一帧");
                    sb.AppendLine("3. 相机通过回调发送图片给软件");
                    sb.AppendLine("4. 软件处理每个回调帧(都是PLC触发帧)");
                }
                else
                {
                    sb.AppendLine("当前为连续采集模式，相机持续拍摄");
                }
                
                sb.AppendLine($"统一帧处理队列: {_unifiedFrameQueue.Count}");
                sb.AppendLine($"队列处理状态: {(_isUnifiedProcessing ? "处理中" : "已停止")}");
                sb.AppendLine($"系统运行状态: {(_globalState.IsRunning ? "自动运行中" : "已停止")}");
                
                return sb.ToString();
            }
            catch (Exception ex)
            {
                return $"获取PLC触发状态异常: {ex.Message}";
            }
        }

        /// <summary>
        /// 重置触发帧计数器 - 用于调试
        /// </summary>
        public void ResetTriggerFrameCounter()
        {
            _triggerFrameCounter = 0;
            AddLog("触发帧计数器已重置", false);
        }

        /// <summary>
        /// 获取保存状态详细信息 - 用于调试照片保存问题
        /// </summary>
        /// <returns>保存状态信息</returns>
        public string GetSaveStatusInfo()
        {
            try
            {
                var sb = new StringBuilder();
                sb.AppendLine("=== OPT相机保存状态详情 ===");
                sb.AppendLine($"系统运行状态: {(_globalState?.IsRunning == true ? "自动运行中" : "已停止")}");
                sb.AppendLine($"已保存帧计数: {_triggerFrameCounter}");
                sb.AppendLine($"总帧计数: {_frameCount}");
                sb.AppendLine($"统一队列长度: {_unifiedFrameQueue.Count}");
                sb.AppendLine($"队列处理状态: {(_isUnifiedProcessing ? "处理中" : "已停止")}");
                sb.AppendLine("");
                sb.AppendLine("📸 保存策略说明：");
                sb.AppendLine("1. 自动运行模式下 → 所有收到的帧都保存");
                sb.AppendLine("2. 文件名和路径完全内部生成，基于配置规则");
                sb.AppendLine("3. 完全独立，不依赖任何外部回调事件");
                sb.AppendLine("4. 不判断PLC触发，只看系统运行状态");
                sb.AppendLine("5. PLC何时触发软件不知道，只管保存收到的所有帧");
                sb.AppendLine("6. 彻底解决回调失效导致的保存失败问题");
                
                return sb.ToString();
            }
            catch (Exception ex)
            {
                return $"获取保存状态异常: {ex.Message}";
            }
        }

        /// <summary>
        /// 强制触发模式重新初始化 - 用于解决卡住的问题
        /// </summary>
        public void ReinitializeTriggerMode()
        {
            try
            {
                AddLog("开始强制重新初始化触发模式...");
                
                // 重置所有触发相关状态
                _triggerFrameCounter = 0;
                _triggerModeSetTime = DateTime.Now;
                
                // 如果当前在触发模式，重新设置
                if (_isTriggerMode)
                {
                    AddLog("重新设置硬件触发配置...");
                    var result = setLineTriggerConf();
                    if (result)
                    {
                        AddLog("✅ 触发模式重新初始化成功");
                    }
                    else
                    {
                        AddLog("❌ 触发模式重新初始化失败", true);
                    }
                }
                else
                {
                    AddLog("当前为连续采集模式，重置状态完成");
                }
            }
            catch (Exception ex)
            {
                AddLog($"强制重新初始化触发模式异常: {ex.Message}", true);
            }
        }

        #region 文件名生成逻辑
        
        /// <summary>
        /// 生成文件夹名称 - 基于配置规则生成
        /// </summary>
        /// <returns>生成的文件夹路径</returns>
        private string GenerateFolderName()
        {
            try
            {
                string folderName = _globalState?.AppConfig?.SaveFolder ?? @"C:\Pictures";
                string subFolder = GenerateSubFolderName();
                if (!string.IsNullOrEmpty(subFolder))
                {
                    folderName = Path.Combine(folderName, subFolder);
                }
                
                // 确保文件夹存在
                if (!Directory.Exists(folderName))
                {
                    Directory.CreateDirectory(folderName);
                }
                
                return folderName;
            }
            catch (Exception ex)
            {
                AddLog($"生成文件夹名失败: {ex.Message}", true);
                string defaultFolder = Path.Combine(@"C:\Pictures", DateTime.Now.ToString("yyyy-MM-dd"));
                if (!Directory.Exists(defaultFolder))
                {
                    Directory.CreateDirectory(defaultFolder);
                }
                return defaultFolder;
            }
        }

        /// <summary>
        /// 生成子文件夹名称
        /// </summary>
        /// <returns>子文件夹名称</returns>
        private string GenerateSubFolderName()
        {
            try
            {
                var folderNameList = _globalState?.AppConfig?.FolderNameList;
                if (folderNameList == null || folderNameList.Count == 0)
                {
                    return DateTime.Now.ToString("yyyy-MM-dd");
                }

                var orderedList = folderNameList.OrderBy(item => item.Index).ToList();
                List<string> names = GenerateNameComponents(orderedList);
                
                var folderNameType = _globalState?.AppConfig?.FolderNameType ?? Common.FolderNameTypeEnum.SAMELEVEL;
                switch (folderNameType)
                {
                    case Common.FolderNameTypeEnum.SAMELEVEL:
                        return string.Join("", names);
                    case Common.FolderNameTypeEnum.OTHERLEVEL:
                        return string.Join(Path.DirectorySeparatorChar.ToString(), names);
                    default:
                        return string.Join("", names);
                }
            }
            catch (Exception ex)
            {
                AddLog($"生成子文件夹名失败: {ex.Message}", true);
                return DateTime.Now.ToString("yyyy-MM-dd");
            }
        }

        /// <summary>
        /// 生成文件名 - 基于配置规则生成，使用正确的引脚编号
        /// </summary>
        /// <returns>生成的文件名（不包含扩展名）</returns>
        private string GenerateFileName()
        {
            try
            {
                // **警告：此方法应该只在没有外部文件名时使用**
                AddLog($"⚠️ 警告：正在使用内部文件名生成，可能导致引脚编号不正确");

                // **修复引脚编号：从VisionPageViewModel获取正确的引脚编号**
                int actualPinIndex = 1; // 默认值
                if (GetActualPinIndex != null)
                {
                    actualPinIndex = GetActualPinIndex.Invoke();
                    AddLog($"🔢 通过委托获取引脚编号: {actualPinIndex}");
                }
                else
                {
                    // 如果没有委托，使用简单累加（向后兼容）
                    _currentPinIndex++;
                    actualPinIndex = _currentPinIndex;
                    AddLog($"⚠️ 未设置引脚编号委托，使用简单累加: {actualPinIndex}");
                }

                var pictureNameList = _globalState?.AppConfig?.PictureList;
                if (pictureNameList == null || pictureNameList.Count == 0)
                {
                    // 默认文件名格式
                    return $"OPT_{DateTime.Now:yyyyMMdd_HHmmss}_{actualPinIndex}";
                }

                var orderedList = pictureNameList.OrderBy(item => item.Index).ToList();
                List<string> names = GenerateNameComponents(orderedList);
                string baseName = string.Join("", names);
                
                // 使用正确的引脚编号
                return $"{baseName}-{actualPinIndex}";
            }
            catch (Exception ex)
            {
                AddLog($"生成文件名失败: {ex.Message}", true);
                // 异常情况下使用时间戳 + 简单累加
                _currentPinIndex++;
                return $"OPT_{DateTime.Now:yyyyMMdd_HHmmss}_{_currentPinIndex}";
            }
        }

        /// <summary>
        /// 根据命名规则生成名称组件
        /// </summary>
        /// <param name="model">命名配置列表</param>
        /// <returns>名称组件列表</returns>
        private List<string> GenerateNameComponents(List<ViewModel.ObservableModel.ObservableCustomNameModel> model)
        {
            List<string> names = new List<string>();
            
            foreach (var item in model)
            {
                try
                {
                    var nameType = (Common.NameEnum)Enum.Parse(typeof(Common.NameEnum), item.NameType);
                    switch (nameType)
                    {
                        case Common.NameEnum.DATA:
                            names.Add(DateTime.Now.Day.ToString());
                            break;
                        case Common.NameEnum.MINUTES:
                            names.Add(DateTime.Now.Minute.ToString());
                            break;
                        case Common.NameEnum.SECONDS:
                            names.Add(DateTime.Now.Second.ToString());
                            break;
                        case Common.NameEnum.PRODUCTNAME:
                            names.Add(_globalState?.CurrentProduct?.ProductName ?? "");
                            break;
                        case Common.NameEnum.MOUNTH:
                            names.Add(DateTime.Now.Month.ToString());
                            break;
                        case Common.NameEnum.CUSTOM:
                            names.Add(item.Content ?? "");
                            break;
                        case Common.NameEnum.YEAR:
                            names.Add(DateTime.Now.Year.ToString());
                            break;
                        case Common.NameEnum.STEPNAME:
                            names.Add(_globalState?.CurrentProcessModel?.ParamName ?? "");
                            break;
                        case Common.NameEnum.WEIGHT:
                            names.Add(_globalState?.Weight ?? "");
                            break;
                        case Common.NameEnum.PRODUCTNUMBER:
                            names.Add(_globalState?.CurrentProduct?.ProductNumber ?? "");
                            break;
                        case Common.NameEnum.SERIALNUMBER:
                            // **修复：从用户输入获取序列号**
                            string serialNumber = "";
                            if (GetSerialNumber != null)
                            {
                                serialNumber = GetSerialNumber.Invoke() ?? "";
                            }
                            names.Add(string.IsNullOrEmpty(serialNumber) ? "" : serialNumber);
                            break;
                        case Common.NameEnum.ORDERNUMBER:
                            // **新增：从用户输入获取订单号**
                            string orderNumber = "";
                            if (GetOrderNumber != null)
                            {
                                orderNumber = GetOrderNumber.Invoke() ?? "";
                            }
                            names.Add(string.IsNullOrEmpty(orderNumber) ? "" : orderNumber);
                            break;
                        case Common.NameEnum.HOUR:
                            names.Add(DateTime.Now.Hour.ToString());
                            break;
                        default:
                            names.Add("");
                            break;
                    }
                }
                catch (Exception ex)
                {
                    AddLog($"解析命名规则失败: {ex.Message}", false);
                    names.Add("");
                }
            }
            
            return names;
        }

        #endregion

        /// <summary>
        /// 获取当前实时画面的帧 - 用于单张拍照功能
        /// **新增功能：从实时画面流直接获取一帧图片用于存储和处理**
        /// </summary>
        /// <returns>当前显示的BitmapSource，如果没有可用帧则返回null</returns>
        public BitmapSource GetCurrentDisplayFrame()
        {
            try
            {
                lock (_displayLockObj)
                {
                    if (_currentDisplayBitmap != null)
                    {
                        // 返回当前显示位图的冻结副本，确保线程安全
                        var frameCopy = _currentDisplayBitmap.Clone();
                        if (!frameCopy.IsFrozen)
                        {
                            frameCopy.Freeze();
                        }
                        
                        AddLog($"📷 获取当前实时画面帧成功 - 尺寸: {frameCopy.PixelWidth}x{frameCopy.PixelHeight}");
                        return frameCopy;
                    }
                    else
                    {
                        AddLog("⚠️ 当前没有可用的实时画面帧", false);
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                AddLog($"❌ 获取当前实时画面帧异常: {ex.Message}", true);
                return null;
            }
        }

        /// <summary>
        /// 单张拍照 - 从实时画面流获取当前帧并直接处理
        /// **新增功能：不依赖相机触发，直接从实时画面获取一帧进行完整处理流程**
        /// </summary>
        /// <param name="folderName">保存文件夹</param>
        /// <param name="fileName">文件名（不含扩展名）</param>
        /// <returns>拍照是否成功启动</returns>
        public bool CaptureSingleFrameFromStream(string folderName, string fileName)
        {
            try
            {
                AddLog($"🎯 开始单张拍照 - 从实时画面流获取帧: {fileName}");
                
                // 检查相机连接状态
                if (!_isConnect || !m_bStartGrab)
                {
                    AddLog("❌ OPT相机未连接或未开始采集，无法进行单张拍照", true);
                    return false;
                }
                
                // 获取当前实时画面帧
                BitmapSource currentFrame = GetCurrentDisplayFrame();
                if (currentFrame == null)
                {
                    AddLog("❌ 无法获取当前实时画面帧，单张拍照失败", true);
                    return false;
                }
                
                // 生成完整文件路径
                string fullPath = System.IO.Path.Combine(folderName, $"{fileName}.jpg");
                
                // 确保文件夹存在
                if (!Directory.Exists(folderName))
                {
                    Directory.CreateDirectory(folderName);
                    AddLog($"📁 创建文件夹: {folderName}");
                }
                
                // 保存图片到指定路径
                Task.Run(() =>
                {
                    try
                    {
                        SaveBitmapSourceToFile(currentFrame, folderName, fileName);
                        
                        // 创建PictureModel并触发后续处理
                        var pictureModel = new PictureModel() 
                        { 
                            FileName = $"{fileName}.jpg", 
                            FileFullPath = fullPath 
                        };
                        
                        // 异步通知上层进行后续处理（旋转、裁剪、检测）
                        Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            try
                            {
                                UpdatePictureModelEvent?.Invoke(pictureModel);
                                AddLog($"✅ 单张拍照完成并已通知后续处理: {fileName}.jpg");
                            }
                            catch (Exception ex)
                            {
                                AddLog($"❌ 通知后续处理异常: {ex.Message}", true);
                            }
                        }));
                    }
                    catch (Exception ex)
                    {
                        AddLog($"❌ 单张拍照保存异常: {ex.Message}", true);
                    }
                });
                
                AddLog($"🚀 单张拍照任务已启动: {fileName}.jpg");
                return true;
            }
            catch (Exception ex)
            {
                AddLog($"❌ 单张拍照异常: {ex.Message}", true);
                return false;
            }
        }

        /// <summary>
        /// 模拟OPT相机拍照回调 - 用于测试目的
        /// </summary>
        /// <param name="pictureModel">图片模型</param>
        public void SimulatePictureCallback(PictureModel pictureModel)
        {
            try
            {
                _logger.LogInfo($"🧪 模拟OPT相机拍照回调: {pictureModel.FileName}");
                UpdatePictureModelEvent?.Invoke(pictureModel);
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 模拟OPT相机拍照回调失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 模拟OPT相机自动触发保存请求 - 用于测试目的
        /// </summary>
        public void SimulateAutoTriggerSaveRequest()
        {
            try
            {
                _logger.LogInfo("🧪 模拟OPT相机自动触发保存请求");
                RequestAutoTriggerSave?.Invoke();
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 模拟OPT相机自动触发保存请求失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 模拟OPT相机帧更新事件 - 用于测试目的
        /// </summary>
        public void SimulateFrameUpdate()
        {
            try
            {
                _logger.LogInfo("🧪 模拟OPT相机帧更新事件");
                FrameUpdateTrrigerEvent?.Invoke();
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 模拟OPT相机帧更新事件失败: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// 统一帧任务类 - FIFO先进先出处理，同时支持显示和保存
    /// </summary>
    public class UnifiedFrameTask
    {
        public OPT_Frame FrameData { get; set; }        // 原始帧数据
        public bool NeedDisplay { get; set; }           // 是否需要显示到页面
        public bool NeedSave { get; set; }              // 是否需要保存为文件
        public string FolderName { get; set; }          // 保存文件夹（仅在NeedSave=true时使用）
        public string FileName { get; set; }            // 保存文件名（仅在NeedSave=true时使用）
        public DateTime Timestamp { get; set; }         // 帧时间戳
        public bool IsTriggerFrame { get; set; }        // 是否为触发帧（用于日志记录）
        public int FrameSequence { get; set; }          // 帧序号（用于调试）
    }

    /// <summary>
    /// 相机任务类型枚举 - 简化版本，只支持保存图像
    /// </summary>
    public enum CameraTaskType
    {
        SaveImage          // 保存图像
    }

    /// <summary>
    /// 相机任务类 - 支持原始帧数据存储，解决跨线程访问问题
    /// </summary>
    public class CameraTask
    {
        public CameraTaskType TaskType { get; set; }
        public string FolderName { get; set; }
        public string FileName { get; set; }
        public BitmapSource ImageData { get; set; }  // 转换后的图像数据
        public OPT_Frame? FrameData { get; set; }    // 原始帧数据（用于跨线程处理）
        public DateTime Timestamp { get; set; }
    }

    // 保持向后兼容的枚举
    public enum TriggerTaskType
    {
        SaveImage = CameraTaskType.SaveImage
    }
}
