﻿<UserControl x:Class="IPM.Vision.Views.Dialogs.BarcodeRuleDoialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             DataContext="{Binding BarcodeRuleDialogViewModel, Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="White" CornerRadius="5" MinWidth="420" Width="320">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Background="#283643" CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText Text="{Binding Title}" Foreground="White" FontSize="18" VerticalAlignment="Center" Margin="10 0 0 0"/>
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Template="{StaticResource CloseTemplate}"
                            Width="40"
                            Height="40"
                            Content="&#xf00d;"
                            Command="{Binding CloseCommand}"/>
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <hc:UniformSpacingPanel
                        Grid.Row="0"
                        Margin="10"
                        Orientation="Vertical"
                        Spacing="5">
                        <hc:TextBox
                            hc:InfoElement.Necessary="True"
                            hc:InfoElement.Placeholder="请输入编码规则"
                            hc:TitleElement.Title="编码规则 &lt; 数字：!, 字符串：?, 任意字符：* &gt;"
                            Text="{Binding RuleModel.Rule,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                        <TextBlock
                            FontSize="12"
                            Foreground="Red"
                            Text="{Binding Message}" />
                        <hc:ComboBox
                            hc:InfoElement.Necessary="True"
                            hc:TitleElement.Title="类型"
                            DisplayMemberPath="Label"
                            ItemsSource="{Binding BarcodeRuleTypes}"
                            SelectedValue="{Binding RuleModel.RuleType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            SelectedValuePath="Value"
                            Style="{StaticResource ComboBoxExtend}" />
                        <hc:NumericUpDown
                            hc:InfoElement.Necessary="True"
                            hc:InfoElement.Placeholder="请输入规则序号"
                            hc:TitleElement.Title="规则序号"
                            Style="{StaticResource NumericUpDownPlus}"
                            Value="{Binding RuleModel.SortNumber,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" />
                        <hc:ComboBox
                            hc:TitleElement.Title="是否移除"
                            DisplayMemberPath="Label"
                            ItemsSource="{Binding RemoveTypes}"
                            SelectedValue="{Binding RuleModel.IsRemove,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            SelectedValuePath="Value"
                            Style="{StaticResource ComboBoxExtend}" />
                    </hc:UniformSpacingPanel>
                    <Border Grid.Row="1" Margin="10">
                        <hc:UniformSpacingPanel
                            HorizontalAlignment="Center"
                            Orientation="Horizontal"
                            Spacing="30">
                            <Button
                                Width="150"
                                Height="35"
                                Command="{Binding ConfirmCommand}"
                                Content="保存"
                                Style="{StaticResource ButtonPrimary}" />
                            <Button
                                Width="150"
                                Height="35"
                                Command="{Binding CloseCommand}"
                                Content="取消"
                                Style="{StaticResource ButtonDefault}" />
                        </hc:UniformSpacingPanel>
                    </Border>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
