﻿<UserControl x:Class="IPM.Vision.Views.Dialogs.UserManageDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             DataContext="{Binding UserManageDialogViewModel, Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="White" CornerRadius="5" MinWidth="420" Width="320">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Background="#283643" CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText Text="{Binding Title}" Foreground="White" FontSize="18" VerticalAlignment="Center" Margin="10 0 0 0"/>
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Template="{StaticResource CloseTemplate}"
                            Width="40"
                            Height="40"
                            Content="&#xf00d;"
                            Command="{Binding CloseCommand}"/>
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <hc:UniformSpacingPanel Margin="0 10 0 0" Orientation="Vertical" Spacing="5" Grid.Row="0">
                        <hc:UniformSpacingPanel Orientation="Vertical">
                            <hc:TextBox hc:InfoElement.Title="账号:"  Text="{Binding UserInfo.Account,Mode=TwoWay}"  IsEnabled="{Binding UserInfo.IsSupper,Converter={StaticResource Boolean2BooleanReConverter}}" hc:InfoElement.Necessary="True"/>
                            <hc:SimpleText Text="{Binding Message}" Margin="10 0 0 0" Foreground="Red"/>
                        </hc:UniformSpacingPanel>
                        <hc:PasswordBox hc:InfoElement.Title="密码:"  hc:InfoElement.Necessary="True" ShowEyeButton="True" IsSafeEnabled="False" UnsafePassword="{Binding UserInfo.Password,Mode=TwoWay}"/>
                        <hc:TextBox hc:InfoElement.Title="用户名:" Text="{Binding UserInfo.UserName,Mode=TwoWay}" IsEnabled="{Binding UserInfo.IsSupper,Converter={StaticResource Boolean2BooleanReConverter}}"/>
                        <hc:UniformSpacingPanel Orientation="Vertical">
                            <hc:SimpleText Text="菜单:" Margin="8 0 0 5"/>
                            <hc:CheckComboBox
                                ItemsSource="{Binding MenuList}"
                                DisplayMemberPath="MenuName"
                                SelectedValuePath="MenuId"
                                SelectionMode="Multiple"
                                ShowSelectAllButton="True"
                                Style="{StaticResource CheckComboBoxPlus}">
                                <hc:Interaction.Triggers>
                                    <hc:EventTrigger EventName="Loaded">
                                        <hc:EventToCommand Command="{Binding MenuLoadedCommand}" PassEventArgsToCommand="True" />
                                    </hc:EventTrigger>
                                    <hc:EventTrigger EventName="SelectionChanged">
                                        <hc:EventToCommand Command="{Binding MenuChangeCommand}" PassEventArgsToCommand="True" />
                                    </hc:EventTrigger>
                                </hc:Interaction.Triggers>
                            </hc:CheckComboBox>
                        </hc:UniformSpacingPanel>
                    </hc:UniformSpacingPanel>
                    <Grid Grid.Row="1" Margin="0 30 0 0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Button Grid.Column="0" Content="保存" Style="{StaticResource ButtonPrimary}" Height="35" Width="180" Command="{Binding SaveCommand}"/>
                        <Button Grid.Column="1" Content="取消" Style="{StaticResource ButtonDanger}" Height="35" Width="180" Command="{Binding CloseCommand}"/>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
