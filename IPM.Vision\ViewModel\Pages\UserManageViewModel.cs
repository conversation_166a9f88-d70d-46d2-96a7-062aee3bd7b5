﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.Dialogs;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.Dialogs;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.Pages
{
    public class UserManageViewModel:ViewModelBase
    {
        private ObservableCollection<ObservableUserInfoModel> _dataList;
        private readonly UserService _userService;
        private string _account;

        public ObservableCollection<ObservableUserInfoModel> DataList
        {
            get => _dataList;
            set => SetProperty(ref _dataList, value);
        }

        public string Account
        {
            get => _account;
            set => SetProperty(ref _account, value);
        }

        private readonly NLogHelper _logger;

        public UserManageViewModel(NLogHelper logger,IUserService userService)
        {
            _logger = logger;
            _userService = (UserService)userService;
        }

        public IRelayCommand LoadCommand => new RelayCommand(async () =>
        {
            await SearchDataAsync();
        });

        public IRelayCommand SearchCommand => new RelayCommand(() =>
        {

        });

        private async Task SearchDataAsync()
        {
            var temp = await _userService.getByWhereIF(!string.IsNullOrEmpty(Account),item=>item.Account.Contains(Account));
            string content = FileHelper.ReadFile(FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER, GlobalConstants.MENUCONFIG));
            ObservableCollection<MenuModel> menus = JsonConvert.DeserializeObject<ObservableCollection<MenuModel>>(content);
            if (temp == null) return;
            var result = temp.MapTo<List<UserModel>, ObservableCollection<ObservableUserInfoModel>>();
            foreach (var item in result)
            {
                foreach (var st in menus)
                {
                    if (item.MenuId.Contains(st.MenuId))
                    {
                        item.MenuName.Add(st.MenuName);
                    }
                }
            }
            DataList = result;
        }

        public IRelayCommand AddUserCommand => new RelayCommand(async () =>
        {
            var result = await Dialog.Show<UserManageDialog>()
             .Initialize<UserManageDialogViewModel>(vm =>
             {
                 vm.Title = "新增用户信息";
                 vm.IsInsert = true;
             })
             .GetResultAsync<bool>();

            if (result) await SearchDataAsync();
        });

        public IRelayCommand<ObservableUserInfoModel> ModifyUserCommand => new RelayCommand<ObservableUserInfoModel>(async (userInfo) =>
        {
            var result = await Dialog.Show<UserManageDialog>()
             .Initialize<UserManageDialogViewModel>(vm =>
             {
                 vm.Title = "修改用户信息";
                 vm.UserInfo = userInfo;
                 vm.IsInsert = false;
             })
             .GetResultAsync<bool>();

            if (result) await SearchDataAsync();
        });

        public IRelayCommand<ObservableUserInfoModel> DeleteCommand => new RelayCommand<ObservableUserInfoModel>(async (userInfo) =>
        {
            if (userInfo != null) {
               var result =  HandyControl.Controls.MessageBox.Show($"确认删除:[{userInfo.Account}]的信息？", "警告",System.Windows.MessageBoxButton.YesNo,System.Windows.MessageBoxImage.Question);
                if (result == System.Windows.MessageBoxResult.Yes) {
                    await _userService.Delete(userInfo.Id);
                }
            }
            await SearchDataAsync();
        });
    }
}
