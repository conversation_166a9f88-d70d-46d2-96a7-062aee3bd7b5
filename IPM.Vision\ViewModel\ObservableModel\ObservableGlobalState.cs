﻿using CommunityToolkit.Mvvm.ComponentModel;
using IPM.Vision.Camera.EDSDKLib;
using IPM.Vision.LEvents;
using IPM.Vision.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public partial class ObservableGlobalState : ViewModelBase
    {
        public delegate void MenuChangedDelegate(MenuModel menu);
        public delegate void CameraControllerDelegate(CameraController cameraController);
        public event Action<ObservableProcessModel> ProcessChangedEvent;
        public event MenuChangedDelegate MenuChangedEvent;
        public event Action<ObservableConfigModel> ConfigChanged;
        public event Action<ObservableProductModel> ProductChanged;
        public event Action<bool> NotifyProgressStatus;
        
        // 图片裁剪任务状态相关事件
        public event Action<bool> CroppingStatusChanged;
        
        private ObservableConfigModel _appConfig;
        private HObservable _hObservable = new HObservable();
        private ObservableProcessModel _processModel;
        private ObservableProductModel _currentProduct;
        private bool _showMainCamera = true;
        
        // 图片裁剪任务状态管理
        private bool _isProcessingPictures = false;
        private int _waitingCropCount = 0;
        private readonly object _cropStatusLock = new object();

        private MenuModel _currentMenu;
        public MenuModel CurrentMenu
        {
            get => _currentMenu;
            set
            {
                if (_currentMenu == value) return;
                _currentMenu = value;
                MenuChangedEvent?.Invoke(value);
            }
        }

        public bool ShowMainCamera
        {
            get => _showMainCamera;
            set => SetProperty(ref _showMainCamera, value);
        }

        public HObservable HObservable { get => _hObservable; }

        public ObservableUserInfoModel LoginUser { get; set; }

        public ObservableConfigModel AppConfig
        {
            get => _appConfig;
            set
            {
                if( _appConfig == value) return;
                _appConfig = value;
                ConfigChanged?.Invoke(value);
            }
        }

        private bool _isRunning = false;

        public bool IsRunning
        {
            get => _isRunning;
            set
            {
                if (_isRunning == value) return;
                NotifyProgressStatus?.Invoke(value);
                SetProperty(ref _isRunning, value);
            }
        }

        private string _weight;
        public string Weight
        {
            get => _weight;
            set => SetProperty(ref _weight, value);
        }

        public ObservableProcessModel CurrentProcessModel
        {
            get => _processModel;
            set
            {
                if( _processModel == value) return;
                ProcessChangedEvent?.Invoke(value);
                SetProperty(ref _processModel, value);
            }
        }

        public ObservableProductModel CurrentProduct
        {
            get => _currentProduct;
            set {
                if( _currentProduct == value) return;
                SetProperty(ref _currentProduct, value);
                ProductChanged?.Invoke(value);
            } 
        }

        /// <summary>
        /// 是否正在处理图片裁剪任务
        /// </summary>
        public bool IsProcessingPictures
        {
            get 
            {
                lock (_cropStatusLock)
                {
                    return _isProcessingPictures;
                }
            }
        }

        /// <summary>
        /// 等待裁剪的图片数量
        /// </summary>
        public int WaitingCropCount
        {
            get 
            {
                lock (_cropStatusLock)
                {
                    return _waitingCropCount;
                }
            }
        }

        /// <summary>
        /// 是否有裁剪任务正在进行或等待中
        /// </summary>
        public bool HasCroppingTasks
        {
            get
            {
                lock (_cropStatusLock)
                {
                    return _isProcessingPictures || _waitingCropCount > 0;
                }
            }
        }

        /// <summary>
        /// 更新图片处理状态
        /// </summary>
        /// <param name="isProcessing">是否正在处理</param>
        /// <param name="waitingCount">等待处理的数量</param>
        public void UpdateCroppingStatus(bool isProcessing, int waitingCount)
        {
            bool statusChanged = false;
            
            lock (_cropStatusLock)
            {
                bool oldHasTasks = _isProcessingPictures || _waitingCropCount > 0;
                
                _isProcessingPictures = isProcessing;
                _waitingCropCount = Math.Max(0, waitingCount); // 确保不为负数
                
                bool newHasTasks = _isProcessingPictures || _waitingCropCount > 0;
                statusChanged = oldHasTasks != newHasTasks;
            }
            
            // 只有状态真正变化时才触发事件
            if (statusChanged)
            {
                CroppingStatusChanged?.Invoke(HasCroppingTasks);
            }
            
            OnPropertyChanged(nameof(IsProcessingPictures));
            OnPropertyChanged(nameof(WaitingCropCount));
            OnPropertyChanged(nameof(HasCroppingTasks));
        }

        /// <summary>
        /// 清除所有裁剪任务状态
        /// </summary>
        public void ClearCroppingStatus()
        {
            UpdateCroppingStatus(false, 0);
        }

    }
}
