﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.CustomControls;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.CustomControls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows;
using IPM.Vision.Mappers;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class LightCtrlDialogViewModel: ViewModelBase, IDialogResultable<bool>
    {
        private bool _result = false;
        private bool _isInsert = false;
        private string _title = string.Empty;
        private readonly NLogHelper _logger;
        private bool _showCamera = false;
        private ObservableLightModel _lightModel;
        private readonly LightParamService _lightParamService;
        private ObservableGlobalState _globalState;
        private string _btnMessage = "打开相机";

        public LightCtrlDialogViewModel(NLogHelper logger,ILightParamService lightParamService, ObservableGlobalState globalState)
        {
            _logger = logger;
            _lightParamService = (LightParamService)lightParamService;
            _globalState = globalState;
        }

        public bool Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        public string BtnMessage
        {
            get => _btnMessage;
            set => SetProperty(ref _btnMessage, value);
        }

        public bool ShowCamera
        {
            get => _showCamera;
            set => SetProperty(ref _showCamera, value);
        }

        public ObservableLightModel LightModel
        {
            get => _lightModel;
            set => SetProperty(ref _lightModel, value);
        }

        public bool IsInsert
        {
            get => _isInsert;
            set => SetProperty(ref _isInsert, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public Action CloseAction { get; set; }

        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            Result = false;
            CloseAction?.Invoke();
        });
        public IRelayCommand<RoutedEventArgs> CameraLoadedCommand => new RelayCommand<RoutedEventArgs>((e) =>
        {
            var temp = (e.Source as Border).Child as CaptureView;
            if (temp != null)
            {
                var models = temp.DataContext as CaptureViewModel;
                models.NeedSetting = false;
                models.HaveChild = true;
            }

        });

        public IRelayCommand OpenCommand => new RelayCommand(() => {
            ShowCamera = !ShowCamera;
            if (!ShowCamera) BtnMessage = "打开相机";
            else BtnMessage = "关闭相机";
        });

        public IRelayCommand SaveCommand => new RelayCommand(async () =>
        {
            if (string.IsNullOrEmpty(LightModel.ParamName))
            {
                HandyControl.Controls.MessageBox.Error("参数名称不能为空！");
                return;
            }
            try
            {
                if (IsInsert)
                {
                    var temp = LightModel.MapTo<ObservableLightModel, LightParamModel>();
                    temp.Id = Guid.NewGuid().ToString();
                    temp.CreateTime = DateTime.Now;
                    temp.Operator = _globalState.LoginUser.Account;
                    Result = await _lightParamService.Add(temp);
                }
                else
                {
                    var temp = LightModel.MapTo<ObservableLightModel, LightParamModel>();
                    Result = await _lightParamService.Update(temp);
                }
                CloseAction?.Invoke();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }
        });

        public IRelayCommand<string> MainLightChangedCommand => new RelayCommand<string>((channel) =>
        {
            switch (channel)
            {
                case "1":
                    _lightParamService.SetLight(1, (byte)LightModel.LOneLuminance);
                    break;
                case "2":
                    _lightParamService.SetLight(2, (byte)LightModel.LTwoLuminance);
                    break;
                case "3":
                    _lightParamService.SetLight(3, (byte)LightModel.LThreeLuminance);
                    break;
                case "4":
                    _lightParamService.SetLight(4, (byte)LightModel.LFourLuminance);
                    break;
                case "5":
                    _lightParamService.SetLight(5, (byte)LightModel.LFiveLuminance);
                    break;
                case "6":
                    _lightParamService.SetLight(6, (byte)LightModel.LSixLuminance);
                    break;
                case "7":
                    _lightParamService.SetLight(7, (byte)LightModel.LSevenLuminance);
                    break;
                case "8":
                    _lightParamService.SetLight(8, (byte)LightModel.LEightLuminance);
                    break;
                case "9":
                    _lightParamService.SetLight(9, (byte)LightModel.LNineLuminance);
                    break;
                case "10":
                    _lightParamService.SetLight(10, (byte)LightModel.LTenLuminance);
                    break;
                case "11":
                    _lightParamService.SetLight(11, (byte)LightModel.LElevenLuminance);
                    break;
                case "12":
                    _lightParamService.SetLight(12, (byte)LightModel.LTwelveLuminance);
                    break;
                case "13":
                    _lightParamService.SetLight(13, (byte)LightModel.LThirteenLuminance);
                    break;
                case "14":
                    _lightParamService.SetLight(14, (byte)LightModel.LFourteenLuminance);
                    break;
                case "15":
                    _lightParamService.SetLight(15, (byte)LightModel.LFifteenLuminance);
                    break;
                case "16":
                    _lightParamService.SetLight(16, (byte)LightModel.LSixteenLuminance);
                    break;
            }
        });
    }
}
