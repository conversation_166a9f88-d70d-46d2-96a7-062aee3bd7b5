﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class PictureStyleComboBox : PropertyComboBox, IObserver
    {
        private EDSDK.EdsPropertyDesc _desc;
        public PictureStyleComboBox()
        {

            if (items == null) items = new Dictionary<uint, string>();
            items.Add(0x0081, "Standard");
            items.Add(0x0082, "Portrait");
            items.Add(0x0088, "FineDetail");
            items.Add(0x0083, "LandsScape");


            items.Add(0x0084, "Neutral");
            items.Add(0x0085, "Faithful");
            items.Add(0x0086, "Monochrome");
            items.Add(0x0087, "Auto");

            //items.Add(0X0021, "User Def. 1");
            //items.Add(0X0022, "User Def. 2");
            //items.Add(0X0023, "User Def. 3");
            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }

        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = (KeyValuePair<uint, string>)e.AddedItems[0];
                CameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_PICTURESTYLE, (IntPtr)selectedItem.Key));

            }
            base.OnSelectionChanged(e);
        }

        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();

                if (propertyID == EDSDK.PropID_PictureStyle)
                {
                    uint property = model.PictureStyle;

                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            this.UpdateProperty(property);
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:
                            _desc = model.PictureStyleDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            this.UpdateProperty(property);
                            break;
                    }
                }
            }
        }
    }
}
