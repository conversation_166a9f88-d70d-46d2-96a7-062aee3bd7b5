﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class DoEvfAFCommand : BasicCommand
    {
        private uint _status;

        public DoEvfAFCommand(ref CanonCameraModel model, uint status)
            : base(ref model)
        {
            _status = status;
        }

        public override bool Execute()
        {
            uint num = EDSDK.EdsSendCommand(_model.Camera, 258u, (int)_status);
            switch (num)
            {
                case 129u:
                    {
                        CameraEvent e2 = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e2);
                        return true;
                    }
                default:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e);
                        return true;
                    }
                case 0u:
                    return true;
            }
        }
    }
}
