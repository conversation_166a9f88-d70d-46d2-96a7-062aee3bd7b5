﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("equipment_para")]
    public class EquipmentModel:BasicModel
    {
        [SugarColumn(ColumnName = "para_name", IsNullable = true)]
        public string ParamName { get; set; }

        [SugarColumn(ColumnName = "x")]
        public float X { get; set; }

        [SugarColumn(ColumnName = "y")]
        public float Y { get; set; }

        [SugarColumn(ColumnName = "z")]
        public float Z { get; set; }

        [SugarColumn(ColumnName = "o")]
        public float O { get; set; }

        [SugarColumn(ColumnName = "r")]
        public float R { get; set; }

        [SugarColumn(ColumnName = "t")]
        public float T { get; set; }


        [SugarColumn(ColumnName = "create_time")]
        public DateTime CreateTime { get; set; }

        [SugarColumn(ColumnName = "operator")]
        public string Operator {  get; set; }

    }
}
