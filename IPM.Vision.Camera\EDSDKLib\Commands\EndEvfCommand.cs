﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class EndEvfCommand : BasicCommand
    {
        public EndEvfCommand(ref CanonCameraModel model)
            : base(ref model)
        {
        }

        public override bool Execute()
        {
            uint num = 0u;
            uint evfOutputDevice = _model.EvfOutputDevice;
            if ((evfOutputDevice & 2) == 0)
            {
                return true;
            }

            if (_model.EvfDepthOfFieldPreview != 0)
            {
                num = EDSDK.EdsGetPropertySize(_model.Camera, 1284u, 0, out var _, out var outSize);
                num = EDSDK.EdsSetPropertyData(_model.Camera, 1284u, 0, outSize, 0u);
                if (num == 0)
                {
                    Thread.Sleep(500);
                }
            }

            if (num == 0)
            {
                evfOutputDevice &= 0xFFFFFFFDu;
                num = EDSDK.EdsGetPropertySize(_model.Camera, 1280u, 0, out var _, out var outSize2);
                num = EDSDK.EdsSetPropertyData(_model.Camera, 1280u, 0, outSize2, evfOutputDevice);
            }

            switch (num)
            {
                case 41218u:
                    return false;
                case 129u:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e);
                        return false;
                    }
                default:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e);
                        return false;
                    }
                case 0u:
                    _model.RollPitch = 1u;
                    _model.NotifyObservers(new CameraEvent(CameraEvent.Type.ANGLEINFO, IntPtr.Zero));
                    return true;
            }
        }
    }
}
