﻿using CommunityToolkit.Mvvm.Input;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.CustomControls;
using IPM.Vision.Common;
using MvCameraControl;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace IPM.Vision.ViewModel.CustomControls
{
    /// <summary>
    /// 海康相机控件ViewModel - 专注于UI展示和用户交互
    /// 连接管理由GlobalCameraManager统一负责
    /// </summary>
    public class HKVisionControlViewModel : ViewModelBase, IDisposable
    {
        private bool _isConnect = false;
        private readonly HKVisionService _hkVisionService;
        private readonly ObservableGlobalState _globalState;
        private readonly EquipmentService _equipmentService;
        private readonly GlobalCameraManager _globalCameraManager; // 添加全局相机管理器引用
        public string _message = "无消息";
        private BitmapSource _renderImageData;
        private float _angle = 0;

        // 控制UnLoad行为的标志
        private static bool _isApplicationShuttingDown = false;
        private bool _isControlLoaded = false;


        public BitmapSource RenderImageData
        {
            get => _renderImageData;
            set => SetProperty(ref _renderImageData, value);
        }

        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        public bool IsCameraConnected
        {
            get => _isConnect;
            set => SetProperty(ref _isConnect, value);
        }
        public float CurrentAngle
        {
            get => _angle;
            set => SetProperty(ref _angle, value);
        }

        public HKVisionControlViewModel(HKVisionService hkVisionService, ObservableGlobalState globalState,
            IEquipmentService equipmentService, GlobalCameraManager globalCameraManager)
        {
            _hkVisionService = hkVisionService;
            _globalState = globalState;
            _equipmentService = (EquipmentService)equipmentService;
            _globalCameraManager = globalCameraManager;

            // **修复：将事件订阅抽取为独立方法，便于重新订阅**
            SubscribeToEvents();

            // 订阅全局相机管理器的连接状态变化
            _globalCameraManager.ConnectionStatusChanged += OnGlobalCameraStatusChanged;
        }

        /// <summary>
        /// 订阅相机和设备事件 - 独立方法便于重新订阅
        /// </summary>
        private void SubscribeToEvents()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[海康控件] 开始订阅事件");

                // 订阅相机服务事件（仅用于UI更新）
                _hkVisionService.CameraConnectedEvent += OnHkCameraConnected;
                _hkVisionService.UpdateGrappingEvent += OnHkCameraFrameUpdate;
                _hkVisionService.DisConnectEvent += OnHkCameraDisconnected;
                _hkVisionService.HKErrorEvent += OnHkCameraError;

                // 订阅设备角度数据
                _equipmentService.McDataNotify += OnEquipmentAngleUpdate;

                System.Diagnostics.Debug.WriteLine("[海康控件] 事件订阅完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] 事件订阅异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 取消订阅相机和设备事件 - 独立方法便于管理
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[海康控件] 开始取消事件订阅");

                // 取消订阅相机服务事件
                if (_hkVisionService != null)
                {
                    _hkVisionService.UpdateGrappingEvent -= OnHkCameraFrameUpdate;
                    _hkVisionService.CameraConnectedEvent -= OnHkCameraConnected;
                    _hkVisionService.DisConnectEvent -= OnHkCameraDisconnected;
                    _hkVisionService.HKErrorEvent -= OnHkCameraError;
                }

                // 取消订阅设备服务事件
                if (_equipmentService != null)
                {
                    _equipmentService.McDataNotify -= OnEquipmentAngleUpdate;
                }

                System.Diagnostics.Debug.WriteLine("[海康控件] 事件订阅取消完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] 取消事件订阅异常: {ex.Message}");
            }
        }

        #region 事件处理方法 - 仅负责UI更新

        /// <summary>
        /// 处理海康相机画面更新事件 - 仅负责UI显示
        /// **关键修复：确保画面能正确显示**
        /// </summary>
        /// <param name="bitmap">从海康相机获取的位图数据</param>
        private void OnHkCameraFrameUpdate(BitmapSource bitmap)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] 🎯 收到画面更新事件");

                if (bitmap == null)
                {
                    System.Diagnostics.Debug.WriteLine("[海康控件] ❌ 收到空的位图数据");
                    return;
                }

                // 基本画面数据验证
                if (bitmap.PixelWidth <= 0 || bitmap.PixelHeight <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"[海康控件] ❌ 收到无效尺寸的画面数据: {bitmap.PixelWidth}x{bitmap.PixelHeight}");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"[海康控件] ✅ 收到有效画面数据: {bitmap.PixelWidth}x{bitmap.PixelHeight}");

                // **关键修复：确保在UI线程中更新画面**
                if (Application.Current?.Dispatcher != null)
                {
                    if (Application.Current.Dispatcher.CheckAccess())
                    {
                        // 已在UI线程中，直接更新
                        System.Diagnostics.Debug.WriteLine("[海康控件] 🖼️ 在UI线程中直接更新画面");
                        RenderImageData = bitmap;
                        System.Diagnostics.Debug.WriteLine("[海康控件] ✅ 画面数据已设置到RenderImageData");
                    }
                    else
                    {
                        // 需要调度到UI线程
                        System.Diagnostics.Debug.WriteLine("[海康控件] 🔄 调度到UI线程更新画面");
                        Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            try
                            {
                                RenderImageData = bitmap;
                                System.Diagnostics.Debug.WriteLine("[海康控件] ✅ UI线程中画面数据已更新");
                            }
                            catch (Exception uiEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"[海康控件] ❌ UI线程更新异常: {uiEx.Message}");
                            }
                        }));
                    }
                }
                else
                {
                    // 如果没有Dispatcher，直接更新（测试环境）
                    System.Diagnostics.Debug.WriteLine("[海康控件] ⚠️ 无Dispatcher，直接更新画面");
                    RenderImageData = bitmap;
                }

                System.Diagnostics.Debug.WriteLine($"[海康控件] 🎉 画面更新处理完成: {bitmap.PixelWidth}x{bitmap.PixelHeight}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] ❌ 画面更新严重异常: {ex.Message}");
                // **防止崩溃：不重新抛出异常**
            }
        }

        /// <summary>
        /// 处理设备角度数据更新
        /// </summary>
        private void OnEquipmentAngleUpdate(int arg1, float arg2)
        {
            CurrentAngle = arg2;
        }

        /// <summary>
        /// 处理海康相机断开连接事件 - 仅更新UI状态
        /// </summary>
        private void OnHkCameraDisconnected()
        {
            System.Diagnostics.Debug.WriteLine("[海康控件] 收到相机断开连接事件");
            IsCameraConnected = false;
            RenderImageData = null; // 清理画面数据

            // 通知UI状态变化
            _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
            {
                EventCode = LEvents.HEventCode.SUCCESS,
                StatusShowType = LEvents.ShowType.ICON,
                SourceType = LEvents.SourceType.LIGHT,
                EquipmentStatus = 4
            });
        }

        /// <summary>
        /// 处理海康相机连接成功事件 - 仅更新UI状态
        /// </summary>
        private void OnHkCameraConnected()
        {
            System.Diagnostics.Debug.WriteLine("[海康控件] 收到相机连接事件");
            IsCameraConnected = true;

            // 通知UI状态变化
            _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
            {
                EventCode = LEvents.HEventCode.SUCCESS,
                StatusShowType = LEvents.ShowType.ALL,
                SourceType = LEvents.SourceType.LIGHT,
                EquipmentStatus = 1,
                EventMessage = "Mark相机连接成功！"
            });
        }

        /// <summary>
        /// 处理海康相机错误事件 - 仅显示错误信息
        /// </summary>
        private void OnHkCameraError(string message)
        {
            _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
            {
                EventCode = LEvents.HEventCode.ERROR,
                StatusShowType = LEvents.ShowType.ALL,
                SourceType = LEvents.SourceType.LIGHT,
                EventMessage = $"Mark相机错误: {message}",
                EquipmentStatus = 4
            });
        }

        /// <summary>
        /// 处理全局相机管理器状态变化 - 同步连接状态显示
        /// </summary>
        private void OnGlobalCameraStatusChanged(string statusMessage)
        {
            System.Diagnostics.Debug.WriteLine($"[海康控件] 全局相机状态变化: {statusMessage}");

            // 同步连接状态显示（从全局管理器获取真实状态）
            if (_globalCameraManager != null)
            {
                bool globalHkStatus = _globalCameraManager.IsHkCameraConnected;
                if (IsCameraConnected != globalHkStatus)
                {
                    IsCameraConnected = globalHkStatus;
                    System.Diagnostics.Debug.WriteLine($"[海康控件] 同步连接状态: {globalHkStatus}");
                }
            }
        }

        #endregion

        #region 命令定义 - 专注于UI操作

        /// <summary>
        /// 控件加载命令 - 仅负责UI初始化，连接管理由GlobalCameraManager负责
        /// **安全修复：全面异常保护防止加载崩溃**
        /// </summary>
        public IRelayCommand<RoutedEventArgs> LoadCommand => new RelayCommand<RoutedEventArgs>(async (args) =>
        {
            try
            {
                if (args?.Source is HKCameraControl control)
                {
                    _isControlLoaded = true;
                    System.Diagnostics.Debug.WriteLine("[海康控件] 控件已加载，开始同步连接状态和画面流");

                    // **关键修复：确保事件订阅正确**
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("[海康控件] 🔄 确保画面更新事件正确订阅");

                        // 先取消订阅，避免重复订阅
                        _hkVisionService.UpdateGrappingEvent -= OnHkCameraFrameUpdate;

                        // 重新订阅
                        _hkVisionService.UpdateGrappingEvent += OnHkCameraFrameUpdate;

                        System.Diagnostics.Debug.WriteLine("[海康控件] ✅ 画面更新事件重新订阅完成");
                    }
                    catch (Exception eventEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"[海康控件] 事件订阅异常: {eventEx.Message}");
                        // 强制重新订阅所有事件
                        try
                        {
                            SubscribeToEvents();
                        }
                        catch (Exception retryEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"[海康控件] 强制重新订阅异常: {retryEx.Message}");
                        }
                    }

                    // **安全修复：包装所有GlobalCameraManager操作**
                    try
                    {
                        // 从全局管理器同步连接状态
                        if (_globalCameraManager != null)
                        {
                            IsCameraConnected = _globalCameraManager.IsHkCameraConnected;
                            System.Diagnostics.Debug.WriteLine($"[海康控件] 同步连接状态: {IsCameraConnected}");

                            // **关键修复：页面切换回来时，确保画面流正常恢复**
                            if (IsCameraConnected)
                            {
                                System.Diagnostics.Debug.WriteLine($"[海康控件] 相机已连接，当前流状态: {_globalCameraManager.IsHkStreamActive}");

                                // **修复：无论流状态如何，都尝试确保画面流正常工作**
                                // 这是因为页面切换可能导致流状态不一致
                                await Task.Delay(200); // 短暂延迟，等待页面切换完成

                                try
                                {
                                    // **关键修复：页面切换回来时，强制重新启动画面流确保正常显示**
                                    bool isCurrentlyGrabbing = _hkVisionService.IsGrabbing;
                                    System.Diagnostics.Debug.WriteLine($"[海康控件] 页面切换回来，当前采集状态: {isCurrentlyGrabbing}");

                                    // **修复：无论当前状态如何，都重新启动一次采集确保画面正常**
                                    System.Diagnostics.Debug.WriteLine("[海康控件] 页面切换恢复：强制重新启动采集确保画面流畅");

                                    // 先停止当前采集
                                    _hkVisionService.StopGrap();
                                    await Task.Delay(300);

                                    // 重新启动采集
                                    _hkVisionService.StartGrap();
                                    await Task.Delay(500);

                                    bool finalStatus = _hkVisionService.IsGrabbing;
                                    System.Diagnostics.Debug.WriteLine($"[海康控件] 页面切换恢复后采集状态: {finalStatus}");

                                    if (!finalStatus)
                                    {
                                        // 如果还是失败，再尝试一次
                                        System.Diagnostics.Debug.WriteLine("[海康控件] 采集启动失败，再次尝试");
                                        await Task.Delay(500);
                                        _hkVisionService.StartGrap();
                                        await Task.Delay(500);

                                        bool retryStatus = _hkVisionService.IsGrabbing;
                                        System.Diagnostics.Debug.WriteLine($"[海康控件] 重试后采集状态: {retryStatus}");
                                    }
                                }
                                catch (Exception streamEx)
                                {
                                    System.Diagnostics.Debug.WriteLine($"[海康控件] 启动画面流异常: {streamEx.Message}");
                                    // 继续执行，不因为画面流问题而崩溃
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("[海康控件] 相机未连接，跳过画面流启动");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("[海康控件] 全局相机管理器未初始化");
                        }
                    }
                    catch (Exception managerEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"[海康控件] 全局管理器操作异常: {managerEx.Message}");
                        // 继续执行，不因为管理器问题而崩溃
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] LoadCommand严重异常: {ex.Message}");
                // **防止崩溃：记录异常但不重新抛出**
                try
                {
                    _globalState?.HObservable?.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                    {
                        EventCode = LEvents.HEventCode.ERROR,
                        StatusShowType = LEvents.ShowType.ALL,
                        SourceType = LEvents.SourceType.LIGHT,
                        EventMessage = $"海康控件加载异常: {ex.Message}",
                        EquipmentStatus = 4
                    });
                }
                catch
                {
                    // 如果连状态通知都失败，说明系统有严重问题，但仍然不崩溃
                }
            }
        });

        /// <summary>
        /// 启动画面采集命令
        /// **安全修复：防止用户操作崩溃**
        /// </summary>
        public IRelayCommand StartGrapCommand => new RelayCommand(() => {
            try
            {
                if (_hkVisionService.IsConnected)
                {
                    _hkVisionService.StartGrap();
                    System.Diagnostics.Debug.WriteLine("[海康控件] 用户手动启动画面采集");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[海康控件] 相机未连接，无法启动采集");
                    try
                    {
                        _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                        {
                            EventCode = LEvents.HEventCode.WARNING,
                            StatusShowType = LEvents.ShowType.ALL,
                            SourceType = LEvents.SourceType.LIGHT,
                            EventMessage = "相机未连接，无法启动采集",
                            EquipmentStatus = 3
                        });
                    }
                    catch { }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] StartGrapCommand异常: {ex.Message}");
                try
                {
                    _globalState?.HObservable?.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                    {
                        EventCode = LEvents.HEventCode.ERROR,
                        StatusShowType = LEvents.ShowType.ALL,
                        SourceType = LEvents.SourceType.LIGHT,
                        EventMessage = $"启动采集异常: {ex.Message}",
                        EquipmentStatus = 4
                    });
                }
                catch { }
            }
        });

        /// <summary>
        /// 停止画面采集命令
        /// **安全修复：防止用户操作崩溃**
        /// </summary>
        public IRelayCommand StopGrapCommand => new RelayCommand(() => {
            try
            {
                _hkVisionService.StopGrap();
                System.Diagnostics.Debug.WriteLine("[海康控件] 用户手动停止画面采集");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] StopGrapCommand异常: {ex.Message}");
                try
                {
                    _globalState?.HObservable?.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                    {
                        EventCode = LEvents.HEventCode.ERROR,
                        StatusShowType = LEvents.ShowType.ALL,
                        SourceType = LEvents.SourceType.LIGHT,
                        EventMessage = $"停止采集异常: {ex.Message}",
                        EquipmentStatus = 4
                    });
                }
                catch { }
            }
        });

        /// <summary>
        /// 重连命令 - 通知全局管理器检查连接状态
        /// **安全修复：防止重连操作崩溃**
        /// </summary>
        public IRelayCommand ReconnectCommand => new RelayCommand(async () => {
            try
            {
                System.Diagnostics.Debug.WriteLine("[海康控件] 用户请求重连，通知全局管理器检查连接状态");

                if (_globalCameraManager != null)
                {
                    await _globalCameraManager.CheckAndRecoverConnectionsAsync();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("[海康控件] 全局管理器不可用，无法执行重连");
                    try
                    {
                        _globalState?.HObservable?.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                        {
                            EventCode = LEvents.HEventCode.WARNING,
                            StatusShowType = LEvents.ShowType.ALL,
                            SourceType = LEvents.SourceType.LIGHT,
                            EventMessage = "全局相机管理器不可用",
                            EquipmentStatus = 3
                        });
                    }
                    catch { }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] ReconnectCommand异常: {ex.Message}");
                try
                {
                    _globalState?.HObservable?.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                    {
                        EventCode = LEvents.HEventCode.ERROR,
                        StatusShowType = LEvents.ShowType.ALL,
                        SourceType = LEvents.SourceType.LIGHT,
                        EventMessage = $"重连操作异常: {ex.Message}",
                        EquipmentStatus = 4
                    });
                }
                catch { }
            }
        });

        /// <summary>
        /// 拍照命令
        /// **安全修复：防止拍照操作崩溃**
        /// </summary>
        public IRelayCommand TakePictureCommand => new RelayCommand(() => {
            try
            {
                // 拍照功能实现
                System.Diagnostics.Debug.WriteLine("[海康控件] 用户请求拍照");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] TakePictureCommand异常: {ex.Message}");
            }
        });

        /// <summary>
        /// 测试画面显示命令 - 用于调试画面显示问题
        /// </summary>
        public IRelayCommand TestDisplayCommand => new RelayCommand(() => {
            try
            {
                System.Diagnostics.Debug.WriteLine("[海康控件] 🔍 开始测试画面显示状态");

                // 检查连接状态
                bool isConnected = _hkVisionService.IsConnected;
                bool isGrabbing = _hkVisionService.IsGrabbing;
                System.Diagnostics.Debug.WriteLine($"[海康控件] 📊 相机连接状态: {isConnected}");
                System.Diagnostics.Debug.WriteLine($"[海康控件] 📊 采集状态: {isGrabbing}");
                System.Diagnostics.Debug.WriteLine($"[海康控件] 📊 当前画面数据: {(RenderImageData != null ? "有数据" : "无数据")}");

                // 如果连接但没有画面，尝试重新启动采集
                if (isConnected && !isGrabbing)
                {
                    System.Diagnostics.Debug.WriteLine("[海康控件] 🔄 相机已连接但未采集，尝试启动采集");
                    _hkVisionService.StartGrap();
                }
                else if (isConnected && isGrabbing && RenderImageData == null)
                {
                    System.Diagnostics.Debug.WriteLine("[海康控件] ⚠️ 相机正在采集但无画面数据，可能是事件订阅问题");

                    // 重新订阅事件
                    _hkVisionService.UpdateGrappingEvent -= OnHkCameraFrameUpdate;
                    _hkVisionService.UpdateGrappingEvent += OnHkCameraFrameUpdate;
                    System.Diagnostics.Debug.WriteLine("[海康控件] 🔄 已重新订阅画面更新事件");
                }
                else if (!isConnected)
                {
                    System.Diagnostics.Debug.WriteLine("[海康控件] ❌ 相机未连接，尝试重新连接");
                    _hkVisionService.RefreshAndConnect();
                }

                System.Diagnostics.Debug.WriteLine("[海康控件] ✅ 画面显示测试完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] TestDisplayCommand异常: {ex.Message}");
            }
        });

        /// <summary>
        /// 旋转命令
        /// **安全修复：防止旋转操作崩溃**
        /// </summary>
        public IRelayCommand RotateCommand => new RelayCommand(() => {
            try
            {
                // 旋转功能实现
                System.Diagnostics.Debug.WriteLine("[海康控件] 用户请求旋转");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] RotateCommand异常: {ex.Message}");
            }
        });

        /// <summary>
        /// 控件卸载命令 - 调整事件清理策略
        /// **安全修复：防止页面卸载崩溃**
        /// </summary>
        public IRelayCommand UnLoadCommand => new RelayCommand(() => {
            try
            {
                // 检查是否为应用程序关闭
                bool isAppShuttingDown = false;
                try
                {
                    isAppShuttingDown = _isApplicationShuttingDown ||
                                          Environment.HasShutdownStarted ||
                                          (Application.Current?.MainWindow?.IsVisible == false);
                }
                catch
                {
                    // 如果检查过程有异常，假设是正常页面切换
                    isAppShuttingDown = false;
                }

                if (isAppShuttingDown)
                {
                    // 应用程序关闭时，完全清理资源
                    System.Diagnostics.Debug.WriteLine("[海康控件] 应用程序关闭，清理所有资源");
                    try
                    {
                        CleanupResources();
                    }
                    catch (Exception cleanupEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"[海康控件] 应用关闭清理异常: {cleanupEx.Message}");
                    }
                }
                else if (_isControlLoaded)
                {
                    // **修复：页面切换时，只清理画面显示相关的事件，保留核心事件订阅**
                    System.Diagnostics.Debug.WriteLine("[海康控件] 页面切换，使用优化的事件清理策略");
                    try
                    {
                        CleanupUIEventsOptimized();
                    }
                    catch (Exception uiCleanupEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"[海康控件] UI清理异常: {uiCleanupEx.Message}");
                    }
                    _isControlLoaded = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] UnLoadCommand严重异常: {ex.Message}");
                // **防止崩溃：重置关键状态**
                try
                {
                    _isControlLoaded = false;
                    RenderImageData = null;
                }
                catch { }
            }
        });

        #endregion
        
        #region 资源清理

        /// <summary>
        /// 清理控件资源 - 应用程序关闭时使用
        /// </summary>
        private void CleanupResources()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[海康控件] 开始清理所有资源");

                // 1. 重置连接状态显示
                IsCameraConnected = false;

                // 2. 清理图像数据
                try
                {
                    RenderImageData = null;
                }
                catch
                {
                    // 忽略UI清理异常
                }

                // 3. 解除所有事件订阅
                CleanupAllEventSubscriptions();

                System.Diagnostics.Debug.WriteLine("[海康控件] 资源清理完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] 资源清理异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 优化的UI事件清理 - 页面切换时使用，保留必要的事件订阅
        /// **关键修复：页面切换时不清理任何事件订阅，确保画面正常显示**
        /// </summary>
        private void CleanupUIEventsOptimized()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[海康控件] 页面切换优化清理：保留所有事件订阅");

                // **关键修复：页面切换时不清理任何事件订阅**
                // 只清空当前显示的画面数据，但保留所有事件订阅
                // 这样确保页面切换回来时事件仍然有效

                // 只清空当前显示的画面数据，不清理事件订阅
                RenderImageData = null;

                // **重要：不清理任何事件订阅，保持连接**
                // 这样确保页面切换回来时画面更新事件仍然有效

                System.Diagnostics.Debug.WriteLine("[海康控件] 页面切换清理完成，所有事件订阅保持不变");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] 优化UI事件清理异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 只清理UI相关的事件订阅，不关闭相机连接 - 完全清理时使用
        /// </summary>
        private void CleanupUIEvents()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[海康控件] 清理UI事件订阅");
                UnsubscribeFromEvents();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] UI事件清理异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理所有事件订阅 - 现在使用独立的取消订阅方法
        /// </summary>
        private void CleanupAllEventSubscriptions()
        {
            try
            {
                // 清理相机和设备事件
                UnsubscribeFromEvents();

                // 清理全局管理器事件
                if (_globalCameraManager != null)
                {
                    _globalCameraManager.ConnectionStatusChanged -= OnGlobalCameraStatusChanged;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[海康控件] 事件订阅清理异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 实现IDisposable接口
        /// </summary>
        public void Dispose()
        {
            CleanupResources();
        }

        #endregion

        #region 静态方法

        /// <summary>
        /// 设置应用程序关闭状态（供主窗口调用）
        /// </summary>
        public static void SetApplicationShuttingDown()
        {
            _isApplicationShuttingDown = true;
            System.Diagnostics.Debug.WriteLine("[海康控件] 应用程序关闭状态已设置");
        }

        #endregion
    }
}
