﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class ExposureCompComboBox : PropertyComboBox, IObserver
    {

        private EDSDK.EdsPropertyDesc _desc;
        public ExposureCompComboBox()
        {
            this.Name = "ae_combo";
            if (this.items == null) items = new Dictionary<uint, string>();
            items.Add(0x18, "+3");
            items.Add(0x15, "+2 2/3");
            items.Add(0x14, "+2 1/2");
            items.Add(0x13, "+2 1/3");
            items.Add(0x10, "+2");
            items.Add(0x0d, "+1 2/3");
            items.Add(0x0c, "+1 1/2");
            items.Add(0x0b, "+1 1/3");
            items.Add(0x08, "+1");
            items.Add(0x05, "+2/3");
            items.Add(0x04, "+1/2");
            items.Add(0x03, "+1/3");
            items.Add(0x00, "0");
            items.Add(0xfd, "-1/3");
            items.Add(0xfc, "-1/2");
            items.Add(0xfb, "-2/3");
            items.Add(0xf8, "-1");
            items.Add(0xf5, "-1 1/3");
            items.Add(0xf4, "-1 1/2");
            items.Add(0xf3, "-1 2/3");
            items.Add(0xf0, "-2");
            items.Add(0xed, "-2 1/3");
            items.Add(0xec, "-2 1/2");
            items.Add(0xeb, "-2 2/3");
            items.Add(0xe8, "-3");
            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }

        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = (KeyValuePair<uint, string>)e.AddedItems[0];
                CameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_EXPOSURE_COMPENSATION, (IntPtr)selectedItem.Key));

            }
            base.OnSelectionChanged(e);
        }

        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();

                if (propertyID == EDSDK.PropID_ExposureCompensation)
                {
                    uint property = model.ExposureCompensation;
                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:

                            this.UpdateProperty(property);
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:

                            _desc = model.ExposureCompensationDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            this.UpdateProperty(property);
                            break;
                    }
                }
            }
        }
    }
}
