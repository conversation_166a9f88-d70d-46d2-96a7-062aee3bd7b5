﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;

namespace IPM.Vision.Common.Converts
{
    class StorageVisiableConvert : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is FolderEnum intValue)
            {
                bool reverse = parameter is string param && param.Equals("Reverse", StringComparison.OrdinalIgnoreCase);

                bool isVisible = intValue == FolderEnum.LOCAL;

                if (reverse)
                {
                    isVisible = !isVisible;
                }

                return isVisible ? Visibility.Visible : Visibility.Collapsed;
            }

            throw new InvalidOperationException("The value must be of type FolderEnum.");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
