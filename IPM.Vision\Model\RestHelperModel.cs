﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    public class QrDetectResponse
    {
        public string Message { get; set; }
        public float Duration { get; set; }
        public int Status { get; set; }
        public QrData[] Data { get; set; }
        public string Event_Id { get; set; }
        public string Event_Name { get; set; }
    }

    public class QrData
    {
        public string Type { get; set; }
        public string Data { get; set; }
    }

    public class CropImageModel
    {
        public string Message { get; set; }
        public float Duration { get; set; }
        public string Output_Path { get; set; }
        public int Status { get; set; }
        public string Event_Id { get; set; }
        public string Event_Name { get; set; }
    }

    public class MarkModel
    {
        // 定义属性以匹配JSON中的字段
        public string Event_Id { get; set; }
        public string Event_Name { get; set; }
        public DataItem[] Data { get; set; }
        public double Duration { get; set; } // duration字段是浮点数，因此使用double
        public string Image_Path { get; set; } // JSON中的image_path字段
        public string Message { get; set; }
        public int Status { get; set; }
    }

    public class DataItem
    {
        public BottomRight Bottom_Right { get; set; } // 使用 PascalCase 命名
        public Center Center { get; set; }
        public string Class { get; set; }
        public double Confidence { get; set; } // confidence字段是浮点数，因此使用double
        public int Diff_X { get; set; } // 使用 PascalCase 命名
        public int Diff_Y { get; set; } // 使用 PascalCase 命名
        public int Id { get; set; }
        public TopLeft Top_Left { get; set; } // 使用 PascalCase 命名
    }

    public class BottomRight
    {
        public int X { get; set; }
        public int Y { get; set; }
    }

    public class Center
    {
        public int X { get; set; }
        public int Y { get; set; }
    }

    public class TopLeft
    {
        public int X { get; set; }
        public int Y { get; set; }
    }

    public class BmpData
    {
        public List<BmpObject> BmpObjects { get; set; }
    }

    public class BmpObject
    {
        public double xmin { get; set; }
        public double ymin { get; set; }
        public double xmax { get; set; }
        public double ymax { get; set; }
        public double confidence { get; set; }
        public int classValue { get; set; }
        public string name { get; set; }
    }
}
