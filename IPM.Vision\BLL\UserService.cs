﻿using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.BLL
{
    public class UserService : BaseService<UserModel>, IUserService
    {
        public UserService(IBaseRepository<UserModel> baseRepository) : base(baseRepository)
        {
        }

        public async void AddSuperUser()
        {
            UserModel userModel = new UserModel()
            {
                Id = Guid.NewGuid().ToString(),
                Account = "admin",
                Password = "1234",
                IsSupper = true,
                MenuId = "1,2,3,4,5,6,7,8,9,10",
                UserName = "超级管理员",
            };
            await this.Add(userModel);
        }

    }
}
