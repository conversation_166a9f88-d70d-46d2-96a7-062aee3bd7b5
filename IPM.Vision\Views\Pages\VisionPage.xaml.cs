﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using IPM.Vision.ViewModel.Pages;
using IPM.Vision.ViewModel.ObservableModel;
using CommunityToolkit.Mvvm.Input;

namespace IPM.Vision.Views.Pages
{
    /// <summary>
    /// VisionPage.xaml 的交互逻辑
    /// </summary>
    public partial class VisionPage : Page
    {
        public VisionPage()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 嵌套DataGrid行双击事件处理 - 发送坐标到设备
        /// </summary>
        private void DataGridRow_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            try
            {
                // 获取被双击的DataGridRow
                var row = sender as DataGridRow;
                if (row?.DataContext is ObservablePinPosition pinPosition)
                {
                    // 获取VisionPageViewModel
                    var viewModel = this.DataContext as VisionPageViewModel;
                    if (viewModel?.SendToEquipmentCommand?.CanExecute(pinPosition) == true)
                    {
                        // 执行发送到设备的命令
                        viewModel.SendToEquipmentCommand.Execute(pinPosition);
                        
                        // 可选：添加日志提示
                        System.Diagnostics.Debug.WriteLine($"双击发送坐标到设备 - 引脚{pinPosition.Index}: X={pinPosition.X:F3}, Y={pinPosition.Y:F3}, Z={pinPosition.Z:F3}");
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录异常但不影响UI
                System.Diagnostics.Debug.WriteLine($"双击事件处理异常: {ex.Message}");
            }
        }
    }
}
