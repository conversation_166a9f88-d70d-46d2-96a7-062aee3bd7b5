﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public partial class ObservablePinPosition : ViewModelBase
    {
        private int _index;
        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        private float _x;
        public float X
        {
            get => _x;
            set => SetProperty(ref _x, value);
        }

        private float _y;
        public float Y
        {
            get => _y;
            set => SetProperty(ref _y, value);
        }

        private float _z;
        public float Z
        {
            get => _z;
            set => SetProperty(ref _z, value);
        }

        private float _r;
        public float R
        {
            get => _r;
            set => SetProperty(ref _r, value);
        }

        private float _t;
        public float T
        {
            get => _t;
            set => SetProperty(ref _t, value);
        }

        private int _edgeNumber;
        /// <summary>
        /// 所属边的编号 (1-4)
        /// </summary>
        public int EdgeNumber
        {
            get => _edgeNumber;
            set => SetProperty(ref _edgeNumber, value);
        }

        private int _pinIndexInEdge;
        /// <summary>
        /// 在当前边中的pin索引 (从0开始)
        /// </summary>
        public int PinIndexInEdge
        {
            get => _pinIndexInEdge;
            set => SetProperty(ref _pinIndexInEdge, value);
        }

        private string _edgeName;
        /// <summary>
        /// 边的名称 (上边、右边、下边、左边)
        /// </summary>
        public string EdgeName
        {
            get => _edgeName;
            set => SetProperty(ref _edgeName, value);
        }
    }
}
