﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class UIUnlockedCommand : BasicCommand
    {
        private uint _status;

        public UIUnlockedCommand(ref CanonCameraModel model, uint paramter)
            : base(ref model)
        {
            _status = paramter;
        }

        public override bool Execute()
        {
            uint num = EDSDK.EdsSendStatusCommand(_model.Camera, 1u, (int)_status);
            if (num != 0)
            {
                CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                _model.NotifyObservers(e);
            }

            return true;
        }
    }
}
