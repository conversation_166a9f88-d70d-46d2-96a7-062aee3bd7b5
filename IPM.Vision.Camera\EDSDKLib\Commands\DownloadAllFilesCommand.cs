﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class DownloadAllFilesCommand : BasicCommand
    {
        private static FileCounterCommand.FileNumber _fileNumber;

        private static int _currentFileNum = 0;

        private static int _fileCount = 0;

        private static List<IntPtr> _imageItems = new List<IntPtr>();

        private CameraEvent _event;

        private IntPtr _volume;

        public DownloadAllFilesCommand(ref CanonCameraModel model, ref IntPtr volume)
            : base(ref model)
        {
            _volume = volume;
        }

        public override bool Execute()
        {
            uint num = 0u;
            IntPtr camera = _model.Camera;
            IntPtr directoryItem = IntPtr.Zero;
            FileCounterCommand fileCounterCommand = new FileCounterCommand(ref _model, ref _volume);
            if (_currentFileNum == 0)
            {
                num = fileCounterCommand.CountDirectory(camera, ref directoryItem, out var directory_count);
                _fileNumber = new FileCounterCommand.FileNumber(directory_count);
                _fileNumber.DcimItem = directoryItem;
                if (fileCounterCommand.CountImages(camera, ref directoryItem, ref directory_count, ref _fileCount, ref _fileNumber, ref _imageItems) != 0)
                {
                    return false;
                }

                _event = new CameraEvent(CameraEvent.Type.DOWNLOAD_START, (IntPtr)_fileCount);
                _model.NotifyObservers(_event);
            }

            if (DownloadImageByDirectory(camera) != 0)
            {
                return false;
            }

            return true;
        }

        private uint DownloadImageByDirectory(IntPtr camera)
        {
            EDSDK.EdsDirectoryItemInfo outDirItemInfo = default(EDSDK.EdsDirectoryItemInfo);
            outDirItemInfo.szFileName = "";
            outDirItemInfo.Size = 0uL;
            int num = 0;
            for (num = 0; num < _imageItems.Count; num++)
            {
                IntPtr intPtr = _imageItems[_currentFileNum];
                uint num2 = EDSDK.EdsGetDirectoryItemInfo(intPtr, out outDirItemInfo);
                if (num2 != 0)
                {
                    return num2;
                }

                string folderPath = Environment.GetFolderPath(Environment.SpecialFolder.MyPictures);
                folderPath = folderPath + "\\" + outDirItemInfo.szFileName;
                num2 = EDSDK.EdsCreateFileStream(folderPath, EDSDK.EdsFileCreateDisposition.CreateAlways, EDSDK.EdsAccess.ReadWrite, out var outStream);
                if (num2 != 0)
                {
                    return num2;
                }

                num2 = EDSDK.EdsDownload(intPtr, outDirItemInfo.Size, outStream);
                if (num2 != 0)
                {
                    return num2;
                }

                num2 = EDSDK.EdsDownloadComplete(intPtr);
                if (num2 != 0)
                {
                    return num2;
                }

                num2 = EDSDK.EdsRelease(intPtr);
                intPtr = IntPtr.Zero;
                num2 = EDSDK.EdsRelease(outStream);
                outStream = IntPtr.Zero;
                _currentFileNum++;
                if (_model._ExecuteStatus == CanonCameraModel.Status.CANCELING)
                {
                    _event = new CameraEvent(CameraEvent.Type.DOWNLOAD_COMPLETE, (IntPtr)num);
                    _model.NotifyObservers(_event);
                    _currentFileNum = 0;
                    _imageItems.Clear();
                }
                else
                {
                    _event = new CameraEvent(CameraEvent.Type.PROGRESS_REPORT, (IntPtr)_currentFileNum);
                    _model.NotifyObservers(_event);
                }
            }

            _event = new CameraEvent(CameraEvent.Type.DOWNLOAD_COMPLETE, camera);
            _model.NotifyObservers(_event);
            _currentFileNum = 0;
            _imageItems.Clear();
            return 0u;
        }
    }
}
