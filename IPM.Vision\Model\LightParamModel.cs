﻿using IPM.Vision.ViewModel;
using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    [SugarTable("light_para")]
    public class LightParamModel:BasicModel
    {
        [SugarColumn(ColumnName = "para_name",IsNullable = true)]
        public string ParamName { get; set; }

        [SugarColumn(ColumnName = "l_one_luminance")]
        public int LOneLuminance { get; set; }

        [SugarColumn(ColumnName = "l_two_luminance")]
        public int LTwoLuminance { get; set; }

        [SugarColumn(ColumnName = "l_three_luminance")]
        public int LThreeLuminance { get; set; }

        [SugarColumn(ColumnName = "l_four_luminance")]
        public int LFourLuminance { get; set; }

        [SugarColumn(ColumnName = "l_five_luminance")]
        public int LFiveLuminance { get; set; }

        [SugarColumn(ColumnName = "l_six_luminance")]
        public int LSixLuminance { get; set; }

        [SugarColumn(ColumnName = "l_seven_luminance")]
        public int LSevenLuminance { get; set; }

        [SugarColumn(ColumnName = "l_eight_luminance")]
        public int LEightLuminance { get; set; }

        [SugarColumn(ColumnName = "l_nine_luminance")]
        public int LNineLuminance { get; set; }

        [SugarColumn(ColumnName = "l_ten_luminance")]
        public int LTenLuminance { get; set; }

        [SugarColumn(ColumnName = "l_eleven_luminance")]
        public int LElevenLuminance { get; set; }

        [SugarColumn(ColumnName = "l_twelve_luminance")]
        public int LTwelveLuminance { get; set; }

        [SugarColumn(ColumnName = "l_thirteen_luminance")]
        public int LThirteenLuminance { get; set; }

        [SugarColumn(ColumnName = "l_fourteen_luminance")]
        public int LFourteenLuminance { get; set; }

        [SugarColumn(ColumnName = "l_fifteen_luminance")]
        public int LFifteenLuminance { get; set; }

        [SugarColumn(ColumnName = "l_sixteen_luminance")]
        public int LSixteenLuminance { get; set; }

        [SugarColumn(ColumnName = "operator")]
        public string Operator { get; set; }

        [SugarColumn(ColumnName = "create_time")]
        public DateTime CreateTime { get; set; }
    }
}
