﻿using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace IPM.Vision.BLL
{
    public class CheckService
    {
        private readonly NLogHelper _logger;
        private readonly HttpClient _client;
        private readonly ObservableGlobalState _globalState;
        private readonly ExcelHelper _excelHelper;
        private readonly ReportService _reportService;
        private string _succesName = "ACK";
        private string _failName = "NACK";
        private readonly string checkPath = "http://127.0.0.1:5002/detect_all";
        private readonly string checkSpecificPath = "http://127.0.0.1:5002/detect_specific";
        public Action<string, bool, ReportModel> CheckCompleteEvent;

        private System.Threading.Timer _timer;
        private Task _timerTask;
        private CancellationTokenSource _cancellationTokenSource;

        public CheckService(NLogHelper logger, ObservableGlobalState globalState, ExcelHelper excelHelper, IReportService reportService)
        {
            _logger = logger;
            _globalState = globalState;
            _excelHelper = excelHelper;
            _reportService = (ReportService)reportService;
            _client = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(1) // 设置超时时间为10分钟
            };
        }

        public void StartGetData()
        {
            try
            {
                _logger.LogInfo("🔍 启动AI结果监控服务...");

                // **关键修复：启动前先清理资源，防止第二轮启动时的资源冲突**
                try
                {
                    _logger.LogInfo("🧹 AI监控启动前资源清理...");
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                    _logger.LogInfo("✅ AI监控启动前资源清理完成");
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogError($"❌ AI监控启动前资源清理异常: {cleanupEx.Message}");
                }

                // **安全检查：确保配置有效**
                if (_globalState?.AppConfig?.AiResult == null)
                {
                    _logger.LogError("❌ AI结果路径配置无效，无法启动监控服务");
                    return;
                }

                _succesName = FileHelper.ConcatFile(_globalState.AppConfig.AiResult, _succesName);
                _failName = FileHelper.ConcatFile(_globalState.AppConfig.AiResult, _failName);

                // **安全的文件夹创建**
                try
                {
                    FileHelper.CreateFolder(_succesName);
                    FileHelper.CreateFolder(_failName);
                }
                catch (Exception folderEx)
                {
                    _logger.LogError($"❌ 创建AI结果文件夹失败: {folderEx.Message}");
                    return;
                }

                _cancellationTokenSource = new CancellationTokenSource();
                _timerTask = Task.Run(() => TimerElapsedAsync(_cancellationTokenSource.Token), _cancellationTokenSource.Token);

                _logger.LogInfo("✅ AI结果监控服务启动成功");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 启动AI结果监控服务异常: {ex.Message}");
                _logger.LogError($"   异常详情: {ex.StackTrace}");
            }
        }

        private async Task TimerElapsedAsync(CancellationToken cancellationToken)
        {
            _logger.LogInfo("🔍 AI结果监控线程已启动");

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // **安全检查：确保配置和路径有效**
                    if (_globalState?.AppConfig?.AiResult == null)
                    {
                        await Task.Delay(5000, cancellationToken); // 配置无效时等待更长时间
                        continue;
                    }

                    // **安全检查：确保目录存在**
                    if (!System.IO.Directory.Exists(_globalState.AppConfig.AiResult))
                    {
                        await Task.Delay(5000, cancellationToken);
                        continue;
                    }

                    List<string> result = FileHelper.ReadFilesWithExtension(_globalState.AppConfig.AiResult, ".csv");
                    if (result?.Count > 0)
                    {
                        await TranslateDataAsync(result);
                    }
                }
                catch (OperationCanceledException)
                {
                    // 正常的取消操作，不记录错误
                    _logger.LogInfo("🔍 AI结果监控线程收到取消信号");
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"❌ AI结果监控线程异常: {ex.Message}");
                    _logger.LogError($"   异常详情: {ex.StackTrace}");

                    // 发生异常时等待更长时间，避免频繁出错
                    await Task.Delay(10000, cancellationToken);
                }

                try
                {
                    await Task.Delay(2000, cancellationToken); // 等待2秒
                }
                catch (OperationCanceledException)
                {
                    break;
                }
            }

            _logger.LogInfo("🔍 AI结果监控线程已停止");
        }

        private void _timer_Disposed(object sender, EventArgs e)
        {
            _timer = null;
        }

        /// <summary>
        /// 调用后润湿检测算法 - 检测默认文件夹
        /// </summary>
        /// <exception cref="Exception"></exception>
        public async void SendToCheck()
        {
            try
            {
                var resuestData = new Dictionary<string, string>();
                HttpResponseMessage response = await _client.PostAsync(checkPath, new FormUrlEncodedContent(resuestData));
                response.EnsureSuccessStatusCode();
                string result = await response.Content.ReadAsStringAsync();
                //var bmpData = JsonConvert.DeserializeObject<List<string>>(result);
                if (result.Length < 5)
                {
                    FileHelper.ClearFolderFiles(_globalState.AppConfig.AiSource);
                    SafeInvokeCheckCompleteEvent($"未接收到AI接口的反馈数据...", true, null);
                }
                //return JsonConvert.DeserializeObject(result);
            }
            catch (Exception ex)
            {
                SafeInvokeCheckCompleteEvent($"送检时AI接口数据反馈异常，原因:{ex}", true, null);
            }
        }

        /// <summary>
        /// 使用检测队列控制，确保一次只检测一批照片 - 防闪退版本，增强日志记录
        /// </summary>
        /// <param name="croppedFiles">已裁剪的文件路径数组</param>
        /// <param name="batchId">批次ID</param>
        public async void SendToCheckSafely(string[] croppedFiles, string batchId)
        {
            // **💀 终极保护：防止AI检测导致程序崩溃 💀**
            try
            {
                await Task.Run(async () =>
                {
                    try
                    {
                _logger.LogInfo($"🔥 SendToCheckSafely启动，批次: {batchId}，输入文件数: {croppedFiles?.Length ?? 0}");
                
                // **输入参数验证**
                if (croppedFiles == null || croppedFiles.Length == 0)
                {
                    _logger.LogError($"❌ 检测输入参数无效，批次: {batchId}，文件数组为空");
                    SafeInvokeCheckCompleteEvent($"检测输入无效，批次: {batchId}", true, null);
                    return;
                }
                
                // 使用静态锁确保同一时间只有一个批次在检测
                var copiedFiles = new List<string>();
                
                lock (typeof(CheckService))
                {
                    try
                    {
                        _logger.LogInfo($"🔒 获得检测锁，开始安全检测批次: {batchId}, 文件数量: {croppedFiles.Length}");
                        
                        // 1. 清空检测文件夹
                        if (FileHelper.FolderIsExists(_globalState.AppConfig.AiSource))
                        {
                            FileHelper.ClearFolderFiles(_globalState.AppConfig.AiSource);
                            _logger.LogInfo($"检测文件夹已清空: {_globalState.AppConfig.AiSource}");
                        }
                        
                        // 2. 将当前批次文件复制到检测文件夹
                        foreach (var sourceFile in croppedFiles)
                        {
                            if (FileHelper.FileExists(sourceFile))
                            {
                                string fileName = FileHelper.GetFileName(sourceFile);
                                string targetFile = FileHelper.ConcatFile(_globalState.AppConfig.AiSource, fileName);
                                
                                // 复制文件（而不是移动，保留原文件）
                                FileHelper.CopyFile(sourceFile, targetFile);
                                copiedFiles.Add(targetFile);
                                _logger.LogInfo($"文件已复制到检测文件夹: {fileName}");
                            }
                            else
                            {
                                _logger.LogInfo($"源文件不存在，跳过: {sourceFile}");
                            }
                        }
                        
                        _logger.LogInfo($"批次 {batchId} 共复制 {copiedFiles.Count} 个文件到检测文件夹");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"文件复制异常，批次: {batchId}, 错误: {ex.Message}");
                        SafeInvokeCheckCompleteEvent($"文件复制失败，批次: {batchId}, 原因: {ex.Message}", true, null);
                        return;
                    }
                }
                
                // 3. 调用AI检测（在锁外进行） - **防闪退保护**
                try
                {
                    _logger.LogInfo($"🚀 准备调用AI检测接口，批次: {batchId}，文件数: {copiedFiles.Count}");
                    
                    await CallAIDetection(batchId);

                    // 注意：不在这里删除检测文件夹中的文件！
                    // AI检测是异步的，需要时间处理文件
                    // 文件清理将在定时任务处理完检测结果后进行

                    _logger.LogInfo($"✅ 批次 {batchId} AI检测接口调用完成，等待后台处理");
                    _logger.LogInfo($"📁 检测文件将保留在 {_globalState.AppConfig.AiSource} 中，等待AI处理");
                }
                catch (Exception ex)
                {
                    // **全面异常保护，防止闪退**
                    string errorDetail = $"批次: {batchId}, 错误: {ex.Message}";
                    _logger.LogError($"💥 AI检测调用发生严重异常，{errorDetail}");
                    _logger.LogError($"🔍 异常堆栈: {ex.StackTrace}");
                    
                    try
                    {
                        SafeInvokeCheckCompleteEvent($"AI检测系统异常，{errorDetail}", true, null);
                    }
                    catch (Exception eventEx)
                    {
                        _logger.LogError($"❌ 检测完成事件触发也异常: {eventEx.Message}");
                    }

                    // 即使AI调用失败，也保留已复制的文件，不删除
                    _logger.LogInfo($"🛡️ AI调用失败，但保留批次文件用于后续分析，批次: {batchId}");
                    foreach (var copiedFile in copiedFiles)
                    {
                        _logger.LogInfo($"📄 保留失败批次文件: {copiedFile}");
                    }
                    
                    // **不再重新抛出异常，防止上层闪退**
                    _logger.LogInfo($"🔒 异常已被安全处理，不会导致程序崩溃，批次: {batchId}");
                }
                    }
                    catch (Exception innerEx)
                    {
                        // 内层异常处理
                        _logger.LogError($"💥 AI检测内层异常，批次: {batchId}，错误: {innerEx.Message}");
                        _logger.LogError($"   异常详情: {innerEx.StackTrace}");

                        try
                        {
                            SafeInvokeCheckCompleteEvent($"AI检测内层异常，批次: {batchId}", true, null);
                        }
                        catch { }
                    }
                });
            }
            catch (Exception outerEx)
            {
                // 外层异常处理 - 最后的防线
                string crashLog = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - AI检测外层致命异常\r\n" +
                                 $"批次: {batchId}\r\n" +
                                 $"文件数: {croppedFiles?.Length ?? 0}\r\n" +
                                 $"异常: {outerEx.Message}\r\n" +
                                 $"堆栈: {outerEx.StackTrace}\r\n" +
                                 $"================================\r\n\r\n";

                try
                {
                    System.IO.File.AppendAllText("ai_crash_log.txt", crashLog);
                    _logger.LogError($"💀💀💀 AI检测外层致命异常: {outerEx.Message}");
                }
                catch { }

                try
                {
                    SafeInvokeCheckCompleteEvent($"AI检测系统崩溃，批次: {batchId}", true, null);
                }
                catch { }
            }
        }

        /// <summary>
        /// 调用AI检测 - 防闪退版本，增强异常保护和详细日志
        /// </summary>
        /// <param name="batchId">批次ID</param>
        /// <returns></returns>
        private async Task CallAIDetection(string batchId)
        {
            bool isSuccess = false;
            string errorDetail = "";
            
            try
            {
                _logger.LogInfo($"🔍 开始调用AI检测接口，批次: {batchId}，目标地址: {checkPath}");
                
                var requestData = new Dictionary<string, string>();
                
                // **网络请求异常保护**
                HttpResponseMessage response = null;
                try
                {
                    response = await _client.PostAsync(checkPath, new FormUrlEncodedContent(requestData));
                    _logger.LogInfo($"📡 AI接口请求已发送，批次: {batchId}，状态码: {response.StatusCode}");
                }
                catch (HttpRequestException httpEx)
                {
                    errorDetail = $"网络请求异常: {httpEx.Message}";
                    _logger.LogError($"❌ AI接口网络请求失败，批次: {batchId}，错误: {errorDetail}");
                    SafeInvokeCheckCompleteEvent($"AI检测网络失败，批次: {batchId}, 原因: {errorDetail}", true, null);
                    return; // **不再throw，防止闪退**
                }
                catch (TaskCanceledException timeoutEx)
                {
                    errorDetail = $"请求超时: {timeoutEx.Message}";
                    _logger.LogError($"⏰ AI接口请求超时，批次: {batchId}，错误: {errorDetail}");
                    SafeInvokeCheckCompleteEvent($"AI检测超时，批次: {batchId}, 原因: {errorDetail}", true, null);
                    return; // **不再throw，防止闪退**
                }

                // **响应状态检查**
                if (response != null)
                {
                    try
                    {
                        response.EnsureSuccessStatusCode();
                        string result = await response.Content.ReadAsStringAsync();
                        
                        _logger.LogInfo($"📄 AI接口响应内容长度: {result.Length}，批次: {batchId}");
                        
                                                 if (result.Length < 5)
                         {
                             errorDetail = "AI接口返回数据过短，可能无效";
                             _logger.LogInfo($"⚠️ {errorDetail}，批次: {batchId}，响应内容: {result}");
                             SafeInvokeCheckCompleteEvent($"AI接口响应异常，批次: {batchId}, 原因: {errorDetail}", true, null);
                         }
                        else
                        {
                            isSuccess = true;
                            _logger.LogInfo($"✅ AI检测接口调用成功，批次: {batchId}，响应数据正常");
                        }
                    }
                    catch (Exception contentEx)
                    {
                        errorDetail = $"响应内容读取异常: {contentEx.Message}";
                        _logger.LogError($"❌ AI接口响应处理失败，批次: {batchId}，错误: {errorDetail}");
                        SafeInvokeCheckCompleteEvent($"AI检测响应处理失败，批次: {batchId}, 原因: {errorDetail}", true, null);
                    }
                }
            }
            catch (Exception ex)
            {
                errorDetail = $"AI检测调用异常: {ex.Message}";
                _logger.LogError($"💥 AI检测调用发生未知异常，批次: {batchId}，错误: {errorDetail}，堆栈: {ex.StackTrace}");
                SafeInvokeCheckCompleteEvent($"AI检测系统异常，批次: {batchId}, 原因: {errorDetail}", true, null);
            }
            
            // **最终结果日志**
            if (isSuccess)
            {
                _logger.LogInfo($"🎉 AI检测接口调用完整成功，批次: {batchId}");
            }
            else
            {
                _logger.LogError($"🚫 AI检测接口调用最终失败，批次: {batchId}，失败原因: {errorDetail}");
            }
            
            // **不再抛出异常，防止闪退**
        }

        // 解析数据
        private async Task TranslateDataAsync(List<string> files)
        {
            try
            {
                if (files?.Count == 0) return;

                foreach (var item in files)
                {
                    try
                    {
                        // **安全检查：确保文件路径有效**
                        if (string.IsNullOrEmpty(item) || !System.IO.File.Exists(item))
                        {
                            _logger.LogInfo($"⚠️ 跳过无效文件: {item}");
                            continue;
                        }

                        string fileName = FileHelper.ConcatFile(_succesName, FileHelper.GetFileName(item));

                        // **安全的文件移动操作**
                        try
                        {
                            FileHelper.MoveFile(item, fileName);
                        }
                        catch (Exception moveEx)
                        {
                            _logger.LogError($"❌ 移动文件失败: {item} -> {fileName}, 错误: {moveEx.Message}");
                            continue;
                        }

                        List<List<string>> temp = null;

                        // **增强CSV文件读取的异常处理**
                        try
                        {
                            temp = _excelHelper.ReadCsvFile(fileName);
                            _logger.LogInfo($"📊 成功读取CSV文件: {fileName}, 行数: {temp?.Count ?? 0}");

                            // **调试：记录CSV文件内容**
                            if (temp != null && temp.Count > 0)
                            {
                                _logger.LogInfo($"📋 CSV文件内容预览:");

                                // 显示标题行
                                if (temp.Count > 0)
                                {
                                    var headerRow = temp[0];
                                    string headerContent = headerRow != null ? string.Join(",", headerRow) : "null";
                                    _logger.LogInfo($"   标题行: {headerContent}");
                                }

                                // 显示前几行数据
                                for (int i = 1; i < Math.Min(temp.Count, 6); i++) // 显示前5行数据（跳过标题行）
                                {
                                    var row = temp[i];
                                    string rowContent = row != null ? string.Join(",", row) : "null";
                                    _logger.LogInfo($"   数据行{i}: {rowContent}");
                                }
                                if (temp.Count > 6)
                                {
                                    _logger.LogInfo($"   ... 还有{temp.Count - 6}行数据");
                                }
                            }
                        }
                        catch (Exception csvEx)
                        {
                            _logger.LogError($"❌ 读取CSV文件失败: {fileName}, 错误: {csvEx.Message}");
                            _logger.LogError($"   异常详情: {csvEx.StackTrace}");
                            continue;
                        }

                        if (temp == null || temp.Count == 0)
                        {
                            _logger.LogInfo($"⚠️ CSV文件为空或无数据: {fileName}");
                            continue;
                        }

                        // **修复：跳过CSV文件的第一行（标题行）**
                        List<List<string>> dataRows = new List<List<string>>();
                        for (int i = 1; i < temp.Count; i++) // 从第二行开始（跳过标题行）
                        {
                            dataRows.Add(temp[i]);
                        }

                        if (dataRows.Count == 0)
                        {
                            _logger.LogInfo($"⚠️ CSV文件只有标题行，无数据: {fileName}");
                            continue;
                        }

                        _logger.LogInfo($"📊 CSV数据行数: {dataRows.Count} (已跳过标题行)");

                        foreach (var y in dataRows)
                        {
                            try
                            {
                                // **严格的CSV数据验证：图像,标签,置信度 (至少3列)**
                                if (y == null || y.Count < 3 || string.IsNullOrEmpty(y[0]) || string.IsNullOrEmpty(y[1]))
                                {
                                    _logger.LogInfo($"⚠️ 跳过无效CSV行数据: 列数={y?.Count ?? 0}, 图像名={y?[0] ?? "null"}, 标签={y?[1] ?? "null"}");
                                    continue;
                                }

                                // **记录CSV行数据用于调试**
                                string imageName = y[0].Trim();
                                string label = y[1].Trim();
                                string confidence = y.Count > 2 ? y[2].Trim() : "未知";

                                _logger.LogInfo($"🔍 处理CSV行数据: 图像={imageName}, 标签={label}, 置信度={confidence}");

                                // **查找数据库记录，支持模糊匹配**
                                var reportData = await _reportService.getFirstOrDefault(x => x.PictureName == imageName);

                                // **修复：如果精确匹配失败，使用安全的模糊匹配**
                                if (reportData == null)
                                {
                                    _logger.LogInfo($"⚠️ 精确匹配失败，尝试模糊匹配: {imageName}");

                                    // **修复：使用SqlSugar支持的方式进行模糊匹配**
                                    string nameWithoutExt = System.IO.Path.GetFileNameWithoutExtension(imageName);

                                    // 方法1：使用Contains进行模糊匹配
                                    reportData = await _reportService.getFirstOrDefault(x => x.PictureName.Contains(nameWithoutExt));

                                    // 方法2：如果还是没找到，尝试更宽松的匹配
                                    if (reportData == null && nameWithoutExt.Length > 5)
                                    {
                                        // 取文件名的前几个字符进行匹配
                                        string shortName = nameWithoutExt.Substring(0, Math.Min(nameWithoutExt.Length - 2, 10));
                                        reportData = await _reportService.getFirstOrDefault(x => x.PictureName.Contains(shortName));
                                    }

                                    if (reportData != null)
                                    {
                                        _logger.LogInfo($"✅ 模糊匹配成功: CSV={imageName} -> DB={reportData.PictureName}");
                                    }
                                    else
                                    {
                                        _logger.LogError($"❌ 模糊匹配也失败: {imageName}");
                                    }
                                }

                            if (reportData != null)
                            {
                                try
                                {
                                    // **安全检查：确保路径有效**
                                    if (string.IsNullOrEmpty(reportData.PicturePath))
                                    {
                                        _logger.LogError($"❌ 照片路径为空: {y[0]}");
                                        continue;
                                    }

                                    string resultFolder = FileHelper.ConcatFile(FileHelper.GetFolderName(reportData.PicturePath), "ai_check");

                                    // **安全的文件夹创建**
                                    try
                                    {
                                        FileHelper.CreateFolder(resultFolder);
                                    }
                                    catch (Exception folderEx)
                                    {
                                        _logger.LogError($"❌ 创建结果文件夹失败: {resultFolder}, 错误: {folderEx.Message}");
                                        continue;
                                    }

                                    // **修复：智能处理结果图片文件名，避免重复添加result_前缀**
                                    string csvImageName = y[0]; // CSV中的图片名称
                                    string resultFileName;

                                    // 检查CSV中的图片名称是否已经包含result_前缀
                                    if (csvImageName.StartsWith("result_"))
                                    {
                                        resultFileName = csvImageName; // 已经有前缀，直接使用
                                        _logger.LogInfo($"📋 CSV中图片名称已包含result_前缀: {csvImageName}");
                                    }
                                    else
                                    {
                                        resultFileName = "result_" + csvImageName; // 添加前缀
                                        _logger.LogInfo($"📋 为CSV图片名称添加result_前缀: {csvImageName} -> {resultFileName}");
                                    }

                                    string resultFile = FileHelper.ConcatFile(_globalState.AppConfig.AiResult, resultFileName);
                                    _logger.LogInfo($"🔍 查找结果图片文件: {resultFile}");

                                    // **修复：如果第一种方式找不到文件，尝试其他可能的文件名格式**
                                    if (!FileHelper.FileExists(resultFile))
                                    {
                                        // 尝试其他可能的文件名格式
                                        // 获取不带扩展名的文件名
                                        string nameWithoutExt = csvImageName.Contains(".") ?
                                            csvImageName.Substring(0, csvImageName.LastIndexOf('.')) : csvImageName;

                                        string[] possibleNames = {
                                            csvImageName, // 直接使用CSV中的名称
                                            csvImageName.StartsWith("result_") ? csvImageName.Substring(7) : "result_" + csvImageName, // 切换前缀状态
                                            nameWithoutExt + ".jpg", // 确保扩展名为.jpg
                                            "result_" + nameWithoutExt + ".jpg" // 前缀+无扩展名+.jpg
                                        };

                                        foreach (string possibleName in possibleNames)
                                        {
                                            string possiblePath = FileHelper.ConcatFile(_globalState.AppConfig.AiResult, possibleName);
                                            if (FileHelper.FileExists(possiblePath))
                                            {
                                                resultFile = possiblePath;
                                                resultFileName = possibleName;
                                                _logger.LogInfo($"✅ 找到结果图片文件: {possiblePath}");
                                                break;
                                            }
                                            else
                                            {
                                                _logger.LogInfo($"❌ 结果图片文件不存在: {possiblePath}");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        _logger.LogInfo($"✅ 直接找到结果图片文件: {resultFile}");
                                    }

                                    if (FileHelper.FileExists(resultFile))
                                    {
                                        string resultTarget = FileHelper.ConcatFile(resultFolder, resultFileName);
                                        _logger.LogInfo($"📁 结果图片目标路径: {resultTarget}");

                                        // **安全的文件移动**
                                        try
                                        {
                                            FileHelper.MoveFile(resultFile, resultTarget);
                                            // **修复：标准化路径格式，确保路径正确**
                                            reportData.CheckFilePath = resultTarget.Replace('/', '\\');
                                            _logger.LogInfo($"✅ 结果图片移动成功: {resultFile} -> {resultTarget}");
                                            _logger.LogInfo($"📋 设置CheckFilePath: {reportData.CheckFilePath}");

                                            // **验证文件是否真的存在**
                                            if (System.IO.File.Exists(reportData.CheckFilePath))
                                            {
                                                _logger.LogInfo($"✅ 验证CheckFilePath文件存在");
                                            }
                                            else
                                            {
                                                _logger.LogError($"❌ 验证CheckFilePath文件不存在: {reportData.CheckFilePath}");
                                            }
                                        }
                                        catch (Exception moveEx)
                                        {
                                            _logger.LogError($"❌ 移动结果文件失败: {resultFile} -> {resultTarget}, 错误: {moveEx.Message}");
                                            // **修复：即使移动失败，也设置原始路径，让用户能看到结果图片**
                                            reportData.CheckFilePath = resultFile.Replace('/', '\\');
                                            _logger.LogInfo($"📋 使用原始路径作为CheckFilePath: {reportData.CheckFilePath}");

                                            // **验证原始文件是否存在**
                                            if (System.IO.File.Exists(reportData.CheckFilePath))
                                            {
                                                _logger.LogInfo($"✅ 验证原始CheckFilePath文件存在");
                                            }
                                            else
                                            {
                                                _logger.LogError($"❌ 验证原始CheckFilePath文件也不存在: {reportData.CheckFilePath}");
                                            }
                                        }

                                        // **修复：根据检测结果移动源文件到相应文件夹**
                                        string sourceFile = FileHelper.ConcatFile(_globalState.AppConfig.AiSource, reportData.PictureName);
                                        try
                                        {
                                            if (FileHelper.FileExists(sourceFile))
                                            {
                                                // 根据检测结果决定移动到成功或失败文件夹
                                                string targetFolder = (label.ToUpper() == "OK" || label.ToUpper() == "PASS") ? _succesName : _failName;
                                                string targetFile = FileHelper.ConcatFile(targetFolder, reportData.PictureName);

                                                FileHelper.MoveFile(sourceFile, targetFile);
                                                _logger.LogInfo($"✅ 源文件已移动: {sourceFile} -> {targetFile}");
                                            }
                                            else
                                            {
                                                _logger.LogInfo($"⚠️ 源文件不存在，跳过移动: {sourceFile}");
                                            }
                                        }
                                        catch (Exception moveEx)
                                        {
                                            _logger.LogError($"❌ 移动源文件失败: {sourceFile}, 错误: {moveEx.Message}");
                                        }

                                        // **修复：标准化检测状态，确保界面正确显示NG/OK**
                                        reportData.Status = NormalizeDetectionStatus(label);
                                        reportData.CheckTime = DateTime.Now;

                                        // 数据库更新前记录日志
                                        _logger.LogInfo($"准备更新数据库 - 照片:{imageName}, 状态:{label}, ID:{reportData.Id}");

                                        // **修复：使用重试机制更新数据库，防止SQLite并发冲突**
                                        bool updateSuccess = await UpdateReportDataWithRetry(reportData, imageName, label);
                                        if (!updateSuccess)
                                        {
                                            SafeInvokeCheckCompleteEvent($"数据库更新失败，照片:{imageName}, 已重试多次", true, reportData);
                                            continue; // 跳过这条记录的后续处理
                                        }

                                        // **修复：安全访问置信度，防止数组越界**
                                        SafeInvokeCheckCompleteEvent($"照片名:{imageName},检测结果:{label},检测时间:{reportData.CheckTime},置信度:{confidence}", false, reportData);
                                    }
                                else
                                {
                                    // **结果图片文件未找到，记录详细信息**
                                    _logger.LogError($"❌ 未找到结果图片文件，CSV图片名称: {csvImageName}");
                                    _logger.LogError($"   尝试过的文件名: {resultFileName}");
                                    _logger.LogError($"   AI结果路径: {_globalState.AppConfig.AiResult}");

                                    // **修复：没有结果图片时，明确设置CheckFilePath为空，避免显示问题**
                                    reportData.CheckFilePath = "";
                                    _logger.LogInfo($"📋 设置CheckFilePath为空（无结果图片）");

                                    // **修复：根据检测结果移动源文件到相应文件夹(无结果图片)**
                                    string sourceFile = FileHelper.ConcatFile(_globalState.AppConfig.AiSource, reportData.PictureName);
                                    try
                                    {
                                        if (FileHelper.FileExists(sourceFile))
                                        {
                                            // 根据检测结果决定移动到成功或失败文件夹
                                            string targetFolder = (label.ToUpper() == "OK" || label.ToUpper() == "PASS") ? _succesName : _failName;
                                            string targetFile = FileHelper.ConcatFile(targetFolder, reportData.PictureName);

                                            FileHelper.MoveFile(sourceFile, targetFile);
                                            _logger.LogInfo($"✅ 源文件已移动(无结果图片): {sourceFile} -> {targetFile}");
                                        }
                                        else
                                        {
                                            _logger.LogInfo($"⚠️ 源文件不存在，跳过移动(无结果图片): {sourceFile}");
                                        }
                                    }
                                    catch (Exception moveEx)
                                    {
                                        _logger.LogError($"❌ 移动源文件失败(无结果图片): {sourceFile}, 错误: {moveEx.Message}");
                                    }

                                    // **修复：标准化检测状态，确保界面正确显示NG/OK**
                                    reportData.Status = NormalizeDetectionStatus(label);
                                    reportData.CheckTime = DateTime.Now;

                                    // 数据库更新前记录日志
                                    _logger.LogInfo($"准备更新数据库(无结果图片) - 照片:{imageName}, 状态:{label}, ID:{reportData.Id}");

                                    // **修复：使用重试机制更新数据库(无结果图片)**
                                    bool updateSuccess = await UpdateReportDataWithRetry(reportData, imageName, label);
                                    if (!updateSuccess)
                                    {
                                        SafeInvokeCheckCompleteEvent($"数据库更新失败(无结果图片)，照片:{imageName}, 已重试多次", true, reportData);
                                        continue; // 跳过这条记录的后续处理
                                    }

                                    SafeInvokeCheckCompleteEvent($"照片名:{imageName},接收到检测结果，结果照片不存在！", true, reportData);
                                }
                                }
                                catch (Exception reportEx)
                                {
                                    _logger.LogError($"❌ 处理检测结果异常: {y?[0] ?? "未知"}, 错误: {reportEx.Message}");
                                    _logger.LogError($"   异常详情: {reportEx.StackTrace}");
                                }
                            }
                            else
                            {
                                // **修复：即使找不到数据库记录，也要移动源文件到失败文件夹**
                                string sourceFile = FileHelper.ConcatFile(_globalState.AppConfig.AiSource, imageName);
                                try
                                {
                                    if (FileHelper.FileExists(sourceFile))
                                    {
                                        // 找不到数据库记录的文件移动到失败文件夹
                                        string targetFile = FileHelper.ConcatFile(_failName, imageName);
                                        FileHelper.MoveFile(sourceFile, targetFile);
                                        _logger.LogInfo($"✅ 孤立源文件已移动到失败文件夹: {sourceFile} -> {targetFile}");
                                    }
                                    else
                                    {
                                        _logger.LogInfo($"⚠️ 孤立源文件不存在: {sourceFile}");
                                    }
                                }
                                catch (Exception moveEx)
                                {
                                    _logger.LogError($"❌ 移动孤立源文件失败: {sourceFile}, 错误: {moveEx.Message}");
                                }

                                SafeInvokeCheckCompleteEvent($"未查询到照片:{imageName}的拍照记录，结果:{label}，文件已移动到失败文件夹", true, null);
                            }
                            }
                            catch (Exception rowEx)
                            {
                                string imageName = y?[0] ?? "未知";
                                _logger.LogError($"❌ 处理CSV行数据异常: {rowEx.Message}");
                                _logger.LogError($"   异常详情: {rowEx.StackTrace}");
                                SafeInvokeCheckCompleteEvent($"处理检测结果异常，照片:{imageName}, 错误:{rowEx.Message}", true, null);
                            }
                        }
                    }
                    catch (Exception fileEx)
                    {
                        _logger.LogError($"❌ 处理文件异常: {item}, 错误: {fileEx.Message}");
                        _logger.LogError($"   异常详情: {fileEx.StackTrace}");

                        // **关键：确保文件处理异常不会导致整个监控线程崩溃**
                        try
                        {
                            SafeInvokeCheckCompleteEvent($"处理检测结果文件异常: {System.IO.Path.GetFileName(item)}, 错误: {fileEx.Message}", true, null);
                        }
                        catch (Exception eventEx)
                        {
                            _logger.LogError($"❌ 发送文件处理异常事件失败: {eventEx.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ AI结果解析异常: {ex.Message}");
                _logger.LogError($"   异常详情: {ex.StackTrace}");
                SafeInvokeCheckCompleteEvent($"检测异常，原因:{ex.Message}", true, null);
            }
            finally
            {
                // **关键修复：每次AI结果解析完成后强制清理资源，防止第二轮闪退**
                try
                {
                    await ForceResourceCleanupAfterAiProcessing();
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogError($"❌ 资源清理异常: {cleanupEx.Message}");
                }
            }
        }

        /// <summary>
        /// AI处理完成后的强制资源清理 - 防止第二轮闪退的关键方法
        /// </summary>
        private async Task ForceResourceCleanupAfterAiProcessing()
        {
            try
            {
                _logger.LogInfo("🧹 开始AI处理后资源清理...");

                // 1. 强制垃圾回收（连续3次，确保彻底清理）
                for (int i = 0; i < 3; i++)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    await Task.Delay(100);
                }

                // 2. 清理HTTP客户端缓存
                try
                {
                    if (_client != null)
                    {
                        // 不关闭客户端，但清理可能的缓存
                        await Task.Delay(50);
                    }
                }
                catch (Exception httpEx)
                {
                    _logger.LogError($"HTTP客户端清理异常: {httpEx.Message}");
                }

                // 3. 等待文件系统操作完成
                await Task.Delay(500);

                // 4. 再次强制垃圾回收
                GC.Collect();

                _logger.LogInfo("✅ AI处理后资源清理完成");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ AI处理后资源清理异常: {ex.Message}");
            }
        }

        private string getFileName(string fileName)
        {
            return fileName.Split('.')[0];
        }

        /// <summary>
        /// 标准化检测状态，确保界面正确显示NG/OK
        /// </summary>
        /// <param name="rawStatus">AI检测服务返回的原始状态</param>
        /// <returns>标准化后的状态</returns>
        private string NormalizeDetectionStatus(string rawStatus)
        {
            if (string.IsNullOrEmpty(rawStatus))
            {
                _logger.LogError("❌ 检测状态为空，设置为NG");
                return "NG";
            }

            // 转换为大写进行比较
            string status = rawStatus.Trim().ToUpper();

            // 记录原始状态用于调试
            _logger.LogInfo($"🔄 状态标准化: 原始='{rawStatus}' -> 处理后='{status}'");

            // 合格状态的各种表示方式
            string[] passStatuses = { "PASS", "OK", "ACK", "GOOD", "合格", "通过", "SUCCESS", "QUALIFIED" };

            // 不合格状态的各种表示方式
            string[] failStatuses = { "FAIL", "NG", "NACK", "BAD", "不合格", "失败", "ERROR", "UNQUALIFIED" };

            // 检查是否为合格状态
            foreach (string passStatus in passStatuses)
            {
                if (string.Equals(passStatus, status, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInfo($"✅ 检测结果标准化: '{rawStatus}' -> 'OK'");
                    return "OK";
                }
            }

            // 检查是否为不合格状态
            foreach (string failStatus in failStatuses)
            {
                if (string.Equals(failStatus, status, StringComparison.OrdinalIgnoreCase))
                {
                    _logger.LogInfo($"❌ 检测结果标准化: '{rawStatus}' -> 'NG'");
                    return "NG";
                }
            }

            // 如果无法识别，默认为NG并记录警告
            _logger.LogError($"⚠️ 无法识别的检测状态: '{rawStatus}'，默认设置为NG");
            return "NG";
        }

        /// <summary>
        /// 使用重试机制更新数据库，防止SQLite并发冲突导致闪退
        /// </summary>
        /// <param name="reportData">要更新的报告数据</param>
        /// <param name="imageName">图像名称</param>
        /// <param name="label">检测标签</param>
        /// <returns>是否更新成功</returns>
        private async Task<bool> UpdateReportDataWithRetry(ReportModel reportData, string imageName, string label)
        {
            const int maxRetries = 5;
            const int baseDelayMs = 100;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    await _reportService.Update(reportData);
                    _logger.LogInfo($"✅ 数据库更新成功 - 照片:{imageName}, 状态:{label}, ID:{reportData.Id}, 尝试次数:{attempt}");
                    return true;
                }
                catch (Exception updateEx)
                {
                    string errorMsg = updateEx.Message?.ToLower() ?? "";
                    bool isRetryable = errorMsg.Contains("database is locked") ||
                                      errorMsg.Contains("busy") ||
                                      errorMsg.Contains("timeout") ||
                                      errorMsg.Contains("集合已修改") ||
                                      updateEx is InvalidOperationException;

                    if (isRetryable && attempt < maxRetries)
                    {
                        // 指数退避延迟
                        int delay = baseDelayMs * (int)Math.Pow(2, attempt - 1) + new Random().Next(0, 100);
                        _logger.LogError($"⚠️ 数据库更新失败，第{attempt}次重试 - 照片:{imageName}, 错误:{updateEx.Message}, {delay}ms后重试");

                        await Task.Delay(delay);
                        continue;
                    }
                    else
                    {
                        _logger.LogError($"❌ 数据库更新最终失败 - 照片:{imageName}, 尝试次数:{attempt}, 错误:{updateEx.Message}");
                        _logger.LogError($"   异常详情: {updateEx.StackTrace}");
                        return false;
                    }
                }
            }

            return false;
        }

        /// <summary>
        /// 安全调用CheckCompleteEvent，防止UI线程阻塞导致闪退
        /// </summary>
        /// <param name="message">消息</param>
        /// <param name="isError">是否为错误</param>
        /// <param name="reportData">报告数据</param>
        private void SafeInvokeCheckCompleteEvent(string message, bool isError, ReportModel reportData)
        {
            try
            {
                // **关键修复：使用Task.Run在后台线程中调用事件，避免UI线程阻塞**
                Task.Run(() =>
                {
                    try
                    {
                        CheckCompleteEvent?.Invoke(message, isError, reportData);
                    }
                    catch (Exception eventEx)
                    {
                        _logger.LogError($"❌ CheckCompleteEvent调用异常: {eventEx.Message}");
                        _logger.LogError($"   异常详情: {eventEx.StackTrace}");
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ SafeInvokeCheckCompleteEvent异常: {ex.Message}");
            }
        }

        public void StopListener()
        {
            try
            {
                _logger.LogInfo("🔍 正在停止AI结果监控线程...");

                _cancellationTokenSource?.Cancel();

                // **安全等待：设置超时时间，避免无限等待**
                if (_timerTask != null)
                {
                    bool completed = _timerTask.Wait(TimeSpan.FromSeconds(10));
                    if (!completed)
                    {
                        _logger.LogError("⚠️ AI监控线程停止超时，将强制结束");
                    }
                    else
                    {
                        _logger.LogInfo("✅ AI监控线程已正常停止");
                    }
                }

                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
                _timerTask = null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 停止AI监控线程异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 手动清理检测结果 - 用户点击"开始执行"时调用
        /// </summary>
        public void ClearDetectionResults()
        {
            try
            {
                _logger.LogInfo("🧹 开始手动清理检测结果...");

                // 1. 清理AI源文件夹
                if (!string.IsNullOrEmpty(_globalState?.AppConfig?.AiSource) &&
                    System.IO.Directory.Exists(_globalState.AppConfig.AiSource))
                {
                    try
                    {
                        var sourceFiles = System.IO.Directory.GetFiles(_globalState.AppConfig.AiSource);
                        foreach (var file in sourceFiles)
                        {
                            try
                            {
                                System.IO.File.Delete(file);
                                _logger.LogInfo($"🗑️ 删除源文件: {System.IO.Path.GetFileName(file)}");
                            }
                            catch (Exception fileEx)
                            {
                                _logger.LogError($"❌ 删除源文件失败: {file}, 错误: {fileEx.Message}");
                            }
                        }
                    }
                    catch (Exception dirEx)
                    {
                        _logger.LogError($"❌ 清理AI源文件夹异常: {dirEx.Message}");
                    }
                }

                // 2. 清理AI结果文件夹
                if (!string.IsNullOrEmpty(_globalState?.AppConfig?.AiResult) &&
                    System.IO.Directory.Exists(_globalState.AppConfig.AiResult))
                {
                    try
                    {
                        var resultFiles = System.IO.Directory.GetFiles(_globalState.AppConfig.AiResult);
                        foreach (var file in resultFiles)
                        {
                            try
                            {
                                System.IO.File.Delete(file);
                                _logger.LogInfo($"🗑️ 删除结果文件: {System.IO.Path.GetFileName(file)}");
                            }
                            catch (Exception fileEx)
                            {
                                _logger.LogError($"❌ 删除结果文件失败: {file}, 错误: {fileEx.Message}");
                            }
                        }
                    }
                    catch (Exception dirEx)
                    {
                        _logger.LogError($"❌ 清理AI结果文件夹异常: {dirEx.Message}");
                    }
                }

                // 3. 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                _logger.LogInfo("✅ 手动清理检测结果完成");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 手动清理检测结果异常: {ex.Message}");
                _logger.LogError($"   异常详情: {ex.StackTrace}");
            }
        }
    }
}