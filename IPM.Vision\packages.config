﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="10.1.1" targetFramework="net472" />
  <package id="BouncyCastle.Cryptography" version="2.5.0" targetFramework="net472" />
  <package id="ClosedXML.Parser" version="1.2.0" targetFramework="net472" />
  <package id="CommunityToolkit.Mvvm" version="8.2.0" targetFramework="net472" />
  <package id="CsvHelper" version="33.0.1" targetFramework="net472" />
  <package id="DocumentFormat.OpenXml" version="3.0.1" targetFramework="net472" />
  <package id="DocumentFormat.OpenXml.Framework" version="3.0.1" targetFramework="net472" />
  <package id="EntityFramework" version="6.4.4" targetFramework="net472" />
  <package id="ExcelNumberFormat" version="1.1.0" targetFramework="net472" />
  <package id="gong-wpf-dragdrop" version="3.2.1" targetFramework="net472" />
  <package id="HandyControl" version="3.5.1" targetFramework="net472" />
  <package id="LiveCharts" version="0.9.7" targetFramework="net472" />
  <package id="LiveCharts.Wpf" version="0.9.7" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Connections.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Hosting.Server.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Extensions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Http.Features" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Server.Kestrel" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Server.Kestrel.Core" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Server.Kestrel.Https" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets" version="2.2.1" targetFramework="net472" />
  <package id="Microsoft.AspNetCore.WebUtilities" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="8.0.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.HashCode" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.EnvironmentVariables" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.FileExtensions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileProviders.Physical" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.FileSystemGlobbing" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Hosting.Abstractions" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.4" targetFramework="net472" />
  <package id="Microsoft.Extensions.ObjectPool" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.Net.Http.Headers" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.NETCore.Platforms" version="1.1.0" targetFramework="net472" />
  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="NETStandard.Library" version="1.6.1" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net472" />
  <package id="NLog" version="5.3.4" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua" version="1.5.374.158" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Bindings.Https" version="1.4.365.23" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Client" version="1.5.374.158" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Configuration" version="1.5.374.158" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Core" version="1.5.374.158" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Gds.Client.Common" version="1.5.374.158" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Gds.Server.Common" version="1.5.374.158" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Security.Certificates" version="1.5.374.158" targetFramework="net472" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Server" version="1.5.374.158" targetFramework="net472" />
  <package id="OpcUaHelper" version="2.2.1" targetFramework="net472" />
  <package id="Portable.BouncyCastle" version="1.8.9" targetFramework="net472" />
  <package id="RBush" version="3.2.0" targetFramework="net472" />
  <package id="SixLabors.Fonts" version="1.0.0" targetFramework="net472" />
  <package id="SqlSugar" version="5.1.4.169" targetFramework="net472" />
  <package id="Stub.System.Data.SQLite.Core.NetFramework" version="1.0.118.0" targetFramework="net472" />
  <package id="System.AppContext" version="4.3.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Collections" version="4.3.0" targetFramework="net472" />
  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net472" />
  <package id="System.Collections.Immutable" version="1.5.0" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net472" />
  <package id="System.Console" version="4.3.0" targetFramework="net472" />
  <package id="System.Data.SQLite" version="1.0.118.0" targetFramework="net472" />
  <package id="System.Data.SQLite.Core" version="1.0.118.0" targetFramework="net472" />
  <package id="System.Data.SQLite.EF6" version="1.0.118.0" targetFramework="net472" />
  <package id="System.Data.SQLite.Linq" version="1.0.118.0" targetFramework="net472" />
  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.1" targetFramework="net472" />
  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net472" />
  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="net472" />
  <package id="System.Formats.Asn1" version="8.0.1" targetFramework="net472" />
  <package id="System.Globalization" version="4.3.0" targetFramework="net472" />
  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="net472" />
  <package id="System.IO" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.IO.Packaging" version="8.0.0" targetFramework="net472" />
  <package id="System.IO.Pipelines" version="4.5.2" targetFramework="net472" />
  <package id="System.IO.Ports" version="8.0.0" targetFramework="net472" />
  <package id="System.Linq" version="4.3.0" targetFramework="net472" />
  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Net.Http" version="4.3.0" targetFramework="net472" />
  <package id="System.Net.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.ObjectModel" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Reflection.Metadata" version="1.6.0" targetFramework="net472" />
  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Runtime.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net472" />
  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Cng" version="4.5.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="4.5.0" targetFramework="net472" />
  <package id="System.Text.RegularExpressions" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.Threading.Timer" version="4.3.0" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
  <package id="System.Xml.ReaderWriter" version="4.3.0" targetFramework="net472" />
  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net472" />
</packages>