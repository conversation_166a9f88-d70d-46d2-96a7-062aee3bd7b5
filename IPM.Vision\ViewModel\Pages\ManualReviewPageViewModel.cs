using CommunityToolkit.Mvvm.Input;
using HandyControl.Data;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using HandyControl.Controls;

namespace IPM.Vision.ViewModel.Pages
{
    public class ManualReviewPageViewModel : ViewModelBase
    {
        private ManualReviewSearchModel _searchModel = new ManualReviewSearchModel();
        private ObservableCollection<ObservableReportModel> _dataList;
        private readonly ReportService _reportService;
        private readonly ObservableGlobalState _globalState;
        private NLogHelper _logger;
        private bool _loading = false;
        private ObservableReportModel _currentSelect;
        private string _reviewRemark = string.Empty;

        public ManualReviewPageViewModel(NLogHelper logger, IReportService reportService, ObservableGlobalState globalState)
        {
            _logger = logger;
            _reportService = (ReportService)reportService;
            _globalState = globalState;
            // 初始化时直接加载数据
            SearchEvent();
        }

        public ObservableReportModel CurrentSelect
        {
            get => _currentSelect;
            set => SetProperty(ref _currentSelect, value);
        }

        public bool Loading
        {
            get => _loading;
            set => SetProperty(ref _loading, value);
        }

        public ManualReviewSearchModel SearchModel
        {
            get => _searchModel;
            set => SetProperty(ref _searchModel, value);
        }

        public ObservableCollection<ObservableReportModel> DataList
        {
            get => _dataList;
            set => SetProperty(ref _dataList, value);
        }

        public string ReviewRemark
        {
            get => _reviewRemark;
            set => SetProperty(ref _reviewRemark, value);
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            SearchEvent();
        });

        public IRelayCommand SearchCommand => new RelayCommand(() =>
        {
            SearchEvent();
        });

        public IRelayCommand ResetCommand => new RelayCommand(() =>
        {
            SearchModel = new ManualReviewSearchModel();
        });

        private async void SearchEvent()
        {
            Loading = true;
            try
            {
                var count = await _reportService.CountManualReviewAsync(SearchModel);
                SearchModel.PageCount = (int)Math.Ceiling(count / (double)SearchModel.PageSize);
                if (SearchModel.PageCount == 0) SearchModel.PageCount = 1;
                DataList = await _reportService.GetManualReviewData(SearchModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }
            finally
            {
                Loading = false;
            }
        }

        public IAsyncRelayCommand<FunctionEventArgs<int>> PageChangedCommand => new AsyncRelayCommand<FunctionEventArgs<int>>(PageChangedEvent);

        private async Task PageChangedEvent(FunctionEventArgs<int> obj)
        {
            await Task.Run(() =>
            {
                SearchModel.PageIndex = obj.Info;
                SearchEvent();
            });
        }

        // 复判合格
        public IRelayCommand PassCommand => new RelayCommand(async () =>
        {
            if (CurrentSelect == null)
            {
                HandyControl.Controls.MessageBox.Warning("请先选择要复判的数据！");
                return;
            }

            var result = await ShowReviewDialog("PASS", "复判合格");
            if (result)
            {
                await UpdateReviewResult("PASS");
            }
        });

        // 复判不合格
        public IRelayCommand FailCommand => new RelayCommand(async () =>
        {
            if (CurrentSelect == null)
            {
                HandyControl.Controls.MessageBox.Warning("请先选择要复判的数据！");
                return;
            }

            var result = await ShowReviewDialog("FAIL", "复判不合格");
            if (result)
            {
                await UpdateReviewResult("FAIL");
            }
        });

        private async Task<bool> ShowReviewDialog(string result, string title)
        {
            return await Task.Run(() =>
            {
                return Application.Current.Dispatcher.Invoke(() =>
                {
                    // 首先确认是否要进行复判
                    var confirmResult = HandyControl.Controls.MessageBox.Show(
                        $"确认要将此检测结果标记为{(result == "PASS" ? "合格" : "不合格")}吗？", 
                        title, 
                        MessageBoxButton.YesNo, 
                        MessageBoxImage.Question);

                    if (confirmResult == MessageBoxResult.Yes)
                    {
                        // 创建一个简单的输入窗口
                        var inputWindow = new System.Windows.Window()
                        {
                            Title = "复判备注",
                            Width = 400,
                            Height = 250,
                            WindowStartupLocation = WindowStartupLocation.CenterOwner,
                            Owner = Application.Current.MainWindow,
                            ResizeMode = ResizeMode.NoResize
                        };

                        var textBox = new System.Windows.Controls.TextBox()
                        {
                            AcceptsReturn = true,
                            TextWrapping = TextWrapping.Wrap,
                            Height = 120,
                            Margin = new Thickness(10, 10, 10, 10),
                            Text = ReviewRemark,
                            VerticalScrollBarVisibility = ScrollBarVisibility.Auto
                        };

                        var okButton = new System.Windows.Controls.Button()
                        {
                            Content = "确定",
                            Width = 80,
                            Height = 30,
                            Margin = new Thickness(0, 0, 10, 0),
                            IsDefault = true
                        };

                        var cancelButton = new System.Windows.Controls.Button()
                        {
                            Content = "取消",
                            Width = 80,
                            Height = 30,
                            IsCancel = true
                        };

                        bool? dialogResult = null;
                        okButton.Click += (s, e) => { inputWindow.DialogResult = true; inputWindow.Close(); };
                        cancelButton.Click += (s, e) => { inputWindow.DialogResult = false; inputWindow.Close(); };

                        var buttonPanel = new System.Windows.Controls.StackPanel()
                        {
                            Orientation = System.Windows.Controls.Orientation.Horizontal,
                            HorizontalAlignment = HorizontalAlignment.Right,
                            Margin = new Thickness(10)
                        };
                        buttonPanel.Children.Add(okButton);
                        buttonPanel.Children.Add(cancelButton);

                        var mainPanel = new System.Windows.Controls.StackPanel();
                        mainPanel.Children.Add(new System.Windows.Controls.TextBlock() 
                        { 
                            Text = "请输入复判备注（可选）：", 
                            Margin = new Thickness(10, 10, 10, 0),
                            FontWeight = FontWeights.Bold
                        });
                        mainPanel.Children.Add(textBox);
                        mainPanel.Children.Add(buttonPanel);

                        inputWindow.Content = mainPanel;

                        if (inputWindow.ShowDialog() == true)
                        {
                            ReviewRemark = textBox.Text;
                            return true;
                        }
                        else
                        {
                            return true; // 取消输入备注，但仍然允许复判
                        }
                    }
                    return false;
                });
            });
        }

        private async Task UpdateReviewResult(string result)
        {
            try
            {
                Loading = true;
                var operatorName = _globalState.LoginUser?.Account ?? "系统";
                var success = await _reportService.UpdateManualReviewResult(
                    CurrentSelect.Id, result, ReviewRemark, operatorName);

                if (success)
                {
                    HandyControl.Controls.MessageBox.Success($"复判结果保存成功！结果：{result}");
                    // 刷新数据
                    SearchEvent();
                    // 清空备注
                    ReviewRemark = string.Empty;
                }
                else
                {
                    HandyControl.Controls.MessageBox.Error("复判结果保存失败！");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                HandyControl.Controls.MessageBox.Error($"复判结果保存失败：{ex.Message}");
            }
            finally
            {
                Loading = false;
            }
        }

        public IRelayCommand<string> OpenDirectoryCommand => new RelayCommand<string>((picturePath) =>
        {
            if (!string.IsNullOrEmpty(picturePath) && FileHelper.FileIsExists(picturePath))
            {
                System.Diagnostics.Process.Start("Explorer", $"/select,{picturePath.Replace('/', '\\')}");
            }
        });

        public IRelayCommand<object> PreviewCommand => new RelayCommand<object>((obj) =>
        {
            if (CurrentSelect != null)
            {
                if (FileHelper.FileIsExists(CurrentSelect.PicturePath))
                {
                    Process.Start(new ProcessStartInfo(CurrentSelect.PicturePath) { UseShellExecute = true });
                }
            }
        });

        public IRelayCommand<object> PreviewCheckCommand => new RelayCommand<object>((obj) =>
        {
            if (CurrentSelect != null && !string.IsNullOrEmpty(CurrentSelect.CheckFilePath))
            {
                if (FileHelper.FileIsExists(CurrentSelect.CheckFilePath))
                {
                    Process.Start(new ProcessStartInfo(CurrentSelect.CheckFilePath) { UseShellExecute = true });
                }
            }
        });

        public IRelayCommand<object> OpenPathCommand => new RelayCommand<object>((obj) =>
        {
            if (CurrentSelect != null)
            {
                if (FileHelper.FileIsExists(CurrentSelect.PicturePath))
                {
                    System.Diagnostics.Process.Start("Explorer", $"/select,{CurrentSelect.PicturePath.Replace('/', '\\')}");
                }
            }
        });
    }
} 