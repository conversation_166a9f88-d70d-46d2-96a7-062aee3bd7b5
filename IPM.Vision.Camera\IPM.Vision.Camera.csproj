﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9A4E650C-F4E4-4248-86BA-8C767E856779}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>IPM.Vision.Camera</RootNamespace>
    <AssemblyName>IPM.Vision.Camera</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="MvCamCtrl.Net, Version=0.0.0.0, Culture=neutral, PublicKeyToken=01e3fc1d84d12cbf, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>DLL\MvCamCtrl.Net.dll</HintPath>
    </Reference>
    <Reference Include="MvCameraControl.Net, Version=4.4.0.2, Culture=neutral, PublicKeyToken=a3c7c5e3a730cd12, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>DLL\MvCameraControl.Net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CameraModel.cs" />
    <Compile Include="Com\CameraEvent.cs" />
    <Compile Include="Com\CanonError.cs" />
    <Compile Include="Com\EnumHelper.cs" />
    <Compile Include="Com\FileHelper.cs" />
    <Compile Include="Com\IObserver.cs" />
    <Compile Include="Com\Observable.cs" />
    <Compile Include="Com\ObservableControl.cs" />
    <Compile Include="Com\PictureModel.cs" />
    <Compile Include="EDSDKLib\CanonCameraController.cs" />
    <Compile Include="EDSDKLib\CanonCameraModel.cs" />
    <Compile Include="EDSDKLib\Commands\BasicCommand.cs" />
    <Compile Include="EDSDKLib\Commands\CloseSessionCommand.cs" />
    <Compile Include="EDSDKLib\Commands\CommandProcessor.cs" />
    <Compile Include="EDSDKLib\Commands\DoEvfAFCommand.cs" />
    <Compile Include="EDSDKLib\Commands\DownloadAllFilesCommand.cs" />
    <Compile Include="EDSDKLib\Commands\DownloadCommand.cs" />
    <Compile Include="EDSDKLib\Commands\DownloadEvfCommand.cs" />
    <Compile Include="EDSDKLib\Commands\DriveLensCommand.cs" />
    <Compile Include="EDSDKLib\Commands\EndEvfCommand.cs" />
    <Compile Include="EDSDKLib\Commands\FileCounterCommand.cs" />
    <Compile Include="EDSDKLib\Commands\GetPropertyCommand.cs" />
    <Compile Include="EDSDKLib\Commands\GetPropertyDescCommand.cs" />
    <Compile Include="EDSDKLib\Commands\OpenSessionCommand.cs" />
    <Compile Include="EDSDKLib\Commands\PressShutterCommand.cs" />
    <Compile Include="EDSDKLib\Commands\SetPropertyCommand.cs" />
    <Compile Include="EDSDKLib\Commands\SetRemoteShootingCommand.cs" />
    <Compile Include="EDSDKLib\Commands\StartEvfCommand.cs" />
    <Compile Include="EDSDKLib\Commands\TakePictureCommand.cs" />
    <Compile Include="EDSDKLib\Commands\UILockedCommand.cs" />
    <Compile Include="EDSDKLib\Commands\UIUnlockedCommand.cs" />
    <Compile Include="EDSDKLib\EDSDK.cs" />
    <Compile Include="EDSDKLib\Events\ActionEvent.cs" />
    <Compile Include="EDSDKLib\Events\ActionListener.cs" />
    <Compile Include="EDSDKLib\Events\ActionSource.cs" />
    <Compile Include="EDSDKLib\Events\CameraEventListener.cs" />
    <Compile Include="EDSDKLib\EVFDataSet.cs" />
    <Compile Include="HKSDKLib\HKCameraController.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="DLL\EDSDK.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="DLL\EdsImage.dll" />
    <None Include="DLL\MvCamCtrl.Net.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="DLL\MvCamCtrl.Net.xml" />
    <None Include="DLL\MvCameraControl.Net.dll">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <Content Include="DLL\MvCameraControl.Net.XML" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="HKSDKLib\Commands\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>