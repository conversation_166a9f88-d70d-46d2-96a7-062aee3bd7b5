using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Common.OPCHelper;
using IPM.Vision.LEvents;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using Newtonsoft.Json;
using Opc.Ua;
using Opc.Ua.Client;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;

namespace IPM.Vision.BLL
{
    public class EquipmentService : BaseService<EquipmentModel>, IEquipmentService
    {
        private OpcNodeModel _opcNodeModel;
        private NLogHelper _logger;
        private readonly OpcUAHelper _opcUAHelper;
        private readonly ObservableGlobalState _globalState;
        public event Action TakePictureEvent;
        public event Action<int> McStatusNotify;
        public event Action<int,float> McDataNotify;
        public event Action<float, float> McPositionEvent;
        public event Action<OpcEventArgs> NotifyInfoEvent;
        public event Action AutoTakeEvent;
        private int _btn1 = 0;
        private int _btn2 = 0;
        

        public bool IsConnect => _opcUAHelper.IsConnected;

        public EquipmentService(IBaseRepository<EquipmentModel> baseRepository, NLogHelper logger,OpcUAHelper opcUAHelper,ObservableGlobalState globalState) : base(baseRepository)
        {
            _logger = logger;
            _opcUAHelper = opcUAHelper;
            _globalState = globalState;
            _opcUAHelper.ConnectedEvent += _opcUAHelper_ConnectedEvent;
        }


        public async void ConnectPlc()
        {
            try
            {
                string plcAddress = _globalState.AppConfig.PlcAddress;
                _logger.LogInfo($"🔗 正在连接OPC UA服务器: {plcAddress}");
                await _opcUAHelper.ConnectWithAnonymousAsync(plcAddress);
                _logger.LogInfo("📤 OPC UA连接请求已发送，等待服务器响应...");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 发送OPC UA连接请求失败: {ex.Message}", ex);
            }
        }

        public void DisconnectPLC() {
            _opcUAHelper.Disconnect();
        }

       

        private void LoadOpcConfig()
        {
            try
            {
                string path = FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER, GlobalConstants.OPCCONFIGNAME);
                _logger.LogInfo($"📄 正在加载OPC配置文件: {path}");
                
                string content = FileHelper.ReadFile(path);
                _opcNodeModel = JsonConvert.DeserializeObject<OpcNodeModel>(content);
                
                _logger.LogInfo("✅ OPC配置文件加载成功");
                _logger.LogInfo($"   配置了 {_opcNodeModel.GetType().GetProperties().Length} 个数据点");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 加载OPC配置文件失败: {ex.Message}", ex);
            }
        }

        private void _opcUAHelper_ConnectedEvent(object sender, OpcEventArgs e)
        {
            if (e.Code == OpcCode.Connected)
            {
                _logger.LogInfo("✅ OPC UA服务器连接建立成功");
                _logger.LogInfo($"   连接消息: {e.Message}");
                _logger.LogInfo($"   连接状态: {(_opcUAHelper.IsConnected ? "已连接" : "未连接")}");
                
                _logger.LogInfo("📂 开始加载OPC配置文件...");
                LoadOpcConfig();

                _logger.LogInfo("📊 开始订阅PLC数据点...");
                SubTag();
                
                // 延迟一段时间后读取设备实际状态，确保订阅已经建立
                Task.Delay(2000).ContinueWith(async _ =>
                {
                    await ReadAndUpdateDeviceStatus();
                });
                
                _logger.LogInfo("🎯 OPC UA设备连接和配置完成！");
            }
            else if (e.Code == OpcCode.Error)
            {
                _logger.LogError($"❌ OPC UA连接失败: {e.Message}");
                if (!string.IsNullOrEmpty(e.Value))
                {
                    _logger.LogError($"   错误详情: {e.Value}");
                }
                
                _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                {
                    EquipmentStatus = 4,
                    StatusShowType = ShowType.ALL,
                    SourceType = SourceType.PLC,
                    EventCode = HEventCode.ERROR,
                    EventMessage = e.Message
                });
                

            }
            else
            {
                _logger.LogInfo($"📡 OPC UA连接事件: {e.Message} (状态码: {e.Code})");
            }
        }


        public void SubTag() {
            try
            {
                List<string> nodeIds = new List<string>();
                foreach (var property in _opcNodeModel.GetType().GetProperties()) {

                    string nodeId = GlobalConstants.OPCSUFFIX + property.GetValue(_opcNodeModel);
                    nodeIds.Add(nodeId);
                }
                
                _logger.LogInfo($"📋 准备订阅 {nodeIds.Count} 个OPC数据点");
                _opcUAHelper.SubscribeMultipleTags("sys", nodeIds, SubCallBack);
                _logger.LogInfo("✅ OPC数据点订阅请求发送成功");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 订阅OPC数据点失败: {ex.Message}", ex);
            }
        }

        private void SubCallBack(string key, MonitoredItem item, MonitoredItemNotificationEventArgs args)
        {
            var notifyTagName = item.DisplayName.Substring(item.DisplayName.LastIndexOf("=") + 1);
            var notifyTag = Convert.ToInt32(notifyTagName);
            var notify = args.NotificationValue as MonitoredItemNotification;
            if (notify.Value.WrappedValue.Value == null) return;
            if (notifyTag == _opcNodeModel.EMCStatus)
            {
                var value = Convert.ToInt32(notify.Value.WrappedValue.Value);
                McStatusNotify?.Invoke(value);
                _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                {
                    StatusShowType = ShowType.ICON,
                    EquipmentStatus = value,
                    EventMessage = $"设备状态:{value}",
                    SourceType = SourceType.PLC,
                    EventCode = HEventCode.SUCCESS
                });
            }

            if(notifyTag == _opcNodeModel.XPosition)
            {
                var value = Convert.ToInt32(notify.Value.WrappedValue.Value);
               // McPositionEvent?.Invoke(value, 0);
            }

            if(notifyTag  == _opcNodeModel.YPosition)
            {
                var value = Convert.ToInt32(notify.Value.WrappedValue.Value);
                //McPositionEvent?.Invoke(0, value);
            }

            if(notifyTag == _opcNodeModel.AutoTake)
            {
                var value = Convert.ToBoolean(notify.Value.WrappedValue.Value);
                if (!value) AutoTakeEvent?.Invoke();
            }

            if(notifyTag == _opcNodeModel.TAngle)
            {
                float result = 0;
                float.TryParse(notify.Value.WrappedValue.Value.ToString(),out result);
                McDataNotify?.Invoke(_opcNodeModel.TAngle, result);
            }

            if(notifyTag == _opcNodeModel.EError)
            {
                var value = notify.Value.WrappedValue.Value.ToString();
                if (string.IsNullOrEmpty(value)) return;
                _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                {
                    EquipmentStatus = 4,
                    SourceType = SourceType.PLC,
                    EventCode = HEventCode.ERROR,
                    StatusShowType = ShowType.ALL,
                    EventMessage = TranslateEventCode(value)
                });
            }

            
            if (notifyTag == _opcNodeModel.Button1) _btn1 = Convert.ToInt32(notify.Value.WrappedValue.Value);

            if( notifyTag == _opcNodeModel.Button2) _btn2 = Convert.ToInt32(notify.Value.WrappedValue.Value);

            if (_btn1 == 1 && _btn2 == 1) TakePictureEvent?.Invoke();

        }

       

        public int ReadCBeginMove()
        {
          return _opcUAHelper.ReadTag<short>(nameof(_opcNodeModel.CBeginMove), _opcNodeModel.CBeginMove);
        }

        public int ReadStatus()
        {
            if (_opcNodeModel == null) return -1;
            return _opcUAHelper.ReadTag<short>(nameof(_opcNodeModel.EMCStatus), _opcNodeModel.EMCStatus);
        }

        public float ReadX()
        {
            if(_opcNodeModel == null) return 0;
            return _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.CXPoint), _opcNodeModel.CXPoint);
        }
        public float ReadY()
        {
            if (_opcNodeModel == null) return 0;
            return _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.CYPoint), _opcNodeModel.CYPoint);
        }
        public float ReadZ()
        {
            if (_opcNodeModel == null) return 0;
            return _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPoint), _opcNodeModel.ZPoint);
        }
        public float ReadR()
        {
            if (_opcNodeModel == null) return 0;
            return _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.RAngle), _opcNodeModel.RAngle);
        }
        public float ReadT()
        {
            if (_opcNodeModel == null) return 0;
            return _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.TAngle), _opcNodeModel.TAngle);
        }

        public int ReadZBeginMove()
        {
            if (_opcNodeModel == null) return 0;
            return _opcUAHelper.ReadTag<short>(nameof(_opcNodeModel.ZBeginMove), _opcNodeModel.ZBeginMove);
        }

        public int ReadTBeginMove()
        {
            if (_opcNodeModel == null) return 0;
            return _opcUAHelper.ReadTag<short>(nameof(_opcNodeModel.TBeginMove), _opcNodeModel.TBeginMove);
        }

        public int ReadRBeginMove()
        {
            if (_opcNodeModel == null) return 0;
            return _opcUAHelper.ReadTag<short>(nameof(_opcNodeModel.RBeginMove), _opcNodeModel.RBeginMove);
        }

        // 检查所有条件是否都满足
        bool AllConditionsMet(bool[] conditions)
        {
            foreach (bool condition in conditions)
            {
                if (!condition)
                {
                    return false; // 如果任何一个条件不满足，返回false
                }
            }
            return true; // 所有条件都满足
        }


        public void ReadTagAsync()
        {
            //ObservableEquipmentModel observableEquipmentModel = new ObservableEquipmentModel();
            //if (_opcNodeModel == null) return 
            //var temp = await _opcUAHelper.ReadTagAsync<dynamic>("1", _opcNodeModel.EResetError);
           
        }


        public async void SetXY(float X,float Y)
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.CXPoint), _opcNodeModel.CXPoint, X);
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.CYPoint), _opcNodeModel.CYPoint, Y);
            await Task.Delay(500);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.CBeginMove), _opcNodeModel.CBeginMove, 1);
        }

        public async void SetZAsync(float Z)
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.ZPoint), _opcNodeModel.ZPoint, Z);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.ZBeginMove), _opcNodeModel.ZBeginMove, 1);
        }

        public async Task SetT(float T)
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.TAngle), _opcNodeModel.TAngle, T);
            await Task.Delay(500);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.TBeginMove), _opcNodeModel.TBeginMove, 1);
        }

        

        public async Task SetR(float R)
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.RAngle), _opcNodeModel.RAngle, R);
            await Task.Delay(500);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.RBeginMove), _opcNodeModel.RBeginMove, 1);
        }

        public async void SetO(float O)
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.OAngle), _opcNodeModel.OAngle, O);
            await Task.Delay(500);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.OBeginMove), _opcNodeModel.OBeginMove, 1);
        }

        /// <summary>
        /// 清除报警
        /// </summary>
        public async void ResetError()
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<bool>(nameof(_opcNodeModel.EResetError), _opcNodeModel.EResetError, true);
            await Task.Delay(500);
            await _opcUAHelper.WriteTagAsync<bool>(nameof(_opcNodeModel.EResetError), _opcNodeModel.EResetError, false);
        }

        /// <summary>
        /// X轴数组
        /// </summary>
        /// <param name="param"></param>
        public async Task WriteXYArray(float[] param, float[] param2,int length)
        {
            if (_opcNodeModel == null) return;
            var varitent = new Variant(param);
            var yVarient = new Variant(param2);
            await _opcUAHelper.WriteTagAsync<Variant>(nameof(_opcNodeModel.CXPointArray), _opcNodeModel.CXPointArray, varitent);
            await _opcUAHelper.WriteTagAsync<Variant>(nameof(_opcNodeModel.CYPointArray), _opcNodeModel.CYPointArray, yVarient);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.PinLength), _opcNodeModel.PinLength, (short)length);
            await Task.Delay(500);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.CBeginMove), _opcNodeModel.CBeginMove, 1);
        }

        public async void WriteXYArrayNotMove(float[] param, float[] param2, int length)
        {
            if (_opcNodeModel == null) return;
            var varitent = new Variant(param);
            var yVarient = new Variant(param2);
            await _opcUAHelper.WriteTagAsync<Variant>(nameof(_opcNodeModel.CXPointArray), _opcNodeModel.CXPointArray, varitent);
            await _opcUAHelper.WriteTagAsync<Variant>(nameof(_opcNodeModel.CYPointArray), _opcNodeModel.CYPointArray, yVarient);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.PinLength), _opcNodeModel.PinLength, (short)length);
        }

        /// <summary>
        /// 设置设备AutoTake为开启状态（自动拍照模式）
        /// </summary>
        public async void SetAutoTake()
        {
            try
            {
                if (_opcNodeModel == null)
                {
                    _logger.LogError("⚠️ OPC节点模型为空，无法设置AutoTake");
                    return;
                }

                _logger.LogInfo("🔧 正在设置设备AutoTake为开启状态...");
                await _opcUAHelper.WriteTagAsync<bool>(nameof(_opcNodeModel.AutoTake), _opcNodeModel.AutoTake, true);
                _logger.LogInfo("✅ 设备AutoTake已设置为开启状态（自动拍照模式）");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 设置AutoTake失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 读取设备AutoTake当前状态
        /// </summary>
        /// <returns>AutoTake状态</returns>
        public bool ReadAutoTake()
        {
            try
            {
                if (_opcNodeModel == null)
                {
                    _logger.LogError("⚠️ OPC节点模型为空，无法读取AutoTake状态");
                    return false;
                }

                var status = _opcUAHelper.ReadTag<bool>(nameof(_opcNodeModel.AutoTake), _opcNodeModel.AutoTake);
                _logger.LogInfo($"📊 读取设备AutoTake状态: {(status ? "开启" : "关闭")}");
                return status;
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 读取AutoTake状态失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 关闭设备AutoTake（退出自动拍照模式）
        /// </summary>
        public async void CloseAutoTake()
        {
            try
            {
                if (_opcNodeModel == null)
                {
                    _logger.LogError("⚠️ OPC节点模型为空，无法关闭AutoTake");
                    return;
                }

                _logger.LogInfo("🔧 正在关闭设备AutoTake...");
                await _opcUAHelper.WriteTagAsync<bool>(nameof(_opcNodeModel.AutoTake), _opcNodeModel.AutoTake, false);
                _logger.LogInfo("✅ 设备AutoTake已设置为关闭状态（退出自动拍照模式）");
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 关闭AutoTake失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 模拟设备状态变化 - 用于测试目的
        /// </summary>
        /// <param name="statusCode">设备状态码</param>
        public void SimulateDeviceStatusChange(int statusCode)
        {
            try
            {
                _logger.LogInfo($"🧪 模拟设备状态变化: {statusCode}");

                // 触发设备状态通知事件
                McStatusNotify?.Invoke(statusCode);

                // 同时更新全局状态观察者
                _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                {
                    StatusShowType = ShowType.ICON,
                    EquipmentStatus = statusCode,
                    EventMessage = $"模拟设备状态: {GetStatusDescription(statusCode)}",
                    SourceType = SourceType.PLC,
                    EventCode = statusCode == 4 ? HEventCode.ERROR : HEventCode.SUCCESS
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 模拟设备状态变化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 模拟设备参数变化 - 用于测试目的
        /// </summary>
        /// <param name="paramId">参数ID</param>
        /// <param name="value">参数值</param>
        public void SimulateDeviceParameterChange(int paramId, float value)
        {
            try
            {
                _logger.LogInfo($"🧪 模拟设备参数变化: ID={paramId}, Value={value}");
                McDataNotify?.Invoke(paramId, value);
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 模拟设备参数变化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 模拟设备位置变化 - 用于测试目的
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        public void SimulateDevicePositionChange(float x, float y)
        {
            try
            {
                _logger.LogInfo($"🧪 模拟设备位置变化: X={x}, Y={y}");
                McPositionEvent?.Invoke(x, y);
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 模拟设备位置变化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 模拟AutoTake事件 - 用于测试目的
        /// </summary>
        public void SimulateAutoTakeEvent()
        {
            try
            {
                _logger.LogInfo("🧪 模拟AutoTake事件");
                AutoTakeEvent?.Invoke();
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 模拟AutoTake事件失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 模拟手动拍照事件 - 用于测试目的
        /// </summary>
        public void SimulateManualPhotoEvent()
        {
            try
            {
                _logger.LogInfo("🧪 模拟手动拍照事件（按钮1+按钮2）");
                TakePictureEvent?.Invoke();
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 模拟手动拍照事件失败: {ex.Message}", ex);
            }
        }

        public async void MoveXY()
        {
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.CBeginMove), _opcNodeModel.CBeginMove, 1);
        }

        /// <summary>
        /// Y轴数组
        /// </summary>
        /// <param name="param"></param>
        public async void WriteYArray(float[] param)
        {
            if (_opcNodeModel == null) return;
            var varitent = new Variant(param);
            await _opcUAHelper.WriteTagAsync<Variant>(nameof(_opcNodeModel.CYPointArray), _opcNodeModel.CYPointArray, varitent);
            await Task.Delay(500);
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.CYPointArray), _opcNodeModel.CYPointArray, 1);
        }

        /// <summary>
        /// 设置X轴运动速度
        /// </summary>
        public async Task SetXSpeed(float param)
        {
            if (_opcNodeModel is null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.XVelocity), _opcNodeModel.XVelocity, param);
        }

        /// <summary>
        /// 设置X轴加速速度
        /// </summary>
        /// <param name="param"></param>
        public async void SetXAcce(float param)
        {
            if (_opcNodeModel is null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.XAcceleration), _opcNodeModel.XAcceleration, param);
        }

        public async void SetXDcce(float param)
        {
            if (_opcNodeModel is null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.XDcceleration), _opcNodeModel.XDcceleration, param);
        }

        /// <summary>
        /// 设置Y轴运动速度
        /// </summary>
        public async Task SetYSpeed(float param)
        {
            if (_opcNodeModel is null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.YVelocity), _opcNodeModel.YVelocity, param);
        }

        /// <summary>
        /// Y轴加速度
        /// </summary>
        /// <param name="param"></param>
        public async void SetYAcce(float param)
        {
            if (_opcNodeModel is null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.YAcceleration), _opcNodeModel.YAcceleration, param);
        }
        /// <summary>
        /// Y轴减速度
        /// </summary>
        /// <param name="param"></param>
        public async void SetYDcce(float param)
        {
            if (_opcNodeModel is null) return;
            await _opcUAHelper.WriteTagAsync<float>(nameof(_opcNodeModel.YDcceleration), _opcNodeModel.YDcceleration, param);
        }

        public void ResetAll()
        {
            ReSetZ();
            ReSetXY();
            //ReSetO();
            ReSetR();
            ReSetT();

        }

        public async Task ResetXYArray()
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<bool>(nameof(_opcNodeModel.Suspend), _opcNodeModel.Suspend, true);
        }

        public async void ReSetXY()
        {
            if (_opcNodeModel == null) return;
            await SetXSpeed(250);
            await SetYSpeed(250);
            await ResetXYArray();
            float x = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPosition), _opcNodeModel.ZPosition);
            if (x == 0)
            {
                await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.CReset), _opcNodeModel.CReset, 1);
                
            }
            else
            {
                SetZAsync(0);
                var timer = new Timer();
                timer.Interval = 1000;
                timer.Elapsed += Timer_Elapsed2;
                timer.Start();
            }
            
        }

        private async void Timer_Elapsed2(object sender, ElapsedEventArgs e)
        {
            float x = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPosition), _opcNodeModel.ZPosition);
            if (x == 0)
            {
                await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.CReset), _opcNodeModel.CReset, 1);
                // 关闭定时器
                ((Timer)sender).Stop();
                // 释放定时器对象
                ((Timer)sender).Dispose();
            }
        }

        public async void ReSetZ()
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.ZReset), _opcNodeModel.ZReset, 1);
        }
        public async void ReSetR()
        {
            if (_opcNodeModel == null) return;
            float z = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPosition), _opcNodeModel.ZPosition);
            if (z == 0)
            {
                await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.RReset), _opcNodeModel.RReset, 1);
               
            }
            else
            {
                SetZAsync(0);
                var timer = new Timer();
                timer.Interval = 1000;
                timer.Elapsed += Timer_Elapsed;
                timer.Start();
            }

        }

        private async void Timer_Elapsed(object sender, ElapsedEventArgs e)
        {
            float z = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPosition), _opcNodeModel.ZPosition);
            
            if (z == 0)
            {
                await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.RReset), _opcNodeModel.RReset, 1);


                // 关闭定时器
                ((Timer)sender).Stop();
                // 释放定时器对象
                ((Timer)sender).Dispose();

            }
           
        }

        public async void ReSetT()
        {
            if (_opcNodeModel == null) return;

            float x = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPosition), _opcNodeModel.ZPosition);
            float r = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.RPosition), _opcNodeModel.RPosition);
            if (x == 0 && r == 60)
            {
                await SetT(0);
            }
            else
            {
                SetZAsync(0);
                await SetR(60);
                var timer = new Timer();
                timer.Interval = 1000;
                timer.Elapsed += Timer_Elapsed1;
                timer.Start();
                
            }
            
        }

        private async void Timer_Elapsed1(object sender, ElapsedEventArgs e)
        {
            float x = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPosition), _opcNodeModel.ZPosition);
            float r = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.RPosition), _opcNodeModel.RPosition);
            if (r == 60 && x == 0)
            {
                await SetT(0);
                // 关闭定时器
                ((Timer)sender).Stop();
                // 释放定时器对象
                ((Timer)sender).Dispose();
            }
        }

        public async void setSuspend(){
           await _opcUAHelper.WriteTagAsync<bool>(nameof(_opcNodeModel.Suspend),_opcNodeModel.Suspend,true);
        }

        public async void ReSetO()
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.OReset), _opcNodeModel.OReset, 1);
        }

        public async void ReStartCamera()
        {
            if (_opcNodeModel == null) return;
            await _opcUAHelper.WriteTagAsync<short>(nameof(_opcNodeModel.Restart), _opcNodeModel.Restart, 1);
        }

        public async void ReStartWeight()
        {
            if (_opcNodeModel == null) return;
            var result = await _opcUAHelper.ReadTagAsync<bool>(nameof(_opcNodeModel.WRestart), _opcNodeModel.WRestart);
            await _opcUAHelper.WriteTagAsync<bool>(nameof(_opcNodeModel.WRestart), _opcNodeModel.WRestart, !result);
        }

        /// <summary>
        /// 读取并更新设备状态
        /// </summary>
        private async Task ReadAndUpdateDeviceStatus()
        {
            if (_opcNodeModel == null)
            {
                _logger.LogInfo("OPC配置为空，无法读取设备状态");
                return;
            }

            if (!_opcUAHelper.IsConnected)
            {
                _logger.LogInfo("OPC未连接，无法读取设备状态");
                return;
            }

            // 检查EMCStatus点位配置
            if (_opcNodeModel.EMCStatus == 0)
            {
                _logger.LogInfo("EMCStatus点位未配置，跳过设备状态读取");
                return;
            }

            try
            {
                _logger.LogInfo("🔍 开始读取设备实际状态...");
                
                // 读取设备状态
                var deviceStatus = await _opcUAHelper.ReadTagAsync<short>(nameof(_opcNodeModel.EMCStatus), _opcNodeModel.EMCStatus);
                
                _logger.LogInfo($"📊 设备实际状态: {deviceStatus}");
                
                // 更新界面显示
                McStatusNotify?.Invoke(deviceStatus);
                _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                {
                    StatusShowType = ShowType.ALL,
                    EquipmentStatus = deviceStatus,
                    EventMessage = $"设备状态: {GetStatusDescription(deviceStatus)}",
                    SourceType = SourceType.PLC,
                    EventCode = deviceStatus == 4 ? HEventCode.ERROR : HEventCode.SUCCESS
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"读取设备状态失败: {ex.Message}", ex);
                
                // 读取失败时不设置错误状态，只记录日志
                _logger.LogInfo("设备状态读取失败，保持当前状态");
            }
        }



        /// <summary>
        /// 获取状态描述
        /// </summary>
        private string GetStatusDescription(int status)
        {
            switch (status)
            {
                case 0:
                    return "停止";
                case 1:
                    return "运行中";
                case 2:
                    return "完成";
                case 3:
                    return "待机";
                case 4:
                    return "错误";
                default:
                    return $"未知状态({status})";
            }
        }



        private void NotifyMessage(OpcEventArgs e) {
            NotifyInfoEvent?.Invoke(e);
        }

        public async void MoveToPoint()
        {
            if (_opcNodeModel == null) return;
            await ResetXYArray();
            await SetXSpeed(250);
            await SetYSpeed(250);
            float z = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPosition), _opcNodeModel.ZPosition);
            bool isOk = true;
            SetZAsync(0);
            do
            {
                z = _opcUAHelper.ReadTag<float>(nameof(_opcNodeModel.ZPosition), _opcNodeModel.ZPosition);
                if (z == 0)
                {
                    await WriteXYArray(new float[] { -93 },new float[] { -250 } , 1);
                    isOk = false;
                }
            }
            while (isOk);

        }

        private string TranslateEventCode(string code)
        {
            string message = $"设备未知异常,错误代码:{code}";
            if (code.Contains("SafetyGrat")) message = "运动机构动作时，安全光栅被触发";
            if (code.Contains("EmergencyStop")) message = "急停故障";
            if (code.Contains("Unsafe")) message = "伺服运动时，安全条件未满足，这里安全条件为满足的意思是，有些伺服互相有安全的互锁，比如Z轴必须在某个高度的时候XY才能动作，如果Z不在安全高度，就触发XY定位的话，就会报这个故障";
            if (code.Contains("LowerLimit")) message = "PLC收到的定位坐标超过最小限位";
            if (code.Contains("UpperLimit")) message = "PLC收到的定位坐标超过最大限位";
            if (code.Contains("PositioningTimeout")) message = "轴定位超时";
            if (code.Contains("Halt_Ng")) message = "轴暂停错误";
            if (code.Contains("Home_Ng")) message = "轴回原错误";
            if (code.Contains("Jog_Ng")) message = "急停故障";
            if (code.Contains("Move_Ng")) message = "轴定位错误";
            if (code.Contains("Reset_Ng")) message = "轴复位错误";
            if (code.Contains("Servo Over Max Torque")) message = "轴堵转";
            return message;
        }

        private void AddLog(string message)
        {
            _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
            {
                EquipmentStatus = 4,
                SourceType = SourceType.PLC,
                EventCode = HEventCode.ERROR,
                StatusShowType = ShowType.LABEL,
                EventMessage = message
            });
        }
    }
}
