﻿<UserControl x:Class="IPM.Vision.Views.CustomControls.HKCameraControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.CustomControls"
             DataContext="{Binding HKVisionControlViewModel, Source={StaticResource Locator}}"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:wf="clr-namespace:System.Windows.Forms;assembly=System.Windows.Forms"
             xmlns:wfi="clr-namespace:System.Windows.Forms.Integration;assembly=WindowsFormsIntegration"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}" PassEventArgsToCommand="True"/>
        </hc:EventTrigger>
        <hc:EventTrigger EventName="Unloaded">
            <hc:EventToCommand Command="{Binding UnLoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="#f5f5f5" BorderBrush="Gray" BorderThickness="0.3">
        <!--<Image Source="{Binding RenderImageData,UpdateSourceTrigger=PropertyChanged}" Stretch="UniformToFill"/>-->
        <Canvas>
            <!--<Image Source="{Binding RenderImageData}"
                   Stretch="UniformToFill"
                   Width="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}"
                   Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}"
                   RenderTransformOrigin="0.5,0.5"
                   Visibility="{Binding IsCameraConnected, Converter={StaticResource BoolToVisibilityConverter}}">
                <Image.LayoutTransform>
                    <TransformGroup>
                        <RotateTransform Angle="90"/>
                    </TransformGroup>
                </Image.LayoutTransform>
            </Image>-->
            <Border  Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}}"
        Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}">
                <Image Source="{Binding RenderImageData}"
        HorizontalAlignment="Center"
        Panel.ZIndex="22"
        Stretch="Uniform" 
        Width="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}"
        Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}"
        RenderTransformOrigin="0.5,0.5"
        Visibility="{Binding IsCameraConnected, Converter={StaticResource BoolToVisibilityConverter}}">
                    <Image.LayoutTransform>
                        <TransformGroup>
                            <RotateTransform Angle="90"/>
                        </TransformGroup>
                    </Image.LayoutTransform>
                </Image>
            </Border>
            <!-- 中心十字准星 -->
            <!-- 水平线 -->
            <Line X1="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}, ConverterParameter=-10}"
                  Y1="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}}"
                  X2="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}, ConverterParameter=10}"
                  Y2="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}}"
                  Visibility="{Binding IsCameraConnected, Converter={StaticResource BoolToVisibilityConverter}}"
                  Stroke="Green"
                  StrokeThickness="1"
                  Panel.ZIndex="1000" />

            <!-- 垂直线 -->
            <Line X1="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}}"
                  Y1="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}, ConverterParameter=-10}"
                  X2="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}}"
                  Y2="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}, Converter={StaticResource HalfConverter}, ConverterParameter=10}"
                  Visibility="{Binding IsCameraConnected, Converter={StaticResource BoolToVisibilityConverter}}"
                  Stroke="Green"
                  StrokeThickness="1"
                  Panel.ZIndex="1000" />

            <!-- 显示未连接的提示信息 -->
            <Border
                Visibility="{Binding IsCameraConnected, Converter={StaticResource BoolToVisibilityConverter},ConverterParameter=True}"
                Width="{Binding ActualWidth, RelativeSource={RelativeSource AncestorType=Canvas}}"
                Height="{Binding ActualHeight, RelativeSource={RelativeSource AncestorType=Canvas}}">
                <Button Content="副相机连接已断开，点击尝试重新连接..."
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Style="{StaticResource ButtonDashedDanger}"
                        FontSize="14"
                        Command="{Binding ReconnectCommand}"/>
            </Border>

        </Canvas>
    </Border>
</UserControl>
