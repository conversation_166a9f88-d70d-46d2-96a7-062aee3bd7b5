# Mark点识别后步骤跳转问题修复说明

## 问题描述

在自动运行模式下，当设备处于等待状态，mark点拍照识别成功后，步骤会自动跳到第三个步骤，跳过了第二个步骤。这个问题只会在第一步是mark点识别的时候出现，每个边的步骤都设置了拍照。

## 问题根源分析

### 1. 冲突的步骤切换逻辑

系统中存在两个可能同时触发步骤切换的逻辑：

#### 设备状态通知触发的步骤切换
在 `_equipmentService_McStatusNotify(int obj)` 方法中：
```csharp
if (obj == 2 && IsRunning) // 设备状态2 = 就绪状态
{
    if (CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE && CurrentProcess.CameraType == CameraTypeEnum.Main)
    {
        // 拍照步骤完成，触发步骤切换
        ContinueToNextProcess();
    }
}
```

#### Mark点识别成功触发的步骤切换
在 `_hkVisionService_UpdatePictureModelEvent` 方法中：
```csharp
if (CurrentProcess.ProcessType == ProcessTypeEnum.POINT)
{
    var temp = await DiffMarkPoint(obj);
    if (temp && IsRunning)
    {
        // Mark点识别成功，触发步骤切换
        await ContinueToNextProcessSafely();
    }
}
```

### 2. 问题场景

当Mark点识别步骤完成后：
1. Mark点识别成功 → 触发 `ContinueToNextProcessSafely()` → 切换到下一步（应该是第二步）
2. 设备状态变为2（就绪） → 触发设备状态通知 → 如果当前步骤是拍照步骤，再次触发 `ContinueToNextProcess()` → 又切换到下一步（变成第三步）

### 3. 时序问题

虽然代码中有 `_isProcessingStepChange` 标志防止并发，但在以下情况下仍可能出现问题：
- Mark点识别成功后，步骤已经切换到第二步（拍照步骤）
- 此时设备状态2的通知到达，检测到当前是拍照步骤，又触发一次步骤切换
- 结果：跳过了第二步，直接到第三步

## 解决方案

### 修改设备状态通知处理逻辑

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`
**方法**: `_equipmentService_McStatusNotify(int obj)`
**行号**: 1881-1917

#### 修改前的问题代码
```csharp
if (CurrentProcess.ProcessType == ProcessTypeEnum.POINT)
{
    // Mark点识别步骤：触发HK相机拍照
    _hkVisionService.TakePicture(folderName, pictureName);
    NotifyLog($"📷 Mark点识别步骤 - HK相机拍照已触发");
}
else if (CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE && CurrentProcess.CameraType == CameraTypeEnum.Main)
{
    // 拍照步骤：触发步骤切换
    ContinueToNextProcess(); // 这里可能导致重复切换
}
```

#### 修改后的解决代码
```csharp
if (CurrentProcess.ProcessType == ProcessTypeEnum.POINT)
{
    // Mark点识别步骤：只触发拍照，不切换步骤
    _hkVisionService.TakePicture(folderName, pictureName);
    NotifyLog($"📷 Mark点识别步骤 - HK相机拍照已触发");
    // **修复关键问题：Mark点识别步骤不在此处切换步骤，由识别成功事件处理步骤切换**
    NotifyLog($"📝 注意：Mark点识别步骤的步骤切换由识别成功事件处理，此处不切换步骤");
}
else if (CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE && CurrentProcess.CameraType == CameraTypeEnum.Main)
{
    // 拍照步骤：触发步骤切换
    ContinueToNextProcess();
}
```

### 修改原理

1. **职责分离**: Mark点识别步骤的步骤切换只由识别成功事件处理，设备状态通知不再处理Mark点步骤的切换
2. **避免重复**: 防止同一个步骤完成被两个不同的事件重复处理
3. **保持一致性**: 拍照步骤仍然由设备状态通知处理步骤切换，保持原有逻辑

## 修改效果

### 修改前的问题流程
```
Mark点识别步骤 → 识别成功 → 切换到第二步(拍照) → 设备状态2 → 再次切换 → 跳到第三步
```

### 修改后的正确流程
```
Mark点识别步骤 → 识别成功 → 切换到第二步(拍照) → 设备状态2 → 不处理Mark点步骤切换 → 停留在第二步
```

## 验证方法

1. **启动自动运行模式**
2. **执行Mark点识别步骤**
3. **观察日志输出**:
   - 应该看到 "Mark点识别步骤的步骤切换由识别成功事件处理，此处不切换步骤"
   - 步骤应该正确从Mark点识别切换到第二步，而不是跳到第三步
4. **检查步骤顺序**: 确认所有步骤按照正确的ProcessNumber顺序执行

## 相关修复

这个修复与之前的其他修复配合使用：

1. **步骤排序修复**: 确保步骤在加载时按ProcessNumber正确排序
2. **防抖机制**: 防止设备状态变化的重复处理
3. **并发控制**: 使用 `_isProcessingStepChange` 标志防止步骤切换的并发问题

## 注意事项

- 此修复专门针对Mark点识别步骤的重复切换问题
- 不影响其他类型步骤的正常切换逻辑
- 保持了原有的异常处理和日志记录机制
- 建议在测试环境充分验证后再部署到生产环境
