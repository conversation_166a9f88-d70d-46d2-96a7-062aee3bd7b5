﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib
{
    public struct EVFDataSet
    {
        public IntPtr stream;

        public uint zoom;

        public EDSDK.EdsRect zoomRect;

        public EDSDK.EdsRect visibleRect;

        public EDSDK.EdsPoint imagePosition;

        public EDSDK.EdsSize sizeJpegLarge;
    }
}
