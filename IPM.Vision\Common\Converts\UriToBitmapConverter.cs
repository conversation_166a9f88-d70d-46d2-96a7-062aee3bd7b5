﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;
using System.Windows.Media.Imaging;

namespace IPM.Vision.Common.Converts
{
    public class UriToBitmapConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            
            try
            {
                if(value == null) return ConvertImage($"pack://application:,,,/Assets/404.png", false);
                if (!FileHelper.FileExists(value.ToString())) throw new Exception("未查询到文件");
                return ConvertImage(value.ToString());
            }
            catch (Exception ex) {
                return ConvertImage($"pack://application:,,,/Assets/404.png",false);
            }
            
            
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return null;
        }

        private BitmapImage ConvertImage(string imageUri,bool isCompress = true)
        {
            // **修复：正确处理Windows文件路径**
            Uri uri;
            try
            {
                // 如果是本地文件路径，转换为file://格式
                if (System.IO.Path.IsPathRooted(imageUri) && System.IO.File.Exists(imageUri))
                {
                    uri = new Uri(imageUri, UriKind.Absolute);
                }
                else
                {
                    uri = new Uri(imageUri, UriKind.Absolute);
                }
            }
            catch
            {
                // 如果URI创建失败，尝试作为本地文件路径处理
                try
                {
                    uri = new Uri(System.IO.Path.GetFullPath(imageUri), UriKind.Absolute);
                }
                catch
                {
                    // 最后的备用方案
                    uri = new Uri(imageUri, UriKind.RelativeOrAbsolute);
                }
            }
            BitmapImage bitmap = new BitmapImage();
            bitmap.BeginInit();
            bitmap.UriSource = uri;
            bitmap.CreateOptions = BitmapCreateOptions.DelayCreation;
            bitmap.CacheOption = BitmapCacheOption.OnLoad;
            if (isCompress)
            {
                bitmap.DecodePixelWidth = 150;  // 设置期望的宽度，例如300像素
            }
            bitmap.EndInit();
           
            
            return bitmap;
        }
    }
}
