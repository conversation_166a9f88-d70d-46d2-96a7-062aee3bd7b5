# OPT相机触发模式保持策略说明

## 需求描述

自动运行模式下，步骤切换也不要改变OPT相机的triggerMode，在自动运行模式下一直保持触发模式开启。

## 当前实现分析

### 1. 自动运行开始时设置触发模式

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`
**方法**: `HandleAutoRunModeChange(bool isRunning)`

```csharp
if (isRunning)
{
    // 进入自动运行模式
    NotifyLog("🚀 进入自动运行模式");

    // **修复需求：开始自动运行时立即设置OPT相机触发模式**
    // 不再检查当前步骤类型，直接设置触发模式
    NotifyLog("📷 开始自动运行，立即设置OPT相机触发模式");
    SetOptCameraFrameTriggerMode();
}
```

### 2. 步骤切换时不改变触发模式

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`
**方法**: `HandleProcessChangeInAutoMode(ObservableProcessModel newProcess)`

```csharp
private void HandleProcessChangeInAutoMode(ObservableProcessModel newProcess)
{
    try
    {
        // 只在自动运行模式下处理
        if (!IsRunning)
        {
            return;
        }

        if (newProcess != null &&
            newProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE &&
            newProcess.CameraType == CameraTypeEnum.Main)
        {
            // 切换到side拍照步骤，重置引脚计数器并打开AutoTake
            NotifyLog($"🔄 自动运行模式：切换到side拍照步骤 [{newProcess.ParamName}]");

            // 重置引脚计数器，为新的拍照步骤开始计数
            _currentPinIndex = 0;
            NotifyLog($"🔢 已重置引脚计数器，开始新步骤的引脚拍摄");

            // **新增功能：打开设备AutoTake**
            _equipmentService.SetAutoTake();
            NotifyLog("🔧 已打开设备AutoTake（自动拍照模式）");
        }
        else
        {
            // 切换到非side拍照步骤，关闭AutoTake
            if (newProcess != null)
            {
                NotifyLog($"🔄 自动运行模式：切换到步骤 [{newProcess.ParamName}] (类型: {newProcess.ProcessType})");

                // **新增功能：关闭设备AutoTake**
                _equipmentService.CloseAutoTake();
                NotifyLog("🔧 已关闭设备AutoTake（退出自动拍照模式）");
            }
        }
    }
    catch (Exception ex)
    {
        NotifyLog($"❌ 处理自动运行模式下步骤切换时发生异常: {ex.Message}");
    }
}
```

**关键点**: 步骤切换时只控制设备的AutoTake开关，**不调用任何OPT相机触发模式相关的方法**。

### 3. 自动运行结束时关闭触发模式

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`
**方法**: `HandleAutoRunModeChange(bool isRunning)`

```csharp
else
{
    // 离开自动运行模式
    NotifyLog("🛑 离开自动运行模式，关闭OPT相机触发模式");

    // 关闭OPT相机触发模式
    _optVisionService.IsTriggerMode = false;
    _optVisionService.CloseTrigger();

    // **新增功能：离开自动运行模式时关闭AutoTake**
    _equipmentService.CloseAutoTake();
    NotifyLog("🔧 已关闭设备AutoTake（离开自动运行模式）");

    NotifyLog("✅ OPT相机触发模式已关闭，恢复连续采集模式");
}
```

## 触发模式保持策略

### 工作流程

1. **自动运行开始**:
   - 调用 `SetOptCameraFrameTriggerMode()`
   - 设置 `_optVisionService.IsTriggerMode = true`
   - 配置硬件触发模式
   - 启动队列健康监控

2. **步骤切换期间**:
   - **不调用** `SetOptCameraFrameTriggerMode()`
   - **不调用** `_optVisionService.CloseTrigger()`
   - **不修改** `_optVisionService.IsTriggerMode`
   - 只控制 `_equipmentService.SetAutoTake()` / `CloseAutoTake()`

3. **自动运行结束**:
   - 设置 `_optVisionService.IsTriggerMode = false`
   - 调用 `_optVisionService.CloseTrigger()`
   - 关闭设备AutoTake

### 触发模式状态图

```
自动运行开始
    ↓
设置触发模式 (TriggerMode = On)
    ↓
步骤1 (拍照) → 打开AutoTake
    ↓
步骤2 (非拍照) → 关闭AutoTake
    ↓
步骤3 (拍照) → 打开AutoTake
    ↓
... (触发模式始终保持开启)
    ↓
自动运行结束
    ↓
关闭触发模式 (TriggerMode = Off)
```

## 优点分析

### 1. 稳定性提升
- 避免了频繁的触发模式开关操作
- 减少了硬件配置变更的次数
- 降低了时序问题的风险

### 2. 性能优化
- 消除了步骤切换时的触发模式配置延迟
- 减少了不必要的硬件通信
- 提高了步骤切换的响应速度

### 3. 逻辑简化
- 触发模式的生命周期与自动运行模式一致
- 步骤切换逻辑更加清晰
- 减少了状态管理的复杂性

## 当前实现确认

✅ **符合需求**: 当前实现已经满足要求  
✅ **触发模式保持**: 自动运行期间触发模式始终开启  
✅ **步骤切换独立**: 步骤切换只控制AutoTake，不影响触发模式  
✅ **生命周期管理**: 触发模式与自动运行模式同步开关  

## 测试验证点

1. **自动运行开始**: 确认触发模式正确开启
2. **步骤切换**: 确认触发模式保持不变，只有AutoTake状态改变
3. **多步骤循环**: 确认多次步骤切换后触发模式仍然稳定
4. **自动运行结束**: 确认触发模式正确关闭

## 结论

当前的实现已经完全符合需求：
- OPT相机的触发模式在自动运行开始时设置一次
- 在整个自动运行期间保持开启状态
- 步骤切换时不会改变触发模式
- 只在自动运行结束时关闭触发模式

无需进行任何修改，现有代码已经实现了所需的触发模式保持策略。
