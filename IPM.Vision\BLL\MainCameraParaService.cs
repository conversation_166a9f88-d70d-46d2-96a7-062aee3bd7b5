﻿using IPM.Vision.BLL.interfaces;
using IPM.Vision.Camera;
using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.HKSDKLib;
using IPM.Vision.Common;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.CanonControls;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;

namespace IPM.Vision.BLL
{
    public class MainCameraParaService : BaseService<MainCameraParamModel>, IMainCameraParaService
    {
        private readonly NLogHelper _logger;
        private CameraController _mainCameraController;
        private CameraController _childCameraController;
        private CanonCameraModel _mainCameraModel;
        private CanonCameraModel _childCameraModel;
        private readonly ObservableGlobalState _globalState;
        private Timer _timer;
        private event EDSDK.EdsCameraAddedHandler _cameraAddedHandler;
        public event Action<CameraController> MainCameraAddedEvent;
        public event Action<CameraController> ChildCameraAddedEvent;
        private bool _isConnect = false;

        public MainCameraParaService(IBaseRepository<MainCameraParamModel> baseRepository,
            NLogHelper logger,
            ObservableGlobalState globalState) : base(baseRepository)
        {
            _logger = logger;
            _globalState = globalState;
        }

        public CameraController MainController { get => _mainCameraController; }
        public bool IsConnect { get => _isConnect; set => _isConnect = value; }

        #region SDK操作
        public bool InitSDK()
        {
            return EDSDK.EdsInitializeSDK() == EDSDK.EDS_ERR_OK;
        }

        public bool ReleaseSDK()
        {
            if(_timer != null && _timer.Enabled) _timer.Close();
            return EDSDK.EdsTerminateSDK() == EDSDK.EDS_ERR_OK;
        }
        #endregion

        #region 获取相机

        public void InitCamera()
        {
            if (_cameraAddedHandler != null) return;
            _cameraAddedHandler = new EDSDK.EdsCameraAddedHandler(InitEvent);
            EDSDK.EdsSetCameraAddedHandler(_cameraAddedHandler, IntPtr.Zero);
        }

        private uint InitEvent(IntPtr inContext)
        {
            InitInstance();
            return 0U;
        }

        public async void InitInstance()
        {
            uint err = EDSDK.EDS_ERR_OK;
            IntPtr outCameraListRef = IntPtr.Zero;
            err = EDSDK.EdsGetCameraList(out outCameraListRef);
            int outCount = 0;
            if(err == EDSDK.EDS_ERR_OK)
                EDSDK.EdsGetChildCount(outCameraListRef, out outCount);
            if (outCount == 0) return;
            for(int index = 0; index < outCount; index++)
            {
                IntPtr outRef = IntPtr.Zero;
                EDSDK.EdsDeviceInfo deviceInfo = new EDSDK.EdsDeviceInfo();
                err = EDSDK.EdsGetChildAtIndex(outCameraListRef, index, out outRef);
                if (err == EDSDK.EDS_ERR_OK)
                {
                    err = EDSDK.EdsGetDeviceInfo(outRef, out deviceInfo);
                    if(deviceInfo.szPortName == _globalState.AppConfig.MainCameraAddress)
                    {
                        _mainCameraModel = new CanonCameraModel(ref outRef);
                        _mainCameraModel.EdsDeviceInfo = deviceInfo;
                        _mainCameraController = new CameraController(ref _mainCameraModel);
                        // **修复：Canon相机只发送日志消息，不更新状态指示器，避免干扰OPT相机状态**
                        _globalState.HObservable.NotifyObservers(new LEvents.HEquipmentStatusArgs()
                        {
                            StatusShowType = LEvents.ShowType.LABEL, // 只发送日志消息
                            EventCode = LEvents.HEventCode.SUCCESS,
                            SourceType = LEvents.SourceType.CAMERA,
                            EventMessage = "Canon相机连接成功！",
                            EquipmentStatus = 1
                        });
                        IsConnect = true;
                        _mainCameraController.Run();
                        await Task.Delay(3000);
                        MainCameraAddedEvent?.Invoke(_mainCameraController);
                    }
                    else
                    {
                        _childCameraModel = new CanonCameraModel(ref outRef);
                        _childCameraModel.EdsDeviceInfo = deviceInfo;
                        _childCameraModel.DownLoadCompleteEvent += _childCameraModel_DownLoadCompleteEvent;
                        _childCameraController = new CameraController(ref _childCameraModel);
                        ChildCameraAddedEvent?.Invoke(_childCameraController);
                    }
                    

                }
                
            }
        }

        private void _childCameraModel_DownLoadCompleteEvent(PictureModel obj)
        {

        }




        #endregion

        public void AddSource(IObserver observer)
        {
            if (_mainCameraController != null && _mainCameraController.GetModel() != null)
            {
                var control = observer as PropertyComboBox;
                if (control != null) control.SetCameraController(_mainCameraController);
                if (observer != null) _mainCameraController.GetModel().Add(ref observer);
            }
        }

        public void RemoveObserver(IObserver item)
        {
            if (this._mainCameraController == null || this._mainCameraController.GetModel() == null)
                return;
            this._mainCameraController.GetModel().Remove(ref item);
        }

        public void RemoveAllSource()
        {
            if(_mainCameraController != null)
            {
                _mainCameraController.GetModel().RemoveAll();
            }
        }

        #region 相机操作
        public void DisconnectCamera()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SHUT_DOWN, IntPtr.Zero));
        }

        public void GetAllProperty()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.GET_ALL_PROPERTY, IntPtr.Zero));
        }

        public void GetAllPropertyDesc()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.GET_ALL_PROPERTYDESC, IntPtr.Zero));
        }

        public void StartEVF()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.START_EVF, _mainCameraController.GetModel().Camera));
        }

        public void EndEVF()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.END_EVF, _mainCameraController.GetModel().Camera));
        }

        public void SetFocusPosition()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.POSITION_POINT, IntPtr.Zero));
        }

        public void PressHalf()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.PRESS_HALFWAY, IntPtr.Zero));
        }


        public void PressOFF()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.PRESS_OFF, IntPtr.Zero));
        }

        public void StartEvfAF()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.EVF_AF_ON, IntPtr.Zero));
        }

        public void StopEvfAF()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.EVF_AF_OFF, IntPtr.Zero));
        }

        public void SetProperty(ObservableMainCameraParaModel model)
        {
            if (_mainCameraController != null)
            {
                if (model.Ae != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_AE_MODE, (IntPtr)model.Ae));
                if (model.Tv != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_TV, (IntPtr)model.Tv));
                if (model.Av != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_AV, (IntPtr)model.Av));
                if (model.AfMode != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_AF_MODE, (IntPtr)model.AfMode));
                if (model.EvfAfMode != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_EVF_AFMODE, (IntPtr)model.EvfAfMode));
                if (model.Iso != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_ISO_SPEED, (IntPtr)model.Iso));
                if (model.DriveMode != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_DRIVE_MODE, (IntPtr)model.DriveMode));
                if (model.Exposure != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_EXPOSURE_COMPENSATION, (IntPtr)model.Exposure));
                if (model.ImageQuality != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_IMAGEQUALITY, (IntPtr)model.ImageQuality));
                if (model.PictureStyle != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_PICTURESTYLE, (IntPtr)model.PictureStyle));
                if (model.WhiteBalance != 0) _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_WHITE_BALANCE, (IntPtr)model.WhiteBalance));
            }
        }
        public void TakePicture()
        {
            if (_mainCameraController != null)
            {
                //_mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.PRESS_HALFWAY, IntPtr.Zero));
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.PRESS_COMPLETELY, IntPtr.Zero));
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.PRESS_OFF, IntPtr.Zero));
                //_mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.TAKE_PICTURE, IntPtr.Zero));
            }
        }

        public void SetPictureInfo(string  pictureName,string folder)
        {
            if(_mainCameraController != null)
            {
                _mainCameraController.GetModel().FileName = pictureName;
                _mainCameraController.GetModel().FolderPath = folder;
                _mainCameraController.GetModel().FileFullPath = Common.FileHelper.ConcatFile(folder, pictureName);
            }

        }

        public void DownLoadPicture()
        {
            if (_mainCameraController != null)
                _mainCameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.DOWNLOAD, IntPtr.Zero));
        }
        #endregion
    }
}
