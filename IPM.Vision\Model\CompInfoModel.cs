﻿using SqlSugar;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using IPM.Vision.Common;

namespace IPM.Vision.Model
{
    [SugarTable("comp_info")]
    public class CompInfoModel : BasicModel
    {
        [SugarColumn(ColumnName = "X1")]
        public float X1 { get; set; }

        [SugarColumn(ColumnName = "Y1")]
        public float Y1 { get; set; }

        [SugarColumn(ColumnName = "Z1")]
        public float Z1 { get; set; }

        [SugarColumn(ColumnName = "R1")]
        public float R1 { get; set; }

        [SugarColumn(ColumnName = "T1")]
        public float T1 { get; set; }

        [SugarColumn(ColumnName = "PIN1")]
        public int Pin1 { get; set; }

        [SugarColumn(ColumnName = "STEP1")]
        public float Step1 { get; set; }

        [SugarColumn(ColumnName = "X2")]
        public float X2 { get; set; }

        [SugarColumn(ColumnName = "Y2")]
        public float Y2 { get; set; }

        [SugarColumn(ColumnName = "Z2")]
        public float Z2 { get; set; }

        [SugarColumn(ColumnName = "R2")]
        public float R2 { get; set; }

        [SugarColumn(ColumnName = "T2")]
        public float T2 { get; set; }

        [SugarColumn(ColumnName = "PIN2")]
        public int Pin2 { get; set; }

        [SugarColumn(ColumnName = "STEP2")]
        public float Step2 { get; set; }

        [SugarColumn(ColumnName = "X3")]
        public float X3 { get; set; }

        [SugarColumn(ColumnName = "Y3")]
        public float Y3 { get; set; }

        [SugarColumn(ColumnName = "Z3")]
        public float Z3 { get; set; }

        [SugarColumn(ColumnName = "R3")]
        public float R3 { get; set; }

        [SugarColumn(ColumnName = "T3")]
        public float T3 { get; set; }

        [SugarColumn(ColumnName = "PIN3")]
        public int Pin3 { get; set; }

        [SugarColumn(ColumnName = "STEP3")]
        public float Step3 { get; set; }

        [SugarColumn(ColumnName = "X4")]
        public float X4 { get; set; }

        [SugarColumn(ColumnName = "Y4")]
        public float Y4 { get; set; }

        [SugarColumn(ColumnName = "Z4")]
        public float Z4 { get; set; }

        [SugarColumn(ColumnName = "R4")]
        public float R4 { get; set; }

        [SugarColumn(ColumnName = "T4")]
        public float T4 { get; set; }

        [SugarColumn(ColumnName = "PIN4")]
        public int Pin4 { get; set; }

        [SugarColumn(ColumnName = "STEP4")]
        public float Step4 { get; set; }

        [SugarColumn(ColumnName = "is_reserver")]
        public bool IsReserver { get; set; } = true;

        [SugarColumn(ColumnName = "reserver_number")]
        public float ReserverNumber { get; set; }

        [SugarColumn(ColumnName = "x1_more")]
        public float X1More { get; set; }

        [SugarColumn(ColumnName = "y1_more")]
        public float Y1More { get; set; }

        [SugarColumn(ColumnName = "x2_more")]
        public float X2More { get; set; }

        [SugarColumn(ColumnName = "y2_more")]
        public float Y2More { get; set; }

        [SugarColumn(ColumnName = "x3_more")]
        public float X3More { get; set; }

        [SugarColumn(ColumnName = "y3_more")]
        public float Y3More { get; set; }

        [SugarColumn(ColumnName = "x4_more")]
        public float X4More { get; set; }

        [SugarColumn(ColumnName = "y4_more")]
        public float Y4More { get; set; }

        [SugarColumn(ColumnName = "is_auto_take")]
        public float IsAutoTake { get; set; }

        [SugarColumn(ColumnName = "move_speed")]
        public float MoveSpeed { get; set; }

        [SugarColumn(ColumnName = "ace_speed")]
        public float AceSpeed { get; set; }

        [SugarColumn(ColumnName = "delay_time")]
        public float DelayTime { get; set; }


        [SugarColumn(ColumnName = "mark_x")]
        public float MarkX { get; set; }

        [SugarColumn(ColumnName = "mark_y")]
        public float MarkY { get; set; }

        /// <summary>
        /// 引脚拍摄起点位置 (0:左上角, 1:右上角, 2:右下角, 3:左下角)
        /// </summary>
        [SugarColumn(ColumnName = "pin_start_position")]
        public int PinStartPosition { get; set; } = 0;

        /// <summary>
        /// 引脚拍摄方向 (0:顺时针, 1:逆时针)
        /// </summary>
        [SugarColumn(ColumnName = "pin_direction")]
        public int PinDirection { get; set; } = 1;
    }
}
