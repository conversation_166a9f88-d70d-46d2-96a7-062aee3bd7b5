<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MvCamCtrl.Net</name>
    </assembly>
    <members>
        <member name="T:MvCamCtrl.NET.cbExceptiondelegate">
            <summary>
            Exception callback
            </summary>
            <param name="nMsgType">Msg type</param>
            <param name="pUser">User defined variable</param>
        </member>
        <member name="T:MvCamCtrl.NET.cbEventdelegate">
            <summary>
            Event callback (Interfaces not recommended)
            </summary>
            <param name="nUserDefinedId">User defined ID</param>
            <param name="pUser">User defined variable</param>
        </member>
        <member name="T:MvCamCtrl.NET.cbEventdelegateEx">
            <summary>
            Event callback
            </summary>
            <param name="pEventInfo">Event Info</param>
            <param name="pUser">User defined variable</param>
        </member>
        <member name="T:MvCamCtrl.NET.IAddition">
            <summary>
            附加模块
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.IAddition.LocalUpgrade(System.String)">
            <summary>
            设备本地升级
            </summary>
            <param name="pFilePathName">文件路径及文件名</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IAddition.GetUpgradeProcess(System.UInt32@)">
            <summary>
            获取升级进度
            </summary>
            <param name="pnProcess">进度接收地址</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IAddition.ReadMemory(System.IntPtr,System.Int64,System.Int64)">
            <summary>
            读内存
            </summary>
            <param name="pBuffer">作为返回值使用，保存读到的内存值（GEV设备内存值是按照大端模式存储的，其它协议设备按照小端存储）</param>
            <param name="nAddress">待读取的内存地址，该地址可以从设备的Camera.xml文件中获取，形如xxx_RegAddr的xml节点值</param>
            <param name="nLength">待读取的内存长度</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IAddition.WriteMemory(System.IntPtr,System.Int64,System.Int64)">
            <summary>
            写内存
            </summary>
            <param name="pBuffer">待写入的内存值（注意GEV设备内存值要按照大端模式存储，其它协议设备按照小端存储）</param>
            <param name="nAddress">待写入的内存地址，该地址可以从设备的Camera.xml文件中获取，形如xxx_RegAddr的xml节点值</param>
            <param name="nLength">待写入的内存长度</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IAddition.RegisterExceptionCallBack(MvCamCtrl.NET.cbExceptiondelegate,System.IntPtr)">
            <summary>
            注册异常消息回调，在打开设备之后调用
            </summary>
            <param name="cbException">异常回调代理</param>
            <param name="pUser">用户自定义变量</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IAddition.RegisterAllEventCallBack(MvCamCtrl.NET.cbEventdelegateEx,System.IntPtr)">
            <summary>
            注册全部事件回调，在打开设备之后调用
            </summary>
            <param name="cbEvent">事件回调代理</param>
            <param name="pUser">用户自定义变量</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IAddition.RegisterEventCallBackEx(System.String,MvCamCtrl.NET.cbEventdelegateEx,System.IntPtr)">
            <summary>
            注册单个事件回调，在打开设备之后调用
            </summary>
            <param name="pEventName">事件名称</param>
            <param name="cbEvent">事件回调代理</param>
            <param name="pUser">用户自定义变量</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IAddition.OpenParamsGUI">
            <summary>
            打开获取或设置相机参数的GUI界面
            </summary>
            <returns>成功，返回MV_OK，失败，返回错误码</returns>
        </member>
        <member name="T:MvCamCtrl.NET.cbOutputdelegate">
            <summary>
            Grab callback
            </summary>
            <param name="pData">Image data</param>
            <param name="pFrameInfo">Frame info</param>
            <param name="pUser">User defined variable</param>
        </member>
        <member name="T:MvCamCtrl.NET.CCamCtrlRef">
            <summary>
            从C/C++接口库导出的函数的类
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CCamera">
            <summary>
            相机控制类
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.IParameter">
            <summary>
            相机参数配置模块
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.XML_GetGenICamXML(System.IntPtr,System.UInt32,System.UInt32@)">
            <summary>
            获取设备属性树XML
            </summary>
            <param name="pData">XML数据接收缓存</param>
            <param name="nDataSize">接收缓存大小</param>
            <param name="pnDataLen">实际数据大小</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.XML_GetNodeAccessMode(System.String,MvCamCtrl.NET.MV_XML_AccessMode@)">
            <summary>
            获得当前节点的访问模式
            </summary>
            <param name="pstrName">节点名称</param>
            <param name="pAccessMode">节点的访问模式</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.XML_GetNodeInterfaceType(System.String,MvCamCtrl.NET.MV_XML_InterfaceType@)">
            <summary>
            获得当前节点的类型
            </summary>
            <param name="pstrName">节点名称</param>
            <param name="pInterfaceType">节点的类型</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.GetIntValue(System.String,MvCamCtrl.NET.CIntValue@)">
            <summary>
            获取Integer属性值
            </summary>
            <param name="strKey">属性键值，如获取宽度信息则为"Width"</param>
            <param name="pcValue">返回给调用者有关设备属性信息</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.SetIntValue(System.String,System.Int64)">
            <summary>
            设置Integer型属性值
            </summary>
            <param name="strKey">属性键值，如获取宽度信息则为"Width"</param>
            <param name="nValue">想要设置的设备的属性值</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.GetEnumValue(System.String,MvCamCtrl.NET.CEnumValue@)">
            <summary>
            获取Enum属性值
            </summary>
            <param name="strKey">属性键值，如获取像素格式信息则为"PixelFormat"</param>
            <param name="pcValue">返回给调用者有关设备属性信息</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.GetEnumEntrySymbolic(System.String,MvCamCtrl.NET.CEnumEntry@)">
            <summary>
            获取指定枚举型节点的指定枚举值所对应的字符串名称
            </summary>
            <param name="strKey">键值</param>
            <param name="pcEnumEntry">枚举型节点指定枚举值信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.SetEnumValue(System.String,System.UInt32)">
            <summary>
            设置Enum型属性值
            </summary>
            <param name="strKey">属性键值，如获取像素格式信息则为"PixelFormat"</param>
            <param name="nValue">想要设置的设备的属性值</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.SetEnumValueByString(System.String,System.String)">
            <summary>
            设置Enum型属性值
            </summary>
            <param name="strKey">属性键值，如获取像素格式信息则为"PixelFormat"</param>
            <param name="strValue">想要设置的设备的属性字符串</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.GetFloatValue(System.String,MvCamCtrl.NET.CFloatValue@)">
            <summary>
            获取Float属性值
            </summary>
            <param name="strKey">属性键值</param>
            <param name="pcValue">返回给调用者有关设备属性信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.SetFloatValue(System.String,System.Single)">
            <summary>
            获取Float属性值
            </summary>
            <param name="strKey">属性键值</param>
            <param name="fValue">想要设置的设备的属性值</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.GetBoolValue(System.String,System.Boolean@)">
            <summary>
            获取Boolean属性值
            </summary>
            <param name="strKey">属性键值</param>
            <param name="pbValue">返回给调用者有关设备属性值</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.SetBoolValue(System.String,System.Boolean)">
            <summary>
            设置Boolean属性值
            </summary>
            <param name="strKey">属性键值</param>
            <param name="bValue">想要设置的设备的属性值</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.GetStringValue(System.String,MvCamCtrl.NET.CStringValue@)">
            <summary>
            获取String属性值
            </summary>
            <param name="strKey">属性键值</param>
            <param name="pcValue">返回给调用者有关设备属性值</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.SetStringValue(System.String,System.String)">
            <summary>
            设置String属性值
            </summary>
            <param name="strKey">属性键值</param>
            <param name="strValue">想要设置的设备的属性值</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.SetCommandValue(System.String)">
            <summary>
            设置Command型属性值
            </summary>
            <param name="strKey">属性键值</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.InvalidateNodes">
            <summary>
            清除GenICam节点缓存
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.FeatureSave(System.String)">
            <summary>
            保存设备属性
            </summary>
            <param name="strFileName">属性文件名</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.FeatureLoad(System.String)">
            <summary>
            导入设备属性
            </summary>
            <param name="strFileName">属性文件名</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.FileAccessRead(MvCamCtrl.NET.CFileAccess@)">
            <summary>
            从设备读取文件
            </summary>
            <param name="pcFileAccess">文件存取信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.FileAccessReadEx(MvCamCtrl.NET.CFileAccessEx@)">
            <summary>
            从设备读取文件到本地缓存
            </summary>
            <param name="pcFileAccessEx">文件数据信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.FileAccessWrite(MvCamCtrl.NET.CFileAccess@)">
            <summary>
            将文件写入设备
            </summary>
            <param name="pcFileAccess">文件存取信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.FileAccessWriteEx(MvCamCtrl.NET.CFileAccessEx@)">
            <summary>
            将文件缓存写入设备
            </summary>
            <param name="pcFileAccessEx">文件数据信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IParameter.GetFileAccessProgress(MvCamCtrl.NET.CFileAccessProgress@)">
            <summary>
            获取文件存取的进度
            </summary>
            <param name="pcFileAccessProgress">进度内容</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="T:MvCamCtrl.NET.IStreamGrabber">
            <summary>
            相机取图模块
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.RegisterImageCallBackEx(MvCamCtrl.NET.cbOutputExdelegate,System.IntPtr)">
            <summary>
            注册图像数据回调
            </summary>
            <param name="cbOutput">图像数据回调代理</param>
            <param name="pUser">用户自定义变量</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.RegisterImageCallBackForRGB(MvCamCtrl.NET.cbOutputExdelegate,System.IntPtr)">
            <summary>
            注册图像数据回调，RGB
            </summary>
            <param name="cbOutput">图像数据回调代理</param>
            <param name="pUser">用户自定义变量</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.RegisterImageCallBackForBGR(MvCamCtrl.NET.cbOutputExdelegate,System.IntPtr)">
            <summary>
            注册图像数据回调，BGR
            </summary>
            <param name="cbOutput">图像数据回调代理</param>
            <param name="pUser">用户自定义变量</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.StartGrabbing">
            <summary>
            开始取流
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.StopGrabbing">
            <summary>
            停止取流
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.GetImageForRGB(System.Byte[],System.UInt32,MvCamCtrl.NET.CFrameoutEx@,System.Int32)">
            <summary>
            获取一帧RGB数据，此函数为查询式获取，每次调用查询内部
            缓存有无数据，有数据则获取数据，无数据返回错误码
            </summary>
            <param name="pData">图像缓存</param>
            <param name="nDataSize">图像缓存大小</param>
            <param name="pcFrameEx">图像信息结构体</param>
            <param name="nMsec">等待超时时间</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.GetImageForBGR(System.Byte[],System.UInt32,MvCamCtrl.NET.CFrameoutEx@,System.Int32)">
            <summary>
            获取一帧BGR数据，此函数为查询式获取，每次调用查询内部
            缓存有无数据，有数据则获取数据，无数据返回错误码
            </summary>
            <param name="pData">图像缓存</param>
            <param name="nDataSize">图像缓存大小</param>
            <param name="pcFrameEx">图像信息结构体</param>
            <param name="nMsec">等待超时时间</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.GetImageBuffer(MvCamCtrl.NET.CFrameout@,System.Int32)">
            <summary>
            使用内部缓存获取一帧图片
            </summary>
            <param name="pcFrame">图像数据和图像信息</param>
            <param name="nMsec">等待超时时间，输入INFINITE时表示无限等待，直到收到一帧数据或者停止取流</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.FreeImageBuffer(MvCamCtrl.NET.CFrameout@)">
            <summary>
            释放图像缓存(此接口用于释放不再使用的图像缓存，与GetImageBuffer配套使用)
            </summary>
            <param name="pcFrame">图像数据和图像信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.GetOneFrameTimeout(System.Byte[],System.UInt32,MvCamCtrl.NET.CFrameoutEx@,System.Int32)">
            <summary>
            采用超时机制获取一帧图片，SDK内部等待直到有数据时返回
            </summary>
            <param name="pData">图像缓存</param>
            <param name="nDataSize">图像缓存大小</param>
            <param name="pcFrameEx">图像信息结构体</param>
            <param name="nMsec">等待超时时间</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.ClearImageBuffer">
            <summary>
            清除取流数据缓存
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.DisplayOneFrame(MvCamCtrl.NET.CDisplayFrameInfo@)">
            <summary>
            显示一帧图像
            </summary>
            <param name="pcDisplayInfo">图像信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.DisplayOneFrameEx(MvCamCtrl.NET.CDisplayFrameInfoEx@)">
            <summary>
            显示一帧图像(扩展图像的高宽)
            </summary>
            <param name="pcDisplayInfoEx">图像信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.SetImageNodeNum(System.UInt32)">
            <summary>
            设置SDK内部图像缓存节点个数，大于等于1，在抓图前调用
            </summary>
            <param name="nNum">缓存节点个数</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.SetGrabStrategy(MvCamCtrl.NET.MV_GRAB_STRATEGY)">
            <summary>
            设置取流策略
            </summary>
            <param name="enGrabStrategy">策略枚举值</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IStreamGrabber.SetOutputQueueSize(System.UInt32)">
            <summary>
            设置输出缓存个数（只有在MV_GrabStrategy_LatestImages策略下才有效，范围：1-ImageNodeNum）
            </summary>
            <param name="nOutputQueueSize">输出缓存个数</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="T:MvCamCtrl.NET.IImageProcess">
            <summary>
            图像处理相关接口
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SaveImageEx(MvCamCtrl.NET.CSaveImageParam@)">
            <summary>
            保存图片，支持Bmp和Jpeg
            </summary>
            <param name="pcSaveParam">保存图片参数信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SaveImageToFile(MvCamCtrl.NET.CSaveImgToFileParam@)">
            <summary>
            保存图像到文件
            </summary>
            <param name="pcSaveFileParam">保存图片文件参数信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SavePointCloudData(MvCamCtrl.NET.CSavePointCloudParam@)">
            <summary>
            保存3D点云数据，支持PLY、CSV和OBJ三种格式
            </summary>
            <param name="pcPointDataParam">保存点云数据参数信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.RotateImage(MvCamCtrl.NET.CRotateImageParam@)">
            <summary>
            图像旋转
            </summary>
            <param name="pcRotateParam">图像旋转参数信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.FlipImage(MvCamCtrl.NET.CFlipImageParam@)">
            <summary>
            图像翻转
            </summary>
            <param name="pcFlipParam">图像翻转参数信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.ConvertPixelType(MvCamCtrl.NET.CPixelConvertParam@)">
            <summary>
            像素格式转换
            </summary>
            <param name="pcCvtParam">像素格式转换参数信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.ConvertPixelType(System.Byte[],MvCamCtrl.NET.CFrameoutEx@,System.Byte[],System.UInt32,MvCamCtrl.NET.MvGvspPixelType)">
            <summary>
            像素格式转换(用于GetOneFrameTimeout接口对应)
            </summary>
            <param name="byteSrcData">源图像数据</param>
            <param name="pcFrameEx">源图像数据信息</param>
            <param name="byteDstData">目标图像数据缓存</param>
            <param name="nDataSize">目标图像数据缓存大小</param>
            <param name="enDstPixelType">目标图像数据格式</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SetBayerCvtQuality(System.UInt32)">
            <summary>
            设置插值算法类型
            </summary>
            <param name="nBayerCvtQuality">Bayer的插值方法  0-快速 1-均衡 2-最优（默认为最优）</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SetBayerGammaValue(System.Single)">
            <summary>
            设置Bayer格式的Gamma值
            </summary>
            <param name="fBayerGammaValue">Gamma值[0.1,4.0]</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SetGammaValue(MvCamCtrl.NET.MvGvspPixelType,System.Single)">
            <summary>
            设置Mono8/Bayer8/10/12/16格式的Gamma值
            </summary>
            <param name="enSrcPixelType">像素格式,支持PixelType_Gvsp_Mono8,Bayer8/10/12/16</param>
            <param name="fGammaValue">Gamma值[0.1,4.0]</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SetBayerGammaParam(MvCamCtrl.NET.CGammaParam@)">
            <summary>
            设置Bayer格式的Gamma信息
            </summary>
            <param name="pcGammaParam">Gamma信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SetBayerCCMParam(MvCamCtrl.NET.CCCMParam@)">
            <summary>
            设置Bayer格式的CCM使能和矩阵，量化系数默认1024
            </summary>
            <param name="pcCCMParam">CCM参数</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SetBayerCCMParamEx(MvCamCtrl.NET.CCCMParamEx@)">
            <summary>
            设置Bayer格式的CCM使能和矩阵
            </summary>
            <param name="pcCCMParam">CCM参数</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.SetBayerFilterEnable(System.Boolean)">
            <summary>
            插值算法平滑使能设置
            </summary>
            <param name="bFilterEnable">平滑使能</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.ImageContrast(MvCamCtrl.NET.CContrastParam@)">
            <summary>
            图像对比度调节
            </summary>
            <param name="pcContrastParam">对比度调节参数</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.HB_Decode(MvCamCtrl.NET.CDecodeParam@)">
            <summary>
            无损解码
            </summary>
            <param name="pcDecodeParam">无损解码参数信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.StartRecord(MvCamCtrl.NET.CRecordParam@)">
            <summary>
            开始录像
            </summary>
            <param name="pcRecordParam">录像参数信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.InputOneFrame(MvCamCtrl.NET.CInputFrameInfo@)">
            <summary>
            输入录像数据
            </summary>
            <param name="pcInputFrameInfo">录像数据信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.StopRecord">
            <summary>
            停止录像
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.DrawRect(MvCamCtrl.NET.CRectArea@)">
            <summary>
            在图像上绘制矩形框辅助线
            </summary>
            <param name="pcRectArea">矩形辅助线的信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.DrawCircle(MvCamCtrl.NET.CCircleArea@)">
            <summary>
            在图像上绘制圆形辅助线
            </summary>
            <param name="pcCircleArea">圆形辅助线的信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.DrawLines(MvCamCtrl.NET.CLinesArea@)">
            <summary>
            在图像上绘制线条
            </summary>
            <param name="pcLinesArea">线条辅助线信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.ReconstructImage(MvCamCtrl.NET.CReconstructImageParam@)">
            <summary>
            重构图像(用于分时曝光功能)
            </summary>
            <param name="pcReconstructParam">重构图像参数</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.ImageToBitmap(System.IntPtr,MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX@)">
            <summary>
            图像裸数据转为Bitmap图像数据
            </summary>
            <param name="pData">裸图数据</param>
            <param name="pFrameInfo">图像信息</param>
            <returns>成功, 返回Bitmap对象, 失败， 返回null</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.ImageToBitmap(System.Byte[],MvCamCtrl.NET.CFrameoutEx@)">
            <summary>
            图像裸数据转为Bitmap图像数据(超时机制获取一帧图像)
            </summary>
            <param name="pData">裸图数据缓存</param>
            <param name="pcFrameEx">图像信息</param>
            <returns>成功, 返回Bitmap对象, 失败， 返回null</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IImageProcess.ImageToBitmap(MvCamCtrl.NET.CFrameout@)">
            <summary>
            图像裸数据转为Bitmap图像数据(内部缓存获取一帧图像)
            </summary>
            <param name="pcFrame">图像数据</param>
            <returns></returns>
        </member>
        <member name="T:MvCamCtrl.NET.IGigeDevice">
            <summary>
            GigEVision 设备独有的接口
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.IDevice">
            <summary>
            设备的基本指令和操作
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.CreateHandle(MvCamCtrl.NET.CCameraInfo@)">
            <summary>
            创建设备句柄
            </summary>
            <param name="pcDevInfo">设备信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.CreateHandleWithoutLog(MvCamCtrl.NET.CCameraInfo@)">
            <summary>
            创建设备句柄，不生成日志
            </summary>
            <param name="pcDevInfo">设备信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.DestroyHandle">
            <summary>
            销毁句柄
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.OpenDevice">
            <summary>
            打开设备
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.OpenDevice(System.UInt32,System.UInt16)">
            <summary>
            打开设备
            </summary>
            <param name="nAccessMode">访问权限</param>
            <param name="nSwitchoverKey">切换访问权限时的密钥</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.CloseDevice">
            <summary>
            关闭设备
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.IsDeviceConnected">
            <summary>
            判断设备是否处于连接状态
            </summary>
            <returns>设备处于连接状态，返回true；没连接或失去连接，返回false</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.GetDeviceInfo(MvCamCtrl.NET.CCameraInfo@)">
            <summary>
            获取设备信息，取流之前调用
            </summary>
            <param name="pcDevInfo">返回给调用者有关设备信息</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.GetAllMatchInfo(MvCamCtrl.NET.CAllMatchInfo@)">
            <summary>
            获取各种类型的信息
            </summary>
            <param name="pcMatchInfo">返回给调用者有关设备各种类型的信息</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IDevice.GetValidImageNum(System.UInt32@)">
            <summary>
            获取当前图像缓存区的有效图像个数
            </summary>
            <param name="pnValidImageNum">当前图像缓存区中有效图像个数</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_ForceIpEx(System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            强制IP
            </summary>
            <param name="nIP">设置的IP</param>
            <param name="nSubNetMask">子网掩码</param>
            <param name="nDefaultGateWay">默认网关</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetIpConfig(MvCamCtrl.NET.MV_GIGE_IP_CONFIG_TYPE)">
            <summary>
            配置IP方式
            </summary>
            <param name="enType">IP类型</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetNetTransMode(MvCamCtrl.NET.MV_GIGE_Net_Transfer_Mode)">
            <summary>
            设置仅使用某种模式,type: MV_NET_TRANS_x，不设置时，默认优先使用driver
            </summary>
            <param name="enType">网络传输模式</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_GetNetTransInfo(MvCamCtrl.NET.CNetTransInfo@)">
            <summary>
            获取网络传输信息
            </summary>
            <param name="pcNetTransInfo">网络传输信息</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetGvspTimeout(System.UInt32)">
            <summary>
            设置GVSP取流超时时间
            </summary>
            <param name="nMillisec">超时时间，默认300ms，范围：>10ms</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_GetGvspTimeout(System.UInt32@)">
            <summary>
            获取GVSP取流超时时间
            </summary>
            <param name="pMillisec">超时时间，以毫秒位单位</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetGvcpTimeout(System.UInt32)">
            <summary>
            设置GVCP命令超时时间
            </summary>
            <param name="nMillisec">超时时间，默认500ms，范围：0-10000ms</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_GetGvcpTimeout(System.UInt32@)">
            <summary>
            获取GVCP命令超时时间
            </summary>
            <param name="pMillisec">超时时间，以毫秒位单位</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetRetryGvcpTimes(System.UInt32)">
            <summary>
            设置重传GVCP命令次数
            </summary>
            <param name="nRetryGvcpTimes">重传次数，范围：0-100</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_GetRetryGvcpTimes(System.UInt32@)">
            <summary>
            获取重传GVCP命令次数
            </summary>
            <param name="pnRetryGvcpTimes">重传次数，默认3次</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_GetOptimalPacketSize">
            <summary>
            获取最佳的packet size，该接口目前只支持GigE设备
            </summary>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetResend(System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            设置是否打开重发包支持，及重发包设置
            </summary>
            <param name="bEnable">是否支持重发包</param>
            <param name="nMaxResendPercent">最大重发比</param>
            <param name="nResendTimeout">重发超时时间</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetResendMaxRetryTimes(System.UInt32)">
            <summary>
            设置重传命令最大尝试次数
            </summary>
            <param name="nRetryTimes">重传命令最大尝试次数，默认20</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_GetResendMaxRetryTimes(System.UInt32@)">
            <summary>
            获取重传命令最大尝试次数
            </summary>
            <param name="pnRetryTimes">重传命令最大尝试次数</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetResendTimeInterval(System.UInt32)">
            <summary>
            设置同一重传包多次请求之间的时间间隔
            </summary>
            <param name="nMillisec">同一重传包多次请求之间的时间间隔，默认10ms</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_GetResendTimeInterval(System.UInt32@)">
            <summary>
            获取同一重传包多次请求之间的时间间隔
            </summary>
            <param name="pnMillisec">同一重传包多次请求之间的时间间隔</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_SetTransmissionType(MvCamCtrl.NET.CTransMissionType@)">
            <summary>
            设置传输模式，可以为单播模式、组播模式等
            </summary>
            <param name="pcTransmissionType">传输模式</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_IssueActionCommand(MvCamCtrl.NET.CActionCmdInfo@,MvCamCtrl.NET.CActionCmdResultList@)">
            <summary>
            发出动作命令
            </summary>
            <param name="pcActionCmdInfo">动作命令信息</param>
            <param name="pcActionCmdResults">动作命令返回信息列表</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGigeDevice.GIGE_GetMulticastStatus(MvCamCtrl.NET.CCameraInfo@,System.Boolean@)">
            <summary>
            获取组播状态
            </summary>
            <param name="pcDevInfo">设备信息</param>
            <param name="pbStatus">组播状态,true:组播状态，false:非组播</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="T:MvCamCtrl.NET.IU3VDevice">
            <summary>
            U3V独有的接口
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.IU3VDevice.USB_SetTransferSize(System.UInt32)">
            <summary>
            设置U3V的传输包大小
            </summary>
            <param name="nTransferSize">传输的包大小, Byte，默认为1M，rang：>=0x10000</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IU3VDevice.USB_GetTransferSize(System.UInt32@)">
            <summary>
            获取U3V的传输包大小
            </summary>
            <param name="pnTransferSize">传输的包大小</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IU3VDevice.USB_SetTransferWays(System.UInt32)">
            <summary>
            设置U3V的传输通道个数
            </summary>
            <param name="nTransferWays">传输通道个数，范围：1-10</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IU3VDevice.USB_GetTransferWays(System.UInt32@)">
            <summary>
            获取U3V的传输通道个数
            </summary>
            <param name="pnTransferWays">传输通道个数</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IU3VDevice.USB_RegisterStreamExceptionCallBack(MvCamCtrl.NET.cbStreamExceptiondelegate,System.IntPtr)">
            <summary>
            注册流异常消息回调，在打开设备之后调用（只支持U3V相机）
            </summary>
            <param name="cbException">异常回调函数</param>
            <param name="pUser">用户自定义变量</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IU3VDevice.USB_SetEventNodeNum(System.UInt32)">
            <summary>
            设置U3V的事件缓存节点个数
            </summary>
            <param name="nEventNodeNum">事件缓存节点个数，范围：1-64</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IU3VDevice.USB_SetSyncTimeOut(System.UInt32)">
            <summary>
            设置U3V的同步读写超时时间，范围为0 ~ UINT_MAX(最小值包含0，最大值根据操作系统位数决定)
            </summary>
            <param name="nMills">设置同步读写超时时间,默认时间为1000ms(1s)</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IU3VDevice.USB_GetSyncTimeOut(System.UInt32@)">
            <summary>
            获取U3V相机同步读写超时时间
            </summary>
            <param name="pnMills">获取的超时时间(ms), 默认1000ms</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="T:MvCamCtrl.NET.ICamlDevice">
            <summary>
            CameraLink独有的接口
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.ICamlDevice.CAML_SetDeviceBaudrate(MvCamCtrl.NET.MV_CAML_BAUDRATE)">
            <summary>
            设置设备波特率
            </summary>
            <param name="enBaudrate">设置的波特率值，如MV_CAML_BAUDRATE_9600</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.ICamlDevice.CAML_GetDeviceBaudrate(MvCamCtrl.NET.MV_CAML_BAUDRATE@)">
            <summary>
            获取设备波特率
            </summary>
            <param name="penCurrentBaudrate">波特率信息，如MV_CAML_BAUDRATE_9600</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.ICamlDevice.CAML_GetSupportBaudrates(MvCamCtrl.NET.MV_CAML_BAUDRATE@)">
            <summary>
            获取设备与主机间连接支持的波特率
            </summary>
            <param name="penBaudrateAblity">支持的波特率信息，所支持波特率的或运算结果，如MV_CAML_BAUDRATE_9600</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.ICamlDevice.CAML_SetGenCPTimeOut(System.UInt32)">
            <summary>
            设置串口操作等待时长
            </summary>
            <param name="nMillisec">串口操作的等待时长, 单位为ms</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="T:MvCamCtrl.NET.IGenTLDevice">
            <summary>
            GenTL相关接口
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.IGenTLDevice.UnloadGenTLLibrary(System.String)">
            <summary>
            卸载cti库
            </summary>
            <param name="strGenTLPath">枚举卡时加载的cti文件路径</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.IGenTLDevice.CreateHandleByGenTL(MvCamCtrl.NET.CGenTLDevInfo@)">
            <summary>
            通过GenTL设备信息创建设备句柄
            </summary>
            <param name="pcDevInfo">设备信息</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CopyMemory(System.IntPtr,System.IntPtr,System.UInt32)">
            <summary>
            内存拷贝
            </summary>
            <param name="dest">目标缓存</param>
            <param name="src">源缓存</param>
            <param name="count">拷贝大小</param>
        </member>
        <member name="F:MvCamCtrl.NET.CCamera.handle">
            <summary>
            设备句柄
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.#ctor">
            <summary>
            Constructor
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.Finalize">
            <summary>
            Destructor
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetCameraHandle">
            <summary>
            Get Camera Handle
            </summary>
            <returns></returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CreateHandle(MvCamCtrl.NET.CCameraInfo@)">
            <summary>
            Create Handle
            </summary>
            <param name="pcDevInfo">Device Info Object</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CreateHandleWithoutLog(MvCamCtrl.NET.CCameraInfo@)">
            <summary>
            Create Device without log
            </summary>
            <param name="pcDevInfo">Device Information Object</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.DestroyHandle">
            <summary>
            Destroy Handle
            </summary>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.OpenDevice">
            <summary>
            Open Device
            </summary>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.OpenDevice(System.UInt32,System.UInt16)">
            <summary>
            Open Device
            </summary>
            <param name="nAccessMode">Access Right</param>
            <param name="nSwitchoverKey">Switch key of access right</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CloseDevice">
            <summary>
            Close Device
            </summary>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.IsDeviceConnected">
            <summary>
            Is the Device Connected
            </summary>
            <returns>Device is Connected, return true. Device is Disconnected, return false. </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.RegisterImageCallBackEx(MvCamCtrl.NET.cbOutputExdelegate,System.IntPtr)">
            <summary>
            Register the image callback function
            </summary>
            <param name="cbOutput">Callback function pointer</param>
            <param name="pUser">User defined variable</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.RegisterImageCallBackForRGB(MvCamCtrl.NET.cbOutputExdelegate,System.IntPtr)">
            <summary>
            Register the RGB image callback function
            </summary>
            <param name="cbOutput">Callback function pointer</param>
            <param name="pUser">User defined variable</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.RegisterImageCallBackForBGR(MvCamCtrl.NET.cbOutputExdelegate,System.IntPtr)">
            <summary>
            Register the BGR image callback function
            </summary>
            <param name="cbOutput">Callback function pointer</param>
            <param name="pUser">User defined variable</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetDeviceInfo(MvCamCtrl.NET.CCameraInfo@)">
            <summary>
            Get device information(Called before start grabbing)
            </summary>
            <param name="pcDevInfo">device information</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetAllMatchInfo(MvCamCtrl.NET.CAllMatchInfo@)">
            <summary>
            Get various type of information
            </summary>
            <param name="pcMatchInfo">Various type of information</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetValidImageNum(System.UInt32@)">
            <summary>
            Get the number of valid images in the current image buffer
            </summary>
            <param name="pnValidImageNum">The number of valid images in the current image buffer</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetIntValue(System.String,MvCamCtrl.NET.CIntValue@)">
            <summary>
            Get Integer value
            </summary>
            <param name="strKey">Key value, for example, using "Width" to get width</param>
            <param name="oValue">Value of device features</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetIntValue(System.String,System.Int64)">
            <summary>
            Set Integer value
            </summary>
            <param name="strKey">Key value, for example, using "Width" to set width</param>
            <param name="nValue">Feature value to set</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetEnumEntrySymbolic(System.String,MvCamCtrl.NET.CEnumEntry@)">
            <summary>
            Get the symbolic of the specified value of the Enum type node
            </summary>
            <param name="strKey">Key value, for example, using "PixelFormat" to set pixel format</param>
            <param name="pcEnumEntry">Symbolic to get</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetEnumValue(System.String,MvCamCtrl.NET.CEnumValue@)">
            <summary>
            Get Enum value
            </summary>
            <param name="strKey">Key value, for example, using "PixelFormat" to get pixel format</param>
            <param name="oValue">Value of device features</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetEnumValue(System.String,System.UInt32)">
            <summary>
            Set Enum value
            </summary>
            <param name="strKey">Key value, for example, using "PixelFormat" to set pixel format</param>
            <param name="nValue">Feature value to set</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetEnumValueByString(System.String,System.String)">
            <summary>
            Set Enum value
            </summary>
            <param name="strKey">Key value, for example, using "PixelFormat" to set pixel format</param>
            <param name="sValue">Feature String to set</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetFloatValue(System.String,MvCamCtrl.NET.CFloatValue@)">
            <summary>
            Get Float value
            </summary>
            <param name="strKey">Key value</param>
            <param name="oValue">Value of device features</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetFloatValue(System.String,System.Single)">
            <summary>
            Set float value
            </summary>
            <param name="strKey">Key value</param>
            <param name="fValue">Feature value to set</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetBoolValue(System.String,System.Boolean@)">
            <summary>
            Get Boolean value
            </summary>
            <param name="strKey">Key value</param>
            <param name="pbValue">Value of device features</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetBoolValue(System.String,System.Boolean)">
            <summary>
            Set Boolean value
            </summary>
            <param name="strKey">Key value</param>
            <param name="bValue">Feature value to set</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetStringValue(System.String,MvCamCtrl.NET.CStringValue@)">
            <summary>
            Get String value
            </summary>
            <param name="strKey">Key value</param>
            <param name="oValue">Value of device features</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetStringValue(System.String,System.String)">
            <summary>
            Set String value
            </summary>
            <param name="strKey">Key value</param>
            <param name="strValue">Feature value to set</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetCommandValue(System.String)">
            <summary>
            Send Command
            </summary>
            <param name="strKey">Key value</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.InvalidateNodes">
            <summary>
            Invalidate GenICam Nodes
            </summary>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.XML_GetGenICamXML(System.IntPtr,System.UInt32,System.UInt32@)">
            <summary>
            Get camera feature tree XML
            </summary>
            <param name="pData">XML data receiving buffer</param>
            <param name="nDataSize">Buffer size</param>
            <param name="pnDataLen">Actual data length</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.XML_GetNodeAccessMode(System.String,MvCamCtrl.NET.MV_XML_AccessMode@)">
            <summary>
            Get Access mode of cur node
            </summary>
            <param name="pstrName">Name of node</param>
            <param name="pAccessMode">Access mode of the node</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.XML_GetNodeInterfaceType(System.String,MvCamCtrl.NET.MV_XML_InterfaceType@)">
            <summary>
            Get Interface Type of cur node
            </summary>
            <param name="pstrName">Name of node</param>
            <param name="pInterfaceType">Interface Type of the node</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.StartGrabbing">
            <summary>
            Start Grabbing
            </summary>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.StopGrabbing">
            <summary>
            Stop Grabbing
            </summary>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetImageForRGB(System.Byte[],System.UInt32,MvCamCtrl.NET.CFrameoutEx@,System.Int32)">
            <summary>
            Get one frame of RGB image, this function is using query to get data
            query whether the internal cache has data, get data if there has, return error code if no data
            </summary>
            <param name="pData">Image data receiving buffer</param>
            <param name="nDataSize">Buffer size</param>
            <param name="pcFrameEx">Frame information</param>
            <param name="nMsec">Waiting timeout</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetImageForBGR(System.Byte[],System.UInt32,MvCamCtrl.NET.CFrameoutEx@,System.Int32)">
            <summary>
            Get one frame of BGR image, this function is using query to get data
            query whether the internal cache has data, get data if there has, return error code if no data
            </summary>
            <param name="pData">Image data receiving buffer</param>
            <param name="nDataSize">Buffer size</param>
            <param name="pcFrameEx">Image information</param>
            <param name="nMsec">Waiting timeout</param>
            <returns>Success, return MV_OK. Failure, return error cod</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetImageBuffer(MvCamCtrl.NET.CFrameout@,System.Int32)">
            <summary>
            Get a frame of an image using an internal cache
            </summary>
            <param name="pcFrame">Image data and image information</param>
            <param name="nMsec">Waiting timeout</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.FreeImageBuffer(MvCamCtrl.NET.CFrameout@)">
            <summary>
            Free image buffer（used with MV_CC_GetImageBuffer）
            </summary>
            <param name="pcFrame">Image data and image information</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetOneFrameTimeout(System.Byte[],System.UInt32,MvCamCtrl.NET.CFrameoutEx@,System.Int32)">
            <summary>
            Get a frame of an image
            </summary>
            <param name="pData">Image data receiving buffer</param>
            <param name="nDataSize">Buffer size</param>
            <param name="pcFrameEx">Image information</param>
            <param name="nMsec">Waiting timeout</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ClearImageBuffer">
            <summary>
            Clear image Buffers to clear old data
            </summary>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.DisplayOneFrame(MvCamCtrl.NET.CDisplayFrameInfo@)">
            <summary>
            Display one frame image
            </summary>
            <param name="pcDisplayInfo">Image information</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.DisplayOneFrameEx(MvCamCtrl.NET.CDisplayFrameInfoEx@)">
            <summary>
            Display one frame image (Extend Width info and Height info)
            </summary>
            <param name="pcDisplayInfoEx">Image information</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetImageNodeNum(System.UInt32)">
            <summary>
            Set the number of the internal image cache nodes in SDK(Greater than or equal to 1, to be called before the capture)
            </summary>
            <param name="nNum">Number of cache nodes</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetGrabStrategy(MvCamCtrl.NET.MV_GRAB_STRATEGY)">
            <summary>
            Set Grab Strategy
            </summary>
            <param name="enGrabStrategy">The value of grab strategy</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetOutputQueueSize(System.UInt32)">
            <summary>
            Set The Size of Output Queue(Only work under the strategy of MV_GrabStrategy_LatestImages，rang：1-ImageNodeNum)
            </summary>
            <param name="nOutputQueueSize">The Size of Output Queue</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_ForceIpEx(System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            Force IP
            </summary>
            <param name="nIP">IP to set</param>
            <param name="nSubNetMask">Subnet mask</param>
            <param name="nDefaultGateWay">Default gateway</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetIpConfig(MvCamCtrl.NET.MV_GIGE_IP_CONFIG_TYPE)">
            <summary>
            IP configuration method
            </summary>
            <param name="enType">IP type</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetNetTransMode(MvCamCtrl.NET.MV_GIGE_Net_Transfer_Mode)">
            <summary>
            Set to use only one mode,type: MV_NET_TRANS_x. When do not set, priority is to use driver by default
            </summary>
            <param name="enType">Net transmission mode, refer to MV_NET_TRANS_x</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_GetNetTransInfo(MvCamCtrl.NET.CNetTransInfo@)">
            <summary>
            Get net transmission information
            </summary>
            <param name="pcNetTransInfo">Transmission information</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetGvspTimeout(System.UInt32)">
            <summary>
            Set GVSP streaming timeout
            </summary>
            <param name="nMillisec">Timeout, default 300ms, range: >10ms</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_GetGvspTimeout(System.UInt32@)">
            <summary>
            Get GVSP streaming timeout
            </summary>
            <param name="pMillisec">Timeout, ms as unit</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetGvcpTimeout(System.UInt32)">
            <summary>
            Set GVCP cammand timeout
            </summary>
            <param name="nMillisec">Timeout, ms as unit, range: 0-10000</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_GetGvcpTimeout(System.UInt32@)">
            <summary>
            Get GVCP cammand timeout
            </summary>
            <param name="pMillisec">Timeout, ms as unit</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetRetryGvcpTimes(System.UInt32)">
            <summary>
            Set the number of retry GVCP cammand
            </summary>
            <param name="nRetryGvcpTimes">The number of retries，rang：0-100</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_GetRetryGvcpTimes(System.UInt32@)">
            <summary>
            Get the number of retry GVCP cammand
            </summary>
            <param name="pnRetryGvcpTimes">The number of retries</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_GetOptimalPacketSize">
            <summary>
            Get the optimal Packet Size, Only support GigE Camera
            </summary>
            <returns>Optimal packet size</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetResend(System.UInt32,System.UInt32,System.UInt32)">
            <summary>
            Set whethe to enable resend, and set resend
            </summary>
            <param name="bEnable">Enable resend</param>
            <param name="nMaxResendPercent">Max resend persent</param>
            <param name="nResendTimeout">Resend timeout</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetResendMaxRetryTimes(System.UInt32)">
            <summary>
            Set the max resend retry times
            </summary>
            <param name="nRetryTimes">The max times to retry resending lost packets，default 20</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_GetResendMaxRetryTimes(System.UInt32@)">
            <summary>
            Get the max resend retry times
            </summary>
            <param name="pnRetryTimes">the max times to retry resending lost packets</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetResendTimeInterval(System.UInt32)">
            <summary>
            Set time interval between same resend requests
            </summary>
            <param name="nMillisec">The time interval between same resend requests,default 10ms</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_GetResendTimeInterval(System.UInt32@)">
            <summary>
            Get time interval between same resend requests
            </summary>
            <param name="pnMillisec">The time interval between same resend requests</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_SetTransmissionType(MvCamCtrl.NET.CTransMissionType@)">
            <summary>
            Set transmission type,Unicast or Multicast
            </summary>
            <param name="pcTransmissionType">Struct of transmission type</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_IssueActionCommand(MvCamCtrl.NET.CActionCmdInfo@,MvCamCtrl.NET.CActionCmdResultList@)">
            <summary>
            Issue Action Command
            </summary>
            <param name="pcActionCmdInfo">Action Command info</param>
            <param name="pcActionCmdResults">Action Command Result List</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GIGE_GetMulticastStatus(MvCamCtrl.NET.CCameraInfo@,System.Boolean@)">
            <summary>
            Get Multicast Status
            </summary>
            <param name="pcDevInfo">Device Information</param>
            <param name="pbStatus">Status of Multicast</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.USB_SetTransferSize(System.UInt32)">
            <summary>
            Set transfer size of U3V device
            </summary>
            <param name="nTransferSize">Transfer size，Byte，default：1M，rang：>=0x10000</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.USB_GetTransferSize(System.UInt32@)">
            <summary>
            Get transfer size of U3V device
            </summary>
            <param name="pnTransferSize">Transfer size，Byte</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.USB_SetTransferWays(System.UInt32)">
            <summary>
            Set transfer ways of U3V device
            </summary>
            <param name="nTransferWays">Transfer ways，rang：1-10</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.USB_GetTransferWays(System.UInt32@)">
            <summary>
            Get transfer ways of U3V device
            </summary>
            <param name="pnTransferWays">Transfer ways</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.USB_RegisterStreamExceptionCallBack(MvCamCtrl.NET.cbStreamExceptiondelegate,System.IntPtr)">
            <summary>
            注册流异常消息回调，在打开设备之后调用（只支持U3V相机）
            </summary>
            <param name="cbException">Exception CallBack Function</param>
            <param name="pUser">User defined variable</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.USB_SetEventNodeNum(System.UInt32)">
            <summary>
            Set the number of U3V device event cache nodes
            </summary>
            <param name="nEventNodeNum">Event Node Number</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.USB_SetSyncTimeOut(System.UInt32)">
            <summary>
            Set U3V Synchronisation timeout,range is 0 ~ UINT_MAX(minimum value contains 0,maximum value according to the operating system)
            </summary>
            <param name="nMills">time out(ms),default 1000ms</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.USB_GetSyncTimeOut(System.UInt32@)">
            <summary>
            Get U3V Camera Synchronisation timeout
            </summary>
            <param name="pnMills">Get Synchronisation time(ms)</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CAML_SetDeviceBaudrate(MvCamCtrl.NET.MV_CAML_BAUDRATE)">
            <summary>
            设置设备波特率
            </summary>
            <param name="enBaudrate">设置的波特率值，如MV_CAML_BAUDRATE_9600</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CAML_GetDeviceBaudrate(MvCamCtrl.NET.MV_CAML_BAUDRATE@)">
            <summary>
            获取设备波特率
            </summary>
            <param name="penCurrentBaudrate">波特率信息，如MV_CAML_BAUDRATE_9600</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CAML_GetSupportBaudrates(MvCamCtrl.NET.MV_CAML_BAUDRATE@)">
            <summary>
            获取设备与主机间连接支持的波特率
            </summary>
            <param name="penBaudrateAblity">支持的波特率信息，所支持波特率的或运算结果，如MV_CAML_BAUDRATE_9600</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CAML_SetGenCPTimeOut(System.UInt32)">
            <summary>
            Sets the timeout for operations on the serial port
            </summary>
            <param name="nMillisec">Timeout in [ms] for operations on the serial port.</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.UnloadGenTLLibrary(System.String)">
            <summary>
            卸载cti库
            </summary>
            <param name="strGenTLPath">枚举卡时加载的cti文件路径</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.CreateHandleByGenTL(MvCamCtrl.NET.CGenTLDevInfo@)">
            <summary>
            Create Device Handle Based On GenTL Device Info
            </summary>
            <param name="pcDevInfo">Device Information Structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.LocalUpgrade(System.String)">
            <summary>
            Device Local Upgrade
            </summary>
            <param name="pFilePathName">File path and name</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetUpgradeProcess(System.UInt32@)">
            <summary>
            Get Upgrade Progress
            </summary>
            <param name="pnProcess">Value of Progress</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ReadMemory(System.IntPtr,System.Int64,System.Int64)">
            <summary>
            Read Memory
            </summary>
            <param name="pBuffer">Used as a return value, save the read-in memory value(Memory value is stored in accordance with the big end model)</param>
            <param name="nAddress">Memory address to be read, which can be obtained from the Camera.xml file of the device, the form xml node value of xxx_RegAddr</param>
            <param name="nLength">Length of the memory to be read</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.WriteMemory(System.IntPtr,System.Int64,System.Int64)">
            <summary>
            Write Memory
            </summary>
            <param name="pBuffer">Memory value to be written ( Note the memory value to be stored in accordance with the big end model)</param>
            <param name="nAddress">Memory address to be written, which can be obtained from the Camera.xml file of the device, the form xml node value of xxx_RegAddr</param>
            <param name="nLength">Length of the memory to be written</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.RegisterExceptionCallBack(MvCamCtrl.NET.cbExceptiondelegate,System.IntPtr)">
            <summary>
            Register Exception Message CallBack, call after open device
            </summary>
            <param name="cbException">Exception Message CallBack Function</param>
            <param name="pUser">User defined variable</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.RegisterAllEventCallBack(MvCamCtrl.NET.cbEventdelegateEx,System.IntPtr)">
            <summary>
            Register event callback, which is called after the device is opened
            </summary>
            <param name="cbEvent">Event CallBack Function</param>
            <param name="pUser">User defined variable</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.RegisterEventCallBackEx(System.String,MvCamCtrl.NET.cbEventdelegateEx,System.IntPtr)">
            <summary>
            Register single event callback, which is called after the device is opened
            </summary>
            <param name="pEventName">Event name</param>
            <param name="cbEvent">Event CallBack Function</param>
            <param name="pUser">User defined variable</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.OpenParamsGUI">
            <summary>
            Open the GUI interface for getting or setting camera parameters
            </summary>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SaveImageEx(MvCamCtrl.NET.CSaveImageParam@)">
            <summary>
            Save image, support Bmp and Jpeg. Encoding quality(50-99]
            </summary>
            <param name="pcSaveParam">Save the image parameters object</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SaveImageToFile(MvCamCtrl.NET.CSaveImgToFileParam@)">
            <summary>
            Save the image file, support Bmp、 Jpeg、Png and Tiff. Encoding quality(50-99]
            </summary>
            <param name="pcSaveFileParam">Save the image parameters object</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SavePointCloudData(MvCamCtrl.NET.CSavePointCloudParam@)">
            <summary>
            Save 3D point data, support PLY、CSV and OBJ
            </summary>
            <param name="pcPointDataParam">Save 3D point data parameters structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.RotateImage(MvCamCtrl.NET.CRotateImageParam@)">
            <summary>
            Rotate Image
            </summary>
            <param name="pcRotateParam">Rotate image parameter structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.FlipImage(MvCamCtrl.NET.CFlipImageParam@)">
            <summary>
            Flip Image
            </summary>
            <param name="pcFlipParam">Flip image parameter structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ConvertPixelType(MvCamCtrl.NET.CPixelConvertParam@)">
            <summary>
            Pixel format conversion
            </summary>
            <param name="pcCvtParam">Convert Pixel Type parameter structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ConvertPixelType(System.Byte[],MvCamCtrl.NET.CFrameoutEx@,System.Byte[],System.UInt32,MvCamCtrl.NET.MvGvspPixelType)">
            <summary>
            Pixel format conversion(Use to GetOneFrameTimeout)
            </summary>
            <param name="byteSrcData">Source Image Data</param>
            <param name="pcFrameEx">Frame Data Info</param>
            <param name="byteDstData">Destination Image Data Buffer</param>
            <param name="nDataSize">Image Data Size</param>
            <param name="enDstPixelType">Destination Image Pixel Type</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetBayerCvtQuality(System.UInt32)">
            <summary>
            Interpolation algorithm type setting
            </summary>
            <param name="nBayerCvtQuality">Bayer interpolation method  0-Fast 1-Equilibrium 2-Optimal</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetBayerGammaValue(System.Single)">
            <summary>
            Set Bayer Gamma value
            </summary>
            <param name="fBayerGammaValue">Gamma value[0.1,4.0]</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetGammaValue(MvCamCtrl.NET.MvGvspPixelType,System.Single)">
            <summary>
            Set Gamma value
            </summary>
            <param name="enSrcPixelType">PixelType,support PixelType_Gvsp_Mono8,Bayer8/10/12/16</param>
            <param name="fGammaValue">Gamma value[0.1,4.0]</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetBayerGammaParam(MvCamCtrl.NET.CGammaParam@)">
            <summary>
            Set Gamma param
            </summary>
            <param name="pcGammaParam">Gamma parameter structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetBayerCCMParam(MvCamCtrl.NET.CCCMParam@)">
            <summary>
            Set CCM param
            </summary>
            <param name="pcCCMParam">CCM parameter structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetBayerCCMParamEx(MvCamCtrl.NET.CCCMParamEx@)">
            <summary>
            Set CCM param
            </summary>
            <param name="pcCCMParam">CCM parameter structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.HB_Decode(MvCamCtrl.NET.CDecodeParam@)">
            <summary>
            High Bandwidth Decode
            </summary>
            <param name="pcDecodeParam">High Bandwidth Decode parameter structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.FeatureSave(System.String)">
            <summary>
            Save camera feature
            </summary>
            <param name="strFileName">File name</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.FeatureLoad(System.String)">
            <summary>
            Load camera feature
            </summary>
            <param name="strFileName">File name</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.FileAccessRead(MvCamCtrl.NET.CFileAccess@)">
            <summary>
            Read the file from the camera
            </summary>
            <param name="pcFileAccess">File access structure</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.FileAccessReadEx(MvCamCtrl.NET.CFileAccessEx@)">
            <summary>
            Read the file from the camera
            </summary>
            <param name="pcFileAccessEx">File access structure</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.FileAccessWrite(MvCamCtrl.NET.CFileAccess@)">
            <summary>
            Write the file to camera
            </summary>
            <param name="pcFileAccess">File access structure</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.FileAccessWriteEx(MvCamCtrl.NET.CFileAccessEx@)">
            <summary>
            Write the file to camera
            </summary>
            <param name="pcFileAccessEx">File access structure</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.GetFileAccessProgress(MvCamCtrl.NET.CFileAccessProgress@)">
            <summary>
            Get File Access Progress 
            </summary>
            <param name="pcFileAccessProgress">File access Progress</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.StartRecord(MvCamCtrl.NET.CRecordParam@)">
            <summary>
            Start Record
            </summary>
            <param name="pcRecordParam">Record param structure</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.InputOneFrame(MvCamCtrl.NET.CInputFrameInfo@)">
            <summary>
            Input RAW data to Record
            </summary>
            <param name="pcInputFrameInfo">Record data structure</param>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.StopRecord">
            <summary>
            Stop Record
            </summary>
            <returns>Success, return MV_OK. Failure, return error code </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.DrawRect(MvCamCtrl.NET.CRectArea@)">
            <summary>
            Draw Rect Auxiliary Line
            </summary>
            <param name="pcRectArea">Rect Auxiliary Line Info</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.DrawCircle(MvCamCtrl.NET.CCircleArea@)">
            <summary>
            Draw Circle Auxiliary Line
            </summary>
            <param name="pcCircleArea">Circle Auxiliary Line Info</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.DrawLines(MvCamCtrl.NET.CLinesArea@)">
            <summary>
            Draw Line Auxiliary Line
            </summary>
            <param name="pcLinesArea">Linear Auxiliary Line Info</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ReconstructImage(MvCamCtrl.NET.CReconstructImageParam@)">
            <summary>
            Restructure Image(For time-division exposure function)
            </summary>
            <param name="pcReconstructParam">Restructure image parameters</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetBayerFilterEnable(System.Boolean)">
            <summary>
            Filter type of the bell interpolation quality algorithm setting
            </summary>
            <param name="bFilterEnable">Filter type enable</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ImageContrast(MvCamCtrl.NET.CContrastParam@)">
            <summary>
            Adjust image contrast
            </summary>
            <param name="pcContrastParam">Contrast parameter structure</param>
            <returns>Success, return MV_OK. Failure, return error code</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ImageToBitmap(System.IntPtr,MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX@)">
            <summary>
            callback image data converted to bitmap
            </summary>
            <param name="pData">callback image data</param>
            <param name="pFrameInfo">image info</param>
            <returns>Success, return bitmap. Failure, return null</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ImageToBitmap(System.Byte[],MvCamCtrl.NET.CFrameoutEx@)">
            <summary>
            The timeout mechanism obtains a frame of image data to Bitmap
            </summary>
            <param name="pData">image data buffer</param>
            <param name="pcFrameEx">image info</param>
            <returns>Success, return bitmap. Failure, return null</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ImageToBitmap(MvCamCtrl.NET.CFrameout@)">
            <summary>
            The internal cache obtains a frame of image data to convert to Bitmap
            </summary>
            <param name="pcFrame">image data</param>
            <returns>Success, return bitmap. Failure, return null</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.ConvertToBitmap(System.IntPtr,System.UInt32,System.UInt32,System.UInt32,MvCamCtrl.NET.MvGvspPixelType)">
            <summary>
            raw frame data converted to bitmap
            </summary>
            <param name="pData">raw frame data</param>
            <param name="nFrameLen">image data len</param>
            <param name="nImageWidth">image width</param>
            <param name="nImageHeight">image height</param>
            <param name="enPixelType">image pixel</param>
            <returns>Success, return bitmap. Failure, return null</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CCamera.SetSDKLogPath(System.String)">
            <summary>
            Set SDK log path (Interfaces not recommended)
            If the logging service MvLogServer is enabled, the interface is invalid and The logging service is enabled by default
            </summary>
            <param name="strSDKLogPath"></param>
            <returns></returns>
        </member>
        <member name="T:MvCamCtrl.NET.CCameraParams">
            <summary>
            相机参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.INFO_MAX_BUFFER_SIZE">
            <summary>
            ch:信息结构体的最大缓存 | en: Max buffer size of information structs
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_DEVICE_NUM">
            <summary>
            最大的相机数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_GENTL_IF_NUM">
            <summary>
            ch:最大Interface数量 | en:Max num of interfaces
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_GENTL_DEV_NUM">
            <summary>
            ch:最大GenTL设备数量 | en:Max num of GenTL devices
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_XML_DISC_STRLEN_C">
            <summary>
            XML节点描述最大长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_XML_NODE_STRLEN_C">
            <summary>
            XML节点最大长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_XML_NODE_NUM_C">
            <summary>
            XML节点最大数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_XML_SYMBOLIC_NUM">
            <summary>
            XML节点显示名最大数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_XML_STRVALUE_STRLEN_C">
            <summary>
            string类型节点值的最大长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_XML_PARENTS_NUM">
            <summary>
            最大父节点数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_XML_SYMBOLIC_STRLEN_C">
            <summary>
            最大节点描述长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_EXCEPTION_DEV_DISCONNECT">
            <summary>
            设备断开连接
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_EXCEPTION_VERSION_CHECK">
            <summary>
            SDK与驱动版本不匹配
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MAX_EVENT_NAME_SIZE">
            <summary>
            相机Event事件名称最大长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_SYMBOLIC_LEN">
            <summary>最大枚举条目对应的符号长度</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraParams.MV_MAX_SPLIT_NUM">
            <summary>分时曝光时最多将源图像拆分的个数</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO">
            <summary>
            GigE设备信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.nIpCfgOption">
            <summary>
            IP配置选项
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.nIpCfgCurrent">
            <summary>
            IP configuration:bit31-static bit30-dhcp bit29-lla
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.nCurrentIp">
            <summary>
            curtent ip
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.nCurrentSubNetMask">
            <summary>
            curtent subnet mask
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.nDefultGateWay">
            <summary>
            current gateway
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.chManufacturerName">
            <summary>
            制造商名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.chModelName">
            <summary>
            模型名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.chDeviceVersion">
            <summary>
            设备版本信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.chManufacturerSpecificInfo">
            <summary>
            制造商特殊信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.chSerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.chUserDefinedName">
            <summary>
            用户自定义名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.nNetExport">
            <summary>
            网口IP地址
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GIGE_DEVICE_INFO.nReserved">
            <summary>
            预留
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO">
            <summary>
            USB设备信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.CrtlInEndPoint">
            <summary>
            控制输入端点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.CrtlOutEndPoint">
            <summary>
            控制输出端点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.StreamEndPoint">
            <summary>
            流端点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.EventEndPoint">
            <summary>
            事件端点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.idVendor">
            <summary>
            供应商ID号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.idProduct">
            <summary>
            产品ID号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.nDeviceNumber">
            <summary>
            设备序列号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.chDeviceGUID">
            <summary>
            设备GUID号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.chVendorName">
            <summary>
            供应商名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.chModelName">
            <summary>
            型号名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.chFamilyName">
            <summary>
            家族名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.chDeviceVersion">
            <summary>
            设备版本号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.chManufacturerName">
            <summary>
            制造商名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.chSerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.chUserDefinedName">
            <summary>
            用户自定义名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.nbcdUSB">
            <summary>
            支持的USB协议
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.nDeviceAddress">
            <summary>设备地址</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_USB3_DEVICE_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CamL_DEV_INFO">
            <summary>
            ch:CamLink设备信息 | en:CamLink device information
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CamL_DEV_INFO.chPortID">
            <summary>
            端口号ID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CamL_DEV_INFO.chModelName">
            <summary>
            模型名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CamL_DEV_INFO.chFamilyName">
            <summary>
            家族名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CamL_DEV_INFO.chDeviceVersion">
            <summary>
            设备版本信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CamL_DEV_INFO.chManufacturerName">
            <summary>
            制造商名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CamL_DEV_INFO.chSerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CamL_DEV_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO">
            <summary>
            ch:设备信息 | en:Device information
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.nMajorVer">
            <summary>
            主版本号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.nMinorVer">
            <summary>
            次版本号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.nMacAddrHigh">
            <summary>
            MAC高地址
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.nMacAddrLow">
            <summary>
            MAC低地址
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.nTLayerType">
            <summary>
            设备传输层协议类型，e.g. MV_GIGE_DEVICE
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.SpecialInfo">
            <summary>
            设备类型
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.#ctor(System.UInt32)">
            <summary>
            构造函数
            </summary>
            <param name="nAnyNum">输入任意数，因为不接受无参构造函数</param>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.SPECIAL_INFO">
            <summary>
            ch:特定类型的设备信息 | en:Special devcie information
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.SPECIAL_INFO.stGigEInfo">
            <summary>
            GigE
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.SPECIAL_INFO.stCamLInfo">
            <summary>
            Camera Link
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO.SPECIAL_INFO.stUsb3VInfo">
            <summary>
            Usb
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO_LIST">
            <summary>
            相机列表
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO_LIST.nDeviceNum">
            <summary>
            在线设备数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_DEVICE_INFO_LIST.pDeviceInfo">
            <summary>
            支持最多256个设备
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO">
            <summary>
            ch:通过GenTL枚举到的Interface信息 | en:Interface Information with GenTL
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO.chInterfaceID">
            <summary>
            GenTL接口ID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO.chTLType">
            <summary>
            传输层类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO.chDisplayName">
            <summary>
            设备显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO.nCtiIndex">
            <summary>
            GenTL的cti文件索引
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO_LIST">
            <summary>
            ch:通过GenTL枚举到的设备信息列表 | en:Interface Information List with GenTL
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO_LIST.nInterfaceNum">
            <summary>
            ch:在线设备数量 | en:Online Interface Number
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_IF_INFO_LIST.pIFInfo">
            <summary>
            ch:支持最多256个设备 | en:Support up to 256 Interfaces
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO">
            <summary>
            ch:通过GenTL枚举到的设备信息 | en:Device Information discovered by with GenTL
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chInterfaceID">
            <summary>
            采集卡ID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chDeviceID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chVendorName">
            <summary>
            供应商名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chModelName">
            <summary>
            模型名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chTLType">
            <summary>
            传输类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chDisplayName">
            <summary>
            显示名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chUserDefinedName">
            <summary>
            用户自定义名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chSerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.chDeviceVersion">
            <summary>
            设备版本信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.nCtiIndex">
            <summary>
            cti文件序号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO_LIST">
            <summary>
            ch:GenTL设备列表 | en:GenTL devices list
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO_LIST.nDeviceNum">
            <summary>
            在线设备数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_GENTL_DEV_INFO_LIST.pDeviceInfo">
            <summary>
            支持最多256个设备
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_NETTRANS_INFO">
            <summary>
            Net Trans Info
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_NETTRANS_INFO.nReviceDataSize">
            <summary>
            已接收数据大小  [统计StartGrabbing和StopGrabbing之间的数据量]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_NETTRANS_INFO.nThrowFrameCount">
            <summary>
            丢帧数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_NETTRANS_INFO.nNetRecvFrameCount">
            <summary>
            接收帧数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_NETTRANS_INFO.nRequestResendPacketCount">
            <summary>
            请求重发包数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_NETTRANS_INFO.nResendPacketCount">
            <summary>
            重发包数
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO">
            <summary>
            Frame Out Info
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nWidth">
            <summary>
            图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nHeight">
            <summary>
            图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.enPixelType">
            <summary>
            像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nFrameNum">
            <summary>
            帧号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nDevTimeStampHigh">
            <summary>
            时间戳高32位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nDevTimeStampLow">
            <summary>
            时间戳低32位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nReserved0">
            <summary>
            保留，8字节对齐
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nHostTimeStamp">
            <summary>
            主机生成的时间戳
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nFrameLen">
            <summary>
            帧数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nLostPacket">
            <summary>
            丢包数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CHUNK_DATA_CONTENT">
            <summary>
            Chunk数据信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CHUNK_DATA_CONTENT.pChunkData">
            <summary>
            Chunk数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CHUNK_DATA_CONTENT.nChunkID">
            <summary>
            ChunkID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CHUNK_DATA_CONTENT.nChunkLen">
            <summary>
            Chunk大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CHUNK_DATA_CONTENT.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX">
            <summary>
            Frame Out Info Ex
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nWidth">
            <summary>
            图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nHeight">
            <summary>
            图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.enPixelType">
            <summary>
            像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nFrameNum">
            <summary>
            帧号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nDevTimeStampHigh">
            <summary>
            时间戳高32位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nDevTimeStampLow">
            <summary>
            时间戳低32位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nReserved0">
            <summary>
            保留，8字节对齐
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nHostTimeStamp">
            <summary>
            主机生成的时间戳
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nFrameLen">
            <summary>
            Frame大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nSecondCount">
            <summary>
            秒数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nCycleCount">
            <summary>
            周期数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nCycleOffset">
            <summary>
            周期偏移量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.fGain">
            <summary>
            增益
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.fExposureTime">
            <summary>
            曝光时间
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nAverageBrightness">
            <summary>
            平均亮度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nRed">
            <summary>
            Red
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nGreen">
            <summary>
            Green
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nBlue">
            <summary>
            Blue
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nFrameCounter">
            <summary>
            帧计数器
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nTriggerIndex">
            <summary>
            触发计数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nInput">
            <summary>
            输入
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nOutput">
            <summary>
            输出
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nOffsetX">
            <summary>
            水平偏移量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nOffsetY">
            <summary>
            垂直偏移量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nChunkWidth">
            <summary>
            Chunk宽度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nChunkHeight">
            <summary>
            Chunk高度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nLostPacket">
            <summary>
            丢包数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nUnparsedChunkNum">
            <summary>
            为解析的Chunk数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.UnparsedChunkList">
            <summary>
            为解析的Chunk列表
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nExtendWidth">
            <summary>
            图像宽扩展
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nExtendHeight">
            <summary>
            图像高扩展
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.UNPARSED_CHUNK_LIST">
            <summary>
            为解析的Chunk列表
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.UNPARSED_CHUNK_LIST.pUnparsedChunkContent">
            <summary>
            为解析的Chunk内容
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT_INFO_EX.UNPARSED_CHUNK_LIST.nAligning">
            <summary>
            对齐结构体，无实际用途
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT">
            <summary>
            输出帧信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT.pBufAddr">
            <summary>
            帧数据地址
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT.stFrameInfo">
            <summary>
            帧信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_FRAME_OUT.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO">
            <summary>
            显示帧信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO.hWnd">
            <summary>
            显示窗口的句柄
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO.pData">
            <summary>
            显示的帧数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO.nDataLen">
            <summary>
            显示的帧数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO.nWidth">
            <summary>
            图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO.nHeight">
            <summary>
            图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO.enPixelType">
            <summary>
            像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO_EX">
            <summary>
            显示帧信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO_EX.nWidth">
            <summary>
            图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO_EX.nHeight">
            <summary>
            图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO_EX.enPixelType">
            <summary>
            像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO_EX.pData">
            <summary>
            显示的帧数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO_EX.nDataLen">
            <summary>
            显示的帧数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_DISPLAY_FRAME_INFO_EX.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM">
            <summary>
            保存的点阵参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.nLinePntNum">
            <summary>
            [IN]     每一行点的数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.nLineNum">
            <summary>
            [IN]     行数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.enSrcPixelType">
            <summary>
            [IN]     输入数据的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.pSrcData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.nSrcDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.pDstBuf">
            <summary>
            [OUT]    输出像素数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小(nLinePntNum * nLineNum * (16*3 + 4) + 2048)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出像素数据缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.enPointCloudFileType">
            <summary>
            保存的点阵文件类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_POINT_CLOUD_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM">
            <summary>
            保存的图像参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.pData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.nDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.enPixelType">
            <summary>
            [IN]     输入数据的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.nWidth">
            <summary>
            [IN]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.nHeight">
            <summary>
            [IN]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.pImageBuffer">
            <summary>
            [OUT]    输出图片缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.nImageLen">
            <summary>
            [OUT]    输出图片大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.nBufferSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM.enImageType">
            <summary>
            [IN]     输出图片格式
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX">
            <summary>
            保存的图像参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.pData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.nDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.enPixelType">
            <summary>
            [IN]     输入数据的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.nWidth">
            <summary>
            [IN]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.nHeight">
            <summary>
            [IN]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.pImageBuffer">
            <summary>
            [OUT]    输出图片缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.nImageLen">
            <summary>
            [OUT]    输出图片大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.nBufferSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.enImageType">
            <summary>
            [IN]     输出图片格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.nJpgQuality">
            <summary>
            [IN]     编码质量, (50-99]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.iMethodValue">
            <summary>
            [IN]     Bayer的插值方法 0-快速 1-均衡 2-最优（如果传入其它值则默认为最优）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2">
            <summary>
            保存的图像信息扩展
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.pData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.nDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.enPixelType">
            <summary>
            [IN]     输入数据的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.nWidth">
            <summary>
            [IN]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.nHeight">
            <summary>
            [IN]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.pImageBuffer">
            <summary>
            [OUT]    输出图片缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.nImageLen">
            <summary>
            [OUT]    输出图片大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.nBufferSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.enImageType">
            <summary>
            [IN]     输出图片格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.nJpgQuality">
            <summary>
            [IN]     编码质量, (50-99]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.iMethodValue">
            <summary>
            [IN]     Bayer的插值方法 0-快速 1-均衡 2-最优（如果传入其它值则默认为最优）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMAGE_PARAM_EX2.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM">
            <summary>
            保存图像到文件的参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.enPixelType">
            <summary>
            [IN]     输入数据的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.pData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.nDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.nWidth">
            <summary>
            [IN]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.nHeight">
            <summary>
            [IN]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.enImageType">
            <summary>
            [IN]     输入图片格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.nQuality">
            <summary>
            [IN]     编码质量, (0-100]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.pImagePath">
            <summary>
            [IN]     输入文件路径
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.iMethodValue">
            <summary>
            [IN]     Bayer的插值方法 0-快速 1-均衡 2-最优（如果传入其它值则默认为最优）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX">
            <summary>
            保存图像到文件信息扩展
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.nWidth">
            <summary>
            [IN]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.nHeight">
            <summary>
            [IN]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.enPixelType">
            <summary>
            [IN]     输入数据的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.pData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.nDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.enImageType">
            <summary>
            [IN]     输入图片格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.pImagePath">
            <summary>
            [IN]     输入文件路径
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.nQuality">
            <summary>
            [IN]     编码质量, (0-100]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.iMethodValue">
            <summary>
            [IN]     Bayer的插值方法 0-快速 1-均衡 2-最优（如果传入其它值则默认为最优）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_SAVE_IMG_TO_FILE_PARAM_EX.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM">
            <summary>
            旋转图像参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.enPixelType">
            <summary>
            [IN]     像素格式(仅支持Mono8/RGB24/BGR24)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.nWidth">
            <summary>
            [IN][OUT]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.nHeight">
            <summary>
            [IN][OUT]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.pSrcData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.nSrcDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.pDstBuf">
            <summary>
            [OUT]    输出图片缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出图片大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.enRotationAngle">
            <summary>
            [IN]     旋转角度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_ROTATE_IMAGE_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM">
            <summary>
            翻转图像参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.enPixelType">
            <summary>
            [IN]     像素格式(仅支持Mono8/RGB24/BGR24)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.nWidth">
            <summary>
            [IN]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.nHeight">
            <summary>
            [IN]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.pSrcData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.nSrcDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.pDstBuf">
            <summary>
            [OUT]    输出图片缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出图片大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.enFlipType">
            <summary>
            [IN]     翻转类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FLIP_IMAGE_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM">
            <summary>
            像素转换参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.nWidth">
            <summary>
            [IN]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.nHeight">
            <summary>
            [IN]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.enSrcPixelType">
            <summary>
            [IN]     源像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.pSrcData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.nSrcDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.enDstPixelType">
            <summary>
            [IN]     目标像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.pDstBuffer">
            <summary>
            [OUT]    输出数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.nDstLen">
            <summary>
            [OUT]    输出数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.nDstBufferSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX">
            <summary>
            图像像素转换信息扩展
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.nWidth">
            <summary>
            [IN]     图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.nHeight">
            <summary>
            [IN]     图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.enSrcPixelType">
            <summary>
            [IN]     源像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.pSrcData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.nSrcDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.enDstPixelType">
            <summary>
            [IN]     目标像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.pDstBuffer">
            <summary>
            [OUT]    输出数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.nDstLen">
            <summary>
            [OUT]    输出数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.nDstBufferSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_PIXEL_CONVERT_PARAM_EX.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_GAMMA_PARAM">
            <summary>
            Gamma参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_GAMMA_PARAM.enGammaType">
            <summary>
            [IN]     Gamma类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_GAMMA_PARAM.fGammaValue">
            <summary>
            [IN]     Gamma值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_GAMMA_PARAM.pGammaCurveBuf">
            <summary>
            [IN]     Gamma曲线缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_GAMMA_PARAM.nGammaCurveBufLen">
            <summary>
            [IN]     Gamma曲线长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_GAMMA_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM">
            <summary>
            CCM参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM.bCCMEnable">
            <summary>
            [IN]     是否启用CCM
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM.nCCMat">
            <summary>
            [IN]     CCM矩阵(-8192~8192)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM_EX">
            <summary>
            CCM参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM_EX.bCCMEnable">
            <summary>
            [IN]     是否启用CCM
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM_EX.nCCMat">
            <summary>
            [IN]     量化3x3矩阵
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM_EX.nCCMScale">
            <summary>
            [IN]     量化系数（2的整数幂）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CCM_PARAM_EX.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_CLUT_PARAM">
            <summary>
            CLUT参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CLUT_PARAM.bCLUTEnable">
            <summary>
            [IN]     是否启用CLUT
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CLUT_PARAM.nCLUTScale">
            <summary>
            [IN]     量化系数(2的整数幂)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CLUT_PARAM.nCLUTSize">
            <summary>
            [IN]     CLUT大小，建议值17
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CLUT_PARAM.pCLUTBuf">
            <summary>
            [OUT]    量化CLUT
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CLUT_PARAM.nCLUTBufLen">
            <summary>
            [IN]     量化CLUT缓存大小（nCLUTSize*nCLUTSize*nCLUTSize*sizeof(int)*3）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CLUT_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM">
            <summary>
            对比度调节参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.nWidth">
            <summary>
            [IN]     图像宽度(最小8)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.nHeight">
            <summary>
            [IN]     图像高度(最小8)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.pSrcBuf">
            <summary>
            [IN]     输入图像缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.nSrcBufLen">
            <summary>
            [IN]     输入图像缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.enPixelType">
            <summary>
            [IN]     输入的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.pDstBuf">
            <summary>
            [OUT]    输出像素数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出像素数据缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.nContrastFactor">
            <summary>
            [IN]     对比度值，范围:[1, 10000]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_CONTRAST_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM">
            <summary>
            锐化参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nWidth">
            <summary>
            [IN]     图像宽度(最小8)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nHeight">
            <summary>
            [IN]     图像高度(最小8)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.pSrcBuf">
            <summary>
            [IN]     输入图像缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nSrcBufLen">
            <summary>
            [IN]     输入图像缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.enPixelType">
            <summary>
            [IN]     输入的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.pDstBuf">
            <summary>
            [OUT]    输出像素数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出像素数据缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nSharpenAmount">
            <summary>
            [IN]     锐度调节强度，范围:[0, 500]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nSharpenRadius">
            <summary>
            [IN]     锐度调节半径（半径越大，耗时越长），范围:[1, 21]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nSharpenThreshold">
            <summary>
            [IN]     锐度调节阈值，范围:[0, 255]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SHARPEN_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM">
            <summary>
            色彩校正参数（包括CCM和CLUT）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.nWidth">
            <summary>
            [IN]     图像宽度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.nHeight">
            <summary>
            [IN]     图像高度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.pSrcBuf">
            <summary>
            [IN]     输入图像缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.nSrcBufLen">
            <summary>
            [IN]     输入图像缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.enPixelType">
            <summary>
            [IN]     输入的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.pDstBuf">
            <summary>
            [OUT]    输出像素数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出像素数据缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.nImageBit">
            <summary>
            [IN]     输入有效图像位数，8 or 10 or 12 or 16
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.stGammaParam">
            <summary>
            [IN]     输入Gamma信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.stCCMParam">
            <summary>
            [IN]     输入CCM信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.stCLUTParam">
            <summary>
            [IN]     输入CLUT信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_COLOR_CORRECT_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_RECT_I">
            <summary>
            矩形ROI参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECT_I.nX">
            <summary>
            [IN]     矩形左上角X轴坐标
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECT_I.nY">
            <summary>
            [IN]     矩形左上角Y轴坐标
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECT_I.nWidth">
            <summary>
            [IN]     矩形宽度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECT_I.nHeight">
            <summary>
            [IN]     矩形高度
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM">
            <summary>
            噪声估计参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.nWidth">
            <summary>
            [IN]     图像宽度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.nHeight">
            <summary>
            [IN]     图像高度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.enPixelType">
            <summary>
            [IN]     输入的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.pSrcBuf">
            <summary>
            [IN]     输入图像缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.nSrcBufLen">
            <summary>
            [IN]     输入图像缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.pstROIRect">
            <summary>
            [IN]     图像ROI
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.nROINum">
            <summary>
            [IN]     ROI个数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.nNoiseThreshold">
            <summary>
            [IN]     噪声阈值[0-4095]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.pNoiseProfile">
            <summary>
            [OUT]    输出噪声特性
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.nNoiseProfileSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.nNoiseProfileLen">
            <summary>
            [OUT]    输出噪声特性长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_NOISE_ESTIMATE_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM">
            <summary>
            空域降噪参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nWidth">
            <summary>
            [IN]     图像宽度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nHeight">
            <summary>
            [IN]     图像高度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.enPixelType">
            <summary>
            [IN]     输入的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.pSrcBuf">
            <summary>
            [IN]     输入图像缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nSrcBufLen">
            <summary>
            [IN]     输入图像缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.pDstBuf">
            <summary>
            [OUT]    输出降噪后的数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出降噪后的数据长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.pNoiseProfile">
            <summary>
            [IN]     输入噪声特性
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nNoiseProfileLen">
            <summary>
            [IN]     输入噪声特性长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nBayerDenoiseStrength">
            <summary>
            [IN]     降噪强度(0-100)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nBayerSharpenStrength">
            <summary>
            [IN]     锐化强度(0-32)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nBayerNoiseCorrect">
            <summary>
            [IN]     噪声校正系数(0-1280)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nNoiseCorrectLum">
            <summary>
            [IN]     亮度校正系数(1-2000)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nNoiseCorrectChrom">
            <summary>
            [IN]     色调校正系数(1-2000)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nStrengthLum">
            <summary>
            [IN]     亮度降噪强度(0-100)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nStrengthChrom">
            <summary>
            [IN]     色调降噪强度(0-100)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nStrengthSharpen">
            <summary>
            [IN]     锐化强度(1-1000)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_SPATIAL_DENOISE_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM">
            <summary>
            LSC标定参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nWidth">
            <summary>
            [IN]     图像宽度(16~65536)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nHeight">
            <summary>
            [IN]     图像高度(16~65536)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.enPixelType">
            <summary>
            [IN]     输入的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.pSrcBuf">
            <summary>
            [IN]     输入图像缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nSrcBufLen">
            <summary>
            [IN]     输入图像缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.pCalibBuf">
            <summary>
            [OUT]    输出标定表缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nCalibBufSize">
            <summary>
            [IN]     提供的标定表缓冲大小（nWidth*nHeight*sizeof(unsigned short)）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nCalibBufLen">
            <summary>
            [OUT]    输出标定表缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nSecNumW">
            <summary>
            [IN]     宽度分块数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nSecNumH">
            <summary>
            [IN]     高度分块数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nPadCoef">
            <summary>
            [IN]     边缘填充系数，范围1~5
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nCalibMethod">
            <summary>
            [IN]     标定方式，0-中心为基准
                               1-最亮区域为基准
                               2-目标亮度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nTargetGray">
            <summary>
            [IN]     目标亮度（8bits，[0,255])
                    （10bits，[0,1023])
                    （12bits，[0,4095])
                    （16bits，[0,65535])
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CALIB_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM">
            <summary>
            LSC校正参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.nWidth">
            <summary>
            [IN]     图像宽度(16~65536)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.nHeight">
            <summary>
            [IN]     图像高度(16~65536)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.enPixelType">
            <summary>
            [IN]     输入的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.pSrcBuf">
            <summary>
            [IN]     输入图像缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.nSrcBufLen">
            <summary>
            [IN]     输入图像缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.pDstBuf">
            <summary>
            [OUT]    输出像素数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出像素数据缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.pCalibBuf">
            <summary>
            [IN]     输入校正表缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.nCalibBufLen">
            <summary>
            [IN]     输入校正表缓存长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_LSC_CORRECT_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_FEATURE_TYPE">
            <summary>
            噪声特性类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_FEATURE_TYPE.MV_CC_BAYER_NOISE_FEATURE_TYPE_INVALID">
            <summary>
            无效 
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_FEATURE_TYPE.MV_CC_BAYER_NOISE_FEATURE_TYPE_PROFILE">
            <summary>
            噪声曲线
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_FEATURE_TYPE.MV_CC_BAYER_NOISE_FEATURE_TYPE_LEVEL">
            <summary>
            噪声水平
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_FEATURE_TYPE.MV_CC_BAYER_NOISE_FEATURE_TYPE_DEFAULT">
            <summary>
            默认值
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO">
            <summary>
            噪声基本信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO.nVersion">
            <summary>
            版本
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO.enNoiseFeatureType">
            <summary>
            噪声特性类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO.enPixelType">
            <summary>
            图像格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO.nNoiseLevel">
            <summary>
            平均噪声水平
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO.nCurvePointNum">
            <summary>
            曲线点数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO.nNoiseCurve">
            <summary>
            噪声曲线
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO.nLumCurve">
            <summary>
            亮度曲线
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_PROFILE_INFO.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM">
            <summary>
            噪声估计参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.nWidth">
            <summary>
            [IN]     图像宽(大于等于8)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.nHeight">
            <summary>
            [IN]     图像高(大于等于8)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.enPixelType">
            <summary>
            [IN]     像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.pSrcData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.nSrcDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.nNoiseThreshold">
            <summary>
            [IN]     噪声阈值(0-4095)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.pCurveBuf">
            <summary>
            [IN]     用于存储噪声曲线和亮度曲线（需要外部分配，缓存大小：4096 * sizeof(int) * 2）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.stNoiseProfile">
            <summary>
            [OUT]    降噪特性信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.nThreadNum">
            <summary>
            [IN]     线程数量，0表示算法库根据硬件自适应；1表示单线程（默认）；大于1表示线程数目
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_NOISE_ESTIMATE_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM">
            <summary>
            降噪参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nWidth">
            <summary>
            [IN]     图像宽(大于等于8)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nHeight">
            <summary>
            [IN]     图像高(大于等于8)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.enPixelType">
            <summary>
            [IN]     像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.pSrcData">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nSrcDataLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.pDstBuf">
            <summary>
            [OUT]    输出降噪后的数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出降噪后的数据长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.stNoiseProfile">
            <summary>
            [IN]    降噪特性信息(来源于噪声估计)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nDenoiseStrength">
            <summary>
            [IN]     降噪强度(0-100) 
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nSharpenStrength">
            <summary>
            [IN]     锐化强度(0-32)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nNoiseCorrect">
            <summary>
            [IN]     噪声校正系数(0-1280)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nThreadNum">
            <summary>
            [IN]     线程数量，0表示算法库根据硬件自适应；1表示单线程（默认）；大于1表示线程数目
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_BAYER_SPATIAL_DENOISE_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO">
            <summary>
            帧特殊信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nSecondCount">
            <summary>
            [OUT]     秒数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nCycleCount">
            <summary>
            [OUT]     周期数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nCycleOffset">
            <summary>
            [OUT]     周期偏移量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.fGain">
            <summary>
            [OUT]     增益
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.fExposureTime">
            <summary>
            [OUT]     曝光时间
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nAverageBrightness">
            <summary>
            [OUT]     平均亮度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nRed">
            <summary>
            [OUT]     红色
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nGreen">
            <summary>
            [OUT]     绿色
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nBlue">
            <summary>
            [OUT]     蓝色
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nFrameCounter">
            <summary>
            [OUT]     总帧数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nTriggerIndex">
            <summary>
            [OUT]     触发计数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nInput">
            <summary>
            [OUT]     输入
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nOutput">
            <summary>
            [OUT]     输出
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nOffsetX">
            <summary>
            [OUT]     水平偏移量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nOffsetY">
            <summary>
            [OUT]     垂直偏移量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nFrameWidth">
            <summary>
            [OUT]     水印宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nFrameHeight">
            <summary>
            [OUT]     水印高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FRAME_SPEC_INFO.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM">
            <summary>
            HB解码参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.pSrcBuf">
            <summary>
            [IN]     输入数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.nSrcLen">
            <summary>
            [IN]     输入数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.nWidth">
            <summary>
            [OUT]    图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.nHeight">
            <summary>
            [OUT]    图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.pDstBuf">
            <summary>
            [OUT]    输出数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.nDstBufSize">
            <summary>
            [IN]     提供的输出缓冲区大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.nDstBufLen">
            <summary>
            [OUT]    输出数据大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.enDstPixelType">
            <summary>
            [OUT]     输出的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.stFrameSpecInfo">
            <summary>
            [OUT]    水印信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_HB_DECODE_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM">
            <summary>
            录像参数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM.enPixelType">
            <summary>
            [IN]     输入数据的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM.nWidth">
            <summary>
            [IN]     图像宽(指定目标参数时需为8的倍数)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM.nHeight">
            <summary>
            [IN]     图像高(指定目标参数时需为8的倍数)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM.fFrameRate">
            <summary>
            [IN]     帧率fps(大于1/16)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM.nBitRate">
            <summary>
            [IN]     码率kbps(128kbps-16Mbps)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM.enRecordFmtType">
            <summary>
            [IN]     录像格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM.strFilePath">
            <summary>
            [IN]     录像文件存放路径
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_RECORD_PARAM.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_INPUT_FRAME_INFO">
            <summary>
            输入帧信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_INPUT_FRAME_INFO.pData">
            <summary>
            [IN]     图像数据指针
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_INPUT_FRAME_INFO.nDataLen">
            <summary>
            [IN]     图像大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_INPUT_FRAME_INFO.nRes">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CAM_ACQUISITION_MODE">
            <summary>
            采集模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_ACQUISITION_MODE.MV_ACQ_MODE_SINGLE">
            <summary>
            单帧模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_ACQUISITION_MODE.MV_ACQ_MODE_MUTLI">
            <summary>
            多帧模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_ACQUISITION_MODE.MV_ACQ_MODE_CONTINUOUS">
            <summary>
            持续采集模式
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CAM_GAIN_MODE">
            <summary>
            增益模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_GAIN_MODE.MV_GAIN_MODE_OFF">
            <summary>
            关闭
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_GAIN_MODE.MV_GAIN_MODE_ONCE">
            <summary>
            一次
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_GAIN_MODE.MV_GAIN_MODE_CONTINUOUS">
            <summary>
            连续
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CAM_EXPOSURE_MODE">
            <summary>
            曝光模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_EXPOSURE_MODE.MV_EXPOSURE_MODE_TIMED">
            <summary>
            Timed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_EXPOSURE_MODE.MV_EXPOSURE_MODE_TRIGGER_WIDTH">
            <summary>
            TriggerWidth
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CAM_EXPOSURE_AUTO_MODE">
            <summary>
            自动曝光模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_EXPOSURE_AUTO_MODE.MV_EXPOSURE_AUTO_MODE_OFF">
            <summary>
            关闭
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_EXPOSURE_AUTO_MODE.MV_EXPOSURE_AUTO_MODE_ONCE">
            <summary>
            一次
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_EXPOSURE_AUTO_MODE.MV_EXPOSURE_AUTO_MODE_CONTINUOUS">
            <summary>
            连续
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_MODE">
            <summary>
            相机触发模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_MODE.MV_TRIGGER_MODE_OFF">
            <summary>
            关闭
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_MODE.MV_TRIGGER_MODE_ON">
            <summary>
            打开
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CAM_GAMMA_SELECTOR">
            <summary>
            Gamma选择器
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_GAMMA_SELECTOR.MV_GAMMA_SELECTOR_USER">
            <summary>
            USER
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_GAMMA_SELECTOR.MV_GAMMA_SELECTOR_SRGB">
            <summary>
            SRGB
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CAM_BALANCEWHITE_AUTO">
            <summary>
            自动白平衡
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_BALANCEWHITE_AUTO.MV_BALANCEWHITE_AUTO_OFF">
            <summary>
            关闭自动白平衡
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_BALANCEWHITE_AUTO.MV_BALANCEWHITE_AUTO_ONCE">
            <summary>
            一次自动白平衡
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_BALANCEWHITE_AUTO.MV_BALANCEWHITE_AUTO_CONTINUOUS">
            <summary>
            连续自动白平衡
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_SOURCE">
            <summary>
            触发源
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_LINE0">
            <summary>
            LINE0
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_LINE1">
            <summary>
            LINE1
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_LINE2">
            <summary>
            LINE2
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_LINE3">
            <summary>
            LINE3
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_COUNTER0">
            <summary>
            COUNTER0
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_SOFTWARE">
            <summary>
            SOFTWARE
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CAM_TRIGGER_SOURCE.MV_TRIGGER_SOURCE_FrequencyConverter">
            <summary>
            FrequencyConverter
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_ALL_MATCH_INFO">
            <summary>
            ALL MATHCH INFO
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ALL_MATCH_INFO.nType">
            <summary>
            需要输出的信息类型，e.g. MV_MATCH_TYPE_NET_DETECT
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ALL_MATCH_INFO.pInfo">
            <summary>
            输出的信息缓存，由调用者分配
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ALL_MATCH_INFO.nInfoSize">
            <summary>
            信息缓存的大小
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_NET_DETECT">
            <summary>
            
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_NET_DETECT.nReviceDataSize">
            <summary>
            已接收数据大小  [统计StartGrabbing和StopGrabbing之间的数据量]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_NET_DETECT.nLostPacketCount">
            <summary>
            丢失的包数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_NET_DETECT.nLostFrameCount">
            <summary>
            丢帧数量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_NET_DETECT.nNetRecvFrameCount">
            <summary>
            帧数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_NET_DETECT.nRequestResendPacketCount">
            <summary>
            请求重发包数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_NET_DETECT.nResendPacketCount">
            <summary>
            重发包数
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_USB_DETECT">
            <summary>
            USB
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_USB_DETECT.nReviceDataSize">
            <summary>
            已接收数据大小    [统计OpenDevicce和CloseDevice之间的数据量]
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_USB_DETECT.nRevicedFrameCount">
            <summary>
            已收到的帧数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_USB_DETECT.nErrorFrameCount">
            <summary>
            错误帧数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_MATCH_INFO_USB_DETECT.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO">
            <summary>
            图像的基本信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nWidthValue">
            <summary>
            宽度值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nWidthMin">
            <summary>
            宽度最小值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nWidthMax">
            <summary>
            宽度最大值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nWidthInc">
            <summary>
            Width Inc
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nHeightValue">
            <summary>
            高度值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nHeightMin">
            <summary>
            高度最小值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nHeightMax">
            <summary>
            高度最大值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nHeightInc">
            <summary>
            Height Inc
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.fFrameRateValue">
            <summary>
            帧率
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.fFrameRateMin">
            <summary>
            最小帧率
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.fFrameRateMax">
            <summary>
            最大帧率
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.enPixelType">
            <summary>
            当前的像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nSupportedPixelFmtNum">
            <summary>
            支持的像素格式种类
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.enPixelList">
            <summary>
            像素列表
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_IMAGE_BASIC_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_Visibility">
            <summary>
            节点是否可见的权限等级
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_Visibility.V_Beginner">
            <summary>
            Always visible
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_Visibility.V_Expert">
            <summary>
            Visible for experts or Gurus
            </summary>        
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_Visibility.V_Guru">
            <summary>
            Visible for Gurus
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_Visibility.V_Invisible">
            <summary>
            Not Visible
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_Visibility.V_Undefined">
            <summary>
            Object is not yet initialized
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO">
            <summary>
            事件信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.EventName">
            <summary>
            事件名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.nEventID">
            <summary>
            Event号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.nStreamChannel">
            <summary>
            流通到序号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.nBlockIdHigh">
            <summary>
            帧号高位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.nBlockIdLow">
            <summary>
            帧号低位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.nTimestampHigh">
            <summary>
            时间戳高位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.nTimestampLow">
            <summary>
            时间戳低位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.pEventData">
            <summary>
            Event数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.nEventDataSize">
            <summary>
            Event数据长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_EVENT_OUT_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS">
            <summary>
            文件存取
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS.pUserFileName">
            <summary>
            用户文件名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS.pDevFileName">
            <summary>
            设备文件名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_EX">
            <summary>
            文件存取
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_EX.pUserFileBuf">
            <summary>
            用户文件数据缓存空间
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_EX.nFileBufSize">
            <summary>
            用户数据缓存大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_EX.nFileBufLen">
            <summary>
            文件实际缓存大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_EX.pDevFileName">
            <summary>
            设备文件名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_EX.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_PROGRESS">
            <summary>
            文件存取进度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_PROGRESS.nCompleted">
            <summary>
            已完成的长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_PROGRESS.nTotal">
            <summary>
            总长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_FILE_ACCESS_PROGRESS.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_TRANSMISSION_TYPE">
            <summary>
            传输模式，可以为单播模式、组播模式等
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_TRANSMISSION_TYPE.enTransmissionType">
            <summary>
            传输模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_TRANSMISSION_TYPE.nDestIp">
            <summary>
            目标IP，组播模式下有意义
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_TRANSMISSION_TYPE.nDestPort">
            <summary>
            目标Port，组播模式下有意义
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_TRANSMISSION_TYPE.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO">
            <summary>
            动作命令信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO.nDeviceKey">
            <summary>
            设备密钥
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO.nGroupKey">
            <summary>
            组键
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO.nGroupMask">
            <summary>
            组掩码
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO.bActionTimeEnable">
            <summary>
            只有设置成1时Action Time才有效，非1时无效
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO.nActionTime">
            <summary>
            预定的时间，和主频有关
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO.pBroadcastAddress">
            <summary>
            广播包地址
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO.nTimeOut">
            <summary>
            等待ACK的超时时间，如果为0表示不需要ACK
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_INFO.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_RESULT">
            <summary>
            动作命令结果
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_RESULT.strDeviceAddress">
            <summary>
            IP address of the device
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_RESULT.nStatus">
            <summary>
            status code returned by the device
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_RESULT.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_RESULT_LIST">
            <summary>
            动作命令结果列表
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_RESULT_LIST.nNumResults">
            <summary>
            返回值个数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_ACTION_CMD_RESULT_LIST.pResults">
            <summary>
            返回的结果
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_NODE_FEATURE">
            <summary>
            XML节点特点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODE_FEATURE.enType">
            <summary>
            节点类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODE_FEATURE.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODE_FEATURE.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODE_FEATURE.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODE_FEATURE.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODE_FEATURE.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODE_FEATURE.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_NODES_LIST">
            <summary>
            XML节点列表
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODES_LIST.nNodeNum">
            <summary>
            节点个数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_NODES_LIST.stNodes">
            <summary>
            节点列表
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE">
            <summary>
            整型节点值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE.nCurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE.nMax">
            <summary>
            最大值
            </summary> 
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE.nMin">
            <summary>
            最小值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE.nInc">
            <summary>
            Inc
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE_EX">
            <summary>
            整型节点值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE_EX.nCurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE_EX.nMax">
            <summary>
            最大值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE_EX.nMin">
            <summary>
            最小值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE_EX.nInc">
            <summary>
            Inc
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_INTVALUE_EX.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_FLOATVALUE">
            <summary>
            浮点型节点值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_FLOATVALUE.fCurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_FLOATVALUE.fMax">
            <summary>
            最大值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_FLOATVALUE.fMin">
            <summary>
            最小值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_FLOATVALUE.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_ENUMVALUE">
            <summary>
            枚举型节点值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_ENUMVALUE.nCurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_ENUMVALUE.nSupportedNum">
            <summary>
            有效数据个数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_ENUMVALUE.nSupportValue">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_ENUMVALUE.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_STRINGVALUE">
            <summary>
            字符串型节点值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_STRINGVALUE.chCurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_STRINGVALUE.nMaxLength">
            <summary>
            节点值的最大长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_STRINGVALUE.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer">
            <summary>
            整型节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.nValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.nMinValue">
            <summary>
            最小值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.nMaxValue">
            <summary>
            最大值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.nIncrement">
            <summary>
            增量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Integer.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean">
            <summary>
            布尔型节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.bValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Boolean.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command">
            <summary>
            命令型节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Command.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float">
            <summary>
            浮点型节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.dfValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.dfMinValue">
            <summary>
            最小值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.dfMaxValue">
            <summary>
            最大值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.dfIncrement">
            <summary>
            增量
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Float.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String">
            <summary>
            字符串类型节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.strValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_String.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register">
            <summary>
            寄存器型节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.nAddrValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Register.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Category">
            <summary>
            类别属性
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Category.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Category.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Category.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Category.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Category.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Category.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry">
            <summary>
            EnumEntry属性节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.bIsImplemented">
            <summary>
            
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.nParentsNum">
            <summary>
            父节点数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.stParentsList">
            <summary>
            父节点列表
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.nValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_EnumEntry.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.StrSymbolic">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.StrSymbolic.str">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration">
            <summary>
            Enumeration属性节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.nSymbolicNum">
            <summary>
            Symbolic数
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.strCurrentSymbolic">
            <summary>
            当前Symbolic索引
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.strSymbolic">
            <summary>
            Symbolic索引
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.nValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Enumeration.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port">
            <summary>
            Port属性节点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port.enVisivility">
            <summary>
            是否可见
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port.strDescription">
            <summary>
            节点描述
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port.strDisplayName">
            <summary>
            显示名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port.strName">
            <summary>
            节点名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port.strToolTip">
            <summary>
            提示
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port.enAccessMode">
            <summary>
            访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port.bIsLocked">
            <summary>
            是否锁定。0-否；1-是
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_XML_FEATURE_Port.nReserved">
            <summary>
            保留字节
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_COLORF">
            <summary>辅助线颜色</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_COLORF.fR">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_COLORF.fG">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_COLORF.fB">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_COLORF.fAlpha">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_COLORF.nReserved">
            <summary>预留字节</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_POINTF">
            <summary>自定义点坐标</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_POINTF.fX">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_POINTF.fY">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_POINTF.nReserved">
            <summary>预留字节</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_RECT_INFO">
            <summary>矩形框区域信息</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_RECT_INFO.fTop">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_RECT_INFO.fBottom">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_RECT_INFO.fLeft">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_RECT_INFO.fRight">
            <summary>[0.0 , 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_RECT_INFO.stColor">
            <summary>辅助线颜色</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_RECT_INFO.nLineWidth">
            <summary>辅助线宽度</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_RECT_INFO.nReserved">
            <summary>预留字节</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_CIRCLE_INFO">
            <summary>圆形框区域信息</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_CIRCLE_INFO.stCenterPoint">
            <summary>圆心信息</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_CIRCLE_INFO.fR1">
            <summary>宽向半径，根据图像的相对位置[0, 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_CIRCLE_INFO.fR2">
            <summary>高向半径，根据图像的相对位置[0, 1.0]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_CIRCLE_INFO.stColor">
            <summary>辅助线颜色信息</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_CIRCLE_INFO.nLineWidth">
            <summary>辅助线宽度</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_CIRCLE_INFO.nReserved">
            <summary>预留字节</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_LINES_INFO">
            <summary>线条辅助线信息</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_LINES_INFO.stStartPoint">
            <summary>线条辅助线的起始点坐标</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_LINES_INFO.stEndPoint">
            <summary>线条辅助线的终点坐标</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_LINES_INFO.stColor">
            <summary>辅助线颜色信息</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_LINES_INFO.nLineWidth">
            <summary>辅助线宽度</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_LINES_INFO.nReserved">
            <summary>预留字节</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MVCC_ENUMENTRY">
            <summary>枚举类型指定条目信息</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_ENUMENTRY.nValue">
            <summary>指定值</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_ENUMENTRY.chSymbolic">
            <summary></summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MVCC_ENUMENTRY.nReserved">
            <summary>预留字节</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_CC_STREAM_EXCEPTION_TYPE">
            <summary>U3V流异常类型</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_STREAM_EXCEPTION_TYPE.MV_CC_STREAM_EXCEPTION_ABNORMAL_IMAGE">
            <summary>异常的图像，该帧被丢弃</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_STREAM_EXCEPTION_TYPE.MV_CC_STREAM_EXCEPTION_LIST_OVERFLOW">
            <summary>缓存列表溢出，清除最旧的一帧</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_STREAM_EXCEPTION_TYPE.MV_CC_STREAM_EXCEPTION_LIST_EMPTY">
            <summary>缓存列表为空，该帧被丢弃</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_STREAM_EXCEPTION_TYPE.MV_CC_STREAM_EXCEPTION_RECONNECTION">
            <summary>断流恢复</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_STREAM_EXCEPTION_TYPE.MV_CC_STREAM_EXCEPTION_DISCONNECTED">
            <summary>断流,恢复失败,取流被中止</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_CC_STREAM_EXCEPTION_TYPE.MV_CC_STREAM_EXCEPTION_DEVICE">
            <summary>设备异常,取流被中止</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_OUTPUT_IMAGE_INFO">
            <summary>重构后的图像列表</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_OUTPUT_IMAGE_INFO.nWidth">
            <summary>源图像宽</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_OUTPUT_IMAGE_INFO.nHeight">
            <summary>源图像高</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_OUTPUT_IMAGE_INFO.enPixelType">
            <summary>像素格式</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_OUTPUT_IMAGE_INFO.pBuf">
            <summary>输出数据缓存</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_OUTPUT_IMAGE_INFO.nBufLen">
            <summary>输出数据长度</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_OUTPUT_IMAGE_INFO.nBufSize">
            <summary>提供的输出缓冲区大小</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_OUTPUT_IMAGE_INFO.nReserved">
            <summary>预留字节</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM">
            <summary>重构图像参数信息</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.nWidth">
            <summary>源图像宽</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.nHeight">
            <summary>源图像高</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.enPixelType">
            <summary>像素格式</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.pSrcData">
            <summary>输入数据缓存</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.nSrcDataLen">
            <summary>输入数据长度</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.nExposureNum">
            <summary>曝光个数(1-8]</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.enReconstructMethod">
            <summary>图像重构方式</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.stDstBufList">
            <summary>
            输出数据缓存信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CameraParams.MV_RECONSTRUCT_IMAGE_PARAM.nReserved">
            <summary>预留字节</summary>
        </member>
        <member name="T:MvCamCtrl.NET.cbStreamExceptiondelegate">
            <summary>
            U3V Stream exception callback
            </summary>
            <param name="enExceptionType">取流异常类型</param>
            <param name="pUser">用户自定义变量</param>
        </member>
        <member name="T:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE">
            <summary>
            GigE传输类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE.MV_GIGE_TRANSTYPE_UNICAST">
            <summary>
            表示单播(默认)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE.MV_GIGE_TRANSTYPE_MULTICAST">
            <summary>
            表示组播
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE.MV_GIGE_TRANSTYPE_LIMITEDBROADCAST">
            <summary>
            表示局域网内广播，暂不支持
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE.MV_GIGE_TRANSTYPE_SUBNETBROADCAST">
            <summary>
            表示子网内广播，暂不支持
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE.MV_GIGE_TRANSTYPE_CAMERADEFINED">
            <summary>
            表示从相机获取，暂不支持
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE.MV_GIGE_TRANSTYPE_UNICAST_DEFINED_PORT">
            <summary>
            表示用户自定义应用端接收图像数据Port号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE.MV_GIGE_TRANSTYPE_UNICAST_WITHOUT_RECV">
            <summary>
            表示设置了单播，但本实例不接收图像数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_TRANSMISSION_TYPE.MV_GIGE_TRANSTYPE_MULTICAST_WITHOUT_RECV">
            <summary>
            表示组播模式，但本实例不接收图像数据
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_GIGE_IP_CONFIG_TYPE">
            <summary>
            GigE设备IP配置方式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_IP_CONFIG_TYPE.MV_IP_CFG_STATIC">
            <summary>
            Static
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_IP_CONFIG_TYPE.MV_IP_CFG_DHCP">
            <summary>
            DHCP
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_IP_CONFIG_TYPE.MV_IP_CFG_LLA">
            <summary>
            LLA
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_GIGE_Net_Transfer_Mode">
            <summary>
            GigE网络传输模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_Net_Transfer_Mode.MV_NET_TRANS_DRIVER">
            <summary>
            驱动模式(默认)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GIGE_Net_Transfer_Mode.MV_NET_TRANS_SOCKET">
            <summary>
            Socket模式
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_CAML_BAUDRATE">
            <summary>
            CameraLink相机波特率
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_UNKNOW">
            <summary>
            未知波特率
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_9600">
            <summary>
            9600
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_19200">
            <summary>
            19200
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_38400">
            <summary>
            38400
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_57600">
            <summary>
            57600
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_115200">
            <summary>
            115200
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_230400">
            <summary>
            230400
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_460800">
            <summary>
            460800
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_921600">
            <summary>
            921600
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CAML_BAUDRATE.MV_CAML_BAUDRATE_AUTOMAX">
            <summary>
            自动选择波特率
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_MATCH_TYPE">
            <summary>
            信息类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_MATCH_TYPE.MV_MATCH_TYPE_NET_DETECT">
            <summary>
            网络流量和丢包信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_MATCH_TYPE.MV_MATCH_TYPE_USB_DETECT">
            <summary>
            host接收到来自U3V设备的字节总数
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CAllMatchInfo">
            <summary>
            各种类型的信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CAllMatchInfo.Type">
            <summary>
            需要输出的信息类型，如MV_MATCH_TYPE_NET_DETECT
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CAllMatchInfo.Info">
            <summary>
            输出的信息缓存，由调用者分配
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CAllMatchInfo.InfoSize">
            <summary>
            信息缓存的大小
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CNetTransInfo">
            <summary>
            网络传输信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CNetTransInfo.ReviceDataSize">
            <summary>
            已接收数据大小  [统计StartGrabbing和StopGrabbing之间的数据量]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CNetTransInfo.ThrowFrameCount">
            <summary>
            丢帧数量
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CNetTransInfo.NetRecvFrameCount">
            <summary>
            接收的帧数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CNetTransInfo.RequestResendPacketCount">
            <summary>
            请求重发包数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CNetTransInfo.ResendPacketCount">
            <summary>
            重发包数
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CTransMissionType">
            <summary>
            传输模式
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CTransMissionType.TransmissionType">
            <summary>
            传输模式
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CTransMissionType.DestIp">
            <summary>
            目标IP，组播模式下有意义
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CTransMissionType.DestPort">
            <summary>
            目标Port，组播模式下有意义
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CActionCmdInfo">
            <summary>
            动作命令信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdInfo.DeviceKey">
            <summary>
            设备密钥
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdInfo.GroupKey">
            <summary>
            组键
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdInfo.GroupMask">
            <summary>
            组掩码
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdInfo.ActionTimeEnable">
            <summary>
            只有设置成1时Action Time才有效，非1时无效
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdInfo.ActionTime">
            <summary>
            预定的时间，和主频有关
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdInfo.BroadcastAddress">
            <summary>
            广播包地址
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdInfo.TimeOut">
            <summary>
            等待ACK的超时时间，如果为0表示不需要ACK
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CActionCmdResultList">
            <summary>
            动作命令结果
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdResultList.NumResults">
            <summary>
            返回值个数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CActionCmdResultList.Results">
            <summary>
            返回结果
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CErrorDefine">
            <summary>
            错误码定义类
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_OK">
            <summary>成功，无错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_HANDLE">
            <summary>错误或无效的句柄</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_SUPPORT">
            <summary>不支持的功能</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_BUFOVER">
            <summary>缓存已满</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_CALLORDER">
            <summary>函数调用顺序错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_PARAMETER">
            <summary>错误的参数</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_RESOURCE">
            <summary>资源申请失败</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_NODATA">
            <summary>无数据</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_PRECONDITION">
            <summary>前置条件有误，或运行环境已发生变化</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_VERSION">
            <summary>版本不匹配</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_NOENOUGH_BUF">
            <summary>传入的内存空间不足</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_ABNORMAL_IMAGE">
            <summary>异常图像，可能是丢包导致图像不完整</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_LOAD_LIBRARY">
            <summary>动态导入DLL失败</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_NOOUTBUF">
            <summary>没有可输出的缓存</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_ENCRYPT">
            <summary>加密错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_UNKNOW">
            <summary>未知的错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_GENERIC">
            <summary>通用错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_ARGUMENT">
            <summary>参数非法</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_RANGE">
            <summary>值超出范围</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_PROPERTY">
            <summary>属性</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_RUNTIME">
            <summary>运行环境有问题</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_LOGICAL">
            <summary>逻辑错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_ACCESS">
            <summary>节点访问条件有误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_TIMEOUT">
            <summary>超时</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_DYNAMICCAST">
            <summary>转换异常</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_GC_UNKNOW">
            <summary>GenICam未知错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_NOT_IMPLEMENTED">
            <summary>命令不被设备支持</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_INVALID_ADDRESS">
            <summary>访问的目标地址不存在</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_WRITE_PROTECT">
            <summary>目标地址不可写</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_ACCESS_DENIED">
            <summary>设备无访问权限</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_BUSY">
            <summary>设备忙，或网络断开</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_PACKET">
            <summary>网络包数据错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_NETER">
            <summary>网络相关错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_IP_CONFLICT">
            <summary>设备IP冲突</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_USB_READ">
            <summary>读usb出错</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_USB_WRITE">
            <summary>写usb出错</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_USB_DEVICE">
            <summary>设备异常</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_USB_GENICAM">
            <summary>GenICam相关错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_USB_BANDWIDTH">
            <summary>带宽不足</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_USB_DRIVER">
            <summary>驱动不匹配或者未装驱动</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_USB_UNKNOW">
            <summary>USB未知的错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_UPG_FILE_MISMATCH">
            <summary>升级固件不匹配</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_UPG_LANGUSGE_MISMATCH">
            <summary>升级固件语言不匹配</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_UPG_CONFLICT">
            <summary>升级冲突（设备已经在升级了再次请求升级即返回此错误）</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_UPG_INNER_ERR">
            <summary>升级时设备内部出现错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_E_UPG_UNKNOW">
            <summary>升级时未知错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_OK">
            <summary>处理正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_ERR">
            <summary>不确定类型错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_ABILITY_ARG">
            <summary>能力集中存在无效参数</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_MEM_NULL">
            <summary>内存地址为空</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_MEM_ALIGN">
            <summary>内存对齐不满足要求</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_MEM_LACK">
            <summary>内存空间大小不够</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_MEM_SIZE_ALIGN">
            <summary>内存空间大小不满足对齐要求</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_MEM_ADDR_ALIGN">
            <summary>内存地址不满足对齐要求</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_IMG_FORMAT">
            <summary>图像格式不正确或者不支持</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_IMG_SIZE">
            <summary>图像宽高不正确或者超出范围</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_IMG_STEP">
            <summary>图像宽高与step参数不匹配</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_IMG_DATA_NULL">
            <summary>图像数据存储地址为空</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_CFG_TYPE">
            <summary>设置或者获取参数类型不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_CFG_SIZE">
            <summary>设置或者获取参数的输入、输出结构体大小不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_PRC_TYPE">
            <summary>处理类型不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_PRC_SIZE">
            <summary>处理时输入、输出参数大小不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_FUNC_TYPE">
            <summary>子处理类型不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_FUNC_SIZE">
            <summary>子处理时输入、输出参数大小不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_PARAM_INDEX">
            <summary>index参数不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_PARAM_VALUE">
            <summary>value参数不正确或者超出范围</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_PARAM_NUM">
            <summary>param_num参数不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_NULL_PTR">
            <summary>函数参数指针为空</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_OVER_MAX_MEM">
            <summary>超过限定的最大内存</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_CALL_BACK">
            <summary>回调函数出错</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_ENCRYPT">
            <summary>加密错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_EXPIRE">
            <summary>算法库使用期限错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_BAD_ARG">
            <summary>参数范围不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DATA_SIZE">
            <summary>数据大小不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_STEP">
            <summary>数据step不正确</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_CPUID">
            <summary>cpu不支持优化代码中的指令集</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_WARNING">
            <summary>警告</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_TIME_OUT">
            <summary>算法库超时</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_LIB_VERSION">
            <summary>算法版本号出错</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_MODEL_VERSION">
            <summary>模型版本号出错</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_GPU_MEM_ALLOC">
            <summary>GPU内存分配错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_FILE_NON_EXIST">
            <summary>文件不存在</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_NONE_STRING">
            <summary>字符串为空</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_IMAGE_CODEC">
            <summary>图像解码器错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_FILE_OPEN">
            <summary>打开文件错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_FILE_READ">
            <summary>文件读取错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_FILE_WRITE">
            <summary>文件写错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_FILE_READ_SIZE">
            <summary>文件读取大小错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_FILE_TYPE">
            <summary>文件类型错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_MODEL_TYPE">
            <summary>模型类型错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_MALLOC_MEM">
            <summary>分配内存错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_BIND_CORE_FAILED">
            <summary>线程绑核失败</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NE_IMG_FORMAT">
            <summary>噪声特性图像格式错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NE_FEATURE_TYPE">
            <summary>噪声特性类型错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NE_PROFILE_NUM">
            <summary>噪声特性个数错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NE_GAIN_NUM">
            <summary>噪声特性增益个数错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NE_GAIN_VAL">
            <summary>噪声曲线增益值输入错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NE_BIN_NUM">
            <summary>噪声曲线柱数错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NE_INIT_GAIN">
            <summary>噪声估计初始化增益设置错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NE_NOT_INIT">
            <summary>噪声估计未初始化</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_COLOR_MODE">
            <summary>颜色空间模式错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_ROI_NUM">
            <summary>图像ROI个数错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_ROI_ORI_PT">
            <summary>图像ROI原点错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_ROI_SIZE">
            <summary>图像ROI大小错误</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_GAIN_NOT_EXIST">
            <summary>输入的相机增益不存在(增益个数已达上限)</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_GAIN_BEYOND_RANGE">
            <summary>输入的相机增益不在范围内</summary>
        </member>
        <member name="F:MvCamCtrl.NET.CErrorDefine.MV_ALG_E_DENOISE_NP_BUF_SIZE">
            <summary>输入的噪声特性内存大小错误</summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_SAVE_IAMGE_TYPE">
            <summary>
            保存的图像格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_IAMGE_TYPE.MV_IMAGE_UNDEFINED">
            <summary>
            未定义类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_IAMGE_TYPE.MV_IMAGE_BMP">
            <summary>
            Bmp图像格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_IAMGE_TYPE.MV_IMAGE_JPEG">
            <summary>
            Jpeg图像格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_IAMGE_TYPE.MV_IMAGE_PNG">
            <summary>
            Png图像格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_IAMGE_TYPE.MV_IMAGE_TIF">
            <summary>
            Tif图像格式
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_SAVE_POINT_CLOUD_FILE_TYPE">
            <summary>
            ch:保存3D数据格式 | en:Save 3D file
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_POINT_CLOUD_FILE_TYPE.MV_POINT_CLOUD_FILE_UNDEFINED">
            <summary>
            未定义数据格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_POINT_CLOUD_FILE_TYPE.MV_POINT_CLOUD_FILE_PLY">
            <summary>
            PLY数据格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_POINT_CLOUD_FILE_TYPE.MV_POINT_CLOUD_FILE_CSV">
            <summary>
            CSV数据格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SAVE_POINT_CLOUD_FILE_TYPE.MV_POINT_CLOUD_FILE_OBJ">
            <summary>
            OBJ数据格式
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_IMG_ROTATION_ANGLE">
            <summary>
            旋转角度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_IMG_ROTATION_ANGLE.MV_IMAGE_ROTATE_90">
            <summary>
            旋转90度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_IMG_ROTATION_ANGLE.MV_IMAGE_ROTATE_180">
            <summary>
            旋转180度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_IMG_ROTATION_ANGLE.MV_IMAGE_ROTATE_270">
            <summary>
            旋转270度
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_IMG_FLIP_TYPE">
            <summary>
            图像翻转类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_IMG_FLIP_TYPE.MV_FLIP_VERTICAL">
            <summary>
            垂直方向翻转
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_IMG_FLIP_TYPE.MV_FLIP_HORIZONTAL">
            <summary>
            水平方向翻转
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_CC_GAMMA_TYPE">
            <summary>
            Gamma类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CC_GAMMA_TYPE.MV_CC_GAMMA_TYPE_NONE">
            <summary>
            不启用
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CC_GAMMA_TYPE.MV_CC_GAMMA_TYPE_VALUE">
            <summary>
            GAMMA值
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CC_GAMMA_TYPE.MV_CC_GAMMA_TYPE_USER_CURVE">
            <summary>
            GAMMA曲线，8位需要的长度：256*sizeof(unsigned char)
            10位需要的长度：1024*sizeof(unsigned short)
            12位需要的长度：4096*sizeof(unsigned short)
            16位需要的长度：65536*sizeof(unsigned short)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CC_GAMMA_TYPE.MV_CC_GAMMA_TYPE_LRGB2SRGB">
            <summary>
            线性RGB转非线性RGB
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_CC_GAMMA_TYPE.MV_CC_GAMMA_TYPE_SRGB2LRGB">
            <summary>
            非线性RGB转线性RGB
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_RECORD_FORMAT_TYPE">
            <summary>
            录像格式定义
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_RECORD_FORMAT_TYPE.MV_FORMAT_TYPE_UNDEFINED">
            <summary>
            未定义格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_RECORD_FORMAT_TYPE.MV_FORMAT_TYPE_AVI">
            <summary>
            AVI格式
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_IMAGE_RECONSTRUCTION_METHOD">
            <summary>
            分时曝光的图像处理方式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_IMAGE_RECONSTRUCTION_METHOD.MV_SPLIT_BY_LINE">
            <summary>
            源图像按行拆分成多张图像
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CSaveImageParam">
            <summary>
            图像参数信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CSaveImageParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImageParam.InImage">
            <summary>
            输入的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImageParam.OutImage">
            <summary>
            输出的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImageParam.ImageType">
            <summary>
            输出图片格式
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImageParam.JpgQuality">
            <summary>
            编码质量, (50-99]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImageParam.MethodValue">
            <summary>
            Bayer的插值方法 0-快速 1-均衡 2-最优（如果传入其它值则默认为最优）
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CSaveImgToFileParam">
            <summary>
            保存图像到文件参数信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CSaveImgToFileParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImgToFileParam.Image">
            <summary>
            输入的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImgToFileParam.ImageType">
            <summary>
            [IN]     输入图片格式
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImgToFileParam.Quality">
            <summary>
            [IN]     编码质量, (0-100]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImgToFileParam.ImagePath">
            <summary>
            [IN]     输入文件路径
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSaveImgToFileParam.MethodValue">
            <summary>
            [IN]     Bayer的插值方法 0-快速 1-均衡 2-最优（如果传入其它值则默认为最优）
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CSavePointCloudParam">
            <summary>
            像素点信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CSavePointCloudParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSavePointCloudParam.InImage">
            <summary>
            输入的图像数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSavePointCloudParam.OutImage">
            <summary>
            输出的图像数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSavePointCloudParam.LinePntNum">
            <summary>
            每一行点的数量
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSavePointCloudParam.LineNum">
            <summary>
            行数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSavePointCloudParam.PointCloudFileType">
            <summary>
            文件类型
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CRotateImageParam">
            <summary>
            旋转图像参数
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CRotateImageParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRotateImageParam.InImage">
            <summary>
            输入的图像数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRotateImageParam.OutImage">
            <summary>
            输出的图像数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRotateImageParam.RotationAngle">
            <summary>
            旋转角度
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CFlipImageParam">
            <summary>
            图像翻转参数信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CFlipImageParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFlipImageParam.InImage">
            <summary>
            输入的图像数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFlipImageParam.OutImage">
            <summary>
            输出的图像数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFlipImageParam.FlipType">
            <summary>
            翻转类型
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CPixelConvertParam">
            <summary>
            像素转换信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CPixelConvertParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CPixelConvertParam.InImage">
            <summary>
            输入的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CPixelConvertParam.OutImage">
            <summary>
            输出的图像信息
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CGammaParam">
            <summary>
            Gamma参数信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CGammaParam.GammaType">
            <summary>
            Gamma类型
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CGammaParam.GammaValue">
            <summary>
            Gamma值
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CGammaParam.GammaCurveBuf">
            <summary>
            Gamma曲线缓存
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CGammaParam.GammaCurveBufLen">
            <summary>
            Gamma曲线长度
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CCCMParam">
            <summary>
            CCM参数
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CCCMParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCCMParam.CCMEnable">
            <summary>
            [IN]     是否启用CCM
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCCMParam.CCMat">
            <summary>
            [IN]     CCM矩阵(-8192~8192)
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CCCMParamEx">
            <summary>
            CCM参数Ex
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CCCMParamEx.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCCMParamEx.CCMEnable">
            <summary>
            是否启用CCM
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCCMParamEx.CCMat">
            <summary>
            量化3x3矩阵
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCCMParamEx.CCMScale">
            <summary>
            量化系数（2的整数幂）
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CContrastParam">
            <summary>
            对比度调节参数
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CContrastParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CContrastParam.InImage">
            <summary>
            输入的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CContrastParam.OutImage">
            <summary>
            输出的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CContrastParam.ContrastFactor">
            <summary>
            对比度值，范围:[1, 10000]
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CSharpenParam">
            <summary>
            锐化参数
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CSharpenParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSharpenParam.InImage">
            <summary>
            输入的图像数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSharpenParam.OutImage">
            <summary>
            输出的图像数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSharpenParam.SharpenAmount">
            <summary>
            锐度调节强度，范围:[0, 500]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSharpenParam.SharpenRadius">
            <summary>
            锐度调节半径（半径越大，耗时越长），范围:[1, 21]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CSharpenParam.SharpenThreshold">
            <summary>
            锐度调节阈值，范围:[0, 255]
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CDecodeParam">
            <summary>
            解码参数
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CDecodeParam.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CDecodeParam.InImage">
            <summary>
            输入的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CDecodeParam.OutImage">
            <summary>
            输出的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CDecodeParam.FrameSpecInfo">
            <summary>
            水印信息
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CRecordParam">
            <summary>
            录像参数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRecordParam.Width">
            <summary>
            图像宽信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRecordParam.Height">
            <summary>
            图像高信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRecordParam.enPixelType">
            <summary>
            图像像素信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRecordParam.FrameRate">
            <summary>
            帧率fps(大于1/16)
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRecordParam.BitRate">
            <summary>
            码率kbps(128kbps-16Mbps)
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRecordParam.RecordFmtType">
            <summary>
            录像格式
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRecordParam.FilePath">
            <summary>
            录像文件存放路径
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CInputFrameInfo">
            <summary>
            输入帧信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CInputFrameInfo.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CInputFrameInfo.InImage">
            <summary>
            输入的图像信息
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CColor">
            <summary>
            辅助线的颜色
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CColor.Red">
            <summary>
            红色，根据像素颜色的相对深度，范围为[0.0 , 1.0]，代表着[0, 255]的颜色深度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CColor.Green">
            <summary>
            绿色，根据像素颜色的相对深度，范围为[0.0 , 1.0]，代表着[0, 255]的颜色深度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CColor.Blue">
            <summary>
            蓝色，根据像素颜色的相对深度，范围为[0.0 , 1.0]，代表着[0, 255]的颜色深度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CColor.Alpha">
            <summary>
            透明度，根据像素颜色的相对透明度，范围为[0.0 , 1.0] (此参数功能暂不支持)
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CPoint">
            <summary>
            自定义点的坐标信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CPoint.X">
            <summary>
            该点距离图像左边缘距离，根据图像的相对位置，范围为[0.0 , 1.0]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CPoint.Y">
            <summary>
            该点距离图像上边缘距离，根据图像的相对位置，范围为[0.0 , 1.0]
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CRectArea">
            <summary>
            矩形框区域信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CRectArea.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRectArea.Top">
            <summary>
            矩形上边缘距离图像上边缘的距离，根据图像的相对位置，范围为[0.0 , 1.0]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRectArea.Bottom">
            <summary>
            矩形下边缘距离图像下边缘的距离，根据图像的相对位置，范围为[0.0 , 1.0]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRectArea.Left">
            <summary>
            矩形左边缘距离图像左边缘的距离，根据图像的相对位置，范围为[0.0 , 1.0]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRectArea.Right">
            <summary>
            矩形右边缘距离图像右边缘的距离，根据图像的相对位置，范围为[0.0 , 1.0]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRectArea.LineWidth">
            <summary>
            辅助线宽度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CRectArea.Color">
            <summary>
            辅助线颜色
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CCircleArea">
            <summary>
            圆形框区域信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CCircleArea.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCircleArea.R1">
            <summary>
            宽向半径，根据图像的相对位置[0, 1.0]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCircleArea.R2">
            <summary>
            高向半径，根据图像的相对位置[0, 1.0]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCircleArea.LineWidth">
            <summary>
            辅助线宽度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCircleArea.Color">
            <summary>
            辅助线颜色信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CCircleArea.CenterPoint">
            <summary>
            圆心信息
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CLinesArea">
            <summary>
            线条辅助线信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CLinesArea.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CLinesArea.LineWidth">
            <summary>
            辅助线宽度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CLinesArea.Color">
            <summary>
            辅助线颜色信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CLinesArea.StartPoint">
            <summary>
            线条辅助线的起始点坐标
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CLinesArea.EndPoint">
            <summary>
            线条辅助线的终点坐标
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CReconstructImageParam">
            <summary>
            图像重组参数信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CReconstructImageParam.#ctor">
            <summary>构造函数</summary>
        </member>
        <member name="P:MvCamCtrl.NET.CReconstructImageParam.InImage">
            <summary>
            原始输入图像
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CReconstructImageParam.ExposureNum">
            <summary>
            曝光个数(1-8]
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CReconstructImageParam.ReconstructMethod">
            <summary>
            图像重构方式
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CReconstructImageParam.OutImages">
            <summary>
            输出的图像列表
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CInnerTool">
            <summary>
            内部使用的公共功能接口类
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CInnerTool.ByteToStruct(System.Byte[],System.Type)">
            <summary>
            Byte array to struct
            </summary>
            <param name="bytes">Byte array</param>
            <param name="type">Struct type</param>
            <returns>Struct object</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CInnerTool.StructToBytes``1(``0,System.Byte[])">
            <summary>
            Struct to Byte array
            </summary>
            <param name="obj">Struct object</param>
            <param name="byteArr">Byte</param>
            <returns>Bytes </returns>
        </member>
        <member name="M:MvCamCtrl.NET.CInnerTool.BytesTrimEnd(System.Byte[])">
            <summary>
            删除byte数组缓存去尾部空白区
            </summary>
            <param name="inputStream">字符串</param>
            <returns></returns>
        </member>
        <member name="M:MvCamCtrl.NET.CInnerTool.IsMonoPixel(MvCamCtrl.NET.MvGvspPixelType)">
            <summary>
            判断像素是否为Mono格式
            </summary>
            <param name="enPixelType"></param>
            <returns></returns>
        </member>
        <member name="M:MvCamCtrl.NET.CInnerTool.IsColorPixel(MvCamCtrl.NET.MvGvspPixelType)">
            <summary>
            判断图像格式是否为彩色格式
            </summary>
            <param name="enPixelType"></param>
            <returns></returns>
        </member>
        <member name="M:MvCamCtrl.NET.CInnerTool.IsHBPixelType(MvCamCtrl.NET.MvGvspPixelType)">
            <summary>
            判断是否是HB格式
            </summary>
            <param name="enPixelType">像素格式</param>
            <returns></returns>
        </member>
        <member name="M:MvCamCtrl.NET.CInnerTool.GetPixelSize(MvCamCtrl.NET.MvGvspPixelType,System.UInt32,System.UInt32)">
            <summary>
            获取图像大小
            </summary>
            <param name="enType">像素格式</param>
            <param name="nWidth">图像宽度</param>
            <param name="nHeight">图像高度</param>
            <returns></returns>
        </member>
        <member name="M:MvCamCtrl.NET.CInnerTool.GetPixelSize(MvCamCtrl.NET.CImage)">
            <summary>
            获取图像大小
            </summary>
            <param name="pcImageInfo">图像信息</param>
            <returns>返回图像大小</returns>
        </member>
        <member name="T:MvCamCtrl.NET.cbXmlUpdatedelegate">
            <summary>
            Xml Update callback(Interfaces not recommended)
            </summary>
            <param name="enType">Node type</param>
            <param name="pstFeature">Current node feature structure</param>
            <param name="pstNodesList">Nodes list</param>
            <param name="pUser">User defined variable</param>
        </member>
        <member name="T:MvCamCtrl.NET.MV_XML_AccessMode">
            <summary>
            节点的读写性
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_AccessMode.AM_NI">
            <summary>
            未实现
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_AccessMode.AM_NA">
            <summary>
            不可获取
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_AccessMode.AM_WO">
            <summary>
            只写
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_AccessMode.AM_RO">
            <summary>
            只读
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_AccessMode.AM_RW">
            <summary>
            可读可写
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_AccessMode.AM_Undefined">
            <summary>
            未定义
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_AccessMode.AM_CycleDetect">
            <summary>
            内部用于AccessMode循环检测
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_XML_InterfaceType">
            <summary>
            每个节点对应的接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IValue">
            <summary>
            IValue接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IBase">
            <summary>
            IBase接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IInteger">
            <summary>
            IInteger接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IBoolean">
            <summary>
            IBoolean接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_ICommand">
            <summary>
            ICommand接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IFloat">
            <summary>
            IFloat接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IString">
            <summary>
            IString接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IRegister">
            <summary>
            IRegister接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_ICategory">
            <summary>
            ICategory接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IEnumeration">
            <summary>
            IEnumeration接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IEnumEntry">
            <summary>
            IEnumEntry接口类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_XML_InterfaceType.IFT_IPort">
            <summary>
            IPort接口类型
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CIntValue">
            <summary>
            整型参数信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CIntValue.CurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CIntValue.Max">
            <summary>
            最大值
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CIntValue.Min">
            <summary>
            最小值
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CIntValue.Inc">
            <summary>
            步进值
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CEnumValue">
            <summary>
            枚举型参数信息
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CEnumValue.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CEnumValue.CurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CEnumValue.SupportedNum">
            <summary>
            所支持的元素数量
            </summary>// 当前值
        </member>
        <member name="P:MvCamCtrl.NET.CEnumValue.SupportValue">
            <summary>
            所支持的元素
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CEnumEntry">
            <summary>枚举类型指定条目信息</summary>
        </member>
        <member name="P:MvCamCtrl.NET.CEnumEntry.Value">
            <summary>指定值</summary>
        </member>
        <member name="P:MvCamCtrl.NET.CEnumEntry.Symbolic">
            <summary>显示名</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CFloatValue">
            <summary>
            浮点型参数信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFloatValue.CurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFloatValue.Max">
            <summary>
            最大值
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFloatValue.Min">
            <summary>
            最小
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CStringValue">
            <summary>
            字符串型参数信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CStringValue.CurValue">
            <summary>
            当前值
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CStringValue.MaxLength">
            <summary>
            字符串最大长度
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CFileAccess">
            <summary>
             文件存取
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFileAccess.pUserFileName">
            <summary>
            用户文件名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFileAccess.pDevFileName">
            <summary>
            设备文件名
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CFileAccessEx">
            <summary>
             设备文件数据存取
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFileAccessEx.pUserFileBuf">
            <summary>
            用户数据缓存
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFileAccessEx.nFileBufSize">
            <summary>
            用户数据缓存大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFileAccessEx.nFileBufLen">
            <summary>
            文件实际缓存大小
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFileAccessEx.pDevFileName">
            <summary>
            设备文件名
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CFileAccessProgress">
            <summary>
            文件存取进度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFileAccessProgress.nCompleted">
            <summary>
            已完成的长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFileAccessProgress.nTotal">
            <summary>
            总长度
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.cbOutputExdelegate">
            <summary>
            Grab callback
            </summary>
            <param name="pData">Image data</param>
            <param name="pFrameInfo">Frame info</param>
            <param name="pUser">User defined variable</param>
        </member>
        <member name="T:MvCamCtrl.NET.MV_GRAB_STRATEGY">
            <summary>
            取流策略
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GRAB_STRATEGY.MV_GrabStrategy_OneByOne">
            <summary>
            从旧到新一帧一帧的获取图像（默认为该策略）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GRAB_STRATEGY.MV_GrabStrategy_LatestImagesOnly">
            <summary>
            获取列表中最新的一帧图像（同时清除列表中的其余图像）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GRAB_STRATEGY.MV_GrabStrategy_LatestImages">
            <summary>
            获取列表中最新的图像，个数由OutputQueueSize决定，范围为1-ImageNodeNum，设置成1等同于LatestImagesOnly，设置成ImageNodeNum等同于OneByOne
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_GRAB_STRATEGY.MV_GrabStrategy_UpcomingImage">
            <summary>
            等待下一帧图像
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MvGvspPixelType">
            <summary>
            像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Undefined">
            <summary>
            未定义像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono1p">
            <summary>
            Mono1p
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono2p">
            <summary>
            Mono2p
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono4p">
            <summary>
            Mono4p
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono8">
            <summary>
            Mono8
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono8_Signed">
            <summary>
            Mono8_Signed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono10">
            <summary>
            Mono10
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono10_Packed">
            <summary>
            Mono10_Packed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono12">
            <summary>
            Mono12
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono12_Packed">
            <summary>
            Mono12_Packed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono14">
            <summary>
            Mono14
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Mono16">
            <summary>
            Mono16
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGR8">
            <summary>
            BayerGR8
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerRG8">
            <summary>
            BayerRG8
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGB8">
            <summary>
            BayerGB8
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerBG8">
            <summary>
            BayerBG8
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerRBGG8">
            <summary>
            BayerRBGG8
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGR10">
            <summary>
            BayerGR10
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerRG10">
            <summary>
            BayerRG10
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGB10">
            <summary>
            BayerGB10
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerBG10">
            <summary>
            BayerBG10
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGR12">
            <summary>
            BayerGR12
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerRG12">
            <summary>
            BayerRG12
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGB12">
            <summary>
            BayerGB12
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerBG12">
            <summary>
            BayerBG12
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGR10_Packed">
            <summary>
            BayerGR10_Packed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerRG10_Packed">
            <summary>
            BayerRG10_Packed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGB10_Packed">
            <summary>
            BayerGB10_Packed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerBG10_Packed">
            <summary>
            BayerBG10_Packed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGR12_Packed">
            <summary>
            BayerGR12_Packed
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerRG12_Packed">
            <summary>BayerRG12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGB12_Packed">
            <summary>BayerGB12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerBG12_Packed">
            <summary>BayerBG12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGR16">
            <summary>BayerGR16</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerRG16">
            <summary>BayerRG16</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerGB16">
            <summary>BayerGB16</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BayerBG16">
            <summary>BayerBG16</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB8_Packed">
            <summary>RGB8_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BGR8_Packed">
            <summary>BGR8_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGBA8_Packed">
            <summary>RGBA8_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BGRA8_Packed">
            <summary>BGRA8_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB10_Packed">
            <summary>RGB10_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BGR10_Packed">
            <summary>BGR10_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB12_Packed">
            <summary>RGB12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BGR12_Packed">
            <summary>BGR12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB16_Packed">
            <summary>RGB16_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BGR16_Packed">
            <summary>BGR16_Packed/// </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGBA16_Packed">
            <summary>RGBA16_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BGRA16_Packed">
            <summary>BGRA16_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB10V1_Packed">
            <summary>RGB10V1_Packe</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB10V2_Packed">
            <summary>RGB10V2_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB12V1_Packed">
            <summary>RGB12V1_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB565_Packed">
            <summary>RGB565_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_BGR565_Packed">
            <summary>BGR565_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YUV411_Packed">
            <summary>YUV411_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YUV422_Packed">
            <summary>YUV422_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YUV422_YUYV_Packed">
            <summary>YUV422_YUYV_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YUV444_Packed">
            <summary>YUV444_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR8_CBYCR">
            <summary>YCBCR8_CBYCR</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR422_8">
            <summary>YCBCR422_8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR422_8_CBYCRY">
            <summary>YCBCR422_8_CBYCRY</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR411_8_CBYYCRYY">
            <summary>YCBCR411_8_CBYYCRYY</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR601_8_CBYCR">
            <summary>YCBCR601_8_CBYCR</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR601_422_8">
            <summary>YCBCR601_422_8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR601_422_8_CBYCRY">
            <summary>YCBCR601_422_8_CBYCRY</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR601_411_8_CBYYCRYY">
            <summary>YCBCR601_411_8_CBYYCRYY</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR709_8_CBYCR">
            <summary>YCBCR709_8_CBYCR</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR709_422_8">
            <summary>YCBCR709_422_8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR709_422_8_CBYCRY">
            <summary>YCBCR709_422_8_CBYCRY</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YCBCR709_411_8_CBYYCRYY">
            <summary>YCBCR709_411_8_CBYYCRYY</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YUV420SP_NV12">
            <summary>YUV420SP_NV12</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_YUV420SP_NV21">
            <summary>YUV420SP_NV21</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB8_Planar">
            <summary>RGB8_Planar</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB10_Planar">
            <summary>RGB10_Planar</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB12_Planar">
            <summary>RGB12_Planar</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_RGB16_Planar">
            <summary>RGB16_Planar</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Jpeg">
            <summary>Jpeg</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_ABC32f">
            <summary>Coord3D_ABC32f</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_ABC32f_Planar">
            <summary>Coord3D_ABC32f_Planar</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_AC32f">
            <summary>Coord3D_AC32f</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_COORD3D_DEPTH_PLUS_MASK">
            <summary>COORD3D_DEPTH_PLUS_MASK</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_ABC32">
            <summary>Coord3D_ABC32</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_AB32f">
            <summary>Coord3D_AB32f</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_AB32">
            <summary>Coord3D_AB32</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_AC32f_64">
            <summary>Coord3D_AC32f_64</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_AC32f_Planar">
            <summary>Coord3D_AC32f_Planar</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_AC32">
            <summary>Coord3D_AC32</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_A32f">
            <summary>Coord3D_A32f</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_A32">
            <summary>Coord3D_A32</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_C32f">
            <summary>Coord3D_C32f</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_C32">
            <summary>Coord3D_C32</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_ABC16">
            <summary>Coord3D_ABC16</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Coord3D_C16">
            <summary>Coord3D_C16</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_Float32">
            <summary>Float32</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_Mono8">
            <summary>HB_Mono8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_Mono10">
            <summary>HB_Mono10</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_Mono10_Packed">
            <summary>HB_Mono10_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_Mono12">
            <summary>HB_Mono12</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_Mono12_Packed">
            <summary>HB_Mono12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_Mono16">
            <summary>HB_Mono16</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGR8">
            <summary>HB_BayerGR8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerRG8">
            <summary>HB_BayerRG8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGB8">
            <summary>HB_BayerGB8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerBG8">
            <summary>HB_BayerBG8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerRBGG8">
            <summary>HB_BayerRBGG8</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGR10">
            <summary>HB_BayerGR10</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerRG10">
            <summary>HB_BayerRG10</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGB10">
            <summary>HB_BayerGB10</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerBG10">
            <summary>HB_BayerBG10</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGR12">
            <summary>HB_BayerGR12</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerRG12">
            <summary>HB_BayerRG12</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGB12">
            <summary>HB_BayerGB12</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerBG12">
            <summary>HB_BayerBG12</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGR10_Packed">
            <summary>HB_BayerGR10_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerRG10_Packed">
            <summary>HB_BayerRG10_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGB10_Packed">
            <summary>HB_BayerGB10_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerBG10_Packed">
            <summary>HB_BayerBG10_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGR12_Packed">
            <summary>HB_BayerGR12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerRG12_Packed">
            <summary>HB_BayerRG12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerGB12_Packed">
            <summary>HB_BayerGB12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BayerBG12_Packed">
            <summary>HB_BayerBG12_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_YUV422_Packed">
            <summary>HB_YUV422_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_YUV422_YUYV_Packed">
            <summary>HB_YUV422_YUYV_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_RGB8_Packed">
            <summary>HB_RGB8_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BGR8_Packed">
            <summary>HB_BGR8_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_RGBA8_Packed">
            <summary>HB_RGBA8_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BGRA8_Packed">
            <summary>HB_BGRA8_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_RGB16_Packed">
            <summary>HB_RGB16_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BGR16_Packed">
            <summary>HB_BGR16_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_RGBA16_Packed">
            <summary>HB_RGBA16_Packed</summary>
        </member>
        <member name="F:MvCamCtrl.NET.MvGvspPixelType.PixelType_Gvsp_HB_BGRA16_Packed">
            <summary>HB_BGRA16_Packed</summary>
        </member>
        <member name="T:MvCamCtrl.NET.CChunkDataContent">
            <summary>
            chunk内容
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CChunkDataContent.#ctor">
            <summary>
            构造函数
            </summary>
            <returns></returns>
        </member>
        <member name="M:MvCamCtrl.NET.CChunkDataContent.Finalize">
            <summary>
            析构函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CChunkDataContent.ChunkAddr">
            <summary>
            Chunk数据地址
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CChunkDataContent.ChunkData">
            <summary>
            Chunk数据
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CChunkDataContent.ChunkID">
            <summary>
            ChunkID
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CChunkDataContent.ChunkLen">
            <summary>
            Chunk数据长度
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CChunk">
            <summary>
            Chunk类
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CChunk.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CChunk.ChunkWidth">
            <summary>
            Chunk宽度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CChunk.ChunkHeight">
            <summary>
            Chunk高度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CChunk.UnparsedChunkNum">
            <summary>
            未解析的Chunk数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CChunk.ChunkDataContent">
            <summary>
            Chunk内容
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CImage">
            <summary>
            图像类
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.CopyMemory(System.IntPtr,System.IntPtr,System.UInt32)">
            <summary>
            内存拷贝
            </summary>
            <param name="dest">目标缓存</param>
            <param name="src">源缓存</param>
            <param name="count">拷贝大小</param>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.pBufAddr">
            <summary>
            Buffer Address
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.pBufData">
            <summary>
            图像数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.nBufSize">
            <summary>
            Buffer Size(分配内存时需要用到)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.nFrameLen">
            <summary>
            帧长度
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.enPixelType">
            <summary>
            像素格式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.nWidth">
            <summary>
            图像宽
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.nHeight">
            <summary>
            图像高
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.bMustBeDisposed">
            <summary>
            是否需要在C#中释放pBufAddr
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.bHBPixelType">
            <summary>
            是否是HB格式(用户可能需要HB解码前的buffer数据)
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.nExtendWidth">
            <summary>
            图像宽扩展
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CImage.nExtendHeight">
            <summary>
            图像高扩展
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.#ctor">
            <summary>
            无参构造函数
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.#ctor(System.UInt16,System.UInt16,MvCamCtrl.NET.MvGvspPixelType)">
            <summary>
            录像时只需要知道图像的宽高和像素格式
            </summary>
            <param name="width">图像宽</param>
            <param name="height">图像高</param>
            <param name="pixel_type">像素格式</param>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.#ctor(System.IntPtr,MvCamCtrl.NET.MvGvspPixelType,System.UInt32,System.UInt16,System.UInt16,System.UInt32,System.UInt32)">
            <summary>
            构造函数(主要用于GetImageBuffer接口)
            </summary>
            <param name="pBufAddr">图像缓存地址</param>
            <param name="enPixelType">图像像素格式</param>
            <param name="nFrameLen">图像实际长度</param>
            <param name="nHeight">图像高度</param>
            <param name="nWidth">图像宽度</param>
            <param name="nHeightEx">图像高度扩展</param>
            <param name="nWidthEx">图像宽度扩展</param>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.#ctor(System.Byte[],MvCamCtrl.NET.MvGvspPixelType,System.UInt32,System.UInt16,System.UInt16,System.UInt32,System.UInt32)">
            <summary>
            构造函数(主要用于GetOneFrameTimeout接口)
            </summary>
            <param name="pBufData">图像缓存</param>
            <param name="enPixelType">图像像素格式</param>
            <param name="nFrameLen">图像实际长度</param>
            <param name="nHeight">图像高度</param>
            <param name="nWidth">图像宽度</param>
            <param name="nHeightEx">图像高度扩展</param>
            <param name="nWidthEx">图像宽度扩展</param>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.#ctor(MvCamCtrl.NET.CImage)">
            <summary>
            构造函数
            </summary>
            <param name="pcOldImage"></param>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.Finalize">
            <summary>
            析构函数
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.Clone">
            <summary>
            CImage的拷贝函数
            </summary>
            <returns>返回拷贝后的CImage</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.AllocateUnmanagedMemory(System.UInt32)">
            <summary>
            Allocate unmanaged memory.
            </summary>
            <param name="nPreBufSize">Memory Size</param>
            <returns></returns>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.UpdateImageInfo(System.IntPtr,System.UInt32)">
            <summary>
            更新图像信息(SaveImageEx、SavePointCloudData接口使用)
            </summary>
            <param name="pBufAddr"></param>
            <param name="nFrameLen"></param>
        </member>
        <member name="M:MvCamCtrl.NET.CImage.UpdateImageInfo(System.IntPtr,System.UInt32,System.UInt16,System.UInt16,MvCamCtrl.NET.MvGvspPixelType,System.UInt32,System.UInt32)">
            <summary>
            更新图像信息
            </summary>
            <param name="pBufAddr">图像缓存地址</param>
            <param name="nFrameLen">图像实际长度</param>
            <param name="nHeight">图像高度</param>
            <param name="nWidth">图像宽度</param>
            <param name="enPixelType">像素格式</param>
            <param name="nHeightEx">图像高度扩展</param>
            <param name="nWidthEx">图像宽度扩展</param>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.ImageAddr">
            <summary>
            获取图像缓存
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.ImageData">
            <summary>
            获取图像缓存(byte数组形式)
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.ImageSize">
            <summary>
            获取和设置图像大小
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.FrameLen">
            <summary>
            图像的实际长度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.PixelType">
            <summary>
            获取图像像素格式
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.Width">
            <summary>
            图像宽度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.Height">
            <summary>
            图像高度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.ExtendWidth">
            <summary>
            图像宽度扩展
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.ExtendHeight">
            <summary>
            图像高度扩展
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImage.IsHBPixelType">
            <summary>
            是否是HB格式的图像数据
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CImageEx">
            <summary>
            图像类(仅包含图像基本信息，不含图像缓存)
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImageEx.Width">
            <summary>
            图像宽
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImageEx.Height">
            <summary>
            图像高
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImageEx.PixelType">
            <summary>
            像素格式
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImageEx.FrameLen">
            <summary>
            帧长度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImageEx.nExtendWidth">
            <summary>
            图像宽扩展
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CImageEx.nExtendHeight">
            <summary>
            图像高扩展
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CFrameSpecInfo">
            <summary>
            水印信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.SecondCount">
            <summary>
            [OUT]     秒数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.CycleCount">
            <summary>
            [OUT]     周期数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.CycleOffset">
            <summary>
            [OUT]     周期偏移量
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.Gain">
            <summary>
            [OUT]     增益
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.ExposureTime">
            <summary>
            [OUT]     曝光时间
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.AverageBrightness">
            <summary>
            [OUT]     平均亮度
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.Red">
            <summary>
            [OUT]     红色
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.Green">
            <summary>
            [OUT]     绿色
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.Blue">
            <summary>
            [OUT]     蓝色
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.FrameCounter">
            <summary>
            [OUT]     总帧数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.TriggerIndex">
            <summary>
            [OUT]     触发计数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.Input">
            <summary>
            [OUT]     输入
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.Output">
            <summary>
            [OUT]     输出
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.OffsetX">
            <summary>
            [OUT]     水平偏移量
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.OffsetY">
            <summary>
            [OUT]     垂直偏移量
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.FrameWidth">
            <summary>
            [OUT]     水印宽
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.FrameHeight">
            <summary>
            [OUT]     水印高
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.FrameNum">
            <summary>
            帧号
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.DevTimeStampHigh">
            <summary>
            时间戳高32位
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.DevTimeStampLow">
            <summary>
            时间戳低32位
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.HostTimeStamp">
            <summary>
            主机生成的时间戳
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameSpecInfo.LostPacket">
            <summary>
            丢包数
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CFrameout">
            <summary>
            帧信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFrameout.pcImage">
            <summary>
            图像
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFrameout.pcChunk">
            <summary>
            Chunk
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CFrameout.pcFrameSpec">
            <summary>
            水印
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CFrameout.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameout.Image">
            <summary>
            图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameout.Chunk">
            <summary>
            Chunk信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameout.FrameSpec">
            <summary>
            水印信息
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CFrameoutEx">
            <summary>
            帧信息(不含图像缓存)
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CFrameoutEx.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameoutEx.pcImageInfoEx">
            <summary>
            图像信息(仅包含图像基本信息，不含图像缓存)
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameoutEx.Chunk">
            <summary>
            Chunk信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CFrameoutEx.FrameSpec">
            <summary>
            水印信息
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CDisplayFrameInfo">
            <summary>
            显示帧信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CDisplayFrameInfo.hWnd">
            <summary>
            显示窗口句柄
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CDisplayFrameInfo.pcInImage">
            <summary>
            输入的图像数据
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CDisplayFrameInfo.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CDisplayFrameInfo.WindowHandle">
            <summary>
            窗口句柄
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CDisplayFrameInfo.Image">
            <summary>
            要显示的图像信息
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CDisplayFrameInfoEx">
            <summary>
            显示帧信息扩展
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CDisplayFrameInfoEx.hWnd">
            <summary>
            显示窗口句柄
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CDisplayFrameInfoEx.pBufAddr">
            <summary>
            缓存数据
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CDisplayFrameInfoEx.pcInImageEx">
            <summary>
            输入的图像数据
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CDisplayFrameInfoEx.#ctor">
            <summary>
            构造函数
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CDisplayFrameInfoEx.WindowHandle">
            <summary>
            窗口句柄
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CDisplayFrameInfoEx.ImageEx">
            <summary>
            要显示的图像信息
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CDisplayFrameInfoEx.ImageAddr">
            <summary>
            获取图像缓存
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_SORT_METHOD">
            <summary>
            排序方式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SORT_METHOD.SORTMETHOD_SERIALNUMBER">
            <summary>
            按序列号排序
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SORT_METHOD.SORTMETHOD_USERID">
            <summary>
            按用户自定义名字排序
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SORT_METHOD.SORTMETHOD_CURRENTIP_ASC">
            <summary>
            按当前IP地址排序（升序）
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_SORT_METHOD.SORTMETHOD_CURRENTIP_DESC">
            <summary>
            按当前IP地址排序（降序）
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.MV_ACCESS_MODE">
            <summary>
            设备的访问模式
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_ACCESS_MODE.MV_ACCESS_EXCLUSIVE">
            <summary>
            独占权限，其他APP只允许读CCP寄存器
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_ACCESS_MODE.MV_ACCESS_EXCLUSIVEWITHSWITCH">
            <summary>
            可以从5模式下抢占权限，然后以独占权限打开
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_ACCESS_MODE.MV_ACCESS_CONTROL">
            <summary>
            控制权限，其他APP允许读所有寄存器
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_ACCESS_MODE.MV_ACCESS_CONTROLWITHSWITCH">
            <summary>
            可以从5的模式下抢占权限，然后以控制权限打开
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_ACCESS_MODE.MV_ACCESS_CONTROLSWITCHENABLE">
            <summary>
            以可被抢占的控制权限打开
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_ACCESS_MODE.MV_ACCESS_CONTROLSWITCHENABLEWITHKEY">
            <summary>
            可以从5的模式下抢占权限，然后以可被抢占的控制权限打开
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.MV_ACCESS_MODE.MV_ACCESS_MONITOR">
            <summary>
            读模式打开设备，适用于控制权限下
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CCameraInfo">
            <summary>
            相机信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraInfo.nMajorVer">
            <summary>
            版本号高位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraInfo.nMinorVer">
            <summary>
            版本号低位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraInfo.nMacAddrHigh">
            <summary>
            MAC地址高位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraInfo.nMacAddrLow">
            <summary>
            MAC地址低位
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCameraInfo.nTLayerType">
            <summary>
            相机类型
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CCameraInfo.IsTextUTF8(System.Byte[])">
            <summary>
            判断字符编码
            </summary>
            <param name="inputStream"></param>
            <returns></returns>
        </member>
        <member name="T:MvCamCtrl.NET.CGigECameraInfo">
            <summary>
            GigE相机信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nIpCfgOption">
            <summary>
            支持的IP配置：bit31-static bit30-DHCP bit29-LLA
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nIpCfgCurrent">
            <summary>
            当前IP配置
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nCurrentIp">
            <summary>
            当前IP
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nCurrentSubNetMask">
            <summary>
            当前子网掩码
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nDefultGateWay">
            <summary>
            默认网关
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nNetExport">
            <summary>
            网卡IP
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.chManufacturerName">
            <summary>
            厂商名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.chModelName">
            <summary>
            相机型号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.chDeviceVersion">
            <summary>
            相机版本
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.chManufacturerSpecificInfo">
            <summary>
            相机厂商信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.chSerialNumber">
            <summary>
            相机序列号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.chUserDefinedName">
            <summary>
            用户自定义名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nHostIp">
            <summary>
            设备主机ip
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nDeviceType">
            <summary>
            设备类型
            值为0:普通设备 值为1:虚拟采集卡上的设备 值为2:自研网卡上的设备
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nMulticastIp">
            <summary>
            组播ip
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGigECameraInfo.nMulticastPort">
            <summary>
            组播port
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CGigECameraInfo.UserDefinedName">
            <summary>
            获取转码后的用户自定义名称
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CUSBCameraInfo">
            <summary>
            USB相机信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.nbcdUSB">
            <summary>
            支持的USB协议
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.CrtlInEndPoint">
            <summary>
            控制输入端点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.CrtlOutEndPoint">
            <summary>
            控制输出端点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.StreamEndPoint">
            <summary>
            流端点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.EventEndPoint">
            <summary>
            事件端点
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.idVendor">
            <summary>
            供应商ID号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.idProduct">
            <summary>
            产品ID号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.nDeviceNumber">
            <summary>
            设备索引号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.chDeviceGUID">
            <summary>
            设备GUID号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.chVendorName">
            <summary>
            供应商名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.chModelName">
            <summary>
            型号名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.chFamilyName">
            <summary>
            家族名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.chDeviceVersion">
            <summary>
            设备版本号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.chManufacturerName">
            <summary>
            制造商名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.chSerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.chUserDefinedName">
            <summary>
            用户自定义名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.nDeviceAddress">
            <summary>
            设备地址
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CUSBCameraInfo.nDeviceType">
            <summary>
            设备类型
            值为0:普通设备 值为1:虚拟采集卡上的设备
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CUSBCameraInfo.UserDefinedName">
            <summary>
            获取转码后的用户自定义名称
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CCamLCameraInfo">
            <summary>
            Camera Link相机信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCamLCameraInfo.chPortID">
            <summary>
            端口号ID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCamLCameraInfo.chModelName">
            <summary>
            Model Name
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCamLCameraInfo.chFamilyName">
            <summary>
            家族名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCamLCameraInfo.chDeviceVersion">
            <summary>
            设备版本
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCamLCameraInfo.chManufacturerName">
            <summary>
            制造商名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CCamLCameraInfo.chSerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CGenTLIFInfo">
            <summary>
            采集卡信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLIFInfo.chInterfaceID">
            <summary>
            采集卡ID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLIFInfo.chTLType">
            <summary>
            采集卡类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLIFInfo.chDisplayName">
            <summary>
            采集卡名称
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLIFInfo.nCtiIndex">
            <summary>
            Cti库的索引
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CGenTLDevInfo">
            <summary>
            采集卡上的相机信息
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chDeviceID">
            <summary>
            设备ID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chDeviceVersion">
            <summary>
            设备版本
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chDisplayName">
            <summary>
            设备显示名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chInterfaceID">
            <summary>
            采集卡ID
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chModelName">
            <summary>
            模型名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chSerialNumber">
            <summary>
            序列号
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chTLType">
            <summary>
            传输类型
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chUserDefinedName">
            <summary>
            用户自定义名字
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.chVendorName">
            <summary>
            Vendor名
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CGenTLDevInfo.nCtiIndex">
            <summary>
            cti文件序号
            </summary>
        </member>
        <member name="P:MvCamCtrl.NET.CGenTLDevInfo.UserDefinedName">
            <summary>
            获取转码后的用户自定义名称
            </summary>
        </member>
        <member name="T:MvCamCtrl.NET.CSystem">
            <summary>
            系统枚举类
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CSystem.MV_UNKNOW_DEVICE">
            <summary>
            未知设备类型 unchecked():发生溢出不抛出异常
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CSystem.MV_GIGE_DEVICE">
            <summary>
            GigE设备
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CSystem.MV_1394_DEVICE">
            <summary>
            1394-a/b设备
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CSystem.MV_USB_DEVICE">
            <summary>
            USB设备
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CSystem.MV_CAMERALINK_DEVICE">
            <summary>
            CameraLink设备
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CSystem.MV_VIR_GIGE_DEVICE">
            <summary>
            虚拟GigE设备
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CSystem.MV_VIR_USB_DEVICE">
            <summary>
            虚拟USB设备
            </summary>
        </member>
        <member name="F:MvCamCtrl.NET.CSystem.MV_GENTL_GIGE_DEVICE">
            <summary>
            自研网卡下GigE设备
            </summary>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.GIGE_SetEnumDevTimeout(System.UInt32)">
            <summary>
            设置GIGE枚举超时时间，仅支持GigE协议，范围 0-  UINT_MAX
            </summary>
            <param name="nMilTimeout">超时时间(ms)，默认100ms</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.GIGE_SetDiscoveryMode(System.UInt32)">
            <summary>
            设置GIGE枚举命令的回复包类型
            </summary>
            <param name="nMode">回复包类型（默认广播），0-单播，1-广播</param>
            <returns>成功,返回MV_OK,失败,返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.GetSDKVersion">
            <summary>
            获取SDK版本信息
            </summary>
            <returns>返回版本号，如V4.1.0.2</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumerateTls">
            <summary>
            获取支持的传输层
            </summary>
            <returns>支持的传输层编号</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumDevices(System.UInt32,System.Collections.Generic.List{MvCamCtrl.NET.CCameraInfo}@)">
            <summary>
            枚举设备
            </summary>
            <param name="nType">枚举传输层</param>
            <param name="ltCameraList">设备列表</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumDevices(System.UInt32)">
            <summary>
            枚举设备
            </summary>
            <param name="nType">枚举传输层</param>
            <returns>返回设备列表信息</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumDevicesEx(System.UInt32,System.Collections.Generic.List{MvCamCtrl.NET.CCameraInfo}@,System.String)">
            <summary>
            根据厂商名字枚举设备
            </summary>
            <param name="nType">枚举传输层</param>
            <param name="ltCameraList">设备列表</param>
            <param name="strManufacturerName">厂商名字</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumDevicesEx(System.UInt32,System.String)">
            <summary>
            根据厂商名字枚举设备
            </summary>
            <param name="nType">枚举传输层</param>
            <param name="strManufacturerName">厂商名字</param>
            <returns>返回设备列表信息</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumDevicesEx2(System.UInt32,System.Collections.Generic.List{MvCamCtrl.NET.CCameraInfo}@,System.String,MvCamCtrl.NET.MV_SORT_METHOD)">
            <summary>
            枚举设备扩展（可指定排序方式枚举、根据厂商名字过滤）
            </summary>
            <param name="nType">枚举传输层</param>
            <param name="ltCameraList">设备列表</param>
            <param name="strManufacturerName">厂商名字（可传NULL，即不过滤）</param>
            <param name="enSortMethod">排序方式</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumDevicesEx2(System.UInt32,System.String,MvCamCtrl.NET.MV_SORT_METHOD)">
            <summary>
            枚举设备扩展（可指定排序方式枚举、根据厂商名字过滤）
            </summary>
            <param name="nType">枚举传输层</param>
            <param name="strManufacturerName">厂商名字（可传NULL，即不过滤）</param>
            <param name="enSortMethod">排序方式</param>
            <returns>返回设备列表信息</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumInterfaceByGenTL(System.Collections.Generic.List{MvCamCtrl.NET.CGenTLIFInfo}@,System.String)">
            <summary>
            通过GenTL枚举Interfaces
            </summary>
            <param name="ltInterfaceList">Interfaces列表</param>
            <param name="strGenTLPath">GenTL的cti文件路径</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumInterfaceByGenTL(System.String)">
            <summary>
            通过GenTL枚举Interfaces
            </summary>
            <param name="strGenTLPath">GenTL的cti文件路径</param>
            <returns>返回Interfaces列表</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumCameraListByGenTL(MvCamCtrl.NET.CGenTLIFInfo@,System.Collections.Generic.List{MvCamCtrl.NET.CGenTLDevInfo}@)">
            <summary>
            通过GenTL Interface枚举设备
            </summary>
            <param name="pcIFInfo">Interface信息</param>
            <param name="ltCameraList">设备列表</param>
            <returns>成功，返回MV_OK；错误，返回错误码</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.EnumCameraListByGenTL(MvCamCtrl.NET.CGenTLIFInfo@)">
            <summary>
            通过GenTL Interface枚举设备
            </summary>
            <param name="pcIFInfo">Interface信息</param>
            <returns>成功, 返回设备列表;</returns>
        </member>
        <member name="M:MvCamCtrl.NET.CSystem.IsDeviceAccessible(MvCamCtrl.NET.CCameraInfo@,MvCamCtrl.NET.MV_ACCESS_MODE)">
            <summary>
            设备是否可达
            </summary>
            <param name="pcDevInfo">设备信息</param>
            <param name="enAccessMode">访问权限</param>
            <returns>可达，返回true；不可达，返回false</returns>
        </member>
    </members>
</doc>
