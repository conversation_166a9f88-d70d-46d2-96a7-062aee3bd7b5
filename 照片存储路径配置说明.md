# 照片存储路径配置说明

## 系统配置文件夹结构

根据您提供的配置信息，系统有以下几个关键文件夹：

### 1. 照片存储地址 (SaveFolder)
- **配置路径**: `E:\AI_images`
- **用途**: 存储源文件和旋转照片
- **说明**: 这是主要的照片存储目录，用于保存原始照片和旋转后的照片

### 2. AI检测文件夹 (AiSource)
- **配置路径**: `E:\SourceImage`
- **用途**: 裁剪后的输出目录
- **说明**: 裁剪后的照片会输出到这个文件夹，供AI检测使用

### 3. AI检测结果文件夹 (AiResult)
- **配置路径**: `E:\Ai_Check_image`
- **用途**: AI检测完后的输出目录
- **说明**: AI检测完成后的结果文件存储在这里

## 当前照片处理流程

### 1. 原始照片存储
```
OPT相机拍照 → 保存到配置的照片存储地址
路径: E:\AI_images\[日期文件夹]\[产品文件夹]\原始照片.jpg
```

### 2. waiting_image文件夹
```
原始照片 → 复制到 → waiting_image文件夹
路径: E:\AI_images\[日期文件夹]\[产品文件夹]\waiting_image\照片.jpg
```

### 3. 旋转照片存储
```
原始照片 → 旋转90度 → 保存到配置的照片存储地址
路径: E:\AI_images\[日期文件夹]\[产品文件夹]\旋转后照片.jpg
```

### 4. 照片裁剪处理
```
waiting_image中的照片 → 裁剪 → 输出到AI检测文件夹
输入: E:\AI_images\[日期文件夹]\[产品文件夹]\waiting_image\照片.jpg
输出: E:\SourceImage\裁剪后照片.jpg
```

### 5. AI检测处理
```
AI检测文件夹中的照片 → AI检测 → 结果输出到AI检测结果文件夹
输入: E:\SourceImage\裁剪后照片.jpg
输出: E:\Ai_Check_image\检测结果.csv
```

## 代码实现确认

### 1. 裁剪输入源 ✅
**当前实现**：
```csharp
// 第三步：使用现有的裁剪方法处理waiting_image中的照片
var waitingImagePictureModel = new PictureModel
{
    FileName = Path.GetFileName(waitingImagePath),
    FileFullPath = waitingImagePath  // 使用waiting_image中的文件
};
```

**说明**: 正确使用waiting_image文件夹中的照片作为裁剪输入源。

### 2. 裁剪输出目录 ✅
**当前实现**：
```csharp
// 直接使用配置的source文件夹，便于AI服务检测
string sourceCropFolder = Common.FileHelper.GetFolderName(_state.AppConfig.AiSource);

// 构建输出路径数组 - 使用source文件夹
List<string> outputPaths = new List<string>();
for (int i = 0; i < fileFullPaths.Length; i++)
{
    outputPaths.Add(Common.FileHelper.ConcatFile(sourceCropFolder, fileNames[i]));
}
```

**说明**: 正确使用配置的AiSource文件夹(`E:\SourceImage`)作为裁剪输出目录。

### 3. 配置文件映射
**ConfigModel.cs**:
```csharp
public string SaveFolder { get; set; } = string.Empty;    // 照片存储地址
public string AiSource { get; set; } = string.Empty;      // AI检测文件夹
public string AiResult { get; set; } = string.Empty;      // AI检测结果文件夹
```

**ObservableConfigModel.cs**:
```csharp
private string _saveFolder = "C://Images";                // 默认照片存储地址
private string _aiSource = "C://Images/source";           // 默认AI检测文件夹
private string _aiResult = "C://AIImages//result";        // 默认AI检测结果文件夹
```

## 文件夹用途总结

| 文件夹 | 配置属性 | 实际路径 | 用途 | 文件类型 |
|--------|----------|----------|------|----------|
| 照片存储地址 | SaveFolder | E:\AI_images | 存储原始照片和旋转照片 | .jpg原图 |
| waiting_image | (子文件夹) | E:\AI_images\...\waiting_image | 临时存储待裁剪照片 | .jpg副本 |
| AI检测文件夹 | AiSource | E:\SourceImage | 存储裁剪后照片 | .jpg裁剪图 |
| AI检测结果 | AiResult | E:\Ai_Check_image | 存储检测结果 | .csv结果文件 |

## 处理流程图

```
原始照片 (E:\AI_images\...)
    ↓ 复制
waiting_image (E:\AI_images\...\waiting_image\...)
    ↓ 裁剪
AI检测文件夹 (E:\SourceImage\...)
    ↓ AI检测
AI检测结果 (E:\Ai_Check_image\...)
```

## 当前实现状态

✅ **裁剪输入源正确**: 使用waiting_image文件夹中的照片  
✅ **裁剪输出目录正确**: 输出到配置的AI检测文件夹(E:\SourceImage)  
✅ **文件夹配置正确**: 各个文件夹的用途和配置都符合要求  
✅ **处理流程正确**: 照片处理流程符合设计要求  

## 验证要点

1. **输入验证**: 确认裁剪时使用的是waiting_image文件夹中的照片
2. **输出验证**: 确认裁剪后的照片输出到E:\SourceImage文件夹
3. **配置验证**: 确认系统配置中的文件夹路径设置正确
4. **流程验证**: 确认整个照片处理流程按照设计执行

当前的实现已经正确按照您的要求配置了文件夹结构和处理流程。
