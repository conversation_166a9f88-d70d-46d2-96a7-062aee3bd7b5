﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public partial class ObservableUserModel : ViewModelBase
    {
        private string _account;
        public string Account
        {
            get => _account;
            set => SetProperty(ref _account, value);
        }

        private string _password;
        public string Password
        {
            get => _password;
            set => SetProperty(ref _password, value);
        }

        private bool _isRemeber = false;
        public bool IsRemeber
        {
            get => _isRemeber;
            set => SetProperty(ref _isRemeber, value);
        }
    }
}
