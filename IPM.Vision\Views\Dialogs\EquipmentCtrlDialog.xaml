﻿<UserControl x:Class="IPM.Vision.Views.Dialogs.EquipmentCtrlDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
             xmlns:controls="clr-namespace:IPM.Vision.Views.CustomControls"
             DataContext="{Binding EquipmentCtrlDialogViewModel, Source={StaticResource Locator}}"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="White" CornerRadius="5" Width="720">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Background="#283643" CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText Text="{Binding Title}" Foreground="White" FontSize="18" VerticalAlignment="Center" Margin="10 0 0 0"/>
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Template="{StaticResource CloseTemplate}"
                            Width="40"
                            Height="40"
                            Content="&#xf00d;"
                            Command="{Binding CloseCommand}"/>
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1"
                    Height="420"
                    Margin="4"
                    BorderBrush="Gray"
                    BorderThickness="0.4"
                    Visibility="{Binding ShowCamera,Converter={StaticResource BoolToVisibilityConverter}}">
                <hc:Interaction.Triggers>
                    <hc:EventTrigger EventName="Loaded">
                        <hc:EventToCommand Command="{Binding CameraLoadedCommand}" PassEventArgsToCommand="True"/>
                    </hc:EventTrigger>
                </hc:Interaction.Triggers>
                <controls:CaptureView/>
            </Border>
            <Border Grid.Row="2" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <hc:UniformSpacingPanel Margin="0 0 10 0" Orientation="Vertical" Spacing="10"  Grid.Column="0">
                        <hc:TextBox
                            hc:InfoElement.Title="参数名称"
                            hc:InfoElement.TitlePlacement="Left"
                            Text="{Binding EquipmentModel.ParamName,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <hc:UniformSpacingPanel Grid.Column="0" Orientation="Vertical" Spacing="10">
                                <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5">
                                    <hc:SimpleText Text="X轴(mm)"/>
                                    <hc:NumericUpDown
                                        Maximum="440"
                                        Minimum="-385"
                                        DecimalPlaces="2"
                                        Increment="0.02"
                                        Value="{Binding EquipmentModel.X,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                        <hc:Interaction.Triggers>
                                            <hc:EventTrigger EventName="KeyDown">
                                                <hc:EventToCommand Command="{Binding XYKeyDownCommand}" PassEventArgsToCommand="True" />
                                            </hc:EventTrigger>
                                        </hc:Interaction.Triggers>
                                    </hc:NumericUpDown>
                                    <hc:PreviewSlider
                                        Maximum="440"
                                        Minimum="-385"
                                        VerticalAlignment="Bottom"
                                        IsSnapToTickEnabled="True"
                                        SmallChange="0.02"
                                        Value="{Binding EquipmentModel.X,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                        <hc:Interaction.Triggers>
                                            <hc:EventTrigger EventName="LostMouseCapture">
                                                <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="XY" />
                                            </hc:EventTrigger>
                                        </hc:Interaction.Triggers>
                                    </hc:PreviewSlider>
                                </hc:UniformSpacingPanel>

                                <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5">
                                    <hc:SimpleText Text="Y轴(mm)"/>
                                    <hc:NumericUpDown
                                        Maximum="195"
                                        Minimum="-250"
                                        DecimalPlaces="2"
                                        Increment="0.02"
                                        Value="{Binding EquipmentModel.Y,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                        <hc:Interaction.Triggers>
                                            <hc:EventTrigger EventName="KeyDown">
                                                <hc:EventToCommand Command="{Binding XYKeyDownCommand}" PassEventArgsToCommand="True" />
                                            </hc:EventTrigger>
                                        </hc:Interaction.Triggers>
                                    </hc:NumericUpDown>
                                    <hc:PreviewSlider
                                        Maximum="195"
                                        Minimum="-250"
                                        VerticalAlignment="Bottom"
                                        IsSnapToTickEnabled="True"
                                        SmallChange="0.02"
                                        Value="{Binding EquipmentModel.Y,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                        <hc:Interaction.Triggers>
                                            <hc:EventTrigger EventName="LostMouseCapture">
                                                <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="XY" />
                                            </hc:EventTrigger>
                                        </hc:Interaction.Triggers>
                                    </hc:PreviewSlider>
                                </hc:UniformSpacingPanel>
                            </hc:UniformSpacingPanel>
                            <Button Margin="10 0 0 0"
                                    Content="重置XY轴"
                                    Grid.Column="1"
                                    Height="140"
                                    Width="80"
                                    Command="{Binding ResetSingleCommand}"
                                    CommandParameter="XY"
                                    Style="{StaticResource ButtonWarning}"/>
                        </Grid>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" Grid.Column="0">
                                <hc:SimpleText Text="Z轴(mm)"/>
                                <hc:NumericUpDown
                                    Maximum="10"
                                    Minimum="-125"
                                    DecimalPlaces="2"
                                    Increment="0.02"
                                    Value="{Binding EquipmentModel.Z,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                    <hc:Interaction.Triggers>
                                        <hc:EventTrigger EventName="KeyDown">
                                            <hc:EventToCommand Command="{Binding ZKeyDownCommand}" PassEventArgsToCommand="True" />
                                        </hc:EventTrigger>
                                    </hc:Interaction.Triggers>
                                </hc:NumericUpDown>
                                <hc:PreviewSlider
                                    Maximum="10"
                                    Minimum="-125"
                                    VerticalAlignment="Bottom"
                                    IsSnapToTickEnabled="True"
                                    SmallChange="0.02"
                                    Value="{Binding EquipmentModel.Z,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                    <hc:Interaction.Triggers>
                                        <hc:EventTrigger EventName="LostMouseCapture">
                                            <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="Z" />
                                        </hc:EventTrigger>
                                    </hc:Interaction.Triggers>
                                </hc:PreviewSlider>
                            </hc:UniformSpacingPanel>
                            <Button Margin="10 0 0 0"
                                    Content="重置Z轴"
                                    Grid.Column="1"
                                    Height="60"
                                    Width="80"
                                    Command="{Binding ResetSingleCommand}"
                                    CommandParameter="Z"
                                    Style="{StaticResource ButtonWarning}"/>
                        </Grid>

                    </hc:UniformSpacingPanel>
                    <hc:UniformSpacingPanel Margin="10 0 0 0" Orientation="Vertical" Spacing="10" Grid.Column="1">
                        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                            <Button  Style="{StaticResource ButtonDanger}" Content="清除报警" Command="{Binding ClearCommand}"/>
                            <Button Style="{StaticResource ButtonWarning}" Content="重置设备" Command="{Binding ResetCommand}"/>
                            <Button  Style="{StaticResource ButtonInfo}" Content="打开相机" Command="{Binding OpenCommand}"/>
                        </hc:UniformSpacingPanel>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" Grid.Column="0">
                                <hc:SimpleText Text="相机角度(°)"/>
                                <hc:NumericUpDown
                                    Maximum="60"
                                    Minimum="30"
                                    DecimalPlaces="2"
                                    Increment="0.02"
                                    Value="{Binding EquipmentModel.R,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                    <hc:Interaction.Triggers>
                                        <hc:EventTrigger EventName="KeyDown">
                                            <hc:EventToCommand Command="{Binding RKeyDownCommand}" PassEventArgsToCommand="True" />
                                        </hc:EventTrigger>
                                    </hc:Interaction.Triggers>
                                </hc:NumericUpDown>
                                <hc:PreviewSlider
                                    Maximum="60"
                                    Minimum="30"
                                    VerticalAlignment="Bottom"
                                    IsSnapToTickEnabled="True"
                                    SmallChange="0.02"
                                    Value="{Binding EquipmentModel.R,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                    <hc:Interaction.Triggers>
                                        <hc:EventTrigger EventName="LostMouseCapture">
                                            <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="R" />
                                        </hc:EventTrigger>
                                    </hc:Interaction.Triggers>
                                </hc:PreviewSlider>
                            </hc:UniformSpacingPanel>
                            <Button Margin="10 0 0 0"
                                    Content="重置角度"
                                    Grid.Column="1"
                                    Height="60"
                                    Width="80"
                                    Command="{Binding ResetSingleCommand}"
                                    CommandParameter="R"
                                    Style="{StaticResource ButtonWarning}"/>
                        </Grid>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" Grid.Column="0">
                                <hc:SimpleText Text="水平旋转角度(°)"/>
                                <hc:NumericUpDown
                                    Maximum="360"
                                    Minimum="0"
                                    DecimalPlaces="2"
                                    Increment="0.02"
                                    Value="{Binding EquipmentModel.T,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                    <hc:Interaction.Triggers>
                                        <hc:EventTrigger EventName="KeyDown">
                                            <hc:EventToCommand Command="{Binding TKeyDownCommand}" PassEventArgsToCommand="True" />
                                        </hc:EventTrigger>
                                    </hc:Interaction.Triggers>
                                </hc:NumericUpDown>
                                <hc:PreviewSlider
                                    Maximum="360"
                                    Minimum="0"
                                    VerticalAlignment="Bottom"
                                    IsSnapToTickEnabled="True"
                                    SmallChange="0.02"
                                    Value="{Binding EquipmentModel.T,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                                    <hc:Interaction.Triggers>
                                        <hc:EventTrigger EventName="LostMouseCapture">
                                            <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="T" />
                                        </hc:EventTrigger>
                                    </hc:Interaction.Triggers>
                                </hc:PreviewSlider>
                            </hc:UniformSpacingPanel>
                            <Button Margin="10 0 0 0"
                                    Content="重置角度"
                                    Grid.Column="1"
                                    Height="60"
                                    Width="80"
                                    Command="{Binding ResetSingleCommand}"
                                    CommandParameter="T"
                                    Style="{StaticResource ButtonWarning}"/>
                        </Grid>

                        <!--<Grid>
                        <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5" Grid.Column="0">
                        <hc:SimpleText Text="镜头远近(°)"/>
                        <hc:NumericUpDown
                        Maximum="195"
                        Minimum="0"
                        Value="{Binding EquipmentModel.O,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                        <hc:Interaction.Triggers>
                        <hc:EventTrigger EventName="KeyDown">
                        <hc:EventToCommand Command="{Binding OKeyDownCommand}" PassEventArgsToCommand="True" />
                        </hc:EventTrigger>
                        </hc:Interaction.Triggers>
                        </hc:NumericUpDown>
                        <hc:PreviewSlider
                        Maximum="195"
                        Minimum="0"
                        VerticalAlignment="Bottom"
                        IsSnapToTickEnabled="True"
                        Value="{Binding EquipmentModel.O,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}">
                        <hc:Interaction.Triggers>
                        <hc:EventTrigger EventName="LostMouseCapture">
                        <hc:EventToCommand Command="{Binding MoveCommand}" CommandParameter="O" />
                        </hc:EventTrigger>
                        </hc:Interaction.Triggers>
                        </hc:PreviewSlider>
                        </hc:UniformSpacingPanel>
                        <Button Margin="10 0 0 0"
                        Content="重置镜头"
                        Grid.Column="1"
                        Height="60"
                        Width="80"
                        Command="{Binding ResetSingleCommand}"
                        CommandParameter="O"
                        Style="{StaticResource ButtonWarning}"/>
                        </Grid>-->

                    </hc:UniformSpacingPanel>
                </Grid>
            </Border>
            <Border Grid.Row="3" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Button Grid.Column="0" Height="35" Width="220" Style="{StaticResource ButtonPrimary}" Content="保存" Command="{Binding SaveCommand}"/>
                    <Button Grid.Column="1" Height="35" Width="220" Content="取消" Command="{Binding CloseCommand}"/>

                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
