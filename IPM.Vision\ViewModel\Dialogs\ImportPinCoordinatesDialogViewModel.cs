using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using HandyControl.Tools.Extension;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class ImportPinCoordinatesDialogViewModel : ViewModelBase, IDialogResultable<bool>
    {
        private bool _result = false;
        private string _title = string.Empty;
        private readonly NLogHelper _logger;
        private readonly ObservableGlobalState _globalState;
        private readonly EquipmentService _equipmentService;

        private string _filePath;
        private double _stepValue = 0.01;
        private int _pinCount = 1;
        private ObservableCollection<ObservablePinCoordinateModel> _coordinateItems = new ObservableCollection<ObservablePinCoordinateModel>();

        public event Action<ObservableCollection<ObservableProcessModel>> SaveProcessedEvent;

        public ImportPinCoordinatesDialogViewModel(
            NLogHelper logger,
            ObservableGlobalState globalState,
            IEquipmentService equipmentService)
        {
            _logger = logger;
            _globalState = globalState;
            _equipmentService = (EquipmentService)equipmentService;
        }

        public bool Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public string FilePath
        {
            get => _filePath;
            set => SetProperty(ref _filePath, value);
        }

        public double StepValue
        {
            get => _stepValue;
            set => SetProperty(ref _stepValue, value);
        }

        public int PinCount
        {
            get => _pinCount;
            set => SetProperty(ref _pinCount, value);
        }

        public ObservableCollection<ObservablePinCoordinateModel> CoordinateItems
        {
            get => _coordinateItems;
            set => SetProperty(ref _coordinateItems, value);
        }

        public Action CloseAction { get; set; }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            // 初始化操作，如果需要的话
        });

        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            Result = false;
            CloseAction?.Invoke();
        });

        public IRelayCommand SelectFileCommand => new RelayCommand(() =>
        {
            try
            {
                // 打开文件对话框
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择坐标文件",
                    Filter = "CSV文件 (*.csv)|*.csv|文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*"
                };

                // 如果用户选择了文件
                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    FilePath = openFileDialog.FileName;

                    // 验证文件是否存在
                    if (!File.Exists(FilePath))
                    {
                        HandyControl.Controls.MessageBox.Error("选定的文件不存在！");
                        return;
                    }

                    // 读取文件内容
                    ParseCoordinateFile(FilePath);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("选择文件失败", ex);
                HandyControl.Controls.MessageBox.Error($"选择文件失败: {ex.Message}");
            }
        });

        private void ParseCoordinateFile(string filePath)
        {
            try
            {
                CoordinateItems.Clear();
                
                // 使用CSV辅助类读取文件
                ExcelHelper excelHelper = new ExcelHelper();
                var rows = excelHelper.ReadCsvFile(filePath);
                
                if (rows == null || rows.Count == 0)
                {
                    HandyControl.Controls.MessageBox.Error("文件内容为空或格式错误！");
                    return;
                }

                // 解析每一行数据
                foreach (var row in rows)
                {
                    if (row.Count >= 4) // 至少有引脚名称、X坐标、Y坐标和备注
                    {
                        CoordinateItems.Add(new ObservablePinCoordinateModel
                        {
                            PinName = row[0],
                            X = double.TryParse(row[1], out double x) ? x : 0,
                            Y = double.TryParse(row[2], out double y) ? y : 0,
                            Remark = row.Count > 3 ? row[3] : string.Empty
                        });
                    }
                }

                _logger.LogInfo($"成功解析坐标文件，共{CoordinateItems.Count}条记录");
            }
            catch (Exception ex)
            {
                _logger.LogError("解析坐标文件失败", ex);
                HandyControl.Controls.MessageBox.Error($"解析坐标文件失败: {ex.Message}");
            }
        }

        public IRelayCommand SaveCommand => new RelayCommand(() =>
        {
            try
            {
                if (CoordinateItems.Count == 0)
                {
                    HandyControl.Controls.MessageBox.Error("没有可用的坐标数据！");
                    return;
                }

                // 根据引脚数量生成步骤
                var processModels = GenerateProcessModels();
                if (processModels.Count > 0)
                {
                    SaveProcessedEvent?.Invoke(new ObservableCollection<ObservableProcessModel>(processModels));
                    Result = true;
                    CloseAction?.Invoke();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("生成步骤失败", ex);
                HandyControl.Controls.MessageBox.Error($"生成步骤失败: {ex.Message}");
            }
        });

        private List<ObservableProcessModel> GenerateProcessModels()
        {
            List<ObservableProcessModel> processModels = new List<ObservableProcessModel>();
            
            try
            {
                // 计算每个步骤需要处理的引脚数
                if (PinCount <= 0)
                {
                    HandyControl.Controls.MessageBox.Error("引脚数量必须大于0！");
                    return processModels;
                }

                // 确保引脚数不超过坐标数据的数量
                int pinsPerStep = Math.Min(PinCount, CoordinateItems.Count);
                int processNumber = 1;

                // 分组处理引脚
                for (int i = 0; i < CoordinateItems.Count; i += pinsPerStep)
                {
                    // 获取当前步骤的引脚组
                    var pinGroup = CoordinateItems.Skip(i).Take(pinsPerStep).ToList();
                    if (pinGroup.Count == 0) break;

                    // 使用第一个引脚的坐标作为该步骤的设备参数
                    var firstPin = pinGroup.First();
                    string equipId = Guid.NewGuid().ToString();

                    // 创建设备参数
                    ObservableEquipmentModel equipmentModel = new ObservableEquipmentModel
                    {
                        Id = equipId,
                        ParamName = $"PIN-{firstPin.PinName}",
                        X = (float)firstPin.X,
                        Y = (float)firstPin.Y,
                        R = 49F, // 默认值
                        CreateTime = DateTime.Now,
                        Operator = _globalState.LoginUser.Account
                    };

                    // 创建步骤模型
                    ObservableProcessModel processModel = new ObservableProcessModel
                    {
                        Id = Guid.NewGuid().ToString(),
                        ProcessNumber = processNumber,
                        ParamName = $"PIN-{firstPin.PinName}",
                        ProcessType = Common.ProcessTypeEnum.TAKEPICTURE,
                        CameraType = Common.CameraTypeEnum.Main,
                        EquipmentParaId = equipId
                    };

                    processModels.Add(processModel);
                    processNumber++;
                }

                return processModels;
            }
            catch (Exception ex)
            {
                _logger.LogError("生成步骤模型失败", ex);
                throw;
            }
        }
    }
} 