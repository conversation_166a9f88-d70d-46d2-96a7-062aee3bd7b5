﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Camera.Com;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.CanonControls;
using IPM.Vision.Views.CustomControls;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace IPM.Vision.ViewModel.CustomControls
{
    public class CaptureViewModel:ViewModelBase
    {
        private bool _needSetting = true;
        private readonly ObservableGlobalState _globalState;
        private ObservableProcessModel _processModel = new ObservableProcessModel();
        private bool _haveChild = false;
        public bool _showMainCamera = true;
        private readonly WeightService _weightService;
        private readonly EquipmentService _equipmentService;
        private readonly MainCameraParaService _mainCameraParaService;
        private PopupWindow _cameraWindow;
        private PopupWindow _equipmentWindow;
        private PopupWindow _lightWindow;
        private string _weightData = "0";
        private IObserver _observerControl;
        private bool _isRunning = false;

        public bool NeedSetting
        {
            get => _needSetting;
            set => SetProperty(ref _needSetting, value);
        }

        public bool IsRunning
        {
            get => _isRunning;
            set => SetProperty(ref _isRunning, value);
        }

        public string WeightData
        {
            get => _weightData;
            set => SetProperty(ref _weightData, value);
        }

        public bool ShowMainCamera
        {
            get => _showMainCamera;
            set
            {
                _globalState.ShowMainCamera = value;
                SetProperty(ref _showMainCamera, value);
            }
        }

        public ObservableProcessModel CurrentProcessModel
        {
            get => _processModel;
            set => SetProperty(ref _processModel, value);
        }

        public bool HaveChild
        {
            get => _haveChild;
            set => SetProperty(ref _haveChild, value);
        }

        public CaptureViewModel(ObservableGlobalState globalState,WeightService weightService, IEquipmentService equipmentService,IMainCameraParaService mainCameraParaService)
        {
            _globalState = globalState;
            _weightService = weightService;
            _mainCameraParaService = (MainCameraParaService)mainCameraParaService;
            _weightService.WeightChangedEvent += _weightService_WeightChangedEvent;
            _globalState.ProcessChangedEvent += _globalState_ProcessChangedEvent;
            _globalState.NotifyProgressStatus += _globalState_NotifyProgressStatus;
            _mainCameraParaService.MainCameraAddedEvent += _mainCameraParaService_MainCameraAddedEvent;
            _equipmentService = (EquipmentService)equipmentService;
        }

        private void _globalState_NotifyProgressStatus(bool obj)
        {
            IsRunning = obj;
        }

        private void _weightService_WeightChangedEvent(string obj)
        {
            WeightData = obj;
        }

        private void _globalState_ProcessChangedEvent(ObservableProcessModel obj)
        {
            if (HaveChild) ShowMainCamera = obj.CameraType == Common.CameraTypeEnum.Main;
            else ShowMainCamera = true;
            CurrentProcessModel = obj;
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            if(CurrentProcessModel != null)
            {
                if (HaveChild) ShowMainCamera = CurrentProcessModel.CameraType == Common.CameraTypeEnum.Main;
                else ShowMainCamera = true;
            }
        });

        public IRelayCommand UnLoadCommand => new RelayCommand(() =>
        {
            if(_cameraWindow != null) _cameraWindow.Close();
            if(_lightWindow != null) _lightWindow.Close();
            if(_equipmentWindow != null) _equipmentWindow.Close();
        });

        public IRelayCommand OpenCameraCommand => new RelayCommand(() => {
            if (_cameraWindow != null && _cameraWindow.IsActive) return;
            var cameraCtrl = new CameraCtrlControl();
            var dataContext = cameraCtrl.DataContext as CameraCtrlControlViewModel;
            _cameraWindow = new PopupWindow()
            {
                Title = "相机参数调整",
                AllowsTransparency = true,
                WindowStyle = WindowStyle.None,
                PopupElement = cameraCtrl,
            };
            dataContext.ShowControl = _cameraWindow;
            _cameraWindow.Closed += (object sender, EventArgs e) =>
            {
                _cameraWindow = null;
            };
            _cameraWindow.Show();
        });


        public IRelayCommand LightControlCommand => new RelayCommand(() => {
            if (_lightWindow != null && _lightWindow.IsActive) return;
            var cameraCtrl = new LightCtrlControl();
            var dataContext = cameraCtrl.DataContext as LightCtrlControlViewModel;
            _lightWindow = new PopupWindow()
            {
                Title = "光源参数调整",
                AllowsTransparency = true,
                WindowStyle = WindowStyle.None,
                PopupElement = cameraCtrl,
            };
            dataContext.ShowControl = _cameraWindow;
            _lightWindow.Closed += (object sender, EventArgs e) =>
            {
                _lightWindow = null;
            };
            _lightWindow.Show();
        });

        public IRelayCommand OpenEquipmentCommand => new RelayCommand(() => {
            if (_equipmentWindow != null && _equipmentWindow.IsActive) return;
            var cameraCtrl = new EquipmentCtrlControl();
            var dataContext = cameraCtrl.DataContext as EquipmentCtrlControlViewModel;
            _equipmentWindow = new PopupWindow()
            {
                Title = "设备参数调整",
                AllowsTransparency = true,
                WindowStyle = WindowStyle.None,
                PopupElement = cameraCtrl,
            };
            _equipmentWindow.Closed += (object sender, EventArgs e) =>
            {
                _equipmentWindow = null;
            };
            dataContext.ShowControl = _cameraWindow;
            _equipmentWindow.Show();
        });
        public IRelayCommand ResetErrorCommand => new RelayCommand(() => {
            _equipmentService.ResetError();
        });

        public IRelayCommand RestAllCommand => new RelayCommand(() => {
            _equipmentService.ResetAll();
        });

        public IRelayCommand<RoutedEventArgs> ProgressLoadedCommand => new RelayCommand<RoutedEventArgs>((args) =>
        {
            var temp = args.Source as Border;
            if (temp != null) {
                var control = temp.Child as LDownLoadProgress;
                if (control != null) {
                    _observerControl = control as IObserver;
                    _mainCameraParaService.AddSource(_observerControl);
                }
            }
        });
        private void _mainCameraParaService_MainCameraAddedEvent(Camera.EDSDKLib.CameraController obj)
        {
            if (_observerControl != null)
                _mainCameraParaService.AddSource(_observerControl);
        }

        public IRelayCommand RestartWeight => new RelayCommand(() => {
            _equipmentService.ReStartWeight();
        });
    }
}
