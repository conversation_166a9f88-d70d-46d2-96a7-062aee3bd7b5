﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class ImageQualityComboBox : PropertyComboBox, IObserver
    {

        private EDSDK.EdsPropertyDesc _desc;
        public ImageQualityComboBox()
        {
            if (this.items == null) items = new Dictionary<uint, string>();
            items.Add((uint)EDSDK.ImageFormat_CR2, "RAW");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRLJF, "RAW + Large Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRMJF, "RAW + Middle Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRSJF, "RAW + Small Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRLJN, "RAW + Large Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRMJN, "RAW + Middle Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRSJN, "RAW + Small Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRS1JF, "RAW + Small1 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRS1JN, "RAW + Small1 Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRS2JF, "RAW + Small2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRS3JF, "RAW + Small3 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRLJ, "RAW + Large Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRM1J, "RAW + Middle1 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRM2J, "RAW + Middle2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LRSJ, "RAW + Small Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MR, "Middle RAW");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRLJF, "Middle RAW + Large Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRMJF, "Middle RAW + Middle Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRSJF, "Middle RAW + Small Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRLJN, "Middle RAW + Large Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRMJN, "Middle RAW + Middle Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRSJN, "Middle RAW + Small Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRS1JF, "Middle RAW + Small1 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRS1JN, "Middle RAW + Small1 Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRS2JF, "Middle RAW + Small2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRS3JF, "Middle RAW + Small3 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRLJ, "Middle RAW + Large Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRM1J, "Middle RAW + Middle1 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRM2J, "Middle RAW + Middle2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MRSJ, "Middle RAW + Small Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SR, "Small RAW");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRLJF, "Small RAW + Large Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRMJF, "Small RAW + Middle Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRSJF, "Small RAW + Small Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRLJN, "Small RAW + Large Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRMJN, "Small RAW + Middle Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRSJN, "Small RAW + Small Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRS1JF, "Small RAW + Small1 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRS1JN, "Small RAW + Small1 Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRS2JF, "Small RAW + Small2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRS3JF, "Small RAW + Small3 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRLJ, "Small RAW + Large Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRM1J, "Small RAW + Middle1 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRM2J, "Small RAW + Middle2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SRSJ, "Small RAW + Small Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LJF, "Large Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LJN, "Large Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MJF, "Middle Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_MJN, "Middle Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SJF, "Small Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SJN, "Small Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_S1JF, "Small1 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_S1JN, "Small1 Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_S2JF, "Small2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_S3JF, "Small3 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_LJ, "Large Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_M1J, "Middle1 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_M2J, "Middle2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_SJ, "Small Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CR, "CRAW");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRLJF, "CRAW + Large Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRMJF, "CRAW + Middle Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRM1JF, "CRAW + Middle1 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRM2JF, "CRAW + Middle2 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRSJF, "CRAW + Small Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRS1JF, "CRAW + Small1 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRS2JF, "CRAW + Small2 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRS3JF, "CRAW + Small3 Fine Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRLJN, "CRAW + Large Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRMJN, "CRAW + Middle Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRM1JN, "CRAW + Middle1 Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRM2JN, "CRAW + Middle2 Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRSJN, "CRAW + Small Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRS1JN, "CRAW + Small1 Normal Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRLJ, "CRAW + Large Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRM1J, "CRAW + Middle1 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRM2J, "CRAW + Middle2 Jpeg");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRSJ, "CRAW + Small Jpeg");

            //HEIF
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_HEIFL, "HEIF Large");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_RHEIFL, "RAW  + HEIF Large");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRHEIFL, "CRAW + HEIF Large");

            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_HEIFLF, "HEIF Large Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_HEIFLN, "HEIF Large Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_HEIFMF, "HEIF Middle Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_HEIFMN, "HEIF Middle Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_HEIFS1F, "HEIF Small1 Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_HEIFS1N, "HEIF Small1 Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_HEIFS2F, "HEIF Small2 Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_RHEIFLF, "RAW + HEIF Large Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_RHEIFLN, "RAW + HEIF Large Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_RHEIFMF, "RAW + HEIF Middle Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_RHEIFMN, "RAW + HEIF Middle Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_RHEIFS1F, "RAW + HEIF Small1 Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_RHEIFS1N, "RAW + HEIF Small1 Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_RHEIFS2F, "RAW + HEIF Small2 Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRHEIFLF, "CRAW + HEIF Large Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRHEIFLN, "CRAW + HEIF Large Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRHEIFMF, "CRAW + HEIF Middle Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRHEIFMN, "CRAW + HEIF Middle Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRHEIFS1F, "CRAW + HEIF Small1 Fine");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRHEIFS1N, "CRAW + HEIF Small1 Normal");
            items.Add((uint)EDSDK.ImageQuality.EdsImageQuality_CRHEIFS2F, "CRAW + HEIF Small2 Fine");

            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }

        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = (KeyValuePair<uint, string>)e.AddedItems[0];
                CameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_IMAGEQUALITY, (IntPtr)selectedItem.Key));

            }
            base.OnSelectionChanged(e);
        }


        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();

                if (propertyID == EDSDK.PropID_ImageQuality)
                {
                    uint property = model.ImageQuality;

                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            this.UpdateProperty(property);
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:
                            _desc = model.ImageQualityDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            this.UpdateProperty(property);
                            break;
                    }
                }
            }
        }
    }
}
