﻿using Opc.Ua.Client;
using Opc.Ua;
using OpcUaHelper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace IPM.Vision.Common.OPCHelper
{
    public class OpcUAHelper
    {
        private OpcUaClient _client;
        private readonly NLogHelper _logger;
        private string _serverUrl;
        private bool _isConnecting;
        private readonly object _lockObject = new object();
        private readonly CancellationTokenSource _reconnectTokenSource;

        public event WarningEventHandler NotifyWarning;
        public event ErrorEventHandler NotifyError;
        public event InfoEventHandler NotifyInfo;
        public event ConnectEventHandler ConnectEvent;
        public event ConnectedEventHandler ConnectedEvent;
        
        public bool IsConnected => _client?.Connected ?? false;

        public OpcUAHelper(NLogHelper logger)
        {
            _logger = logger;
            InitializeClient();
        }

        private void InitializeClient()
        {
            try
            {
                _client = new OpcUaClient
                {
                    UseSecurity = false,
                    ReconnectPeriod = 5000, // 5秒重连间隔
                    UserIdentity = new UserIdentity(new AnonymousIdentityToken())
                };

                _client.ReconnectStarting += OnReconnectStarting;
                _client.ConnectComplete += OnConnectComplete;
                _client.ReconnectComplete += OnReconnectComplete;
                
                _logger.LogInfo("OPC UA客户端初始化完成");
            }
            catch (Exception ex)
            {
                _logger.LogError($"OPC UA客户端初始化失败: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"OPC UA客户端初始化失败: {ex.Message}", 
                    IsError = true
                });
            }
        }

        /// <summary>
        /// 使用匿名用户连接OPC UA服务器
        /// </summary>
        /// <param name="serverAddress">服务器地址 (如: localhost:4840 或 *************:4840)</param>
        public async Task<bool> ConnectWithAnonymousAsync(string serverAddress)
        {
            if (string.IsNullOrWhiteSpace(serverAddress))
            {
                var errorMsg = "服务器地址不能为空";
                _logger.LogError(errorMsg);
                NotifyError?.Invoke(this, new OpcEventArgs { Code = OpcCode.Error, Message = errorMsg, IsError = true });
                return false;
            }

            lock (_lockObject)
            {
                if (_isConnecting)
                {
                    _logger.LogInfo("正在连接中，请勿重复连接");
                    return false;
                }
                _isConnecting = true;
            }

            try
            {
                // 标准化服务器URL
                _serverUrl = NormalizeServerUrl(serverAddress);
                
                _logger.LogInfo($"开始连接OPC UA服务器: {_serverUrl}");
                NotifyInfo?.Invoke(this, new OpcEventArgs { Code = OpcCode.Waiting, Message = $"正在连接: {_serverUrl}" });

                // 确保客户端已初始化
                if (_client == null)
                {
                    InitializeClient();
                }

                // 如果已经连接，先断开
                if (_client.Connected)
                {
                    _client.Disconnect();
                    await Task.Delay(1000); // 等待断开完成
                }

                // 执行连接
                await _client.ConnectServer(_serverUrl);
                
                _logger.LogInfo($"OPC UA服务器连接成功: {_serverUrl}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"连接OPC UA服务器失败: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"连接失败: {ex.Message}", 
                    IsError = true
                });
                return false;
            }
            finally
            {
                lock (_lockObject)
                {
                    _isConnecting = false;
                }
            }
        }

        /// <summary>
        /// 使用用户名和密码连接OPC UA服务器
        /// </summary>
        /// <param name="serverAddress">服务器地址</param>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        public async Task<bool> ConnectWithUserAsync(string serverAddress, string username, string password)
        {
            if (string.IsNullOrWhiteSpace(serverAddress))
            {
                var errorMsg = "服务器地址不能为空";
                _logger.LogError(errorMsg);
                NotifyError?.Invoke(this, new OpcEventArgs { Code = OpcCode.Error, Message = errorMsg, IsError = true });
                return false;
            }

            lock (_lockObject)
            {
                if (_isConnecting)
                {
                    _logger.LogInfo("正在连接中，请勿重复连接");
                    return false;
                }
                _isConnecting = true;
            }

            try
            {
                _serverUrl = NormalizeServerUrl(serverAddress);
                
                _logger.LogInfo($"开始连接OPC UA服务器 (用户认证): {_serverUrl}");
                NotifyInfo?.Invoke(this, new OpcEventArgs { Code = OpcCode.Waiting, Message = $"正在连接: {_serverUrl}" });

                if (_client == null)
                {
                    InitializeClient();
                }

                // 设置用户认证
                _client.UserIdentity = new UserIdentity(username, password);

                if (_client.Connected)
                {
                    _client.Disconnect();
                    await Task.Delay(1000);
                }

                await _client.ConnectServer(_serverUrl);
                
                _logger.LogInfo($"OPC UA服务器连接成功 (用户认证): {_serverUrl}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"用户认证连接失败: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                { 
                    Code = OpcCode.Error, 
                    Message = $"用户认证连接失败: {ex.Message}", 
                    IsError = true 
                });
                return false;
            }
            finally
            {
                lock (_lockObject)
                {
                    _isConnecting = false;
                }
            }
        }

        /// <summary>
        /// 标准化服务器URL
        /// </summary>
        private string NormalizeServerUrl(string serverAddress)
        {
            if (string.IsNullOrWhiteSpace(serverAddress))
                return string.Empty;

            // 如果已经包含协议前缀，直接返回
            if (serverAddress.StartsWith("opc.tcp://", StringComparison.OrdinalIgnoreCase))
                return serverAddress;

            // 如果包含其他协议前缀，先移除
            if (serverAddress.Contains("://"))
            {
                var index = serverAddress.IndexOf("://") + 3;
                serverAddress = serverAddress.Substring(index);
            }

            // 添加标准的OPC UA协议前缀
            return $"opc.tcp://{serverAddress}";
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public void Disconnect()
        {
            try
            {
                // 取消重连
                _reconnectTokenSource?.Cancel();
                
                if (_client?.Connected == true)
                {
                    _client.Disconnect();
                    _logger.LogInfo("OPC UA连接已断开");
                    NotifyInfo?.Invoke(this, new OpcEventArgs { Code = OpcCode.Stop, Message = "连接已断开" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"断开连接时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 读取节点值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="nodeName">节点名称</param>
        /// <param name="nodeId">节点ID</param>
        /// <returns>节点值</returns>
        public T ReadTag<T>(string nodeName, int nodeId)
        {
            return ReadTag<T>(nodeName, nodeId.ToString());
        }

        /// <summary>
        /// 读取节点值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="nodeName">节点名称</param>
        /// <param name="nodeId">节点ID字符串</param>
        /// <returns>节点值</returns>
        public T ReadTag<T>(string nodeName, string nodeId)
        {
            // 检查连接状态
            if (!IsConnected)
            {
                _logger.LogInfo($"未连接到OPC UA服务器，无法读取节点: {nodeName}");
                return default(T);
            }

            // 检查客户端对象
            if (_client == null)
            {
                _logger.LogInfo($"OPC UA客户端为空，无法读取节点: {nodeName}");
                return default(T);
            }

            // 检查节点ID
            if (string.IsNullOrWhiteSpace(nodeId))
            {
                _logger.LogInfo($"节点ID为空，无法读取节点: {nodeName}");
                return default(T);
            }

            try
            {
                var fullNodeId = BuildNodeId(nodeId);
                
                // 检查构建的节点ID
                if (string.IsNullOrWhiteSpace(fullNodeId))
                {
                    _logger.LogInfo($"构建的节点ID为空，无法读取节点: {nodeName}");
                    return default(T);
                }

                var value = _client.ReadNode<T>(fullNodeId);
                
                _logger.LogInfo($"读取节点成功: {nodeName} = {value}");
                NotifyInfo?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.None,
                    Message = $"读取节点: {nodeName} = {value}" 
                });
                
                return value;
            }
            catch (NullReferenceException ex)
            {
                _logger.LogError($"读取节点时发生空引用异常: {nodeName} ({nodeId}), 错误: {ex.Message}", ex);
                _logger.LogError($"客户端状态 - 是否为空: {_client == null}, 连接状态: {IsConnected}");
                return default(T);
            }
            catch (Exception ex)
            {
                _logger.LogError($"读取节点失败: {nodeName} ({nodeId}), 错误: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error, 
                    Message = $"读取节点失败: {nodeName} - {ex.Message}", 
                    IsError = true
                });
                return default(T);
            }
        }

        /// <summary>
        /// 异步读取节点值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="nodeName">节点名称</param>
        /// <param name="nodeId">节点ID</param>
        /// <returns>节点值</returns>
        public async Task<T> ReadTagAsync<T>(string nodeName, int nodeId)
        {
            return await ReadTagAsync<T>(nodeName, nodeId.ToString());
        }

        /// <summary>
        /// 异步读取节点值 - 增强防护版本
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="nodeName">节点名称</param>
        /// <param name="nodeId">节点ID字符串</param>
        /// <returns>节点值</returns>
        public async Task<T> ReadTagAsync<T>(string nodeName, string nodeId)
        {
            // **增强的连接状态验证**
            if (!ValidateClientConnection(nodeName))
            {
                return default(T);
            }

            // 检查节点ID
            if (string.IsNullOrWhiteSpace(nodeId))
            {
                _logger.LogInfo($"节点ID为空，无法读取节点: {nodeName}");
                return default(T);
            }

            // **使用锁保护，防止竞态条件**
            lock (_lockObject)
            {
                if (!ValidateClientConnection(nodeName))
                {
                    return default(T);
                }
            }

            try
            {
                var fullNodeId = BuildNodeId(nodeId);
                
                // 检查构建的节点ID
                if (string.IsNullOrWhiteSpace(fullNodeId))
                {
                    _logger.LogInfo($"构建的节点ID为空，无法读取节点: {nodeName}");
                    return default(T);
                }

                // **最关键的修复：在调用前再次验证客户端状态**
                var clientSnapshot = _client; // 获取客户端快照，防止竞态条件
                if (clientSnapshot == null)
                {
                    _logger.LogError($"❌ 客户端快照为空，无法读取节点: {nodeName}");
                    return default(T);
                }

                if (!clientSnapshot.Connected)
                {
                    _logger.LogError($"❌ 客户端快照显示未连接，无法读取节点: {nodeName}");
                    return default(T);
                }

                _logger.LogInfo($"🔍 准备读取节点: {nodeName}, 节点ID: {fullNodeId}");
                
                // **使用客户端快照调用，避免null引用**
                var value = await clientSnapshot.ReadNodeAsync<T>(fullNodeId);
                
                _logger.LogInfo($"✅ 异步读取节点成功: {nodeName} = {value}");
                NotifyInfo?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.None,
                    Message = $"异步读取节点成功: {nodeName} = {value}" 
                });
                
                return value;
            }
            catch (NullReferenceException ex)
            {
                _logger.LogError($"💥 异步读取节点时发生空引用异常: {nodeName} ({nodeId})");
                _logger.LogError($"   异常消息: {ex.Message}");
                _logger.LogError($"   客户端状态: {(_client == null ? "NULL" : "NOT NULL")}");
                _logger.LogError($"   连接状态: {IsConnected}");
                _logger.LogError($"   堆栈跟踪: {ex.StackTrace}");
                
                // **触发错误事件**
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"读取节点时空引用异常: {nodeName}",
                    IsError = true,
                    Value = $"节点ID: {nodeId}, 客户端状态: {(_client == null ? "NULL" : "NOT NULL")}"
                });
                
                return default(T);
            }
            catch (ObjectDisposedException ex)
            {
                _logger.LogError($"🗑️ 客户端对象已释放，无法读取节点: {nodeName} - {ex.Message}");
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"客户端已释放，无法读取节点: {nodeName}",
                    IsError = true
                });
                return default(T);
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogError($"⚠️ 客户端操作状态异常，无法读取节点: {nodeName} - {ex.Message}");
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"客户端状态异常，无法读取节点: {nodeName}",
                    IsError = true
                });
                return default(T);
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 异步读取节点失败: {nodeName} ({nodeId}), 错误: {ex.Message}", ex);
                _logger.LogError($"   异常类型: {ex.GetType().Name}");
                
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"读取节点失败: {nodeName} - {ex.Message}", 
                    IsError = true 
                });
                return default(T);
            }
        }

        /// <summary>
        /// 验证客户端连接状态
        /// </summary>
        /// <param name="nodeName">节点名称（用于日志）</param>
        /// <returns>连接是否有效</returns>
        private bool ValidateClientConnection(string nodeName)
        {
            // 检查客户端对象
            if (_client == null)
            {
                _logger.LogInfo($"❌ OPC UA客户端为空，无法读取节点: {nodeName}");
                return false;
            }

            // 检查连接状态
            if (!IsConnected)
            {
                _logger.LogInfo($"❌ 未连接到OPC UA服务器，无法读取节点: {nodeName}");
                return false;
            }

            // **新增：检查客户端是否已释放**
            try
            {
                var sessionStatus = _client.Connected; // 尝试访问连接状态
                if (!sessionStatus)
                {
                    _logger.LogInfo($"❌ OPC UA会话状态无效，无法读取节点: {nodeName}");
                    return false;
                }
            }
            catch (ObjectDisposedException)
            {
                _logger.LogError($"🗑️ OPC UA客户端已释放，无法读取节点: {nodeName}");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"⚠️ 检查客户端连接状态异常: {ex.Message}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 写入节点值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="nodeName">节点名称</param>
        /// <param name="nodeId">节点ID</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否成功</returns>
        public async Task<bool> WriteTagAsync<T>(string nodeName, int nodeId, T value)
        {
            return await WriteTagAsync(nodeName, nodeId.ToString(), value);
        }

        /// <summary>
        /// 写入节点值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="nodeName">节点名称</param>
        /// <param name="nodeId">节点ID字符串</param>
        /// <param name="value">要写入的值</param>
        /// <returns>是否成功</returns>
        public async Task<bool> WriteTagAsync<T>(string nodeName, string nodeId, T value)
        {
            if (!IsConnected)
            {
                _logger.LogInfo($"未连接到OPC UA服务器，无法写入节点: {nodeName}");
                return false;
            }

            try
            {
                var fullNodeId = BuildNodeId(nodeId);
                var result = await _client.WriteNodeAsync(fullNodeId, value);
                
                if (result)
                {
                    _logger.LogInfo($"写入节点成功: {nodeName} = {value}");
                    NotifyInfo?.Invoke(this, new OpcEventArgs 
                    { 
                        Code = OpcCode.Working, 
                        Message = $"写入节点成功: {nodeName} = {value}" 
                    });
                }
                else
                {
                    _logger.LogInfo($"写入节点失败: {nodeName} = {value}");
                    NotifyWarning?.Invoke(this, new OpcEventArgs 
                    { 
                        Code = OpcCode.Error, 
                        Message = $"写入节点失败: {nodeName} = {value}" 
                    });
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"写入节点异常: {nodeName} ({nodeId}) = {value}, 错误: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"写入节点异常: {nodeName} - {ex.Message}", 
                    IsError = true 
                });
                return false;
            }
        }

        /// <summary>
        /// 订阅单个节点
        /// </summary>
        /// <param name="key">订阅键</param>
        /// <param name="nodeName">节点名称</param>
        /// <param name="nodeId">节点ID</param>
        /// <param name="callback">回调函数</param>
        /// <returns>是否成功</returns>
        public bool SubscribeTag(string key, string nodeName, int nodeId, Action<string, MonitoredItem, MonitoredItemNotificationEventArgs> callback)
        {
            return SubscribeTag(key, nodeName, nodeId.ToString(), callback);
        }

        /// <summary>
        /// 订阅单个节点
        /// </summary>
        /// <param name="key">订阅键</param>
        /// <param name="nodeName">节点名称</param>
        /// <param name="nodeId">节点ID字符串</param>
        /// <param name="callback">回调函数</param>
        /// <returns>是否成功</returns>
        public bool SubscribeTag(string key, string nodeName, string nodeId, Action<string, MonitoredItem, MonitoredItemNotificationEventArgs> callback)
        {
            if (!IsConnected)
            {
                _logger.LogInfo($"未连接到OPC UA服务器，无法订阅节点: {nodeName}");
                return false;
            }

            try
            {
                var fullNodeId = BuildNodeId(nodeId);
                _client.AddSubscription(key, fullNodeId, callback);
                
                _logger.LogInfo($"订阅节点成功: {nodeName} ({fullNodeId})");
                NotifyInfo?.Invoke(this, new OpcEventArgs 
                { 
                    Code = OpcCode.Working, 
                    Message = $"订阅节点: {nodeName}" 
                });
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"订阅节点失败: {nodeName} ({nodeId}), 错误: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"订阅节点失败: {nodeName} - {ex.Message}", 
                    IsError = true 
                });
                return false;
            }
        }

        /// <summary>
        /// 批量订阅节点
        /// </summary>
        /// <param name="key">订阅键</param>
        /// <param name="nodeIds">节点ID列表</param>
        /// <param name="callback">回调函数</param>
        /// <returns>是否成功</returns>
        public bool SubscribeMultipleTags(string key, List<string> nodeIds, Action<string, MonitoredItem, MonitoredItemNotificationEventArgs> callback)
        {
            if (!IsConnected)
            {
                _logger.LogInfo("未连接到OPC UA服务器，无法订阅节点");
                return false;
            }

            if (nodeIds == null || nodeIds.Count == 0)
            {
                _logger.LogInfo("节点ID列表为空");
                return false;
            }

            try
            {
                // 构建完整的节点ID列表
                var fullNodeIds = nodeIds.Select(id => id.StartsWith("ns=") ? id : BuildNodeId(id)).ToArray();
                
                _client.AddSubscription(key, fullNodeIds, callback);
                
                _logger.LogInfo($"批量订阅节点成功: {nodeIds.Count} 个节点");
                NotifyInfo?.Invoke(this, new OpcEventArgs 
                { 
                    Code = OpcCode.Working, 
                    Message = $"批量订阅 {nodeIds.Count} 个节点" 
                });
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"批量订阅节点失败: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"批量订阅节点失败: {ex.Message}", 
                    IsError = true 
                });
                return false;
            }
        }

        /// <summary>
        /// 取消订阅
        /// </summary>
        /// <param name="key">订阅键</param>
        /// <returns>是否成功</returns>
        public bool UnsubscribeTag(string key)
        {
            try
            {
                _client?.RemoveSubscription(key);
                _logger.LogInfo($"取消订阅成功: {key}");
                NotifyInfo?.Invoke(this, new OpcEventArgs 
                { 
                    Code = OpcCode.None, 
                    Message = $"取消订阅: {key}" 
                });
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"取消订阅失败: {key}, 错误: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"取消订阅失败: {key} - {ex.Message}", 
                    IsError = true 
                });
                return false;
            }
        }

        /// <summary>
        /// 取消所有订阅
        /// </summary>
        public void UnsubscribeAll()
        {
            try
            {
                _client?.RemoveAllSubscription();
                _logger.LogInfo("取消所有订阅成功");
                NotifyInfo?.Invoke(this, new OpcEventArgs 
                { 
                    Code = OpcCode.None, 
                    Message = "取消所有订阅" 
                    });
            }
            catch (Exception ex)
            {
                _logger.LogError($"取消所有订阅失败: {ex.Message}", ex);
                NotifyError?.Invoke(this, new OpcEventArgs 
                {
                    Code = OpcCode.Error,
                    Message = $"取消所有订阅失败: {ex.Message}", 
                    IsError = true 
                });
            }
        }

        /// <summary>
        /// 构建完整的节点ID
        /// </summary>
        /// <param name="nodeId">节点ID</param>
        /// <returns>完整的节点ID</returns>
        private string BuildNodeId(string nodeId)
        {
            if (string.IsNullOrWhiteSpace(nodeId))
            {
                _logger.LogInfo("节点ID为空，无法构建完整节点ID");
                return string.Empty;
            }

            // 如果已经是完整的节点ID格式，直接返回
            if (nodeId.StartsWith("ns=", StringComparison.OrdinalIgnoreCase))
                return nodeId;

            // 检查GlobalConstants.OPCSUFFIX是否存在
            if (string.IsNullOrWhiteSpace(GlobalConstants.OPCSUFFIX))
            {
                _logger.LogError("GlobalConstants.OPCSUFFIX为空，无法构建完整节点ID");
                return string.Empty;
            }

            // 否则添加默认的命名空间前缀
            var fullNodeId = GlobalConstants.OPCSUFFIX + nodeId;
            return fullNodeId;
        }

        /// <summary>
        /// 连接开始事件处理
        /// </summary>
        private void OnReconnectStarting(object sender, EventArgs e)
        {
            _logger.LogInfo("开始重新连接OPC UA服务器...");
            NotifyInfo?.Invoke(this, new OpcEventArgs 
            { 
                Code = OpcCode.Waiting, 
                Message = "正在重新连接..." 
            });
        }

        /// <summary>
        /// 连接完成事件处理
        /// </summary>
        private void OnConnectComplete(object sender, EventArgs e)
        {
            // 延迟检查连接状态，给连接过程一些时间
            Task.Run(async () =>
            {
                await Task.Delay(1000);
                
                if (IsConnected)
                {
                    _logger.LogInfo($"OPC UA服务器连接成功: {_serverUrl}");
                    _logger.LogInfo($"连接状态详情:");
                    _logger.LogInfo($"  - 会话状态: {(_client?.Session?.Connected ?? false)}");
                    _logger.LogInfo($"  - 客户端状态: {_client?.Connected}");
                    _logger.LogInfo($"  - 服务器URL: {_serverUrl}");
                    
                    ConnectedEvent?.Invoke(this, new OpcEventArgs 
                    { 
                        Code = OpcCode.Connected, 
                        Message = "设备连接成功!",
                        Value = _serverUrl
                    });
                }
                else
                {
                    _logger.LogError("OPC UA连接完成但状态未连接");
                    await DiagnoseConnectionIssues();
                }
            });
        }

        /// <summary>
        /// 连接问题诊断
        /// </summary>
        private async Task DiagnoseConnectionIssues()
        {
            try
            {
                
                // 检查网络连接
                if (!string.IsNullOrEmpty(_serverUrl))
                {
                    var uri = new Uri(_serverUrl);
                    _logger.LogInfo($"网络诊断:");
                    _logger.LogInfo($"  - 目标主机: {uri.Host}");
                    _logger.LogInfo($"  - 目标端口: {uri.Port}");
                    
                    // 尝试网络连接测试
                    try
                    {
                        using (var tcpClient = new System.Net.Sockets.TcpClient())
                        {
                            var connectTask = tcpClient.ConnectAsync(uri.Host, uri.Port);
                            var timeoutTask = Task.Delay(5000);
                            
                            if (await Task.WhenAny(connectTask, timeoutTask) == connectTask)
                            {
                                _logger.LogInfo($"  - 网络连接: 成功");
                            }
                            else
                            {
                                _logger.LogError($"  - 网络连接: 超时");
                            }
                        }
                    }
                    catch (Exception netEx)
                    {
                        _logger.LogError($"  - 网络连接: 失败 - {netEx.Message}");
                    }
                }
                // 触发连接失败事件
                NotifyError?.Invoke(this, new OpcEventArgs 
                { 
                    Code = OpcCode.Error, 
                    Message = "连接诊断完成 - 连接状态异常", 
                    IsError = true,
                    Value = "请检查服务器状态和网络连接"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError($"连接诊断过程中发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 重连完成事件处理
        /// </summary>
        private void OnReconnectComplete(object sender, EventArgs e)
        {
            if (IsConnected)
            {
                _logger.LogInfo($"OPC UA服务器重连成功: {_serverUrl}");
                ConnectedEvent?.Invoke(this, new OpcEventArgs 
                { 
                    Code = OpcCode.Connected, 
                    Message = "设备重连成功!",
                    Value = _serverUrl
                });
            }
            else
            {
                _logger.LogInfo("OPC UA重连完成但状态未连接");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            try
            {
                _reconnectTokenSource?.Cancel();
                _reconnectTokenSource?.Dispose();
                
                if (_client != null)
                {
                    _client.Disconnect();
                }
                
                _logger.LogInfo("OPC UA客户端资源已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError($"释放OPC UA客户端资源时发生错误: {ex.Message}", ex);
            }
        }

        // 事件通知方法
        public void notifyWarning(OpcEventArgs e)
        {
            _logger.LogInfo(e.Value);
            NotifyWarning?.Invoke(this, e);
        }

        public void notifyError(OpcEventArgs e)
        {
            _logger.LogError($"{e.Value} --> {e.Message}");
            NotifyError?.Invoke(this, e);
        }

        public void notifyInfo(OpcEventArgs e)
        {
            _logger.LogInfo(e.Value);
            NotifyInfo?.Invoke(this, e);
        }

        public void connectEvent(OpcEventArgs e)
        {
            ConnectEvent?.Invoke(this, e);
        }

        public void connectedEvent(OpcEventArgs e)
        {
            ConnectedEvent?.Invoke(this, e);
        }

        /// <summary>
        /// 测试OPC UA连接
        /// </summary>
        /// <param name="serverAddress">服务器地址</param>
        /// <returns>测试结果</returns>
        public async Task<bool> TestConnectionAsync(string serverAddress)
        {
            try
            {
                _logger.LogInfo($"🧪 开始测试OPC UA连接: {serverAddress}");
                
                // 创建临时客户端进行测试
                var testClient = new OpcUaClient();
                testClient.UseSecurity = false;
                testClient.UserIdentity = new UserIdentity(new AnonymousIdentityToken());
                
                var normalizedUrl = NormalizeServerUrl(serverAddress);
                _logger.LogInfo($"标准化URL: {normalizedUrl}");
                
                // 尝试连接
                await testClient.ConnectServer(normalizedUrl);
                
                // 检查连接状态
                bool isConnected = testClient.Connected;
                _logger.LogInfo($"测试连接状态: {isConnected}");
                
                if (isConnected)
                {
                    _logger.LogInfo("✅ 连接测试成功");
                    
                    // 测试读取一个基本节点
                    try
                    {
                        var serverStatus = await testClient.ReadNodeAsync<int>("ns=0;i=2259"); // Server_ServerStatus_State
                        _logger.LogInfo($"服务器状态节点读取成功: {serverStatus}");
                    }
                    catch (Exception readEx)
                    {
                        _logger.LogError($"读取服务器状态节点失败: {readEx.Message}");
                    }
                    
                    // 断开测试连接
                    testClient.Disconnect();
                    
                    return true;
                }
                else
                {
                    _logger.LogError("❌ 连接测试失败 - 客户端状态未连接");
                    testClient.Disconnect();
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"❌ 连接测试异常: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 获取连接状态详情
        /// </summary>
        /// <returns>状态信息</returns>
        public string GetConnectionStatusDetails()
        {
            var details = new StringBuilder();
            details.AppendLine("OPC UA连接状态详情:");
            details.AppendLine($"  - 服务器URL: {_serverUrl ?? "未设置"}");
            details.AppendLine($"  - 客户端对象: {(_client != null ? "已创建" : "未创建")}");
            details.AppendLine($"  - 客户端连接状态: {(_client?.Connected ?? false)}");
            details.AppendLine($"  - 会话状态: {(_client?.Session?.Connected ?? false)}");
            details.AppendLine($"  - 会话ID: {(_client?.Session?.SessionId ?? null)}");
            details.AppendLine($"  - 安全策略: {(_client?.UseSecurity == false ? "None" : "Enabled")}");
            details.AppendLine($"  - 重连周期: {(_client?.ReconnectPeriod ?? 0)}ms");
            details.AppendLine($"  - 正在连接: {_isConnecting}");
            
            return details.ToString();
        }
    }
}
