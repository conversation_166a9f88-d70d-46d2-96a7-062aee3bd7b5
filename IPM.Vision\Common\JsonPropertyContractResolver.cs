﻿using Newtonsoft.Json.Serialization;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common
{
    public class JsonPropertyContractResolver : DefaultContractResolver
    {
        private IEnumerable<string> _listExclude;

        public JsonPropertyContractResolver(params string[] ignoreProperties) => this._listExclude = (IEnumerable<string>)ignoreProperties;

        protected override IList<JsonProperty> CreateProperties(
          Type type,
          MemberSerialization memberSerialization)
        {
            return (IList<JsonProperty>)base.CreateProperties(type, memberSerialization).ToList<JsonProperty>().FindAll((Predicate<JsonProperty>)(p => !this._listExclude.Contains<string>(p.PropertyName)));
        }
    }
}
