﻿<UserControl x:Class="IPM.Vision.Views.Dialogs.CameraCtrlDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:canon="clr-namespace:IPM.Vision.Views.CanonControls"
             DataContext="{Binding CameraCtrlDialogViewModel, Source={StaticResource Locator}}"
             xmlns:controls="clr-namespace:IPM.Vision.Views.CustomControls"
             mc:Ignorable="d"
             d:DesignHeight="950" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="White" CornerRadius="5" Width="720">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Background="#283643" CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText Text="{Binding Title}" Foreground="White" FontSize="18" VerticalAlignment="Center" Margin="10 0 0 0"/>
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Template="{StaticResource CloseTemplate}"
                            Width="40"
                            Height="40"
                            Content="&#xf00d;"
                            Command="{Binding CloseCommand}"/>
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1"
                    Height="420"
                    Margin="4"
                    BorderBrush="Gray"
                    BorderThickness="0.4"
                    Visibility="{Binding ShowCamera,Converter={StaticResource BoolToVisibilityConverter}}">
                <hc:Interaction.Triggers>
                    <hc:EventTrigger EventName="Loaded">
                        <hc:EventToCommand Command="{Binding CameraLoadedCommand}" PassEventArgsToCommand="True"/>
                    </hc:EventTrigger>
                </hc:Interaction.Triggers>
                <controls:CaptureView/>
            </Border>
            <Border Grid.Row="2" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <hc:UniformSpacingPanel Margin="0 0 10 0" Orientation="Vertical" Spacing="10"  Grid.Column="0">
                        <hc:Interaction.Triggers>
                            <hc:EventTrigger EventName="Loaded">
                                <hc:EventToCommand Command="{Binding LeftPanelLoadCommand}" PassEventArgsToCommand="True"/>
                            </hc:EventTrigger>
                        </hc:Interaction.Triggers>
                        <hc:TextBox
                            FontSize="14"
                            hc:InfoElement.Title="参数名称"
                            hc:InfoElement.TitlePlacement="Left"
                            Text="{Binding CameraParamModel.ParamName,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <canon:AeComboBox
                            FontSize="14"
                            hc:TitleElement.Title="曝光模式"
                            SelectedValue="{Binding CameraParamModel.Ae, Mode=TwoWay}"
                            Style="{StaticResource ComboBoxExtend}"/>
                        <canon:DriveModeComboBox
                            FontSize="14"
                            hc:TitleElement.Title="快门驱动方式"
                            SelectedValue="{Binding CameraParamModel.DriveMode, Mode=TwoWay}"
                            Style="{StaticResource ComboBoxExtend}" />
                        <canon:AvComboBox
                            FontSize="14"
                            hc:TitleElement.Title="光圈"
                            SelectedItem="{Binding CameraParamModel.Av, Mode=TwoWay}"
                            Style="{StaticResource ComboBoxExtend}" />
                        <canon:ExposureCompComboBox
                            FontSize="14"
                            hc:TitleElement.Title="曝光补偿"
                            SelectedValue="{Binding CameraParamModel.Exposure}"
                            Style="{StaticResource ComboBoxExtend}" />
                        <canon:AfModeComboBox
                            FontSize="14"
                            hc:TitleElement.Title="自动对焦操作"
                            SelectedValue="{Binding CameraParamModel.AfMode}"
                            Style="{StaticResource ComboBoxExtend}" />
                        <canon:ImageQualityComboBox
                            FontSize="14"
                            hc:TitleElement.Title="照片格式"
                            SelectedValue="{Binding CameraParamModel.ImageQuality}"
                            Style="{StaticResource ComboBoxExtend}" />
                    </hc:UniformSpacingPanel>
                    <hc:UniformSpacingPanel Margin="10 0 0 0" Orientation="Vertical" Spacing="10" Grid.Column="1">
                        <hc:Interaction.Triggers>
                            <hc:EventTrigger EventName="Loaded">
                                <hc:EventToCommand Command="{Binding RightPanelLoadCommand}" PassEventArgsToCommand="True"/>
                            </hc:EventTrigger>
                        </hc:Interaction.Triggers>
                        <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                            <Button  Style="{StaticResource ButtonInfo}" Content="{Binding BtnMessage,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" Command="{Binding OpenCommand}"/>
                        </hc:UniformSpacingPanel>
                        <canon:EvfAfModeComboBox
                            FontSize="14"
                            hc:TitleElement.Title="自动对焦方式"
                            SelectedValue="{Binding CameraParamModel.EvfAfMode}"
                            Style="{StaticResource ComboBoxExtend}" />
                        <canon:IsoComboBox
                            FontSize="14"
                            hc:TitleElement.Title="感光度"
                            SelectedValue="{Binding CameraParamModel.Iso}"
                            Style="{StaticResource ComboBoxExtend}" />
                        <canon:WhiteBalanceComboBox
                            FontSize="14"
                            hc:TitleElement.Title="白平衡"
                            SelectedValue="{Binding CameraParamModel.WhiteBalance}"
                            Style="{StaticResource ComboBoxExtend}" />
                        <canon:TvComboBox
                            FontSize="14"
                            hc:TitleElement.Title="快门触发速度"
                            SelectedValue="{Binding CameraParamModel.Tv}"
                            Style="{StaticResource ComboBoxExtend}" />
                        <canon:PictureStyleComboBox
                            FontSize="14"
                            hc:TitleElement.Title="照片风格"
                            SelectedValue="{Binding CameraParamModel.PictureStyle}"
                            Style="{StaticResource ComboBoxExtend}" />
                    </hc:UniformSpacingPanel>
                </Grid>
            </Border>
            <Border Grid.Row="3" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Button Grid.Column="0" Height="35" Width="220" Style="{StaticResource ButtonPrimary}" Content="保存" Command="{Binding SaveCommand}"/>
                    <Button Grid.Column="1" Height="35" Width="220" Content="取消" Command="{Binding CloseCommand}"/>

                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
