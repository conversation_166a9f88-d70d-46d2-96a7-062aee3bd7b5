﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class AeComboBox : PropertyComboBox, IObserver
    {


        private EDSDK.EdsPropertyDesc _desc;


        public AeComboBox()
        {
            if (this.items == null) items = new Dictionary<uint, string>();
            items.Add(22, "场景智能自动");
            items.Add(0, "程序自动曝光");
            items.Add(1, "快门优先自动曝光");
            items.Add(2, "光圈优先自动曝光");
            items.Add(3, "手动曝光");
            items.Add(55, "灵活优先自动曝光");
            items.Add(4, "B门");
            items.Add(7, "C1");
            items.Add(16, "C2");
            items.Add(17, "C3");
            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }





        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = (KeyValuePair<uint, string>)e.AddedItems[0];
                CameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_AE_MODE, (IntPtr)selectedItem.Key));
            }
            base.OnSelectionChanged(e);
        }

        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();

                if (propertyID == EDSDK.PropID_AEModeSelect)
                {
                    uint property = model.AEMode;
                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            this.UpdateProperty(property);
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:
                            _desc = model.AEModeDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            this.UpdateProperty(property);
                            break;

                    }
                }
            }
        }
    }
}
