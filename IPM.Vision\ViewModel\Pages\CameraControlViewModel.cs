﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.Dialogs;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.Dialogs;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace IPM.Vision.ViewModel.Pages
{
    public class CameraControlViewModel:ViewModelBase
    {
        private readonly MainCameraParaService _cameraService;
        private bool _isLoading = false;
        private readonly NLogHelper _logger;
        private readonly ProcessParaService _processParaService;
        private string _paraName;
        private ObservableCollection<ObservableMainCameraParaModel> _dataList;
        public CameraControlViewModel(IMainCameraParaService cameraService,IProcessParaService processParaService,NLogHelper logger)
        {
            _cameraService = (MainCameraParaService)cameraService;
            _processParaService = (ProcessParaService)processParaService;
            _logger = logger;
        }

        public ObservableCollection<ObservableMainCameraParaModel> DataList
        {
            get => _dataList;
            set => SetProperty(ref _dataList, value);
        }
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        public string ParamName
        {
            get => _paraName;
            set => SetProperty(ref _paraName, value);
        }

        public IRelayCommand AddParamCommand => new RelayCommand(async () =>
        {
            var result = await Dialog.Show<CameraCtrlDialog>()
             .Initialize<CameraCtrlDialogViewModel>(vm =>
             {
                 vm.Title = "新增相机参数";
                 vm.IsInsert = true;
                 vm.CameraParamModel = new ObservableMainCameraParaModel();
             })
             .GetResultAsync<bool>();
            SearchEvent();
        });

        public IRelayCommand<ObservableMainCameraParaModel> ModifyParamCommand => new RelayCommand<ObservableMainCameraParaModel>(async (model) =>
        {
            var result = await Dialog.Show<CameraCtrlDialog>()
             .Initialize<CameraCtrlDialogViewModel>(vm =>
             {
                 vm.Title = "修改相机参数";
                 vm.IsInsert = false;
                 vm.CameraParamModel = model;
             })
             .GetResultAsync<bool>();
            SearchEvent();
        });

        public IRelayCommand SearchCommand => new RelayCommand(() =>
        {
            SearchEvent();
        });

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            SearchEvent();
        });

        public IRelayCommand<ObservableMainCameraParaModel> DeleteCommand => new RelayCommand<ObservableMainCameraParaModel>(async (model) =>
        {
            var temp = await _processParaService.getIsAny(item => item.MainCameraId == model.Id);
            if (temp)
            {
                HandyControl.Controls.MessageBox.Error("无法删除该参数，有步骤在使用！");
                return;
            }
            var tempResult = HandyControl.Controls.MessageBox.Show($"确认删除参数信息？", "提示", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (tempResult == MessageBoxResult.Yes)
            {
                var deleteResult = await _cameraService.Delete(model.Id);
                if (deleteResult) SearchEvent();
            }

        });

        private async void SearchEvent()
        {
            IsLoading = true;
            try
            {
                var temp = await _cameraService.getByWhereIF(!string.IsNullOrEmpty(ParamName), item => item.ParamName.Contains(ParamName));
                DataList = temp.MapTo<List<MainCameraParamModel>, ObservableCollection<ObservableMainCameraParaModel>>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
            }
            finally
            {
                IsLoading = false;
            }
        }
    }
}
