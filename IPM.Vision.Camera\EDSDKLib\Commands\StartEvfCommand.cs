﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class StartEvfCommand : BasicCommand
    {
        public StartEvfCommand(ref CanonCameraModel model)
            : base(ref model)
        {
        }

        public override bool Execute()
        {
            uint num = 0u;
            if (_model.EvfMode == 0)
            {
                uint num2 = 1u;
                num = EDSDK.EdsSetPropertyData(_model.Camera, 1281u, 0, 4, num2);
            }

            if (num == 0)
            {
                uint evfOutputDevice = _model.EvfOutputDevice;
                evfOutputDevice = 2u;
                num = EDSDK.EdsSetPropertyData(_model.Camera, 1280u, 0, 4, evfOutputDevice);
            }

            switch (num)
            {
                case 129u:
                    {
                        CameraEvent e2 = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e2);
                        return false;
                    }
                default:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e);
                        break;
                    }
                case 0u:
                    break;
            }

            return true;
        }
    }
}
