﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.Dialogs;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.Dialogs;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace IPM.Vision.ViewModel.Pages
{
    public class ProductManagePageViewModel:ViewModelBase
    {
        private readonly ProductParaService _productService;
        private readonly ProcessParaService _processParaService;
        private string _productNumber = string.Empty;
        public ProductManagePageViewModel(IProductParaService productService, IProcessParaService processParaService)
        {
            _productService = (ProductParaService)productService;
            _processParaService = (ProcessParaService)processParaService;
        }

        public string ProductNumber
        {
            get => _productNumber;
            set => SetProperty(ref _productNumber, value);
        }

        private ObservableCollection<ObservableProductModel> _productModelList;

        public ObservableCollection<ObservableProductModel> ProductModelList
        {
            get => _productModelList;
            set => SetProperty(ref _productModelList, value);
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            SearchEvent();
        });

        public IRelayCommand SearchCommand => new RelayCommand(() =>
        {
            SearchEvent();
        });

        private async void SearchEvent()
        {
            var result = await _productService.getByWhereIF(!string.IsNullOrEmpty(ProductNumber),item=>item.ProductNumber.Contains(ProductNumber));
            ProductModelList = result.MapTo<List<ProductParamModel>, ObservableCollection<ObservableProductModel>>();
        }


        public IRelayCommand AddProductCommand => new RelayCommand(async () =>
        {
            var temp = await Dialog.Show<ProductManageDialog>("main")
                .Initialize<ProductManageDialogViewModel>(vm =>
                {
                    vm.Title = "新增产品信息";
                    vm.ProductModel = new ObservableProductModel();
                    vm.IsInsert = true;
                }).GetResultAsync<bool>();
            if(temp) SearchEvent();
        });

        public IRelayCommand<ObservableProductModel> ModifyProductCommand => new RelayCommand<ObservableProductModel>(async (models) =>
        {
            var temp = await Dialog.Show<ProductManageDialog>("main")
                .Initialize<ProductManageDialogViewModel>(vm =>
                {
                    vm.Title = "修改产品信息";
                    vm.ProductModel = models;
                    vm.IsInsert = false;
                }).GetResultAsync<bool>();
            if (temp) SearchEvent();
        });

        public IRelayCommand<ObservableProductModel> DeleteCommand => new RelayCommand<ObservableProductModel>(async (models) =>
        {
            try
            {
                var temp = HandyControl.Controls.MessageBox.Show($"确认删除该产品:[{models.ProductNumber}]信息？", "提示", MessageBoxButton.OKCancel, MessageBoxImage.Question);
                if (temp == MessageBoxResult.OK)
                {
                    if(models.ProcessIds != null && models.ProcessIds.Count() > 0)
                    {
                        foreach (var item in models.ProcessIds)
                        {
                           await _processParaService.Delete(item);
                        }
                    }
                    var result = await _productService.DeleteWhere(item => item.Id == models.Id);
                    if (result) SearchEvent();
                }
            }
            catch (Exception ex) {
                HandyControl.Controls.MessageBox.Error(ex.Message);            
            }
            
        });
    }
}
