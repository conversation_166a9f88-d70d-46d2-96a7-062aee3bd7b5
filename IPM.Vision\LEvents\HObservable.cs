﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.LEvents
{
    public class HObservable
    {
        private List<IHObservable> observers = new List<IHObservable>();

        public void Add(ref IHObservable observer)
        {
            if (observers.IndexOf(observer) == -1)
            {
                observers.Add(observer);
            }
        }

        public void Add(IHObservable observer)
        {
            if (observers.IndexOf(observer) == -1)
            {
                observers.Add(observer);
            }
        }

        public void Remove(ref IHObservable observer)
        {
            observers.Remove(observer);
        }

        public void RemoveAll()
        {
            observers.Clear();
        }

        public void NotifyObservers(HEquipmentStatusArgs e)
        {
            for (int i = 0; i < observers.Count; i++)
            {
                observers[i].UpdateAsync(this, e);
            }
        }
    }
}
