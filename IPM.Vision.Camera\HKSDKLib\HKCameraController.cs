﻿using IPM.Vision.Camera.EDSDKLib.Commands;
using MvCameraControl;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.HKSDKLib
{
    public class HKCameraController
    {
        private IDeviceInfo _deviceInfo;
        private IDevice _device;
        public HKCameraController(IDeviceInfo deviceInfo,IDevice device)
        {
            _deviceInfo = deviceInfo;
            _device = device;
        }
        public IDeviceInfo DeviceInfo { get { return _deviceInfo; } }

       
    }
}
