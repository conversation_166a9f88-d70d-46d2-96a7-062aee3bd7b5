﻿<Page x:Class="IPM.Vision.Views.Pages.SettingPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:IPM.Vision.Views.Pages"
      mc:Ignorable="d"
      xmlns:hc="https://handyorg.github.io/handycontrol"
      xmlns:dd="urn:gong-wpf-dragdrop"
      DataContext="{Binding SettingPageViewModel, Source={StaticResource Locator}}"
      xmlns:enumDatas="clr-namespace:IPM.Vision.Common"
      xmlns:convert="clr-namespace:IPM.Vision.Common.Converts"
      d:DesignHeight="450" d:DesignWidth="800"
      Title="SettingPage">
    <Page.Resources>
        <ResourceDictionary>
            <convert:UsbAndPortVisiblityConvert x:Key="UsbAndPortVisiblityConvert"/>
            <convert:CameraToVisible x:Key="CameraToVisiable"/>
            <convert:StorageVisiableConvert x:Key="StorageVisiableConvert"/>
            <convert:ContentToVisiableConvert x:Key="ContentToVisiableConvert"/>
        </ResourceDictionary>
    </Page.Resources>
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
        <hc:EventTrigger EventName="Unloaded">
            <hc:EventToCommand Command="{Binding UnLoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <hc:WaterfallPanel Orientation="Horizontal" Groups="3">
        <GroupBox Grid.Column="0" Header="系统配置" HorizontalContentAlignment="Left" Style="{StaticResource GroupBoxTab}" Background="White" >
            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10">
                <hc:UniformSpacingPanel Orientation="Vertical" Spacing="5">
                    <hc:SimpleText Text="系统图标" Margin="10 0 0 0"/>
                    <hc:ImageSelector
                        Width="100"
                        Height="100"
                        Filter="所有文件(*.*)|*.*">
                        <hc:Interaction.Triggers>
                            <hc:EventTrigger EventName="ImageSelected">
                                <hc:EventToCommand Command="{Binding ImageSelectedCommand}" PassEventArgsToCommand="True" />
                            </hc:EventTrigger>
                        </hc:Interaction.Triggers>
                    </hc:ImageSelector>
                </hc:UniformSpacingPanel>
                <hc:TextBox hc:TitleElement.Title="工位" Text="{Binding Config.WorkStation,Mode=TwoWay}"/>
                <hc:ComboBox hc:TitleElement.Title="扫码枪接口类型"
                             ItemsSource="{Binding ConnectItem}"
                             DisplayMemberPath="Label"
                             SelectedValuePath="Value"
                             SelectedValue="{Binding Config.ScanType}"/>
                <hc:ComboBox hc:TitleElement.Title="扫码枪端口"
                             SelectedValue="{Binding Config.ScanPort}"
                             DisplayMemberPath="Label"
                             SelectedValuePath="Value"
                             Visibility="{Binding Config.ScanType,Converter={StaticResource UsbAndPortVisiblityConvert}}"
                             ItemsSource="{Binding PortItem}"/>
                <hc:ComboBox hc:InfoElement.Title="OCR条码识别"
                             ItemsSource="{Binding OpenItem}"
                             DisplayMemberPath="Label"
                             SelectedValuePath="Value"
                             SelectedValue="{Binding Config.OpenOCR}"/>
                <!--<hc:ComboBox hc:InfoElement.Title="光源连接模式" SelectedValue="{Binding LightType}">
                <hc:CheckComboBoxItem Content="串口通讯" Tag="0"/>
                <hc:CheckComboBoxItem Content="TCP通讯" Tag="1"/>
                </hc:ComboBox>-->
                <!--<hc:TextBox hc:InfoElement.Title="光源IP地址" Text="{Binding }" />-->
                <hc:ComboBox hc:InfoElement.Title="主光源端口"
                             SelectedValue="{Binding Config.LightPort}"
                             DisplayMemberPath="Label"
                             SelectedValuePath="Value"
                             ItemsSource="{Binding PortItem}"/>
                <hc:ComboBox hc:InfoElement.Title="采集相机类型"
                             SelectedValue="{Binding Config.MainCameraType}"
                             DisplayMemberPath="Label"
                             SelectedValuePath="Value"
                             ItemsSource="{Binding CameraItem}"/>
                <hc:TextBox hc:InfoElement.Title="采集相机IP地址"
                            Text="{Binding Config.MainCameraAddress}"
                            Visibility="{Binding Config.MainCameraType,Converter={StaticResource CameraToVisiable}}"
                            />
                <hc:ComboBox hc:TitleElement.Title="开启原点相机"
                             ItemsSource="{Binding OpenItem}"
                             DisplayMemberPath="Label"
                             SelectedValuePath="Value"
                             SelectedValue="{Binding Config.HaveMarkCamera}"
                             />
                <hc:TextBox hc:InfoElement.Title="原点相机IP地址"
                            Visibility="{Binding Config.HaveMarkCamera,Converter={StaticResource EnumToVisibleConvert},ConverterParameter=OPEN}"
                            Text="{Binding Config.MarkCameraAddress}" />
                <hc:TextBox hc:InfoElement.Title="本机IP地址"
                            Visibility="{Binding Config.HaveMarkCamera,Converter={StaticResource EnumToVisibleConvert},ConverterParameter=OPEN}"
                            Text="{Binding Config.LocalAddress}" />
                <!--<hc:ComboBox hc:InfoElement.Title="副相机光源端口"
                SelectedValue="{Binding Config.MarkLightPort}"
                DisplayMemberPath="Label"
                SelectedValuePath="Value"
                Visibility="{Binding Config.HaveMarkCamera,Converter={StaticResource EnumToVisibleConvert},ConverterParameter=OPEN}"
                ItemsSource="{Binding PortItem}"/>-->
                <hc:TextBox hc:InfoElement.Title="设备通讯IP地址"
                            Text="{Binding Config.PlcAddress}"/>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <hc:TextBox hc:TitleElement.Title="AI检测文件夹" Text="{Binding Config.AiSource}"/>
                    <Button Grid.Column="1" Content="选择文件夹" VerticalAlignment="Bottom" Margin="10 0 0 0" Style="{StaticResource ButtonInfo}" Command="{Binding ChoseFolderCommand}" CommandParameter="1"/>
                </Grid>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <hc:TextBox hc:TitleElement.Title="AI检测结果文件夹" Text="{Binding Config.AiResult,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                    <Button Grid.Column="1" Content="选择文件夹" VerticalAlignment="Bottom" Margin="10 0 0 0" Style="{StaticResource ButtonInfo}" Command="{Binding ChoseFolderCommand}" CommandParameter="2"/>
                </Grid>
            </hc:UniformSpacingPanel>
        </GroupBox>
        <GroupBox Grid.Column="1" Margin="10 0 0 0" Header="照片配置" HorizontalContentAlignment="Left" Style="{StaticResource GroupBoxTab}" Background="White">
            <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10">
                <hc:ComboBox hc:TitleElement.Title="照片存储方式"
                             SelectedValue="{Binding Config.SaveType}"
                             ItemsSource="{Binding FolderItem}"
                             DisplayMemberPath="Label"
                             SelectedValuePath="Value"
                             />
                <Grid Visibility="{Binding Config.SaveType,Converter={StaticResource StorageVisiableConvert}}">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <hc:TextBox hc:TitleElement.Title="照片存储地址" Text="{Binding Config.SaveFolder}"/>
                    <Button Grid.Column="1" Content="选择文件夹" VerticalAlignment="Bottom" Margin="10 0 0 0" Style="{StaticResource ButtonInfo}" Command="{Binding ChoseFolderCommand}" CommandParameter="0"/>
                </Grid>

                <hc:TextBox hc:TitleElement.Title="远程存储地址" Text="{Binding Config.SaveFolderAddress}" Visibility="{Binding Config.SaveType,Converter={StaticResource StorageVisiableConvert}, ConverterParameter=Reverse}"/>

                <hc:ComboBox hc:InfoElement.Title="文件夹命名关系"
                             ItemsSource="{Binding FolderNameTypeItem}"
                             DisplayMemberPath="Label"
                             SelectedValuePath="Value"
                             SelectedValue="{Binding Config.FolderNameType}"/>

                <Border Height="280">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <hc:UniformSpacingPanel Orientation="Horizontal" Grid.Row="0"  Margin="8 0 0 0" Spacing="10">
                            <hc:SimpleText Text="文件夹命名规则" VerticalAlignment="Bottom"/>
                            <Button Style="{StaticResource ButtonPrimary.Small}"
                                    FontFamily="{StaticResource FontAwesome}"
                                    Command="{Binding AddRuleCommand}"
                                    CommandParameter="folder"
                                    Content="&#xf067; 添加规则"/>
                        </hc:UniformSpacingPanel>

                        <DataGrid Margin="0 10 0 0"
                                  Grid.Row="1"
                                  x:Name="FolderNameDataGrid"
                                  CanUserSortColumns="False"
                                  SelectionMode="Single"
                                  AutoGenerateColumns="False"
                                  dd:DragDrop.IsDragSource="True"
                                  dd:DragDrop.IsDropTarget="True"
                                  dd:DragDrop.UseDefaultEffectDataTemplate="True"
                                  dd:DragDrop.DropHandler="{Binding}"
                                  dd:DragDrop.UseDefaultDragAdorner="True"
                                  ItemsSource="{Binding Config.FolderNameList}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="序号" Width="60" Binding="{Binding Index}"/>
                                <DataGridTemplateColumn Header="命名类型" Width="220">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <ComboBox ItemsSource="{Binding DataContext.NameItem, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                      SelectedValue="{Binding NameType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                                                      SelectedValuePath="Value"
                                                      DisplayMemberPath="Label"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Header="自定义内容" Width="170">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding Content,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NameType,Converter={StaticResource ContentToVisiableConvert}}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Header="操作">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="5">
                                                <Button Content="&#xf1f8;"
                                                        Style="{StaticResource ButtonDanger}"
                                                        FontFamily="{StaticResource FontAwesome}"
                                                        Command="{Binding DataContext.DelFolderCommand,RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                        CommandParameter="{Binding}"/>
                                            </hc:UniformSpacingPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>

                <Border Height="280">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <hc:UniformSpacingPanel Orientation="Horizontal" Grid.Row="0"  Margin="8 0 0 0" Spacing="10">
                            <hc:SimpleText Text="照片命名规则" VerticalAlignment="Bottom"/>
                            <Button Style="{StaticResource ButtonPrimary.Small}"
                                    FontFamily="{StaticResource FontAwesome}"
                                    Command="{Binding AddRuleCommand}"
                                    CommandParameter="pictureName"
                                    Content="&#xf067; 添加规则"/>
                        </hc:UniformSpacingPanel>
                        <DataGrid Margin="0 10 0 0"
                                  x:Name="PictureNameDataGrid"
                                  Grid.Row="1"
                                  dd:DragDrop.IsDragSource="True"
                                  dd:DragDrop.IsDropTarget="True"
                                  dd:DragDrop.DropHandler="{Binding}"
                                  AutoGenerateColumns="False"
                                  ItemsSource="{Binding Config.PictureList}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="序号" Width="60" Binding="{Binding Index}"/>
                                <DataGridTemplateColumn Header="命名类型" Width="220">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <ComboBox ItemsSource="{Binding DataContext.NameItem, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                      SelectedValue="{Binding NameType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                                                      SelectedValuePath="Value"
                                                      DisplayMemberPath="Label"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Header="自定义内容" Width="170">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <TextBox Text="{Binding Content,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" IsEnabled="{Binding NameType,Converter={StaticResource ContentToVisiableConvert}}"/>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                                <DataGridTemplateColumn Header="操作">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="5">
                                                <Button Content="&#xf1f8;"
                                                        Style="{StaticResource ButtonDanger}"
                                                        FontFamily="{StaticResource FontAwesome}"
                                                        Command="{Binding DataContext.DelFileNameCommand,RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                        CommandParameter="{Binding}"/>
                                            </hc:UniformSpacingPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Grid>
                </Border>

                <!--<Border Height="280">
                <Grid>
                <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <hc:UniformSpacingPanel Orientation="Horizontal" Grid.Row="0"  Margin="8 0 0 0" Spacing="10">
                <hc:SimpleText Text="水印命名规则" VerticalAlignment="Bottom"/>
                <Button Style="{StaticResource ButtonPrimary.Small}"
                FontFamily="{StaticResource FontAwesome}"
                Command="{Binding AddRuleCommand}"
                CommandParameter="watermark"
                Content="&#xf067; 添加规则"/>
                <Button Style="{StaticResource ButtonPrimary.Small}">
                <Button.Content>
                <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="3">
                <hc:SimpleText Foreground="White" Text="水印位置" VerticalAlignment="Center"/>
                </hc:UniformSpacingPanel>
                </Button.Content>
                </Button>
                </hc:UniformSpacingPanel>
                <DataGrid Margin="0 10 0 0"
                Grid.Row="1"
                x:Name="WatermarkNameDataGrid"
                dd:DragDrop.IsDragSource="True"
                dd:DragDrop.IsDropTarget="True"
                dd:DragDrop.DropHandler="{Binding}"
                AutoGenerateColumns="False"
                ItemsSource="{Binding Config.WatermarkList}">
                <DataGrid.Columns>
                <DataGridTextColumn Header="序号" Width="60" Binding="{Binding Index}"/>
                <DataGridTemplateColumn Header="命名类型" Width="220">
                <DataGridTemplateColumn.CellTemplate>
                <DataTemplate>
                <ComboBox ItemsSource="{Binding DataContext.NameItem, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                SelectedValue="{Binding NameType,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                SelectedValuePath="Value"
                DisplayMemberPath="Label"/>
                </DataTemplate>
                </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="自定义内容" Width="170">
                <DataGridTemplateColumn.CellTemplate>
                <DataTemplate>
                <TextBox Text="{Binding Content,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged }" IsEnabled="{Binding NameType,Converter={StaticResource ContentToVisiableConvert}}"/>
                </DataTemplate>
                </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTemplateColumn Header="操作">
                <DataGridTemplateColumn.CellTemplate>
                <DataTemplate>
                <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="5">
                <Button Content="&#xf1f8;"
                Style="{StaticResource ButtonDanger}"
                FontFamily="{StaticResource FontAwesome}"
                Command="{Binding DataContext.DelWaterCommand,RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                CommandParameter="{Binding}"/>
                </hc:UniformSpacingPanel>
                </DataTemplate>
                </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                </DataGrid.Columns>
                </DataGrid>
                </Grid>
                </Border>-->
            </hc:UniformSpacingPanel>
        </GroupBox>
    </hc:WaterfallPanel>
</Page>
