﻿using CommunityToolkit.Mvvm.Input;
using DocumentFormat.OpenXml.Drawing.Charts;
using HandyControl.Data;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.ViewModel.ObservableModel;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.Pages
{
    public class ReportPageViewModel:ViewModelBase
    {
        private ReportSearchModel _reportSearchModel = new ReportSearchModel();
        private ObservableCollection<ObservableReportModel> _dataList;
        private readonly ReportService _reportService;
        private NLogHelper _logger;
        private bool _loading = false;
        private ObservableReportModel _currentSelect;

        public ReportPageViewModel(NLogHelper logger,IReportService reportService)
        {
            _logger = logger;
            _reportService = (ReportService)reportService;
        }

        public ObservableReportModel CurrentSelect
        {
            get => _currentSelect;
            set => SetProperty(ref _currentSelect, value);
        }

        public bool Loading
        {
            get => _loading;
            set => SetProperty(ref _loading, value);
        }
        public ReportSearchModel ReportSearchModel
        {
            get => _reportSearchModel;
            set => SetProperty(ref _reportSearchModel, value);
        }
        public ObservableCollection<ObservableReportModel> DataList
        {
            get => _dataList;
            set => SetProperty(ref _dataList, value);
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            SerachEevent();
        });

        public IRelayCommand SearchCommand => new RelayCommand(() =>
        {
            SerachEevent();
        });

        public IRelayCommand ResetCommand => new RelayCommand(() =>
        {
            ReportSearchModel = new ReportSearchModel();
        });

        private async void SerachEevent()
        {
            Loading = true;
            try
            {
                var count = await _reportService.CountAsync(ReportSearchModel);
                ReportSearchModel.PageCount = count / ReportSearchModel.PageSize;
                DataList = await _reportService.GetDataByPage(ReportSearchModel);
            }
            catch (Exception ex) {

                _logger.LogError(ex);

            }
            finally
            {
                Loading = false;
            }
        }
        public IAsyncRelayCommand<FunctionEventArgs<int>> PageChangedCommand => new AsyncRelayCommand<FunctionEventArgs<int>>(PageChangedEvent);

        private async Task PageChangedEvent(FunctionEventArgs<int> obj)
        {
            await Task.Run(() =>
            {
                ReportSearchModel.PageIndex = obj.Info;
                SerachEevent();

            });
           
        }

        public IRelayCommand<string> OpenDirectoryCommand => new RelayCommand<string>((picturePath) =>
        {
            System.Diagnostics.Process.Start("Explorer", $"/select,{picturePath.Replace('/', '\\')}");
        });

        public IRelayCommand<object> PreviewCommand => new RelayCommand<object>((obj) =>
        {
            if(CurrentSelect != null)
            {
                if (FileHelper.FileIsExists(CurrentSelect.PicturePath))
                {
                    Process.Start(new ProcessStartInfo(CurrentSelect.PicturePath) { UseShellExecute = true });
                }
            }
        });
        public IRelayCommand<object> OpenPathCommand => new RelayCommand<object>((obj) =>
        {
            if (CurrentSelect != null)
            {
                if (FileHelper.FileIsExists(CurrentSelect.PicturePath))
                {
                    System.Diagnostics.Process.Start("Explorer", $"/select,{CurrentSelect.PicturePath.Replace('/', '\\')}");
                }
            }
        });

    }

    public class ReportSearchModel : ViewModelBase
    {
        private string _orderNumber = string.Empty;
        private string _serialNumber = string.Empty;
        private string _productNumber = string.Empty;
        private DateTime _beginTime = DateTime.Now.AddDays(-7);
        private DateTime _endTime = DateTime.Now.AddDays(1);
        private int _pageSize = 20;
        private int _pageCount = 1;
        private int _pageIndex = 1;

        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value);
        }

        public string ProductNumber
        {
            get => _productNumber;
            set => SetProperty(ref _productNumber, value);
        }

        public string SerialNumber
        {
            get => _serialNumber;
            set => SetProperty(ref _serialNumber, value);
        }

        public DateTime BeginTime
        {
            get => _beginTime; 
            set => SetProperty(ref _beginTime, value);
        }

        public DateTime EndTime
        {
            get => _endTime;
            set => SetProperty(ref _endTime, value);
        }

        public int PageSize
        {
            get =>_pageSize;
            set => SetProperty(ref _pageSize, value);
        }

        public int PageIndex
        {
            get => _pageIndex;
            set => SetProperty(ref _pageIndex, value);
        }

        public int PageCount
        {
            get => _pageCount; 
            set => SetProperty(ref _pageCount, value);
        }
    }

    public class ManualReviewSearchModel : ViewModelBase
    {
        private string _orderNumber = string.Empty;
        private string _serialNumber = string.Empty;
        private string _productNumber = string.Empty;
        private int _pageSize = 20;
        private int _pageCount = 1;
        private int _pageIndex = 1;

        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value);
        }

        public string ProductNumber
        {
            get => _productNumber;
            set => SetProperty(ref _productNumber, value);
        }

        public string SerialNumber
        {
            get => _serialNumber;
            set => SetProperty(ref _serialNumber, value);
        }

        public int PageSize
        {
            get => _pageSize;
            set => SetProperty(ref _pageSize, value);
        }

        public int PageIndex
        {
            get => _pageIndex;
            set => SetProperty(ref _pageIndex, value);
        }

        public int PageCount
        {
            get => _pageCount; 
            set => SetProperty(ref _pageCount, value);
        }
    }
}
