<UserControl
    x:Class="IPM.Vision.Views.Dialogs.ImportPinCoordinatesDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:IPM.Vision.Views.Dialogs"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="400"
    d:DesignWidth="600"
    DataContext="{Binding ImportPinCoordinatesDialogViewModel, Source={StaticResource Locator}}"
    mc:Ignorable="d">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}" />
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border
        Width="600"
        Height="400"
        Background="White"
        CornerRadius="5">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <Border
                Grid.Row="0"
                Background="#283643"
                CornerRadius="5 5 0 0">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="Auto" />
                    </Grid.ColumnDefinitions>
                    <Border Grid.Column="0">
                        <hc:SimpleText
                            Margin="10,0,0,0"
                            VerticalAlignment="Center"
                            FontSize="18"
                            Foreground="White"
                            Text="{Binding Title}" />
                    </Border>
                    <Border Grid.Column="1">
                        <Button
                            Width="40"
                            Height="40"
                            Command="{Binding CloseCommand}"
                            Content="&#xf00d;"
                            Template="{StaticResource CloseTemplate}" />
                    </Border>
                </Grid>
            </Border>
            <Border Grid.Row="1" Margin="10">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <hc:UniformSpacingPanel
                            Grid.Column="0"
                            Margin="0,0,5,0"
                            Orientation="Vertical"
                            Spacing="5">
                            <hc:NumericUpDown
                                hc:TitleElement.Title="数字步进值"
                                hc:TitleElement.TitlePlacement="Left"
                                hc:TitleElement.TitleWidth="80"
                                DecimalPlaces="2"
                                Maximum="10"
                                Minimum="0.01"
                                Style="{StaticResource NumericUpDownExtend}"
                                Value="{Binding StepValue, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                        </hc:UniformSpacingPanel>
                        <hc:UniformSpacingPanel
                            Grid.Column="1"
                            Margin="5,0,0,0"
                            Orientation="Vertical"
                            Spacing="5">
                            <hc:NumericUpDown
                                hc:TitleElement.Title="引脚数量"
                                hc:TitleElement.TitlePlacement="Left"
                                hc:TitleElement.TitleWidth="80"
                                Maximum="100"
                                Minimum="1"
                                Style="{StaticResource NumericUpDownExtend}"
                                Value="{Binding PinCount, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                        </hc:UniformSpacingPanel>
                    </Grid>

                    <Grid Grid.Row="1" Margin="0,0,0,10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <hc:TextBox
                            Grid.Column="0"
                            hc:TitleElement.Title="文件路径"
                            hc:TitleElement.TitlePlacement="Left"
                            hc:TitleElement.TitleWidth="80"
                            IsReadOnly="True"
                            Text="{Binding FilePath, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" />
                        <Button
                            Grid.Column="1"
                            Width="100"
                            Margin="10,0,0,0"
                            Command="{Binding SelectFileCommand}"
                            Content="选择文件"
                            Style="{StaticResource ButtonPrimary}" />
                    </Grid>

                    <DataGrid
                        Grid.Row="2"
                        Margin="0,10,0,0"
                        AutoGenerateColumns="False"
                        CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                        ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}"
                        ItemsSource="{Binding CoordinateItems}"
                        Style="{StaticResource DataGridBaseStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding PinName}"
                                Header="引脚名称" />
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding X}"
                                Header="X坐标" />
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding Y}"
                                Header="Y坐标" />
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding Remark}"
                                Header="备注" />
                        </DataGrid.Columns>
                    </DataGrid>

                    <Grid Grid.Row="3" Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Button
                            Grid.Column="0"
                            Width="200"
                            Height="40"
                            Margin="0,0,5,0"
                            Command="{Binding SaveCommand}"
                            Content="确认"
                            Style="{StaticResource ButtonPrimary}" />
                        <Button
                            Grid.Column="1"
                            Width="200"
                            Height="40"
                            Margin="5,0,0,0"
                            Command="{Binding CloseCommand}"
                            Content="取消"
                            Style="{StaticResource ButtonDanger}" />
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl> 