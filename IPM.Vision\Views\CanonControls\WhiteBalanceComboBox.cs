﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class WhiteBalanceComboBox : PropertyComboBox, IObserver
    {
        private EDSDK.EdsPropertyDesc _desc;
        public WhiteBalanceComboBox()
        {
            if (this.items == null) items = new Dictionary<uint, string>();
            items.Add(0, "Auto: Ambience priority");
            items.Add(23, "Auto: White priority");
            items.Add(1, "Daylight");
            items.Add(8, "Shade");
            items.Add(2, "Cloudy");
            items.Add(3, "Tungsten light");
            items.Add(4, "White fluorescent light");
            items.Add(5, "Flash");
            items.Add(6, "Custom1");
            items.Add(9, "Color temp.");
            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }

        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = (KeyValuePair<uint, string>)e.AddedItems[0];
                CameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_WHITE_BALANCE, (IntPtr)selectedItem.Key));

            }
            base.OnSelectionChanged(e);
        }



        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();

                if (propertyID == EDSDK.PropID_WhiteBalance)
                {
                    uint property = (uint)model.WhiteBalance;

                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            this.UpdateProperty(property);
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:
                            _desc = model.WhiteBalanceDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            this.UpdateProperty(property);
                            break;
                    }
                }
                else if (propertyID == EDSDK.PropID_Evf_ClickWBCoeffs)
                {
                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            int size;
                            EDSDK.EdsDataType datatype;
                            uint err = EDSDK.EdsGetPropertySize(model.Camera, EDSDK.PropID_Evf_ClickWBCoeffs, 0, out datatype, out size);
                            if (err != EDSDK.EDS_ERR_OK)
                            {
                                return;
                            }

                            //Get the WB coefficient
                            EDSDK.EdsManualWBData wbCoefs = new EDSDK.EdsManualWBData();
                            IntPtr ptr = Marshal.AllocHGlobal(size);
                            err = EDSDK.EdsGetPropertyData(model.Camera, EDSDK.PropID_Evf_ClickWBCoeffs, 0, size, ptr);
                            if (err != EDSDK.EDS_ERR_OK)
                            {
                                Marshal.FreeHGlobal(ptr);
                                return;
                            }

                            //Set the WB coefficient converted to the manual white balance data structure to manual white balance.
                            wbCoefs = EDSDK.MarshalPtrToManualWBData(ptr);
                            byte[] mwb = EDSDK.ConvertMWB(wbCoefs);
                            err = EDSDK.EdsSetPropertyData(model.Camera, EDSDK.PropID_ManualWhiteBalanceData, 0, mwb.Length, mwb);
                            if (err != EDSDK.EDS_ERR_OK)
                            {
                                Marshal.FreeHGlobal(ptr);
                                return;
                            }

                            //Change the camera's white balance setting to manual white balance.
                            err = EDSDK.EdsSetPropertyData(model.Camera, EDSDK.PropID_WhiteBalance, 0, sizeof(uint), 6);

                            Marshal.FreeHGlobal(ptr);
                            break;
                    }
                }
            }
        }
    }
}
