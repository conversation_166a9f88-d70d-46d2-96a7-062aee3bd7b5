using CommunityToolkit.Mvvm.Input;
using DocumentFormat.OpenXml.ExtendedProperties;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Camera.Com;
using IPM.Vision.Common;
using IPM.Vision.LEvents;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.CustomControls;
using IPM.Vision.ViewModel.Dialogs;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views;
using IPM.Vision.Views.CustomControls;
using IPM.Vision.Views.Dialogs;
using LiveCharts;
using LiveCharts.Wpf;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Application = System.Windows.Application;
using MessageBox = HandyControl.Controls.MessageBox;

namespace IPM.Vision.ViewModel.Pages
{
    public class VisionPageViewModel : ViewModelBase
    {
        private readonly ObservableGlobalState _state;
        private readonly NLogHelper _logHelper;
        private readonly OptVisionService _optVisionService;
        private readonly HKVisionService _hkVisionService;
        private readonly EquipmentService _equipmentService;
        private readonly APIServices _apiService;
        private readonly CheckService _checkService;
        private readonly ReportService _reportService;


        public VisionPageViewModel(ObservableGlobalState globalState, 
            NLogHelper logHelper, 
            OptVisionService optVisionService, 
            HKVisionService hkVisionService,
            IEquipmentService equipmentService,
            APIServices apiService,
            CheckService checkService,
            IReportService reportService)
        {
            _state = globalState;
            _logHelper = logHelper;
            _optVisionService = optVisionService;
            _hkVisionService = hkVisionService;
            _equipmentService = (EquipmentService)equipmentService;
            _apiService = apiService;
            _checkService = checkService;
            _reportService = (ReportService) reportService;
        }

        #region 属性

        private float diffXLength = 0;
        private float diffYLength = 0;
        private bool _isRunning = false;
        private System.Windows.Controls.DataGrid _dataGrid;
        private int _currentPinIndex = 0; // 当前引脚编号索引，用于照片命名（从DataGrid获取实际编号）
        private bool _isProcessingStepChange = false; // 防止步骤切换时的重复处理
        private DateTime _lastStepChangeTime = DateTime.MinValue; // 上次步骤切换时间
        private DateTime _autoRunStartTime = DateTime.MinValue; // 自动运行开始时间，用于避免立即步骤切换

        private ObservableCollection<ObservableReportModel> _reportList =
            new ObservableCollection<ObservableReportModel>();

        public bool IsRunning
        {
            get => _isRunning;
            set => SetProperty(ref _isRunning, value);
        }

        public ObservableCollection<ObservableReportModel> ReportList
        {
            get => _reportList;
            set => SetProperty(ref _reportList, value);
        }

        private ObservableReportModel _currentSelect;

        /// <summary>
        /// 当前选中的报告项
        /// </summary>
        public ObservableReportModel CurrentSelect
        {
            get => _currentSelect;
            set => SetProperty(ref _currentSelect, value);
        }



        private ObservableProductModel _productModel;
        public ObservableProductModel CurrentProductModel
        {
            get => _productModel;
            set => SetProperty(ref _productModel, value);
        }

        private ObservableCollection<ObservablePinPosition> _pinCoordinates;
        public ObservableCollection<ObservablePinPosition> PinCoordinates
        {
            get => _pinCoordinates;
            set => SetProperty(ref _pinCoordinates, value);
        }

        private ObservableCollection<ObservableProcessModel> _processDataList;
        /// <summary>
        /// 步骤数据列表，用于DataGrid绑定
        /// </summary>
        public ObservableCollection<ObservableProcessModel> ProcessDataList
        {
            get => _processDataList;
            set => SetProperty(ref _processDataList, value);
        }

        private ObservableProcessModel _currentProcess;
        /// <summary>
        /// 当前选中的步骤
        /// </summary>
        public ObservableProcessModel CurrentProcess
        {
            get => _currentProcess;
            set
            {
                SetProperty(ref _currentProcess, value);

                // 当选中步骤改变时，更新PositionList
                UpdatePositionList();
                _state.CurrentProcessModel = value;

                // 如果不在自动运行模式下，且是拍照步骤，发送第一个引脚位置给设备
                HandleManualProcessChange(value);

                // 如果在自动运行模式下，处理步骤切换时的相机触发模式设置
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await HandleProcessChangeInAutoModeSafely(value);
                    }
                    catch (Exception ex)
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ 自动运行模式步骤切换异常: {ex.Message}"));
                    }
                });
            }
        }

        private ObservableCollection<ObservablePinPosition> _positionList;
        /// <summary>
        /// 当前步骤的pin位置列表，用于行详情DataGrid绑定
        /// </summary>
        public ObservableCollection<ObservablePinPosition> PositionList
        {
            get => _positionList;
            set => SetProperty(ref _positionList, value);
        }

        private ObservablePinPosition _currentPinPosition;
        /// <summary>
        /// 当前选中的pin位置
        /// </summary>
        public ObservablePinPosition CurrentPinPosition
        {
            get => _currentPinPosition;
            set => SetProperty(ref _currentPinPosition, value);
        }

        private string _serialNumber;
        /// <summary>
        /// 产品编号
        /// </summary>
        public string SerialNumber
        {
            get => _serialNumber;
            set => SetProperty(ref _serialNumber, value);
        }

        private string _orderNumber;
        /// <summary>
        /// 作业计划号
        /// </summary>
        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value);
        }

        private bool _isCameraConnecting;
        /// <summary>
        /// 相机是否正在连接中
        /// </summary>
        public bool IsCameraConnecting
        {
            get => _isCameraConnecting;
            set => SetProperty(ref _isCameraConnecting, value);
        }

        private string _cameraConnectionStatus;
        /// <summary>
        /// 相机连接状态
        /// </summary>
        public string CameraConnectionStatus
        {
            get => _cameraConnectionStatus;
            set => SetProperty(ref _cameraConnectionStatus, value);
        }

        private int _checkCount;
        /// <summary>
        /// 已检测数量
        /// </summary>
        public int CheckCount
        {
            get => _checkCount;
            set => SetProperty(ref _checkCount, value);
        }

        private ObservableReportModel _observableReportData;
        /// <summary>
        /// 当前检测结果数据，用于显示检测结果图和相关信息
        /// </summary>
        public ObservableReportModel ObservableReportData
        {
            get => _observableReportData;
            set => SetProperty(ref _observableReportData, value);
        }

        private string _detectionStatus;
        /// <summary>
        /// 检测状态显示
        /// </summary>
        public string DetectionStatus
        {
            get => _detectionStatus;
            set => SetProperty(ref _detectionStatus, value);
        }

        private int _pendingResultCount;
        /// <summary>
        /// 待处理结果数量
        /// </summary>
        public int PendingResultCount
        {
            get => _pendingResultCount;
            set => SetProperty(ref _pendingResultCount, value);
        }

        private SeriesCollection _pieCharts;
        /// <summary>
        /// 饼图数据，用于显示检测结果统计
        /// </summary>
        public SeriesCollection PieCharts
        {
            get => _pieCharts;
            set => SetProperty(ref _pieCharts, value);
        }

        private int _passCount;
        /// <summary>
        /// 合格数量
        /// </summary>
        public int PassCount
        {
            get => _passCount;
            set => SetProperty(ref _passCount, value);
        }

        private int _failCount;
        /// <summary>
        /// 不合格数量
        /// </summary>
        public int FailCount
        {
            get => _failCount;
            set => SetProperty(ref _failCount, value);
        }

        private bool _isDetectionEnabled = true; // 默认勾选
        /// <summary>
        /// 是否启用检验功能
        /// </summary>
        public bool IsDetectionEnabled
        {
            get => _isDetectionEnabled;
            set => SetProperty(ref _isDetectionEnabled, value);
        }

        private DateTime _triggerModeSetTime = DateTime.MinValue; // 触发模式设置时间
        private readonly TimeSpan _triggerModeDelay = TimeSpan.FromMilliseconds(1500); // 触发模式延迟1.5秒

        // 设备状态变化防抖相关
        private DateTime _lastDeviceStatusChangeTime = DateTime.MinValue; // 上次设备状态变化时间
        private readonly TimeSpan _deviceStatusDebounceDelay = TimeSpan.FromMilliseconds(500); // 设备状态防抖延迟500ms
        private int _lastDeviceStatus = -1; // 上次设备状态值

        // 步骤检查日志计数器
        private int _stepCheckLogCounter = 0; // 用于控制步骤检查日志的输出频率

        /// <summary>
        /// 检查是否还在触发模式切换延迟期内
        /// </summary>
        private bool IsInTriggerModeDelay
        {
            get
            {
                if (_triggerModeSetTime == DateTime.MinValue) return false;
                return DateTime.Now - _triggerModeSetTime < _triggerModeDelay;
            }
        }

        /// <summary>
        /// 检查设备状态变化是否需要防抖处理
        /// </summary>
        /// <param name="newStatus">新的设备状态</param>
        /// <returns>true表示应该处理，false表示应该忽略（防抖期内）</returns>
        private bool ShouldProcessDeviceStatusChange(int newStatus)
        {
            var now = DateTime.Now;

            // 如果是相同状态且在防抖期内，忽略
            if (_lastDeviceStatus == newStatus &&
                _lastDeviceStatusChangeTime != DateTime.MinValue &&
                now - _lastDeviceStatusChangeTime < _deviceStatusDebounceDelay)
            {
                return false;
            }

            // 更新状态和时间
            _lastDeviceStatus = newStatus;
            _lastDeviceStatusChangeTime = now;

            return true;
        }



        // 照片处理队列相关
        private readonly Queue<PictureModel> _photoProcessingQueue = new Queue<PictureModel>();
        private readonly object _photoQueueLock = new object();
        private bool _isPhotoProcessorRunning = false;
        private int _processedPhotoCount = 0;
        private int _receivedPhotoCount = 0;





        /// <summary>
        /// 所有步骤中pin脚的总数
        /// </summary>
        public int TotalPinCount
        {
            get
            {
                if (ProcessDataList == null) return 0;
                return ProcessDataList.Sum(step => step.PinPositions?.Count ?? 0);
            }
        }

        /// <summary>
        /// 已接收照片数量 - 用于监控照片处理状态
        /// </summary>
        public int ReceivedPhotoCount
        {
            get
            {
                lock (_photoQueueLock)
                {
                    return _receivedPhotoCount;
                }
            }
        }

        /// <summary>
        /// 已处理照片数量 - 用于监控照片处理状态
        /// </summary>
        public int ProcessedPhotoCount
        {
            get
            {
                lock (_photoQueueLock)
                {
                    return _processedPhotoCount;
                }
            }
        }

        /// <summary>
        /// 照片处理队列长度 - 用于监控照片处理状态
        /// </summary>
        public int PhotoQueueLength
        {
            get
            {
                lock (_photoQueueLock)
                {
                    return _photoProcessingQueue.Count;
                }
            }
        }
        #endregion



        #region 方法

        /// <summary>
        /// 切换产品
        /// </summary>
        public IAsyncRelayCommand ChangedProductCommand => new AsyncRelayCommand(() =>
        {
           return Dialog.Show<ProductChangedDialog>().Initialize<ProductChangedDialogViewModel>(vm => { })
                .GetResultAsync<bool>();
        });

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            _state.ProductChanged += _state_ProductChanged;
            _optVisionService.UpdatePictureModelEvent += _optVisionService_UpdatePictureModelEvent;
            _hkVisionService.UpdatePictureModelEvent += _hkVisionService_UpdatePictureModelEvent;
            _equipmentService.McStatusNotify += _equipmentService_McStatusNotify;
            _state.NotifyProgressStatus += _state_NotifyProgressStatusAsync;
            _state.ProcessChangedEvent += _state_ProcessChangedEvent;
            _equipmentService.TakePictureEvent += _equipmentService_TakePictureEvent;
            _checkService.CheckCompleteEvent += _checkService_CheckCompleteEvent; // 订阅检测完成事件
            _optVisionService.RequestAutoTriggerSave += _optVisionService_RequestAutoTriggerSave; // 订阅OPT自动触发保存事件
            _optVisionService.ShouldSaveCurrentFrame += _optVisionService_ShouldSaveCurrentFrame; // 订阅步骤类型检查事件
            _optVisionService.GetActualPinIndex += () => GetCurrentGlobalPinIndex(); // **修复引脚编号：提供正确的引脚编号计算方法**
            _optVisionService.GetSerialNumber += () => SerialNumber; // **修复照片命名：提供用户输入的序列号**
            _optVisionService.GetOrderNumber += () => OrderNumber; // **修复照片命名：提供用户输入的订单号**

            // 初始化集合
            if (ProcessDataList == null)
                ProcessDataList = new ObservableCollection<ObservableProcessModel>();
            if (PinCoordinates == null)
                PinCoordinates = new ObservableCollection<ObservablePinPosition>();
            if (PositionList == null)
                PositionList = new ObservableCollection<ObservablePinPosition>();

            // 初始化检测相关属性
            CheckCount = 0;
            DetectionStatus = "等待检测...";
            PendingResultCount = 0;
            PassCount = 0;
            FailCount = 0;

            // 初始化饼图
            InitializePieChart();

        });

        

        private void _state_ProcessChangedEvent(ObservableProcessModel obj)
        {
            ScrollToRow(obj);
        }

        public IRelayCommand UnLoadCommand => new RelayCommand(() =>
        {
            _state.ProductChanged -= _state_ProductChanged;
            _optVisionService.UpdatePictureModelEvent -= _optVisionService_UpdatePictureModelEvent;
            _hkVisionService.UpdatePictureModelEvent -= _hkVisionService_UpdatePictureModelEvent;
            _equipmentService.McStatusNotify -= _equipmentService_McStatusNotify;
            _state.NotifyProgressStatus -= _state_NotifyProgressStatusAsync;
            _state.ProcessChangedEvent -= _state_ProcessChangedEvent;
            _equipmentService.TakePictureEvent -= _equipmentService_TakePictureEvent;
            _checkService.CheckCompleteEvent -= _checkService_CheckCompleteEvent; // 取消订阅检测完成事件
            _optVisionService.RequestAutoTriggerSave -= _optVisionService_RequestAutoTriggerSave; // 取消订阅OPT自动触发保存事件
            _optVisionService.ShouldSaveCurrentFrame -= _optVisionService_ShouldSaveCurrentFrame; // 取消订阅步骤类型检查事件
            _optVisionService.GetActualPinIndex -= () => GetCurrentGlobalPinIndex(); // **取消订阅引脚编号委托**
            _optVisionService.GetSerialNumber -= () => SerialNumber; // **取消订阅序列号委托**
            _optVisionService.GetOrderNumber -= () => OrderNumber; // **取消订阅订单号委托**
        });

        /// <summary>
        /// DataGrid加载完成命令
        /// </summary>
        public IRelayCommand<RoutedEventArgs> DataGridLoadedCommand => new RelayCommand<RoutedEventArgs>((e) =>
        {
            var temp = e.Source as DataGrid;
            if (temp != null)
            {
                _dataGrid = temp;
            }
        });

        /// <summary>
        /// 发送pin坐标到设备命令
        /// </summary>
        public IRelayCommand<ObservablePinPosition> SendToEquipmentCommand => new RelayCommand<ObservablePinPosition>(async (pin) =>
        {

            if (pin != null)
            {
               await _equipmentService.SetT(pin.T);
               await  _equipmentService.SetR(pin.R);
                _equipmentService.SetZAsync(pin.Z);
               await  _equipmentService.WriteXYArray(new float[]{pin.X}, new []{pin.Y}, 1);
            }
        });

        /// <summary>
        /// 开始命令
        /// </summary>
        public IRelayCommand BeginCommand => new RelayCommand(() =>
        {
            StartProcess();
        });

        /// <summary>
        /// 强制停止命令 - 恢复所有状态到初始化，并强制处理完所有待处理照片
        /// </summary>
        public IRelayCommand ForceStopCommand => new RelayCommand(async () =>
        {
            try
            {
                NotifyLog("🛑 开始强制停止，恢复所有状态到初始化");

                // 停止自动运行（先设置_state.IsRunning会触发事件处理相机触发模式和AutoTake）
                _state.IsRunning = false;
                IsRunning = false;

                // **新增功能：清除触发模式延迟保护**
                _triggerModeSetTime = DateTime.MinValue;
                NotifyLog("⏰ 强制停止：已清除触发模式延迟保护");

                // **新增功能：重置设备状态防抖**
                _lastDeviceStatusChangeTime = DateTime.MinValue;
                _lastDeviceStatus = -1;
                NotifyLog("🔄 强制停止：已重置设备状态防抖");

                // **新增：重置自动运行开始时间**
                _autoRunStartTime = DateTime.MinValue;
                NotifyLog("⏰ 强制停止：已重置自动运行开始时间");
                _equipmentService.setSuspend();

                // **新增：强制处理完所有待处理照片**
                await ForceProcessAllPendingPhotos();

                // 重置引脚计数器
                _currentPinIndex = 0;
                NotifyLog("🔢 已重置引脚计数器");

                // 重置照片处理队列计数器
                lock (_photoQueueLock)
                {
                    _processedPhotoCount = 0;
                    _receivedPhotoCount = 0;
                }
                NotifyLog("📊 已重置照片处理队列计数器");

                // 重置检测计数器和状态
                CheckCount = 0;
                DetectionStatus = "待机中...";
                PendingResultCount = 0;
                PassCount = 0;
                FailCount = 0;
                NotifyLog("📊 已重置检测计数器和状态");

                // 清空当前检测结果显示
                ObservableReportData = null;
                NotifyLog("📋 已清空检测结果显示");

                // 重置饼图
                UpdatePieChart();
                NotifyLog("📈 已重置饼图显示");

                // 初始化位置
                InitPosition();
                NotifyLog("📍 已初始化设备位置");

                // 确保OPT相机触发模式关闭（防止事件处理失败）
                try
                {
                    if (_optVisionService.IsTriggerMode)
                    {
                        _optVisionService.IsTriggerMode = false;
                        _optVisionService.CloseTrigger();
                        NotifyLog("📷 已强制关闭OPT相机触发模式");
                    }
                }
                catch (Exception ex)
                {
                    NotifyLog($"⚠️ 关闭OPT相机触发模式时异常: {ex.Message}");
                }

                // 确保设备AutoTake关闭（防止事件处理失败）
                try
                {
                    _equipmentService.CloseAutoTake();
                    NotifyLog("🔧 已强制关闭设备AutoTake");
                }
                catch (Exception ex)
                {
                    NotifyLog($"⚠️ 关闭设备AutoTake时异常: {ex.Message}");
                }

                // 输出最终照片处理统计
                LogPhotoProcessingStats();

                NotifyLog("✅ 强制停止完成，所有状态已恢复初始化");
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 强制停止过程中发生异常: {ex.Message}");
            }
        });

        /// <summary>
        /// 拍照命令 - 根据当前选中内容执行不同逻辑
        /// 选中步骤：执行整个步骤；选中pin脚：只拍该pin脚照片
        /// </summary>
        public IRelayCommand TakePictureCommand => new RelayCommand(async () =>
        {
            try
            {
                // 检查前置条件
                if (CurrentProductModel == null)
                {
                    MessageBox.Warning("未选择产品，无法执行拍照！", "警告");
                    return;
                }

                if (IsRunning)
                {
                    MessageBox.Warning("当前正在自动运行，请先停止自动运行后再手动拍照！", "警告");
                    return;
                }

                // 根据当前选中内容执行不同逻辑
                if (CurrentPinPosition != null && CurrentProcess != null)
                {
                    // 选中了具体的pin脚坐标 - 只拍一张该pin脚的照片
                    await ExecuteSinglePinPhotoCapture();
                }
                else if (CurrentProcess != null)
                {
                    // 选中了步骤 - 执行整个步骤
                    await ExecuteStepPhotoCapture();
                }
                else
                {
                    MessageBox.Warning("请先选择要拍照的步骤或pin脚位置！", "警告");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 手动拍照执行异常: {ex.Message}");
                MessageBox.Error($"拍照执行失败: {ex.Message}", "错误");
            }
        });

        public IRelayCommand ResetErrorCommand => new RelayCommand(() =>
        {
            _equipmentService.ResetError();
        });

        /// <summary>
        /// 打开设备命令
        /// </summary>
        public IRelayCommand OpenEquipmentCommand => new RelayCommand(() =>
        {
            // TODO: 实现打开设备逻辑
            NotifyLog("打开设备");
        });


        /// <summary>
        /// 重置所有命令
        /// </summary>
        public IRelayCommand RestAllCommand => new RelayCommand(() =>
        {
            // TODO: 实现重置所有逻辑
            NotifyLog("重置所有");
            _equipmentService.setSuspend();
            _equipmentService.ResetAll();
        });

        /// <summary>
        /// 打开测试命令 - 打开OPT相机触发测试窗口
        /// </summary>
        public IRelayCommand OpenTestCommand => new RelayCommand(() =>
        {
            try
            {
                NotifyLog("🔧 正在打开OPT相机触发测试窗口...");

                // 检查是否已经有测试窗口打开
                var existingWindow = Application.Current.Windows.OfType<TestWindow>().FirstOrDefault();
                if (existingWindow != null)
                {
                    // 如果窗口已存在，激活并置于前台
                    existingWindow.Activate();
                    existingWindow.WindowState = WindowState.Normal;
                    NotifyLog("📋 测试窗口已存在，已激活到前台");
                    return;
                }

                // 创建新的测试窗口，传递所需的服务
                var testWindowViewModel = new TestWindowViewModel(
                    _optVisionService,
                    _equipmentService,
                    _hkVisionService,
                    _checkService,
                    _logHelper,
                    _state);

                var testWindow = new TestWindow
                {
                    DataContext = testWindowViewModel
                };

                // 设置窗口属性
                testWindow.Owner = Application.Current.MainWindow; // 设置父窗口
                testWindow.WindowStartupLocation = WindowStartupLocation.CenterOwner; // 居中显示

                // 显示窗口
                testWindow.Show();

                NotifyLog("✅ OPT相机触发测试窗口已打开");
                NotifyLog("💡 提示：该窗口可用于测试OPT相机的软触发功能，包括单次触发和定时自动触发");
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 打开测试窗口失败: {ex.Message}");
                _logHelper.LogError($"打开测试窗口异常: {ex}");
            }
        });

        /// <summary>
        /// 查看触发模式状态命令 - 诊断帧触发问题
        /// </summary>
        public IRelayCommand CheckTriggerStatusCommand => new RelayCommand(() =>
        {
            try
            {
                string status = _optVisionService.GetTriggerModeStatus();
                NotifyLog("📊 OPT相机触发模式状态查询：");
                NotifyLog(status);
                
                // 显示状态对话框
                MessageBox.Info(status, "OPT相机触发模式状态");
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 查看触发模式状态失败: {ex.Message}");
            }
        });

        /// <summary>
        /// 重置触发模式命令 - 解决帧触发卡住问题
        /// </summary>
        public IRelayCommand ResetTriggerModeCommand => new RelayCommand(() =>
        {
            try
            {
                NotifyLog("🔄 开始重置OPT相机触发模式...");
                
                // 重置触发帧计数器
                _optVisionService.ResetTriggerFrameCounter();
                
                // 强制重新初始化触发模式
                _optVisionService.ReinitializeTriggerMode();
                
                NotifyLog("✅ OPT相机触发模式重置完成");
                MessageBox.Success("OPT相机触发模式已重置！\n\n如果问题仍然存在，请尝试重启相机连接。", "重置成功");
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 重置触发模式失败: {ex.Message}");
                MessageBox.Error($"重置触发模式失败：\n{ex.Message}", "重置失败");
            }
        });

        /// <summary>
        /// 预览图片命令
        /// </summary>
        public IRelayCommand<object> PreviewCommand => new RelayCommand<object>((obj) =>
        {
            if (CurrentSelect != null)
            {
                if (Common.FileHelper.FileIsExists(CurrentSelect.PicturePath))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo(CurrentSelect.PicturePath) { UseShellExecute = true });
                        NotifyLog($"📷 已打开图片预览: {CurrentSelect.PicturePath}");
                    }
                    catch (Exception ex)
                    {
                        NotifyLog($"❌ 打开图片预览失败: {ex.Message}");
                        MessageBox.Error($"无法打开图片预览：\n{ex.Message}", "预览失败");
                    }
                }
                else
                {
                    MessageBox.Warning("图片文件不存在，可能已被删除或移动。", "文件不存在");
                }
            }
        });

        /// <summary>
        /// 预览检测结果图片命令
        /// </summary>
        public IRelayCommand<object> PreviewCheckCommand => new RelayCommand<object>((obj) =>
        {
            if (CurrentSelect != null && !string.IsNullOrEmpty(CurrentSelect.CheckFilePath))
            {
                if (Common.FileHelper.FileIsExists(CurrentSelect.CheckFilePath))
                {
                    try
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo(CurrentSelect.CheckFilePath) { UseShellExecute = true });
                        NotifyLog($"🔍 已打开检测结果预览: {CurrentSelect.CheckFilePath}");
                    }
                    catch (Exception ex)
                    {
                        NotifyLog($"❌ 打开检测结果预览失败: {ex.Message}");
                        MessageBox.Error($"无法打开检测结果预览：\n{ex.Message}", "预览失败");
                    }
                }
                else
                {
                    MessageBox.Warning("检测结果文件不存在，可能还未生成或已被删除。", "文件不存在");
                }
            }
            else
            {
                MessageBox.Info("当前项目没有检测结果文件。", "无检测结果");
            }
        });

        /// <summary>
        /// 打开图片所在文件夹命令
        /// </summary>
        public IRelayCommand<object> OpenPathCommand => new RelayCommand<object>((obj) =>
        {
            if (CurrentSelect != null)
            {
                if (Common.FileHelper.FileIsExists(CurrentSelect.PicturePath))
                {
                    try
                    {
                        string path = CurrentSelect.PicturePath.Replace('/', '\\');
                        System.Diagnostics.Process.Start("Explorer", $"/select,{path}");
                        NotifyLog($"📁 已打开文件夹: {System.IO.Path.GetDirectoryName(path)}");
                    }
                    catch (Exception ex)
                    {
                        NotifyLog($"❌ 打开文件夹失败: {ex.Message}");
                        MessageBox.Error($"无法打开文件夹：\n{ex.Message}", "打开失败");
                    }
                }
                else
                {
                    MessageBox.Warning("图片文件不存在，无法定位文件夹。", "文件不存在");
                }
            }
        });

        /// <summary>
        /// 打开检测结果文件所在文件夹命令
        /// </summary>
        public IRelayCommand<object> OpenCheckPathCommand => new RelayCommand<object>((obj) =>
        {
            string filePath = obj as string;
            if (!string.IsNullOrEmpty(filePath))
            {
                if (Common.FileHelper.FileIsExists(filePath))
                {
                    try
                    {
                        string path = filePath.Replace('/', '\\');
                        System.Diagnostics.Process.Start("Explorer", $"/select,{path}");
                        NotifyLog($"📁 已打开检测结果文件夹: {System.IO.Path.GetDirectoryName(path)}");
                    }
                    catch (Exception ex)
                    {
                        NotifyLog($"❌ 打开检测结果文件夹失败: {ex.Message}");
                        MessageBox.Error($"无法打开检测结果文件夹：\n{ex.Message}", "打开失败");
                    }
                }
                else
                {
                    MessageBox.Warning("检测结果文件不存在，无法定位文件夹。", "文件不存在");
                }
            }
            else if (CurrentSelect != null && !string.IsNullOrEmpty(CurrentSelect.CheckFilePath))
            {
                if (Common.FileHelper.FileIsExists(CurrentSelect.CheckFilePath))
                {
                    try
                    {
                        string path = CurrentSelect.CheckFilePath.Replace('/', '\\');
                        System.Diagnostics.Process.Start("Explorer", $"/select,{path}");
                        NotifyLog($"📁 已打开检测结果文件夹: {System.IO.Path.GetDirectoryName(path)}");
                    }
                    catch (Exception ex)
                    {
                        NotifyLog($"❌ 打开检测结果文件夹失败: {ex.Message}");
                        MessageBox.Error($"无法打开检测结果文件夹：\n{ex.Message}", "打开失败");
                    }
                }
                else
                {
                    MessageBox.Warning("检测结果文件不存在，无法定位文件夹。", "文件不存在");
                }
            }
            else
            {
                MessageBox.Info("没有可用的检测结果文件路径。", "无检测结果");
            }
        });

        private void _state_ProductChanged(ObservableProductModel obj)
        {
            CurrentProductModel = obj;

            if (obj != null)
            {
                NotifyLog(
                    $"时间:{DateTime.Now:yyyy-MM-dd HH:mm:ss}切换{obj.ProductName}产品成功");

                // 更新步骤数据列表
                if (obj.StepList != null && obj.StepList.Count > 0)
                {
                    // 初始化ProcessDataList
                    if (ProcessDataList == null)
                    {
                        ProcessDataList = new ObservableCollection<ObservableProcessModel>();
                    }
                    else
                    {
                        ProcessDataList.Clear();
                    }

                    // **修复Mark点识别后跳过Side1问题：按ProcessNumber排序步骤**
                    var sortedSteps = obj.StepList.OrderBy(s => s.ProcessNumber).ToList();
                    NotifyLog($"📋 步骤排序：按ProcessNumber从小到大排序，共{sortedSteps.Count}个步骤");

                    // 添加排序后的步骤数据
                    foreach (var step in sortedSteps)
                    {
                        ProcessDataList.Add(step);
                    }

                    // 设置第一个步骤为当前步骤
                    CurrentProcess = ProcessDataList.FirstOrDefault();

                    // 通知TotalPinCount属性更新
                    OnPropertyChanged(nameof(TotalPinCount));

                    var totalPins = obj.StepList.Sum(s => s.PinPositions?.Count ?? 0);
                    if (totalPins > 0)
                    {
                        NotifyLog($"产品包含{totalPins}个pin坐标信息");

                        // 更新PinCoordinates集合用于显示
                        if (PinCoordinates == null)
                        {
                            PinCoordinates = new ObservableCollection<ObservablePinPosition>();
                        }
                        else
                        {
                            PinCoordinates.Clear();
                        }

                        foreach (var step in obj.StepList)
                        {
                            if (step.PinPositions != null)
                            {
                                foreach (var pin in step.PinPositions)
                                {
                                    PinCoordinates.Add(pin);
                                }
                            }
                        }

                    }
                }
                else
                {
                    // 清空数据
                    ProcessDataList?.Clear();
                    PinCoordinates?.Clear();
                    PositionList?.Clear();
                    CurrentProcess = null;

                    // 通知TotalPinCount属性更新
                    OnPropertyChanged(nameof(TotalPinCount));
                }
            }
            else
            {
                MessageBox.Show("产品切换失败！请联系管理员或尝试重新切换一次！", "提示", MessageBoxButton.OK);
            }
        }

        /// <summary>
        /// 更新当前步骤的pin位置列表
        /// </summary>
        private void UpdatePositionList()
        {

            // 确保在UI线程上执行
            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                if (PositionList == null)
                {
                    PositionList = new ObservableCollection<ObservablePinPosition>();
                }
                else
                {
                    PositionList.Clear();
                }

                if (CurrentProcess?.PinPositions != null)
                {
                    foreach (var pin in CurrentProcess.PinPositions)
                    {
                        PositionList.Add(pin);
                    }

                }
            });
        }

        /// <summary>
        /// 处理手动切换步骤时的逻辑
        /// </summary>
        /// <param name="process">切换到的步骤</param>
        private async void HandleManualProcessChange(ObservableProcessModel process)
        {
            try
            {
                // 只有在非自动运行模式下才处理
                if (IsRunning || process == null)
                {
                    return;
                }

                // 如果是拍照步骤（side步骤）
                if (process.ProcessType == ProcessTypeEnum.TAKEPICTURE)
                {
                    // 检查是否有引脚位置
                    if (process.PinPositions != null && process.PinPositions.Count > 0)
                    {
                        // 获取第一个引脚位置
                        var firstPin = process.PinPositions.OrderBy(p => p.Index).First();

                        // 发送设备参数
                        await _equipmentService.SetT(process.EquipmentPara.T);
                        _equipmentService.SetZAsync(process.EquipmentPara.Z);
                        await _equipmentService.SetR(process.EquipmentPara.R);

                        // 发送第一个引脚位置
                        await _equipmentService.WriteXYArray(new float[] { firstPin.X }, new float[] { firstPin.Y }, 1);
                    }
                    else
                    {
                        NotifyLog($"拍照步骤 {process.ParamName} 没有引脚位置数据");
                    }
                }
                // 如果是Mark点识别步骤
                else if (process.ProcessType == ProcessTypeEnum.POINT)
                {
                    // 发送设备参数
                    await _equipmentService.SetT(process.EquipmentPara.T);
                    _equipmentService.SetZAsync(process.EquipmentPara.Z);
                    await _equipmentService.SetR(process.EquipmentPara.R);

                    // 发送EquipmentPara坐标
                    await _equipmentService.WriteXYArray(new float[] { process.EquipmentPara.X }, new float[] { process.EquipmentPara.Y }, 1);
                }
                else
                {
                    NotifyLog($"切换到其他类型步骤: {process.ParamName} (类型: {process.ProcessType})");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"处理手动步骤切换时发生异常: {ex.Message}");
            }
        }

        private void _equipmentService_TakePictureEvent()
        {
            StartProcess();
        }

        /// <summary>
        /// 检查当前步骤是否需要保存OPT照片
        /// **关键修复：只有在拍照步骤且使用主相机时才保存OPT照片**
        /// </summary>
        /// <returns>true表示需要保存，false表示不需要保存</returns>
        private bool _optVisionService_ShouldSaveCurrentFrame()
        {
            try
            {
                // **安全检查：确保必要的对象不为null**
                if (CurrentProcess == null)
                {
                    return false;
                }

                // **关键逻辑：只有拍照步骤且使用主相机时才保存OPT照片**
                bool shouldSave = CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE &&
                                 CurrentProcess.CameraType == CameraTypeEnum.Main;

                if (!shouldSave)
                {
                    string stepType = CurrentProcess.ProcessType.ToString();
                    string cameraType = CurrentProcess.CameraType.ToString();
                    // 使用静态计数器进行低频率日志，避免过多输出
                    _stepCheckLogCounter++;
                    if (_stepCheckLogCounter % 50 == 0) // 每50次检查记录一次
                    {
                        NotifyLog($"🔍 当前步骤不需要保存OPT照片: {stepType}/{cameraType}");
                    }
                }

                return shouldSave;
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 检查是否需要保存OPT照片时异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// PLC硬件触发保存事件处理
        /// **PLC触发流程：PLC→相机硬件触发→相机拍摄→回调产生任务→此方法设置保存路径**
        /// **修复：引脚编号计算基于实际的引脚索引，避免重复累加**
        /// </summary>
        private void _optVisionService_RequestAutoTriggerSave()
        {
            try
            {
                // **线程安全：确保在UI线程中执行**
                if (!Application.Current.Dispatcher.CheckAccess())
                {
                    Application.Current.Dispatcher.Invoke(() => _optVisionService_RequestAutoTriggerSave());
                    return;
                }

                // **安全检查：确保必要的对象不为null**
                if (CurrentProcess == null)
                {
                    NotifyLog($"⚠️ PLC硬件触发时CurrentProcess为null，跳过处理");
                    return;
                }

                // 使用现有的文件夹和文件名生成逻辑
                string folderName = CheckFolder();
                string basePictureName = CheckPictureName();

                string pictureNameWithPin;

                // **修复：根据当前步骤类型计算引脚编号 - 只计算一次，避免重复累加**
                if (CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE)
                {
                    // **拍照步骤：基于全局连续引脚编号计算**
                    int actualPinIndex = GetNextGlobalPinIndex();
                    pictureNameWithPin = $"{basePictureName}-{actualPinIndex}";
                    NotifyLog($"📷 PLC硬件触发任务已设置保存路径 - 文件夹: {folderName}, 文件名: {pictureNameWithPin} (引脚编号: {actualPinIndex})");
                }
                else
                {
                    // **Mark点或其他步骤：不使用引脚编号，使用简单递增**
                    _currentPinIndex++;
                    pictureNameWithPin = $"{basePictureName}-{_currentPinIndex}";
                    string stepType = CurrentProcess.ProcessType.ToString();
                    NotifyLog($"📷 PLC硬件触发任务已设置保存路径 - 文件夹: {folderName}, 文件名: {pictureNameWithPin} (步骤类型: {stepType}, 序号: {_currentPinIndex})");
                }

                // **PLC硬件触发模式：为队列中的PLC触发任务设置文件夹和文件名**
                // 此时帧已经通过硬件触发产生，只需要设置保存路径
                _optVisionService.UpdateHardwareTriggerTask(folderName, pictureNameWithPin);
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ PLC硬件触发任务路径设置失败: {ex.Message}");
                NotifyLog($"❌ 异常详情: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 获取下一个全局连续引脚编号
        /// **核心逻辑：第一条边1-N，第二条边N+1-2N，依此类推**
        /// </summary>
        /// <returns>全局连续的引脚编号</returns>
        private int GetNextGlobalPinIndex()
        {
            try
            {
                // 递增步骤内的引脚计数器
                _currentPinIndex++;

                // 获取当前步骤在所有拍照步骤中的索引
                int currentStepIndex = GetCurrentTakePictureStepIndex();
                if (currentStepIndex == -1)
                {
                    NotifyLog($"⚠️ 无法确定当前步骤索引，使用简单递增编号: {_currentPinIndex}");
                    return _currentPinIndex;
                }

                // 计算前面所有边的引脚总数
                int previousEdgesPinCount = GetPreviousEdgesPinCount(currentStepIndex);

                // 当前边内的引脚编号（从1开始）
                int pinIndexInCurrentEdge = _currentPinIndex;

                // 全局引脚编号 = 前面所有边的引脚总数 + 当前边内的引脚编号
                int globalPinIndex = previousEdgesPinCount + pinIndexInCurrentEdge;

                NotifyLog($"🔢 全局引脚编号计算 - 当前步骤索引: {currentStepIndex}, 前面边总数: {previousEdgesPinCount}, 边内编号: {pinIndexInCurrentEdge}, 全局编号: {globalPinIndex}");
                return globalPinIndex;
            }
            catch (Exception ex)
            {
                // 异常情况下使用简单递增
                NotifyLog($"❌ 获取全局引脚编号异常: {ex.Message}，使用简单递增: {_currentPinIndex}");
                return _currentPinIndex;
            }
        }

        /// <summary>
        /// 获取当前拍照步骤在所有拍照步骤中的索引（从0开始）
        /// </summary>
        /// <returns>步骤索引，-1表示未找到</returns>
        private int GetCurrentTakePictureStepIndex()
        {
            try
            {
                if (ProcessDataList == null || CurrentProcess == null)
                {
                    NotifyLog($"⚠️ GetCurrentTakePictureStepIndex: ProcessDataList或CurrentProcess为null");
                    return -1;
                }

                // 获取所有拍照步骤
                var takePictureSteps = ProcessDataList.Where(p => p != null && p.ProcessType == ProcessTypeEnum.TAKEPICTURE).ToList();

                if (takePictureSteps.Count == 0)
                {
                    NotifyLog($"⚠️ GetCurrentTakePictureStepIndex: 没有找到拍照步骤");
                    return -1;
                }

                // 找到当前步骤的索引
                for (int i = 0; i < takePictureSteps.Count; i++)
                {
                    if (takePictureSteps[i] != null &&
                        !string.IsNullOrEmpty(takePictureSteps[i].Id) &&
                        !string.IsNullOrEmpty(CurrentProcess.Id) &&
                        takePictureSteps[i].Id == CurrentProcess.Id)
                    {
                        return i;
                    }
                }

                NotifyLog($"⚠️ GetCurrentTakePictureStepIndex: 未找到当前步骤 {CurrentProcess.Id}");
                return -1;
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ GetCurrentTakePictureStepIndex异常: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 获取前面所有边的引脚总数
        /// </summary>
        /// <param name="currentStepIndex">当前步骤索引</param>
        /// <returns>前面所有边的引脚总数</returns>
        private int GetPreviousEdgesPinCount(int currentStepIndex)
        {
            try
            {
                if (ProcessDataList == null || currentStepIndex <= 0)
                {
                    return 0;
                }

                // 获取所有拍照步骤
                var takePictureSteps = ProcessDataList.Where(p => p != null && p.ProcessType == ProcessTypeEnum.TAKEPICTURE).ToList();

                if (takePictureSteps.Count == 0)
                {
                    return 0;
                }

                int totalCount = 0;

                // 累加前面所有步骤的引脚数量
                for (int i = 0; i < currentStepIndex && i < takePictureSteps.Count; i++)
                {
                    if (takePictureSteps[i] != null)
                    {
                        int stepPinCount = takePictureSteps[i].PinPositions?.Count ?? 0;
                        totalCount += stepPinCount;
                    }
                }

                return totalCount;
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ GetPreviousEdgesPinCount异常: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 获取当前全局引脚编号（不递增计数器）
        /// **用于OPT相机服务的委托回调**
        /// </summary>
        /// <returns>当前的全局引脚编号</returns>
        private int GetCurrentGlobalPinIndex()
        {
            try
            {
                // 获取当前步骤在所有拍照步骤中的索引
                int currentStepIndex = GetCurrentTakePictureStepIndex();
                if (currentStepIndex == -1)
                {
                    return Math.Max(1, _currentPinIndex);
                }

                // 计算前面所有边的引脚总数
                int previousEdgesPinCount = GetPreviousEdgesPinCount(currentStepIndex);

                // 当前边内的引脚编号（当前计数器值）
                int pinIndexInCurrentEdge = Math.Max(1, _currentPinIndex);

                // 全局引脚编号 = 前面所有边的引脚总数 + 当前边内的引脚编号
                int globalPinIndex = previousEdgesPinCount + pinIndexInCurrentEdge;

                return globalPinIndex;
            }
            catch (Exception ex)
            {
                // 异常情况下返回当前计数器值
                NotifyLog($"❌ 获取当前全局引脚编号异常: {ex.Message}");
                return Math.Max(1, _currentPinIndex);
            }
        }

        /// <summary>
        /// 检测完成事件处理 - 更新已检测数量和结果图显示
        /// </summary>
        /// <param name="message">检测消息</param>
        /// <param name="isError">是否为错误</param>
        /// <param name="reportData">检测结果数据</param>
        private void _checkService_CheckCompleteEvent(string message, bool isError, ReportModel reportData)
        {
            try
            {
                // **安全检查：确保Application.Current存在**
                if (Application.Current == null)
                {
                    _logHelper.LogError("❌ Application.Current为null，无法更新UI");
                    return;
                }

                // **安全检查：确保Dispatcher存在**
                if (Application.Current.Dispatcher == null)
                {
                    _logHelper.LogError("❌ Application.Current.Dispatcher为null，无法更新UI");
                    return;
                }

                // **使用BeginInvoke而不是Invoke，避免阻塞**
                Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    try
                    {
                        // **安全检查：确保参数有效**
                        if (string.IsNullOrEmpty(message))
                        {
                            _logHelper.LogError("❌ 检测完成事件消息为空");
                            return;
                        }

                        if (!isError && reportData != null)
                        {
                            // **安全检查：确保reportData的关键属性不为null**
                            if (string.IsNullOrEmpty(reportData.PictureName))
                            {
                                _logHelper.LogError("❌ 检测结果数据中照片名称为空");
                                return;
                            }

                            // 成功检测，累加已检测数量
                            CheckCount++;

                            // 根据检测结果更新合格/不合格计数
                            try
                            {
                                if (IsPassResult(reportData.Status))
                                {
                                    PassCount++;
                                }
                                else
                                {
                                    FailCount++;
                                }
                            }
                            catch (Exception statusEx)
                            {
                                _logHelper.LogError($"❌ 更新检测状态计数异常: {statusEx.Message}");
                            }

                            // 更新检测状态
                            try
                            {
                                string statusText = reportData.Status ?? "未知";
                                string statusDisplay = statusText == "OK" ? "✅ 合格" :
                                                      statusText == "NG" ? "❌ 不合格" :
                                                      statusText;
                                DetectionStatus = $"检测完成 - {statusDisplay}";
                            }
                            catch (Exception statusUpdateEx)
                            {
                                _logHelper.LogError($"❌ 更新检测状态异常: {statusUpdateEx.Message}");
                                DetectionStatus = "检测完成";
                            }

                            // 更新检测结果数据，用于显示结果图
                            try
                            {
                                if (ObservableReportData == null)
                                {
                                    ObservableReportData = new ObservableReportModel();
                                }

                                // **修复：创建新的ObservableReportModel实例，确保属性通知正确触发**
                                var newObservableData = new ObservableReportModel
                                {
                                    Id = reportData.Id,
                                    PictureName = reportData.PictureName ?? "",
                                    ProcessName = reportData.ProcessName ?? "",
                                    Status = reportData.Status ?? "",
                                    CheckTime = reportData.CheckTime,
                                    CheckFilePath = reportData.CheckFilePath ?? "",
                                    CreateTime = reportData.CreateTime.ToString("yyyy-MM-dd HH:mm:ss")
                                };

                                // **关键修复：替换整个对象，确保UI绑定更新**
                                ObservableReportData = newObservableData;

                                // **调试：记录CheckFilePath的设置**
                                _logHelper.LogInfo($"🖼️ 更新检测结果图片路径: {ObservableReportData.CheckFilePath}");
                                if (string.IsNullOrEmpty(ObservableReportData.CheckFilePath))
                                {
                                    _logHelper.LogError($"❌ CheckFilePath为空，检测结果图片将不显示");
                                }
                                else if (!System.IO.File.Exists(ObservableReportData.CheckFilePath))
                                {
                                    _logHelper.LogError($"❌ CheckFilePath文件不存在: {ObservableReportData.CheckFilePath}");
                                }
                                else
                                {
                                    _logHelper.LogInfo($"✅ CheckFilePath文件存在，图片应该正常显示");
                                }

                                // **额外调试：强制触发属性通知**
                                OnPropertyChanged(nameof(ObservableReportData));

                                // **最终验证：确保ObservableReportData不为null且CheckFilePath正确**
                                if (ObservableReportData != null)
                                {
                                    _logHelper.LogInfo($"✅ ObservableReportData已更新，CheckFilePath: {ObservableReportData.CheckFilePath}");

                                    // **测试URI转换**
                                    try
                                    {
                                        if (!string.IsNullOrEmpty(ObservableReportData.CheckFilePath))
                                        {
                                            var testUri = new Uri(ObservableReportData.CheckFilePath, UriKind.Absolute);
                                            _logHelper.LogInfo($"✅ URI转换成功: {testUri}");
                                        }
                                    }
                                    catch (Exception uriEx)
                                    {
                                        _logHelper.LogError($"❌ URI转换失败: {uriEx.Message}，路径: {ObservableReportData.CheckFilePath}");

                                        // **尝试修复路径格式**
                                        try
                                        {
                                            string fixedPath = "file:///" + ObservableReportData.CheckFilePath.Replace('\\', '/');
                                            var testUri2 = new Uri(fixedPath, UriKind.Absolute);
                                            _logHelper.LogInfo($"✅ 修复后URI转换成功: {testUri2}");
                                        }
                                        catch (Exception uri2Ex)
                                        {
                                            _logHelper.LogError($"❌ 修复后URI转换仍失败: {uri2Ex.Message}");
                                        }
                                    }
                                }
                                else
                                {
                                    _logHelper.LogError($"❌ ObservableReportData为null，UI绑定将失败");
                                }
                            }
                            catch (Exception dataUpdateEx)
                            {
                                _logHelper.LogError($"❌ 更新检测结果数据异常: {dataUpdateEx.Message}");
                            }

                            // 更新饼图
                            try
                            {
                                UpdatePieChart();
                            }
                            catch (Exception chartEx)
                            {
                                _logHelper.LogError($"❌ 更新饼图异常: {chartEx.Message}");
                            }

                            // 记录成功日志
                            try
                            {
                                NotifyLog($"✅ 检测完成 - 照片:{reportData.PictureName}, 结果:{reportData.Status}, 已检测总数:{CheckCount}, 合格:{PassCount}, 不合格:{FailCount}");

                                // 如果有结果图，记录路径
                                if (!string.IsNullOrEmpty(reportData.CheckFilePath))
                                {
                                    NotifyLog($"📷 检测结果图已更新: {reportData.CheckFilePath}");
                                }
                            }
                            catch (Exception logEx)
                            {
                                _logHelper.LogError($"❌ 记录检测日志异常: {logEx.Message}");
                            }
                        }
                        else
                        {
                            // 检测失败或错误
                            try
                            {
                                DetectionStatus = "检测失败";
                                NotifyLog($"❌ 检测失败: {message}");
                            }
                            catch (Exception errorLogEx)
                            {
                                _logHelper.LogError($"❌ 记录检测失败日志异常: {errorLogEx.Message}");
                            }
                        }

                        // 更新待处理结果数量
                        try
                        {
                            PendingResultCount = 0; // 处理完成，清零
                        }
                        catch (Exception pendingEx)
                        {
                            _logHelper.LogError($"❌ 更新待处理结果数量异常: {pendingEx.Message}");
                        }
                    }
                    catch (Exception uiEx)
                    {
                        _logHelper.LogError($"❌ 更新检测结果界面时异常: {uiEx.Message}");
                        _logHelper.LogError($"   异常详情: {uiEx.StackTrace}");
                    }
                }));
            }
            catch (Exception ex)
            {
                _logHelper.LogError($"❌ 处理检测完成事件时异常: {ex.Message}");
                _logHelper.LogError($"   异常详情: {ex.StackTrace}");

                // **关键：即使发生异常也要确保程序继续运行**
                try
                {
                    // 尝试简单的状态更新
                    if (Application.Current?.Dispatcher != null)
                    {
                        Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                        {
                            DetectionStatus = "检测异常";
                        }));
                    }
                }
                catch
                {
                    // 忽略二次异常，防止级联崩溃
                }
            }
        }

        /// <summary>
        /// 设备报警事件处理
        /// </summary>
        /// <param name="errorMessage">报警信息</param>
        private void _equipmentService_EquipmentErrorEvent(string errorMessage)
        {
            try
            {
                NotifyLog($"⚠️ 设备报警: {errorMessage}");

                // 如果正在自动运行，立即停止
                if (IsRunning)
                {
                    NotifyLog("🛑 检测到设备报警，立即停止自动运行");
                    // 停止自动运行（先设置_state.IsRunning会触发事件处理相机触发模式）
                    _state.IsRunning = false;
                    IsRunning = false;

                    // 可选：显示报警对话框
                    System.Windows.Application.Current.Dispatcher.Invoke(() => {
                        try
                        {
                            MessageBox.Warning($"设备报警，自动运行已停止！\n\n报警信息：{errorMessage}\n\n请处理设备故障后重新开始运行。", "设备报警");
                        }
                        catch (Exception ex)
                        {
                            NotifyLog($"显示报警对话框失败: {ex.Message}");
                        }
                    });
                }
                else
                {
                    NotifyLog("设备报警（当前未在自动运行状态）");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"处理设备报警事件时发生异常: {ex.Message}");
            }
        }


        #endregion

        #region Inner Method

        private async void _state_NotifyProgressStatusAsync(bool obj)
        {
            // 处理自动运行模式的开始和结束
            HandleAutoRunModeChange(obj);

            if (obj && CurrentProductModel != null && CurrentProcess != null)
            {
                if (CurrentProcess.ProcessType == ProcessTypeEnum.POINT)
                {
                    await _equipmentService.SetT(CurrentProcess.EquipmentPara.T);
                    _equipmentService.SetZAsync(CurrentProcess.EquipmentPara.Z);
                    await _equipmentService.SetR(CurrentProcess.EquipmentPara.R);
                    await _equipmentService.WriteXYArray(new[] { CurrentProcess.EquipmentPara.X }, new[] { CurrentProcess.EquipmentPara.Y }, 1);
                }
                else
                {
                    if (CurrentProcess.PinPositions != null && CurrentProcess.PinPositions.Count > 0)
                    {
                        await _equipmentService.SetT(CurrentProcess.EquipmentPara.T);
                        _equipmentService.SetZAsync(CurrentProcess.EquipmentPara.Z);
                        await _equipmentService.SetR(CurrentProcess.EquipmentPara.R);

                        // 动态计算包含加速减速点的坐标数组
                        var (xArrayWithAccelDecel, yArrayWithAccelDecel) = CalculateCoordinatesWithAccelDecelForRuntime(CurrentProcess.PinPositions);
                        await _equipmentService.WriteXYArray(xArrayWithAccelDecel, yArrayWithAccelDecel, xArrayWithAccelDecel.Length);
                    }
                }


            }
        }

        /// <summary>
        /// 处理自动运行模式的开始和结束，控制OPT相机触发模式
        /// **核心需求：开始自动运行时立即设置OPT触发模式，只有全部执行完或取消执行才关闭**
        /// </summary>
        /// <param name="isRunning">是否正在运行</param>
        private void HandleAutoRunModeChange(bool isRunning)
        {
            try
            {
                if (isRunning)
                {
                    // 进入自动运行模式
                    NotifyLog("🚀 进入自动运行模式");
                    NotifyLog($"🔍 当前检验配置: {(IsDetectionEnabled ? "✅ 已启用检验" : "❌ 已禁用检验")}");

                    // **重置引脚计数器，确保每次自动运行从正确编号开始**
                    _currentPinIndex = 0;
                    NotifyLog("🔢 自动运行开始 - 已重置引脚计数器");

                    // **修复需求：开始自动运行时立即设置OPT相机触发模式，不管当前步骤类型**
                    NotifyLog("📷 开始自动运行，立即设置OPT相机触发模式");
                    SetOptCameraFrameTriggerMode();

                    // **修复时序问题：在设置触发模式后启动延迟保护**
                    _triggerModeSetTime = DateTime.Now;
                    NotifyLog($"⏰ 触发模式切换保护：等待 {_triggerModeDelay.TotalSeconds} 秒后开始正式处理照片");

                    // **修复时序问题：不在开始时设置AutoTake，避免立即触发拍照**
                    // **AutoTake将在真正需要拍照的步骤时才设置**
                    NotifyLog("🔧 自动运行开始，AutoTake将在拍照步骤时设置，避免立即触发");
                }
                else
                {
                    // 离开自动运行模式
                    NotifyLog("🛑 离开自动运行模式，关闭OPT相机触发模式");
                    NotifyLog($"🔍 最终检验配置: {(IsDetectionEnabled ? "✅ 已启用检验" : "❌ 已禁用检验")}");

                    // **清除触发模式延迟保护**
                    _triggerModeSetTime = DateTime.MinValue;
                    NotifyLog("⏰ 触发模式延迟保护已清除");

                    // 关闭OPT相机触发模式
                    _optVisionService.IsTriggerMode = false;
                    _optVisionService.CloseTrigger();

                    // **新增功能：离开自动运行模式时关闭AutoTake**
                    _equipmentService.CloseAutoTake();
                    NotifyLog("🔧 已关闭设备AutoTake（离开自动运行模式）");

                    NotifyLog("✅ OPT相机触发模式已关闭，恢复连续采集模式");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 处理自动运行模式变化时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置OPT相机为PLC硬件触发模式
        /// **PLC触发原理：PLC发送硬件信号→相机硬件响应→拍摄一帧→回调给软件**
        /// </summary>
        private void SetOptCameraFrameTriggerMode()
        {
            try
            {
                if (!_optVisionService.IsConnect)
                {
                    NotifyLog("⚠️ OPT相机未连接，无法设置PLC硬件触发模式");
                    return;
                }

                NotifyLog("📷 设置OPT相机为PLC硬件触发模式（等待PLC信号触发）");

                // **配置PLC硬件触发：相机只在接收到PLC信号时才拍摄**
                bool triggerConfigSuccess = _optVisionService.setLineTriggerConf();

                if (triggerConfigSuccess)
                {
                    // **设置触发模式标志：告诉软件当前为PLC触发模式**
                    _optVisionService.IsTriggerMode = true;

                    // 启动队列健康监控
                    StartQueueHealthMonitoring();

                    NotifyLog("✅ PLC硬件触发模式设置完成（相机等待PLC信号，软件等待回调）");
                    NotifyLog("💡 工作流程：PLC信号 → 相机拍摄 → 软件回调处理");
                }
                else
                {
                    NotifyLog("❌ PLC硬件触发配置失败，相机仍为连续采集模式");
                    _optVisionService.IsTriggerMode = false;
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 设置PLC硬件触发模式时发生异常: {ex.Message}");
                // 如果配置失败，确保不设置触发模式标志
                _optVisionService.IsTriggerMode = false;
            }
        }

        /// <summary>
        /// 启动队列健康监控，防止10ms高频触发导致队列积压
        /// </summary>
        private void StartQueueHealthMonitoring()
        {
            // 每5秒检查一次队列健康状况
            Task.Run(async () =>
            {
                while (IsRunning && _optVisionService.IsTriggerMode)
                {
                    try
                    {
                        _optVisionService.CheckQueueHealth();

                        // 记录队列状态（降低频率，避免日志过多）
                        string queueStatus = _optVisionService.GetQueueStatus();
                        _logHelper.LogInfo($"[QueueMonitor] {queueStatus}");

                        await Task.Delay(5000); // 5秒检查一次
                    }
                    catch (Exception ex)
                    {
                        _logHelper.LogError($"队列监控异常: {ex.Message}");
                        break;
                    }
                }
            });
        }

        /// <summary>
        /// 处理自动运行模式下的步骤切换 - 修复async void问题
        /// **修复需求：步骤切换时保持OPT相机触发模式开启，不关闭触发模式**
        /// </summary>
        /// <param name="newProcess">新的步骤</param>
        private async Task HandleProcessChangeInAutoModeSafely(ObservableProcessModel newProcess)
        {
            try
            {
                // 只在自动运行模式下处理
                if (!IsRunning)
                {
                    return;
                }

                // **修复关键问题：添加空检查**
                if (newProcess == null)
                {
                    NotifyLog("⚠️ HandleProcessChangeInAutoMode: newProcess为空");
                    return;
                }

                // **修复关键问题：检查是否正在处理步骤切换，避免重复处理**
                if (_isProcessingStepChange)
                {
                    NotifyLog($"⚠️ HandleProcessChangeInAutoMode被忽略 - 正在处理步骤切换中，步骤: {newProcess.ParamName}");
                    return;
                }

                if (newProcess.PinPositions != null && newProcess.PinPositions.Count > 0)
                {
                    // **修复关键问题：添加设备通讯异常处理**
                    try
                    {
                        await _equipmentService.SetT(newProcess.EquipmentPara?.T ?? 0);
                        _equipmentService.SetZAsync(newProcess.EquipmentPara?.Z ?? 0);
                        await _equipmentService.SetR(newProcess.EquipmentPara?.R ?? 0);

                        // 动态计算包含加速减速点的坐标数组
                        var (xArrayWithAccelDecel, yArrayWithAccelDecel) = CalculateCoordinatesWithAccelDecelForRuntime(newProcess.PinPositions);
                        await _equipmentService.WriteXYArray(xArrayWithAccelDecel, yArrayWithAccelDecel, xArrayWithAccelDecel.Length);
                    }
                    catch (Exception equipEx)
                    {
                        NotifyLog($"❌ 设备通讯异常: {equipEx.Message}");
                        throw; // 重新抛出异常，让上层处理
                    }
                }

                // **修复需求：步骤切换时保持OPT触发模式开启，不关闭**
                if (newProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE &&
                    newProcess.CameraType == CameraTypeEnum.Main)
                {
                    // **拍照步骤 + 主相机：保持触发模式开启，现在设置AutoTake**
                    NotifyLog($"🔄 自动运行模式：切换到OPT拍照步骤 [{newProcess.ParamName}] - 保持触发模式开启，设置AutoTake");

                    // **关键修复：现在才设置AutoTake，避免开始时立即触发拍照**
                    _equipmentService.SetAutoTake();
                    NotifyLog("🔧 已打开设备AutoTake（OPT拍照步骤）");

                    // **重置引脚计数器**
                    _currentPinIndex = 0;
                    NotifyLog("🔢 已重置引脚计数器");
                }
                else if (newProcess.ProcessType == ProcessTypeEnum.POINT)
                {
                    // **Mark点识别步骤：保持OPT触发模式开启，但关闭AutoTake**
                    NotifyLog($"🔄 自动运行模式：切换到Mark点识别步骤 [{newProcess.ParamName}] - 保持触发模式开启");
                    NotifyLog($"📝 注意：OPT触发模式保持开启，但Mark点识别只使用海康相机");

                    // **确保AutoTake关闭**
                    _equipmentService.CloseAutoTake();
                    NotifyLog("🔧 已关闭设备AutoTake（Mark点识别步骤）");
                }
                else
                {
                    // **其他步骤：保持OPT触发模式开启，但关闭AutoTake**
                    string stepType = newProcess.ProcessType.ToString();
                    string cameraType = newProcess.CameraType.ToString();
                    NotifyLog($"🔄 自动运行模式：切换到{stepType}步骤 [{newProcess.ParamName}], 相机类型: {cameraType} - 保持触发模式开启");

                    // **确保AutoTake关闭**
                    _equipmentService.CloseAutoTake();
                    NotifyLog("🔧 已关闭设备AutoTake（非OPT拍照步骤）");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 处理自动运行模式下步骤切换时发生异常: {ex.Message}");
                throw; // 重新抛出异常，让上层处理
            }
        }

        private void _equipmentService_McStatusNotify(int obj)
        {
            // **新增：设备状态变化防抖检查**
            if (!ShouldProcessDeviceStatusChange(obj))
            {
                return; // 防抖期内，忽略此次状态变化
            }

            // 检测设备错误状态，如果状态为4（错误），立即停止自动执行
            if (obj == 4 && IsRunning)
            {
                NotifyLog("🛑 检测到设备错误状态(状态码:4)，立即停止自动运行");
                // 停止自动运行（先设置_state.IsRunning会触发事件处理相机触发模式）
                _state.IsRunning = false;
                IsRunning = false;

                // 显示错误提示
                System.Windows.Application.Current.Dispatcher.Invoke(() => {
                    try
                    {
                        MessageBox.Warning("设备状态异常，自动运行已停止！\n\n请检查设备状态，处理故障后重新开始运行。", "设备状态异常");
                    }
                    catch (Exception ex)
                    {
                        NotifyLog($"显示设备状态异常对话框失败: {ex.Message}");
                    }
                });
                return; // 直接返回，不执行后续逻辑
            }

            if (obj == 2 && IsRunning)
            {
                NotifyLog($"📡 收到设备状态通知: {obj} (设备就绪)，已通过防抖检查，开始处理步骤逻辑");

                try
                {
                    // **增强异常处理：检查关键对象状态**
                    if (CurrentProcess == null)
                    {
                        NotifyLog($"❌ 关键错误：CurrentProcess为null，无法处理设备状态通知");
                        return;
                    }

                    // **新增：检查是否刚开始自动运行，避免立即触发步骤切换**
                    var timeSinceAutoRunStart = DateTime.Now - _autoRunStartTime;
                    if (timeSinceAutoRunStart < TimeSpan.FromSeconds(2))
                    {
                        NotifyLog($"⏰ 自动运行刚开始 {timeSinceAutoRunStart.TotalSeconds:F1}秒，忽略设备状态通知，避免立即步骤切换");
                        return;
                    }

                    string folderName = CheckFolder();
                    string pictureName = CheckPictureName();

                    if (CurrentProcess.ProcessType == ProcessTypeEnum.POINT)
                    {
                        folderName = Common.FileHelper.ConcatFile(folderName, "point_picture");
                        Common.FileHelper.CreateFolder(folderName);
                        _hkVisionService.TakePicture(folderName, pictureName);
                        NotifyLog($"📷 Mark点识别步骤 - HK相机拍照已触发");
                        // **修复关键问题：Mark点识别步骤不在此处切换步骤，由识别成功事件处理步骤切换**
                        NotifyLog($"📝 注意：Mark点识别步骤的步骤切换由识别成功事件处理，此处不切换步骤");
                    }
                    else if (CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE && CurrentProcess.CameraType == CameraTypeEnum.Main)
                    {
                        NotifyLog($"📷 拍照步骤 '{CurrentProcess.ParamName}' - 设备就绪，开始处理步骤逻辑");

                        // **修复关键问题：检查是否正在处理步骤切换，避免重复切换**
                        if (_isProcessingStepChange)
                        {
                            NotifyLog($"⚠️ 拍照步骤切换被忽略 - 正在处理步骤切换中");
                            return;
                        }

                        // **设置处理标志，防止并发问题**
                        _isProcessingStepChange = true;
                        _lastStepChangeTime = DateTime.Now;

                        // 继续执行下一个步骤（使用安全的异步方法）
                        NotifyLog($"📋 当前步骤 '{CurrentProcess.ParamName}' 处理完成，准备切换到下一步骤");

                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await ContinueToNextProcessSafely();
                            }
                            catch (Exception ex)
                            {
                                Application.Current?.Dispatcher.Invoke(() =>
                                    NotifyLog($"❌ 拍照步骤切换过程中异常: {ex.Message}"));
                            }
                            finally
                            {
                                _isProcessingStepChange = false;
                            }
                        });
                    }
                    else
                    {
                        NotifyLog($"📋 当前步骤 '{CurrentProcess?.ParamName}' (类型: {CurrentProcess?.ProcessType}) - 设备就绪但无需特殊处理");
                    }
                }
                catch (OutOfMemoryException memEx)
                {
                    // **增强异常处理：内存不足异常**
                    _logHelper.LogError($"💥 设备状态处理 - 内存不足异常: {memEx.Message}");
                    NotifyLog($"❌ 内存不足，触发垃圾回收: {memEx.Message}");
                    
                    // 强制垃圾回收
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }
                catch (System.Data.SQLite.SQLiteException sqlEx)
                {
                    // **增强异常处理：数据库异常**
                    _logHelper.LogError($"💥 设备状态处理 - 数据库异常: {sqlEx.Message}");
                    NotifyLog($"❌ 数据库操作异常: {sqlEx.Message}");
                }
                catch (System.Runtime.InteropServices.COMException comEx)
                {
                    // **增强异常处理：COM异常（通常与设备驱动相关）**
                    _logHelper.LogError($"💥 设备状态处理 - COM异常: {comEx.Message}");
                    NotifyLog($"❌ 设备驱动异常: {comEx.Message}");
                }
                catch (Exception ex)
                {
                    // **增强异常处理：记录详细信息**
                    _logHelper.LogError($"💥 设备状态处理 - 未知异常: {ex.GetType().Name} - {ex.Message}");
                    _logHelper.LogError($"堆栈跟踪: {ex.StackTrace}");
                    NotifyLog($"❌ 处理设备状态通知时发生异常: {ex.Message}");
                    
                    // 写入专门的自动运行异常日志
                    WriteAutoRunExceptionLog(ex, "设备状态通知处理");
                }
                finally
                {
                    // **修复关键问题：不在这里清除处理标志，让Task.Run的finally块来清除**
                    // **避免过早清除标志导致Mark点识别成功后的步骤切换能够执行**
                    NotifyLog($"✅ 设备状态通知处理完成，处理标志将由异步任务清除");
                }
            }
        }

        private async void _hkVisionService_UpdatePictureModelEvent(PictureModel obj)
        {
            try
            {
                // **修复关键问题：添加空检查防止闪退**
                if (CurrentProcess == null)
                {
                    NotifyLog($"⚠️ CurrentProcess为空，忽略HK相机照片处理 - 照片:{obj.FileName}");
                    return;
                }

                if (CurrentProcess.ProcessType == ProcessTypeEnum.POINT)
                {
                    NotifyLog($"📷 开始HK相机Mark点识别处理 - 照片:{obj.FileName}");

                    // **修复需求：防止Mark点识别重复触发步骤切换**
                    if (_isProcessingStepChange)
                    {
                        NotifyLog($"⚠️ HK相机Mark点识别被忽略 - 正在处理步骤切换中，照片:{obj.FileName}");
                        return;
                    }

                    // **修复关键问题：设置处理标志，防止并发问题**
                    _isProcessingStepChange = true;
                    _lastStepChangeTime = DateTime.Now;

                    try
                    {
                        var temp = await DiffMarkPoint(obj);
                        if (!temp)
                        {
                            NotifyLog($"❌ HK相机Mark点识别失败 - 照片:{obj.FileName}，mark点识别失败，isrunning要设置为false，停止自动运行模式，设备回到initposition位置，并且messageBox要告诉用户mark点识别失败了，重新尝试");
                            // **修复：Mark点识别失败时，DiffMarkPoint方法内部已经处理了停止自动运行、设备回位和用户提示**
                            // 这里只需要清理处理标志并返回
                            return;
                        }

                        NotifyLog($"✅ HK相机Mark点识别完成 - 照片:{obj.FileName}, 偏移量X:{diffXLength}, Y:{diffYLength}");

                        // Mark点识别成功后，如果处于自动运行状态，继续执行下一个步骤
                        if (IsRunning)
                        {
                            NotifyLog("🔄 HK相机Mark点识别成功，继续执行下一个步骤");

                            // **新增：输出当前步骤信息和下一步骤预测**
                            Application.Current?.Dispatcher.Invoke(() =>
                            {
                                if (CurrentProcess != null)
                                {
                                    var currentIndex = ProcessDataList.IndexOf(CurrentProcess);
                                    NotifyLog($"📍 当前Mark点识别步骤: {CurrentProcess.ParamName} (索引: {currentIndex}, ProcessNumber: {CurrentProcess.ProcessNumber})");

                                    var nextIndex = currentIndex + 1;
                                    if (nextIndex < ProcessDataList.Count)
                                    {
                                        var nextProcess = ProcessDataList[nextIndex];
                                        NotifyLog($"🎯 预计下一个步骤: {nextProcess.ParamName} (索引: {nextIndex}, ProcessNumber: {nextProcess.ProcessNumber}, ProcessType: {nextProcess.ProcessType})");
                                    }
                                    else
                                    {
                                        NotifyLog($"⚠️ 下一个步骤索引 {nextIndex} 超出范围，将切换回第一步");
                                    }
                                }
                            });

                            // **修复关键问题：使用Task.Run避免async void问题**
                            _ = Task.Run(async () =>
                            {
                                try
                                {
                                    await ContinueToNextProcessSafely();
                                }
                                catch (Exception ex)
                                {
                                    Application.Current?.Dispatcher.Invoke(() =>
                                        NotifyLog($"❌ 步骤切换过程中异常: {ex.Message}"));
                                }
                                finally
                                {
                                    _isProcessingStepChange = false;
                                }
                            });
                        }
                        else
                        {
                            _isProcessingStepChange = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        NotifyLog($"❌ Mark点识别处理异常: {ex.Message}");
                        _isProcessingStepChange = false;
                    }

                    return; // Mark点处理完成，不进行产品拍照处理
                }

                // 二维码识别处理 - 不保存到数据库
                if (CurrentProcess.ProcessType == ProcessTypeEnum.QRCODE)
                {
                    NotifyLog($"开始HK相机二维码识别处理 - 照片:{obj.FileName}");
                    var temp = await GetQRCode(obj);
                    if (!temp)
                    {
                        NotifyLog($"HK相机二维码识别失败 - 照片:{obj.FileName}");
                        // 不停止自动运行，照片处理失败不影响步骤切换
                        return;
                    }

                    NotifyLog($"HK相机二维码识别完成 - 照片:{obj.FileName}");

                    // 二维码识别成功后，如果处于自动运行状态，继续执行下一个步骤
                    if (IsRunning)
                    {
                        NotifyLog("HK相机二维码识别成功，继续执行下一个步骤");
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await ContinueToNextProcessSafely();
                            }
                            catch (Exception ex)
                            {
                                Application.Current?.Dispatcher.Invoke(() =>
                                    NotifyLog($"❌ 二维码识别后步骤切换异常: {ex.Message}"));
                            }
                        });
                    }
                    return; // 二维码处理完成，不进行产品拍照处理
                }

                // 产品拍照处理 - HK相机照片不做任何处理，直接保存到数据库
                if (CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE)
                {
                    NotifyLog($"=== HK相机产品照片处理开始 === 照片:{obj.FileName}");

                    var reportModel = new ReportModel()
                    {
                        CreateTime = DateTime.Now,
                        OperatorName = _state.LoginUser?.Account ?? "System",
                        WorkStation = _state.AppConfig?.WorkStation ?? "Unknown",
                        ProcessId = _state.CurrentProcessModel?.Id ?? "",
                        ProcessName = _state.CurrentProcessModel?.ParamName ?? "",
                        ProductNumber = _state.CurrentProduct?.ProductNumber ?? "",
                        PictureName = obj.FileName,
                        PicturePath = obj.FileFullPath,
                        SerialNumber = SerialNumber ?? "",
                        CheckFilePath = "",
                        OrderNumber = OrderNumber ?? "",
                        Weight = "",
                        Status = "WAITING"
                    };

                    // **修复：使用重试机制保存到数据库，防止SQLite并发冲突**
                    bool saveSuccess = await SaveReportDataWithRetry(reportModel, obj.FileName);
                    if (saveSuccess)
                    {
                        NotifyLog($"HK相机照片已保存到数据库 - 照片:{obj.FileName}, 状态:WAITING");
                    }
                    else
                    {
                        NotifyLog($"HK相机保存照片到数据库失败 - 照片:{obj.FileName}, 已重试多次");
                    }

                    GetTopSixPicture();
                    NotifyLog($"HK相机照片处理完成 - 照片:{obj.FileName}");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ HK相机照片处理总异常: {ex.Message}");
                _logHelper.LogError($"HK相机照片处理异常: {ex}");
                
                // 确保清除处理标志
                _isProcessingStepChange = false;
            }
        }

        /// <summary>
        /// 安全的步骤切换方法 - 修复async void问题
        /// </summary>
        private async Task ContinueToNextProcessSafely()
        {
            try
            {
                // 在切换步骤前，检查当前步骤是否为拍照步骤，如果是则根据检验开关决定是否执行批量检测
                if (CurrentProcess != null &&
                    CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE &&
                    CurrentProcess.CameraType == CameraTypeEnum.Main)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"📸 当前拍照步骤 '{CurrentProcess.ParamName}' 完成，立即处理上一步骤的照片"));

                    // **修改为单张检测模式：步骤切换时只记录统计，不再批量检测**
                    Application.Current?.Dispatcher.Invoke(() => 
                    {
                        if (IsDetectionEnabled)
                        {
                            NotifyLog($"📸 当前拍照步骤完成，每张照片已立即进行单张检测");
                        }
                        else
                        {
                            NotifyLog($"⏭️  检验已禁用，照片仅存储不检测");
                        }
                        NotifyLog($"📊 照片处理统计：");
                        LogPhotoProcessingStats();
                    });
                }

                if (ProcessDataList == null || ProcessDataList.Count == 0)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog("流程数据列表为空，无法切换到下一个流程"));
                    _state.IsRunning = false;
                    return;
                }

                var currentIndex = ProcessDataList.IndexOf(CurrentProcess);
                if (currentIndex == -1)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog("当前流程在流程列表中未找到，重置为第一个流程"));
                    currentIndex = -1; // 设置为-1，这样下一个索引就是0（第一个流程）
                }

                var nextIndex = currentIndex + 1;
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    NotifyLog($"🔄 步骤切换详情:");
                    NotifyLog($"  当前步骤: {CurrentProcess?.ParamName} (索引: {currentIndex}, ProcessNumber: {CurrentProcess?.ProcessNumber})");
                    NotifyLog($"  下一个索引: {nextIndex}, 总流程数: {ProcessDataList.Count}");

                    // **新增：显示所有步骤的详细信息**
                    NotifyLog($"📋 当前所有步骤列表:");
                    for (int i = 0; i < ProcessDataList.Count; i++)
                    {
                        var process = ProcessDataList[i];
                        string marker = (i == currentIndex) ? "👉" : (i == nextIndex) ? "🎯" : "  ";
                        NotifyLog($"  {marker} 索引{i}: {process.ParamName} (ProcessNumber: {process.ProcessNumber}, ProcessType: {process.ProcessType})");
                    }
                });

                if (nextIndex < ProcessDataList.Count)
                {
                    // **步骤验证：记录当前步骤切换情况**
                    var nextProcess = ProcessDataList[nextIndex];

                    // 如果当前是Mark点识别步骤，记录下一个拍照步骤信息
                    if (CurrentProcess?.ProcessType == ProcessTypeEnum.POINT &&
                        nextProcess?.ProcessType == ProcessTypeEnum.TAKEPICTURE)
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                        {
                            NotifyLog($"🔍 Mark点识别完成，下一个拍照步骤: {nextProcess.ParamName} (ProcessNumber: {nextProcess.ProcessNumber})");
                            
                            // **由于已在步骤加载时按ProcessNumber排序，步骤顺序应该正确**
                            NotifyLog($"✅ 步骤顺序已通过ProcessNumber排序确保正确，无需额外修正");
                        });
                    }

                    // 在UI线程上设置CurrentProcess属性
                    await Application.Current.Dispatcher.InvokeAsync(() => {
                        try
                        {
                            NotifyLog($"准备切换到: {nextProcess.ParamName} (ProcessNumber: {nextProcess.ProcessNumber})");
                            CurrentProcess = nextProcess;
                        }
                        catch (Exception ex)
                        {
                            NotifyLog($"在UI线程中设置CurrentProcess失败: {ex.Message}");
                            _state.IsRunning = false;
                        }
                    });
                }
                else
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"已到达最后一个流程，下一个流程索引 {nextIndex} 超出范围，切换回第一步"));

                    // **修改为单张检测模式：所有照片已立即进行单张检测，无需最终批量检测**
                        Application.Current?.Dispatcher.Invoke(() =>
                    {
                        if (IsDetectionEnabled)
                        {
                            NotifyLog($"🔍 所有步骤完成，每张照片已立即进行单张检测");
                        }
                        else
                        {
                            NotifyLog($"⏭️ 所有步骤完成，检验已禁用，照片仅存储");
                    }
                    });

                    // 所有步骤完成后，生成MES文件
                    await GenerateMESFilesIfEnabled();

                    // 切换回第一步，并重置所有状态
                    await Application.Current.Dispatcher.InvokeAsync(() => {
                        try
                        {
                            var firstProcess = ProcessDataList?.FirstOrDefault();
                            if (firstProcess != null)
                            {
                                NotifyLog($"🔄 循环执行：切换回第一步 {firstProcess.ParamName} (ProcessNumber: {firstProcess.ProcessNumber})");

                                // 重置所有状态到初始化
                                ResetAllStatesForNewCycle();

                                // 切换到第一步
                                CurrentProcess = firstProcess;

                                // 调用InitPosition而不是发送设备坐标
                                NotifyLog("📍 执行InitPosition，移动到初始位置");
                                InitPosition();
                                
                            }
                            else
                            {
                                NotifyLog("❌ 无法找到第一个流程，停止运行");
                                _state.IsRunning = false;
                                IsRunning = false;
                            }
                        }
                        catch (Exception ex)
                        {
                            NotifyLog($"切换回第一步时失败: {ex.Message}");
                            _state.IsRunning = false;
                            IsRunning = false;
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                Application.Current?.Dispatcher.Invoke(() => {
                    NotifyLog($"继续执行下一个流程时发生异常: {ex.Message}");
                    NotifyLog($"详细错误信息: {ex.StackTrace}");
                });
                _state.IsRunning = false;
            }
        }

        // **修复：图片下载完成后立即直接处理复制和旋转**
        private void _optVisionService_UpdatePictureModelEvent(PictureModel obj)
        {
            try
            {
                // **安全检查：确保参数有效**
                if (obj == null || string.IsNullOrEmpty(obj.FileName))
                {
                    _logHelper.LogError("❌ OPT照片回调参数无效");
                    return;
                }

                // **新增功能：触发模式切换延迟保护，防止切换期间错误下载多张图片**
                if (IsInTriggerModeDelay)
                {
                    var remainingDelay = _triggerModeDelay - (DateTime.Now - _triggerModeSetTime);
                    // **使用BeginInvoke避免阻塞**
                    Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                        NotifyLog($"⏰ 触发模式切换保护中，忽略照片 - {obj.FileName} (剩余: {remainingDelay.TotalSeconds:F1}秒)")));
                    return;
                }

                // **修复关键问题：根据当前步骤类型过滤OPT照片处理，防止Mark点识别步骤误处理OPT照片**
                if (CurrentProcess == null)
                {
                    // **使用BeginInvoke避免阻塞**
                    Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                        NotifyLog($"⚠️ 收到OPT照片但CurrentProcess为空 - 忽略照片 {obj.FileName}")));
                    return;
                }

                if (CurrentProcess.ProcessType == ProcessTypeEnum.POINT)
                {
                    // **Mark点识别步骤：OPT照片应该被忽略，因为Mark点识别只使用海康相机**
                    Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                        NotifyLog($"⏭️ 当前为Mark点识别步骤，OPT照片应被忽略 - {obj.FileName} (Mark点识别只使用海康相机)")));
                    return;
                }

                if (CurrentProcess.ProcessType != ProcessTypeEnum.TAKEPICTURE ||
                    CurrentProcess.CameraType != CameraTypeEnum.Main)
                {
                    string stepType = CurrentProcess.ProcessType.ToString();
                    string cameraType = CurrentProcess.CameraType.ToString();
                    // **使用BeginInvoke避免阻塞**
                    Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                        NotifyLog($"⏭️ 收到OPT照片但当前步骤不需要：{stepType}/{cameraType} - 忽略照片 {obj.FileName}")));
                    return;
                }

                // **使用BeginInvoke避免阻塞**
                Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                    NotifyLog($"📷 收到OPT照片 - {obj.FileName} (延迟保护已结束，当前拍照步骤，立即处理)")));

                // **新方式：立即在后台线程中直接处理，不使用队列**
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await ProcessOptImageDirectly(obj);

                        // 异步更新UI显示
                        _ = Task.Run(() => GetTopSixPicture());
                    }
                    catch (Exception ex)
                    {
                        // **使用BeginInvoke避免阻塞**
                        Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                            NotifyLog($"❌ OPT照片直接处理异常 - {obj.FileName}: {ex.Message}")));

                        _logHelper.LogError($"❌ OPT照片直接处理异常: {ex.Message}");
                        _logHelper.LogError($"   异常详情: {ex.StackTrace}");
                    }
                });
            }
            catch (Exception ex)
            {
                // **使用BeginInvoke避免阻塞**
                Application.Current?.Dispatcher.BeginInvoke(new Action(() =>
                    NotifyLog($"❌ OPT照片回调异常 - {obj?.FileName ?? "未知"}: {ex.Message}")));

                _logHelper.LogError($"❌ OPT照片回调异常: {ex.Message}");
                _logHelper.LogError($"   异常详情: {ex.StackTrace}");
            }
        }





        /// <summary>
        /// 后台照片队列处理器 - 批量处理，高效不阻塞
        /// </summary>
        private async Task ProcessPhotoQueue()
        {
            try
            {
                while (true)
                {
                    PictureModel photoToProcess = null;

                    // 从队列获取待处理照片
                    lock (_photoQueueLock)
                    {
                        if (_photoProcessingQueue.Count > 0)
                        {
                            photoToProcess = _photoProcessingQueue.Dequeue();
                        }
                    }

                    if (photoToProcess != null)
                    {
                        try
                        {
                            // 处理单张照片
                            await ProcessSinglePhotoOptimized(photoToProcess);

                            lock (_photoQueueLock)
                            {
                                _processedPhotoCount++;
                            }

                                                        Application.Current?.Dispatcher.Invoke(() => 
                            {
                                NotifyLog($"✅ 照片处理完成 - {photoToProcess.FileName} (已处理: {_processedPhotoCount}/{_receivedPhotoCount})");
                                
                                // 通知UI更新照片处理状态
                                OnPropertyChanged(nameof(ProcessedPhotoCount));
                                OnPropertyChanged(nameof(PhotoQueueLength));
                            });
                        }
                        catch (OutOfMemoryException memEx)
                        {
                            // **增强异常处理：内存不足异常**
                            _logHelper.LogError($"💥 照片队列处理 - 内存不足异常: {memEx.Message} - 照片: {photoToProcess.FileName}");
                            Application.Current?.Dispatcher.Invoke(() =>
                                NotifyLog($"❌ 内存不足，强制垃圾回收: {photoToProcess.FileName}"));
                            
                            // 强制垃圾回收
                            GC.Collect();
                            GC.WaitForPendingFinalizers();
                            GC.Collect();
                            
                            WriteAutoRunExceptionLog(memEx, $"照片队列处理内存不足-{photoToProcess.FileName}");
                        }
                        catch (Exception ex)
                        {
                            // **增强异常处理：记录详细信息**
                            _logHelper.LogError($"💥 照片队列处理 - 异常: {ex.GetType().Name} - {ex.Message} - 照片: {photoToProcess.FileName}");
                            _logHelper.LogError($"堆栈跟踪: {ex.StackTrace}");
                            Application.Current?.Dispatcher.Invoke(() =>
                                NotifyLog($"❌ 处理照片异常 - {photoToProcess.FileName}: {ex.Message}"));
                            
                            WriteAutoRunExceptionLog(ex, $"照片队列处理异常-{photoToProcess.FileName}");
                        }
                    }
                    else
                    {
                        // 队列为空，稍作等待
                        await Task.Delay(50);

                                        // **修复关键问题：只有在队列为空且一段时间内没有新照片时才停止处理器**
                        lock (_photoQueueLock)
                        {
                    // 只要队列中还有照片，就继续处理，不管是否在运行状态
                    if (_photoProcessingQueue.Count == 0)
                    {
                        // 队列为空时，等待一段时间看是否有新照片
                        bool shouldStop = !IsRunning && (_receivedPhotoCount == _processedPhotoCount);
                        
                        if (shouldStop)
                            {
                                _isPhotoProcessorRunning = false;
                                Application.Current?.Dispatcher.Invoke(() =>
                                    NotifyLog($"⏹️ 照片后台处理器已停止 (总接收: {_receivedPhotoCount}, 总处理: {_processedPhotoCount})"));
                                break;
                        }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                lock (_photoQueueLock)
                {
                    _isPhotoProcessorRunning = false;
                }

                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 照片后台处理器异常停止: {ex.Message}"));
            }
        }

        /// <summary>
        /// 优化的单张照片处理 - 专注于性能和稳定性，确保每张照片都被完整处理
        /// </summary>
        /// <param name="obj">照片模型</param>
        private async Task ProcessSinglePhotoOptimized(PictureModel obj)
        {
            Application.Current?.Dispatcher.Invoke(() =>
                NotifyLog($"🚀 开始处理照片: {obj.FileName}"));

            bool databaseSaved = false;
            bool imageCopied = false;
            bool imageRotated = false;
            bool aiProcessed = false;

            try
            {
                // **步骤1：快速保存到数据库（最重要的操作）**
                try
                {
                await SavePhotoToDatabaseOptimized(obj);
                    databaseSaved = true;
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"✅ 数据库保存完成: {obj.FileName}"));

                    // **关键修复：每保存一张照片就清理资源，防止第二轮闪退**
                    await ForceResourceCleanupAfterPhotoSave();
                }
                catch (Exception dbEx)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 数据库保存失败: {obj.FileName} - {dbEx.Message}"));
                }

                // **步骤2：复制原图到waitingImage文件夹**
                string waitingImagePath = null;
                try
                {
                    waitingImagePath = await CopyOriginalToWaitingImageFolder(obj);
                    imageCopied = !string.IsNullOrEmpty(waitingImagePath);
                    if (imageCopied)
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"✅ 图片复制完成: {obj.FileName}"));
                    }
                }
                catch (Exception copyEx)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 图片复制失败: {obj.FileName} - {copyEx.Message}"));
                }

                // **步骤3：旋转原图（强制执行，确保每张照片都被旋转）**
                try
                {
                    await RotateImageInPlaceOptimized(obj);
                    imageRotated = true;
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"✅ 图片旋转完成: {obj.FileName}"));
                }
                catch (Exception rotateEx)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 图片旋转失败: {obj.FileName} - {rotateEx.Message}"));
                    // 旋转失败不影响后续处理
                }

                // **步骤4：AI处理准备**
                if (imageCopied && !string.IsNullOrEmpty(waitingImagePath))
                {
                    try
                {
                    await ProcessWaitingImageForAIDetection(waitingImagePath, obj.FileName);
                        aiProcessed = true;
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"✅ AI处理准备完成: {obj.FileName}"));
                    }
                    catch (Exception aiEx)
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ AI处理准备失败: {obj.FileName} - {aiEx.Message}"));
                    }
                }

                // **输出处理总结**
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"📊 照片处理总结 {obj.FileName}: 数据库={databaseSaved}, 复制={imageCopied}, 旋转={imageRotated}, AI准备={aiProcessed}"));
            }
            catch (OutOfMemoryException memEx)
            {
                // **增强异常处理：内存不足异常**
                _logHelper.LogError($"💥 照片处理 - 内存不足异常: {memEx.Message} - 照片: {obj.FileName}");
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 内存不足，强制垃圾回收: {obj.FileName}"));
                
                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                WriteAutoRunExceptionLog(memEx, $"照片处理内存不足-{obj.FileName}");
                throw new Exception($"照片处理内存不足: {obj.FileName}", memEx);
            }
            catch (System.IO.IOException ioEx)
            {
                // **增强异常处理：文件IO异常**
                _logHelper.LogError($"💥 照片处理 - 文件IO异常: {ioEx.Message} - 照片: {obj.FileName}");
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 文件操作异常: {obj.FileName} - {ioEx.Message}"));
                
                WriteAutoRunExceptionLog(ioEx, $"照片处理文件IO异常-{obj.FileName}");
                throw new Exception($"照片文件操作异常: {obj.FileName}", ioEx);
            }
            catch (System.Data.SQLite.SQLiteException sqlEx)
            {
                // **增强异常处理：数据库异常**
                _logHelper.LogError($"💥 照片处理 - 数据库异常: {sqlEx.Message} - 照片: {obj.FileName}");
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 数据库操作异常: {obj.FileName} - {sqlEx.Message}"));
                
                WriteAutoRunExceptionLog(sqlEx, $"照片处理数据库异常-{obj.FileName}");
                throw new Exception($"照片数据库操作异常: {obj.FileName}", sqlEx);
            }
            catch (Exception ex)
            {
                // **增强异常处理：记录详细信息**
                _logHelper.LogError($"💥 照片处理 - 未知异常: {ex.GetType().Name} - {ex.Message} - 照片: {obj.FileName}");
                _logHelper.LogError($"堆栈跟踪: {ex.StackTrace}");
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 照片处理总异常: {obj.FileName} - {ex.Message}"));
                
                WriteAutoRunExceptionLog(ex, $"照片处理总异常-{obj.FileName}");
                throw new Exception($"优化处理照片 {obj.FileName} 异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 优化的数据库保存 - 减少锁定时间
        /// </summary>
        /// <param name="pictureModel">照片模型</param>
        private async Task SavePhotoToDatabaseOptimized(PictureModel pictureModel)
        {
            try
            {
                var reportModel = new ReportModel()
                {
                    CreateTime = DateTime.Now,
                    OperatorName = _state.LoginUser?.Account ?? "System",
                    WorkStation = _state.AppConfig?.WorkStation ?? "Unknown",
                    ProcessId = _state.CurrentProcessModel?.Id ?? "",
                    ProcessName = _state.CurrentProcessModel?.ParamName ?? "",
                    ProductNumber = _state.CurrentProduct?.ProductNumber ?? "",
                    PictureName = pictureModel.FileName,
                    PicturePath = pictureModel.FileFullPath,
                    SerialNumber = SerialNumber ?? "",
                    CheckFilePath = "",
                    OrderNumber = OrderNumber ?? "",
                    Weight = "",
                    Status = "WAITING"
                };

                // **修复：使用重试机制异步保存，防止SQLite并发冲突**
                bool saveSuccess = await SaveReportDataWithRetry(reportModel, pictureModel.FileName);
                if (!saveSuccess)
                {
                    throw new Exception("数据库保存失败，已重试多次");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"保存照片到数据库失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 优化的图片原地旋转 - 解决文件锁定问题，确保每张照片都被旋转
        /// </summary>
        /// <param name="obj">照片模型</param>
        private async Task RotateImageInPlaceOptimized(PictureModel obj)
        {
            await Task.Run(() =>
            {
                string tempPath = obj.FileFullPath + ".tmp";
                
                try
                {
                    // **关键修复：增加详细日志，确保每张照片都被处理**
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"🔄 开始旋转照片: {obj.FileName}"));

                    if (!File.Exists(obj.FileFullPath))
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ 旋转失败，文件不存在: {obj.FileFullPath}"));
                        return;
                    }

                    // 使用重试机制处理文件锁定问题
                    RetryWithDelay(() =>
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"📖 开始读取图片文件: {obj.FileName}"));

                        // **修复：直接从文件加载图片，类似Windows的旋转方式**
                        using (var originalImage = System.Drawing.Image.FromFile(obj.FileFullPath))
                                {
                            Application.Current?.Dispatcher.Invoke(() =>
                                NotifyLog($"🔄 正在旋转图片 (逆时针90度): {obj.FileName}"));

                            // 直接在原图上进行逆时针90度旋转（类似Windows旋转）
                            originalImage.RotateFlip(System.Drawing.RotateFlipType.Rotate270FlipNone);
                            
                            Application.Current?.Dispatcher.Invoke(() =>
                                NotifyLog($"💾 保存旋转后的图片: {obj.FileName}"));
                            
                            // 保存到临时文件
                            originalImage.Save(tempPath, System.Drawing.Imaging.ImageFormat.Jpeg);
                        }
                            
                            // 原子操作替换原文件
                            if (File.Exists(obj.FileFullPath))
                            {
                                File.Delete(obj.FileFullPath);
                            }
                            File.Move(tempPath, obj.FileFullPath);

                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"✅ 图片旋转完成: {obj.FileName}"));
                        
                    }, maxRetries: 3, delayMs: 100);
                }
                catch (Exception ex)
                {
                    // 清理临时文件
                    if (File.Exists(tempPath))
                    {
                        try { File.Delete(tempPath); } catch { }
                    }

                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 图片旋转失败: {obj.FileName} - {ex.Message}"));
                    
                    throw new Exception($"旋转图片失败 (文件: {obj.FileFullPath}): {ex.Message}", ex);
                }
            });
        }

        /// <summary>
        /// 重试机制 - 处理文件锁定等临时性错误
        /// </summary>
        /// <param name="action">要重试的操作</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="delayMs">重试间隔（毫秒）</param>
        private void RetryWithDelay(Action action, int maxRetries = 3, int delayMs = 100)
        {
            Exception lastException = null;
            
            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    action();
                    return; // 成功执行，退出重试循环
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    
                    // 如果是最后一次尝试，直接抛出异常
                    if (attempt == maxRetries)
                    {
                        break;
                    }
                    
                    // 等待一段时间后重试
                    System.Threading.Thread.Sleep(delayMs);
                    
                    // 记录重试日志
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"⚠️ 文件操作重试 {attempt}/{maxRetries} - {ex.Message}"));
                }
            }
            
            // 所有重试都失败，抛出最后一个异常
            throw lastException;
        }

        /// <summary>
        /// 在后台处理OPT相机照片的完整流程
        /// 流程：1.复制到waitingImage文件夹 → 2.旋转原图并存入数据库 → 3.使用waitingImage中的照片进行AI检测处理
        /// </summary>
        /// <param name="obj">OPT相机照片模型</param>
        private async Task ProcessOptImageInBackground(PictureModel obj)
        {
            try
            {
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"📷 开始处理OPT相机照片 - {obj.FileName}"));

                // 步骤1：复制原图到同级目录下的waitingImage文件夹（保持原始状态，用于后续AI检测）
                string waitingImagePath = await CopyOriginalToWaitingImageFolder(obj);
                if (string.IsNullOrEmpty(waitingImagePath))
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 步骤1失败：复制照片到waitingImage文件夹失败 - {obj.FileName}"));
                    return;
                }

                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"✅ 步骤1完成：原图已复制到waitingImage文件夹 - {obj.FileName}"));

                // 步骤2：将原照片逆时针旋转90度并存入数据库
                await RotateOriginalImageAndSaveToDatabase(obj);

                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"✅ 步骤2完成：原照片已旋转90度并存入数据库 - {obj.FileName}"));

                // 步骤3：使用waitingImage文件夹中的照片进行AI检测处理（裁剪+检测队列）
                await ProcessWaitingImageForAIDetection(waitingImagePath, obj.FileName);

                // 处理完成
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"✅ OPT相机照片处理完成 - {obj.FileName}"));
            }
            catch (Exception ex)
            {
                // 记录异常但不影响拍照流程
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ OPT相机照片处理流程异常 - {obj.FileName}: {ex.Message}"));
            }
        }



        /// <summary>
        /// 步骤1：将原图复制到同级目录下的waitingImage文件夹（保持原始状态，用于后续AI检测）
        /// </summary>
        /// <param name="obj">OPT相机照片模型</param>
        /// <returns>复制后的文件路径</returns>
        private async Task<string> CopyOriginalToWaitingImageFolder(PictureModel obj)
        {
            try
            {
                // 获取照片同步录路径（原图所在目录）
                string sourceDir = Path.GetDirectoryName(obj.FileFullPath);
                string waitingImageDir = Path.Combine(sourceDir, "waiting_image");

                // 确保waiting_image文件夹存在
                if (!Directory.Exists(waitingImageDir))
                {
                    Directory.CreateDirectory(waitingImageDir);
                    NotifyLog($"📁 创建waitingImage文件夹: {waitingImageDir}");
                }

                // 构造目标文件路径
                string targetPath = Path.Combine(waitingImageDir, obj.FileName);

                // **修复：直接使用File.Copy进行文件复制，不操作Stream**
                await Task.Run(() =>
                {
                    if (File.Exists(obj.FileFullPath))
                    {
                        NotifyLog($"📋 开始复制文件: {obj.FileName}");
                        
                        if (File.Exists(targetPath))
                        {
                            File.Delete(targetPath); // 如果目标文件存在，先删除
                            NotifyLog($"🗑️ 删除已存在的目标文件: {obj.FileName}");
                        }
                        
                        // 直接使用File.Copy复制文件
                        File.Copy(obj.FileFullPath, targetPath);
                        NotifyLog($"✅ 文件复制完成: {obj.FileName}");
                    }
                    else
                    {
                        NotifyLog($"❌ 源文件不存在，无法复制: {obj.FileFullPath}");
                    }
                });

                NotifyLog($"📋 原图已复制到waitingImage文件夹: {obj.FileName}");
                return targetPath;
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 复制原图到waitingImage文件夹失败: {ex.Message}");
                return null;
            }
        }



        /// <summary>
        /// 步骤2：将原照片逆时针旋转90度并存入数据库
        /// </summary>
        /// <param name="obj">OPT相机照片模型</param>
        private async Task RotateOriginalImageAndSaveToDatabase(PictureModel obj)
        {
            try
            {
                NotifyLog($"🔄 开始旋转OPT原始照片: {obj.FileName}");

                // 检查文件是否存在
                if (!File.Exists(obj.FileFullPath))
                {
                    NotifyLog($"❌ 文件不存在，无法旋转: {obj.FileFullPath}");
                    return;
                }

                // 创建临时文件进行安全旋转
                string tempFilePath = obj.FileFullPath + ".temp";

                await Task.Run(() =>
                {
                    try
                    {
                        // 读取原图并旋转
                        using (var originalImage = System.Drawing.Image.FromFile(obj.FileFullPath))
                        {
                            // 逆时针旋转90度
                            originalImage.RotateFlip(System.Drawing.RotateFlipType.Rotate270FlipNone);

                            // 先保存到临时文件
                            originalImage.Save(tempFilePath, System.Drawing.Imaging.ImageFormat.Jpeg);
                        }

                        // 删除原文件并重命名临时文件
                        File.Delete(obj.FileFullPath);
                        File.Move(tempFilePath, obj.FileFullPath);
                    }
                    catch (Exception ex)
                    {
                        // 如果出现异常，清理临时文件
                        if (File.Exists(tempFilePath))
                        {
                            try { File.Delete(tempFilePath); } catch { }
                        }
                        throw new Exception($"旋转图片时发生错误: {ex.Message}", ex);
                    }
                });

                // 保存到数据库（使用原始的PictureModel）
                await SavePictureToDatabase(obj);

                NotifyLog($"✅ OPT原始照片已旋转并保存到数据库: {obj.FileName}");
            }
            catch (Exception ex)
            {
                // 记录异常但不影响整个流程
                NotifyLog($"❌ 旋转并保存OPT照片到数据库失败: {ex.Message}");
                NotifyLog($"❌ 详细错误信息: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// 步骤3：使用waitingImage文件夹中的照片进行AI检测处理（裁剪+检测队列）
        /// </summary>
        /// <param name="waitingImagePath">waitingImage中的照片路径</param>
        /// <param name="originalFileName">原始文件名</param>
        private async Task ProcessWaitingImageForAIDetection(string waitingImagePath, string originalFileName)
        {
            try
            {
                // 使用waitingImage中的照片进行裁剪，输出到配置的AI检测文件夹
                var waitingImagePictureModel = new PictureModel
                {
                    FileName = Path.GetFileName(waitingImagePath),
                    FileFullPath = waitingImagePath
                };

                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"📸 步骤3开始：使用waitingImage中的照片进行裁剪 - {waitingImagePath}"));

                string[] croppedImages = await CropImageAsync(waitingImagePictureModel);
                if (croppedImages == null || croppedImages.Length == 0)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 裁剪照片失败 - {originalFileName}"));
                    return;
                }

                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"✅ 照片裁剪完成，输出到AI检测文件夹 - 数量: {croppedImages.Length}"));

            }
            catch (Exception ex)
            {
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 步骤3失败：处理waitingImage照片进行AI检测时异常 - {originalFileName}: {ex.Message}"));
            }
        }



        /// <summary>
        /// 将图片逆时针旋转90度
        /// </summary>
        /// <param name="imagePath">原图路径</param>
        /// <returns>旋转后的图片路径</returns>
        private async Task<string> RotateImageCounterClockwise90(string imagePath)
        {
            try
            {
                return await Task.Run(() =>
                {
                    using (var originalImage = System.Drawing.Image.FromFile(imagePath))
                    {
                        // 逆时针旋转90度
                        originalImage.RotateFlip(System.Drawing.RotateFlipType.Rotate270FlipNone);

                        // 生成旋转后的文件名
                        string directory = Path.GetDirectoryName(imagePath);
                        string fileNameWithoutExt = Path.GetFileNameWithoutExtension(imagePath);
                        string extension = Path.GetExtension(imagePath);
                        string rotatedFileName = $"{fileNameWithoutExt}_rotated{extension}";
                        string rotatedPath = Path.Combine(directory, rotatedFileName);

                        // 保存旋转后的图片
                        originalImage.Save(rotatedPath);

                        return rotatedPath;
                    }
                });
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 旋转图片失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 保存照片到数据库
        /// </summary>
        /// <param name="pictureModel">照片模型</param>
        private async Task SavePictureToDatabase(PictureModel pictureModel)
        {
            try
            {
                // 创建报告模型保存到数据库
                var reportModel = new ReportModel()
                {
                    CreateTime = DateTime.Now,
                    OperatorName = _state.LoginUser.Account,
                    WorkStation = _state.AppConfig.WorkStation,
                    ProcessId = _state.CurrentProcessModel?.Id ?? "",
                    ProcessName = _state.CurrentProcessModel?.ParamName ?? "",
                    ProductNumber = _state.CurrentProduct?.ProductNumber ?? "",
                    PictureName = pictureModel.FileName,
                    PicturePath = pictureModel.FileFullPath,
                    SerialNumber = SerialNumber,
                    CheckFilePath = "",
                    OrderNumber = OrderNumber,
                    Weight = "",
                    Status = "WAITING"
                };

                // **修复：使用重试机制保存到数据库**
                bool saveSuccess = await SaveReportDataWithRetry(reportModel, pictureModel.FileName);
                if (saveSuccess)
                {
                    NotifyLog($"💾 照片已保存到数据库: {pictureModel.FileName}");
                }
                else
                {
                    NotifyLog($"❌ 保存照片到数据库失败: {pictureModel.FileName}, 已重试多次");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 保存照片到数据库失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 开始自动执行
        /// </summary>
        private void StartProcess()
        {
            try
            {
                if(CurrentProductModel == null)
                {
                    MessageBox.Show("未选择产品，无法开始自动执行！", "警告");
                    return;
                }
                if (CurrentProcess == null)
                {
                    MessageBox.Show("未选择步骤，无法开始自动执行！", "警告");
                    return;
                }

            // **关键修复：用户点击开始执行时清理之前的检测结果**
            try
            {
                NotifyLog("🧹 开始执行前清理检测结果...");
                _checkService?.ClearDetectionResults();
                NotifyLog("✅ 检测结果清理完成");
            }
            catch (Exception clearEx)
            {
                NotifyLog($"⚠️ 清理检测结果异常: {clearEx.Message}");
            }

            // 重置检测计数器和状态
            CheckCount = 0;
            DetectionStatus = "自动运行中...";
            PendingResultCount = 0;
            PassCount = 0;
            FailCount = 0;

            // 重置引脚计数器
            _currentPinIndex = 0;

            // 重置照片处理队列计数器
            lock (_photoQueueLock)
            {
                _processedPhotoCount = 0;
                _receivedPhotoCount = 0;
            }

            // 清空当前检测结果显示
            ObservableReportData = null;

            // 重置饼图
            UpdatePieChart();

            // **修复：开始自动运行时重置设备状态记录，避免响应手动切换步骤后的旧设备状态**
            _lastDeviceStatus = -1;
            _lastDeviceStatusChangeTime = DateTime.MinValue;
            NotifyLog("🔄 已重置设备状态记录，避免响应手动切换步骤后的旧状态");

            // **新增：记录自动运行开始时间，避免立即步骤切换**
            _autoRunStartTime = DateTime.Now;
            NotifyLog("⏰ 已记录自动运行开始时间，避免立即响应设备状态通知");

            _state.IsRunning = true;
            IsRunning = true;

            NotifyLog($"🚀 开始自动运行 - 已重置检测计数器、引脚计数器、照片处理队列和设备状态记录，当前已检测数量: {CheckCount}");
            }
            catch (OutOfMemoryException memEx)
            {
                // **增强异常处理：内存不足异常**
                _logHelper.LogError($"💥 启动自动运行 - 内存不足异常: {memEx.Message}");
                NotifyLog($"❌ 内存不足，无法启动自动运行: {memEx.Message}");
                
                // 强制垃圾回收
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                WriteAutoRunExceptionLog(memEx, "启动自动运行内存不足");
                MessageBox.Show($"内存不足，无法启动自动运行！\n\n{memEx.Message}", "内存不足");
            }
            catch (Exception ex)
            {
                // **增强异常处理：记录详细信息**
                _logHelper.LogError($"💥 启动自动运行 - 异常: {ex.GetType().Name} - {ex.Message}");
                _logHelper.LogError($"堆栈跟踪: {ex.StackTrace}");
                NotifyLog($"❌ 启动自动运行异常: {ex.Message}");
                
                WriteAutoRunExceptionLog(ex, "启动自动运行异常");
                MessageBox.Show($"启动自动运行失败！\n\n{ex.Message}", "启动失败");
            }
        }

        /// <summary>
        /// 初始化饼图
        /// </summary>
        private void InitializePieChart()
        {
            try
            {
                PieCharts = new SeriesCollection
                {
                    new PieSeries
                    {
                        Title = "合格",
                        Values = new ChartValues<int> { PassCount },
                        DataLabels = true,
                        Fill = System.Windows.Media.Brushes.Green
                    },
                    new PieSeries
                    {
                        Title = "不合格",
                        Values = new ChartValues<int> { FailCount },
                        DataLabels = true,
                        Fill = System.Windows.Media.Brushes.Red
                    }
                };
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 初始化饼图失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新饼图数据
        /// </summary>
        private void UpdatePieChart()
        {
            try
            {
                if (PieCharts != null && PieCharts.Count >= 2)
                {
                    // 更新合格数量
                    PieCharts[0].Values.Clear();
                    PieCharts[0].Values.Add(PassCount);

                    // 更新不合格数量
                    PieCharts[1].Values.Clear();
                    PieCharts[1].Values.Add(FailCount);
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 更新饼图失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 执行批量裁剪 - 收集waitingImage文件夹中的图片，批量调用裁剪接口
        /// **新增功能：多个图片绝对路径用逗号分隔传递给裁剪接口**
        /// </summary>
        /// <param name="imagesToProcess">待处理的图片路径列表</param>  
        private async Task ExecuteBatchCrop(List<string> imagesToProcess)
        {
            try
            {
                if (imagesToProcess.Count == 0)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog("⏭️  批量裁剪：无图片需要裁剪"));
                    return;
                }

                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"✂️ 开始批量裁剪 - 共 {imagesToProcess.Count} 张图片"));

                // 收集waitingImage文件夹中对应的图片路径
                List<string> waitingImagePaths = new List<string>();
                string waitingImageFolder = Path.Combine(_state?.AppConfig?.SaveFolder ?? @"C:\Pictures", "waiting_image");

                foreach (string imagePath in imagesToProcess)
                {
                    // 从原始路径获取文件名
                    string fileName = Path.GetFileName(imagePath);
                    string waitingImagePath = Path.Combine(waitingImageFolder, fileName);

                    if (File.Exists(waitingImagePath))
                    {
                        waitingImagePaths.Add(waitingImagePath);
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"📍 找到waitingImage文件: {waitingImagePath}"));
                    }
                    else
            {
                Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"⚠️ waitingImage文件不存在: {waitingImagePath}"));
            }
        }

                if (waitingImagePaths.Count == 0)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog("❌ 批量裁剪：waitingImage文件夹中没有找到对应的图片"));
                    return;
                }

                // **关键功能：将多个图片路径用逗号分隔**
                string commaSeparatedPaths = string.Join(",", waitingImagePaths);

                // **新增功能：详细记录裁剪接口参数日志**
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    NotifyLog($"📊 批量裁剪接口参数详情：");
                    NotifyLog($"   📈 图片总数量: {waitingImagePaths.Count} 张");
                    NotifyLog($"   📋 每张图片的完整路径:");
                    for (int i = 0; i < waitingImagePaths.Count; i++)
                {
                        NotifyLog($"      [{i + 1:D2}] {waitingImagePaths[i]}");
                    }
                    NotifyLog($"   🔗 接口传参（逗号分隔格式）: {commaSeparatedPaths}");
                });

                // 获取配置的AiSource文件夹路径
                string aiSourceFolder = _state?.AppConfig?.AiSource ?? @"C:\AIImages\source";
                
                // 确保AiSource目录存在
                if (!Directory.Exists(aiSourceFolder))
                {
                    Directory.CreateDirectory(aiSourceFolder);
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"📁 创建AiSource文件夹: {aiSourceFolder}"));
                }

                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"📂 批量裁剪输出文件夹: {aiSourceFolder}"));

                // **调用批量裁剪接口 - 传递逗号分隔的路径字符串**
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"🚀 开始调用批量裁剪接口..."));
                var result = await _apiService.CropImageAsync(commaSeparatedPaths, aiSourceFolder);
                
                if (result?.Status == 200)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"✅ 批量裁剪接口调用成功 - {waitingImagePaths.Count} 张图片已裁剪到 {aiSourceFolder}"));
                }
                else
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 批量裁剪接口调用失败 - 状态码: {result?.Status}, 消息: {result?.Message}"));
                }
            }
            catch (Exception ex)
            {
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 批量裁剪异常: {ex.Message}"));
                
                // 记录详细错误日志
                _logHelper.LogError($"批量裁剪异常: {ex}");
            }
        }

        /// <summary>
        /// 执行批量检测的内部实现 - 在批量裁剪完成后调用
        /// </summary>
        /// <param name="imagesToDetect">待检测的图片路径列表</param>
        private async Task ExecuteBatchDetectionInternal(List<string> imagesToDetect)
        {
            try
            {
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"🔍 开始批量检测 - 共 {imagesToDetect.Count} 张图片"));

                    // 生成批次ID
                    string batchId = $"BATCH_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";

                    // 调用检测服务进行批量检测
                    _checkService.SendToCheckSafely(imagesToDetect.ToArray(), batchId);

                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"✅ 批量检测已提交 - 批次ID: {batchId}，图片数量: {imagesToDetect.Count}"));
            }
            catch (Exception ex)
            {
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 批量检测异常: {ex.Message}"));
                
                // 记录详细错误日志  
                _logHelper.LogError($"批量检测异常: {ex}");
            }
        }

        /// <summary>
        /// 判断检测结果是否为合格
        /// </summary>
        /// <param name="status">检测状态</param>
        /// <returns>true表示合格，false表示不合格</returns>
        private bool IsPassResult(string status)
        {
            if (string.IsNullOrEmpty(status))
                return false;

            // 常见的合格状态标识
            string[] passStatuses = { "PASS", "OK", "ACK", "GOOD", "合格", "通过" };

            return passStatuses.Any(s => string.Equals(s, status, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 根据像素点相对于图片中心的偏差，分别计算X和Y方向的实际物理移动距离。
        /// <param name="dx">像素点相对于中心的X偏差</param>
        /// <param name="dy">像素点相对于中心的Y偏差</param>
        /// <param name="mmPerPixel">每像素对应的毫米数</param>
        /// <returns>实际移动距离（X方向和Y方向，单位mm，精确到0.001）</returns>
        /// </summary>
        private (double xDistance, double yDistance) CalculateIndependentMovements(int dx, int dy, double mmPerPixel)
        {
            // 计算X方向的物理距离
            double xDistance = Math.Round(Math.Abs(dx) * mmPerPixel, 3);

            // 计算Y方向的物理距离
            double yDistance = Math.Round(Math.Abs(dy) * mmPerPixel, 3);

            return (-xDistance, -yDistance);
        }


        private void ScrollToRow(ObservableProcessModel model)
        {
            var index = ProcessDataList.IndexOf(model);
            if (_dataGrid.Items.Count == 0) return;
            System.Windows.Application.Current.Dispatcher.Invoke(() => {
                _dataGrid.ScrollIntoView(_dataGrid.Items[index]);
            });

        }

        private async void ContinueToNextProcess()
        {
            try
            {
                // **修复关键问题：检查是否正在处理步骤切换，避免重复切换**
                if (_isProcessingStepChange)
                {
                    NotifyLog($"⚠️ 步骤切换被忽略 - 正在处理步骤切换中");
                    return;
                }

                // **设置处理标志，防止并发问题**
                _isProcessingStepChange = true;
                _lastStepChangeTime = DateTime.Now;

                // **重定向到安全的异步方法**
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await ContinueToNextProcessSafely();
                    }
                    catch (Exception ex)
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ 步骤切换过程中异常: {ex.Message}"));
                    }
                    finally
                    {
                        _isProcessingStepChange = false;
                    }
                });
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ ContinueToNextProcess异常: {ex.Message}");
                _isProcessingStepChange = false;
            }
        }

        /// <summary>
        /// 输出照片处理统计信息 - 用于诊断照片丢失问题
        /// </summary>
        private void LogPhotoProcessingStats()
        {
            lock (_photoQueueLock)
            {
                NotifyLog($"📊 照片处理统计 - 已接收: {_receivedPhotoCount}, 已处理: {_processedPhotoCount}, 队列中: {_photoProcessingQueue.Count}, 处理器运行: {(_isPhotoProcessorRunning ? "是" : "否")}");

                if (_receivedPhotoCount != _processedPhotoCount)
                {
                    int missing = _receivedPhotoCount - _processedPhotoCount - _photoProcessingQueue.Count;
                    if (missing > 0)
                    {
                        NotifyLog($"⚠️ 发现照片处理异常 - 可能丢失 {missing} 张照片！已接收但未处理且不在队列中");
                    }
                }
            }
        }

        /// <summary>
        /// 强制处理所有待处理照片 - 确保没有照片被遗漏
        /// </summary>
        private async Task ForceProcessAllPendingPhotos()
        {
            try
            {
                NotifyLog("🔄 开始强制处理所有待处理照片...");

                int pendingCount = 0;
                lock (_photoQueueLock)
                {
                    pendingCount = _photoProcessingQueue.Count;
                }

                if (pendingCount == 0)
                {
                    NotifyLog("✅ 照片队列为空，无需强制处理");
                    return;
                }

                NotifyLog($"📋 发现 {pendingCount} 张待处理照片，开始强制处理");

                // 注：已改为直接处理方式，无需启动队列处理器

                // 等待所有照片处理完成，最多等待5分钟
                int maxWaitSeconds = 300; // 5分钟
                int waitedSeconds = 0;

                while (waitedSeconds < maxWaitSeconds)
                {
                    int currentPending = 0;
                    bool isProcessorRunning = false;

                    lock (_photoQueueLock)
                    {
                        currentPending = _photoProcessingQueue.Count;
                        isProcessorRunning = _isPhotoProcessorRunning;
                    }

                    if (currentPending == 0)
                    {
                        NotifyLog($"✅ 所有待处理照片已处理完成 (等待了 {waitedSeconds} 秒)");
                        break;
                    }

                    // 注：已改为直接处理方式，无需检查队列处理器状态

                    // 每5秒输出一次进度
                    if (waitedSeconds % 5 == 0)
                    {
                        NotifyLog($"⏳ 等待照片处理完成... 剩余: {currentPending} 张，已等待: {waitedSeconds} 秒");
                    }

                    await Task.Delay(1000); // 等待1秒
                    waitedSeconds++;
                }

                // 最终检查
                int finalPending = 0;
                lock (_photoQueueLock)
                {
                    finalPending = _photoProcessingQueue.Count;
                }

                if (finalPending > 0)
                {
                    NotifyLog($"⚠️ 超时：仍有 {finalPending} 张照片未处理完成");
                }
                else
                {
                    NotifyLog("✅ 强制处理完成，所有照片都已处理");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 强制处理待处理照片时异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 重置所有状态为新循环做准备
        /// </summary>
        private void ResetAllStatesForNewCycle()
        {
            try
            {
                NotifyLog("🔄 开始重置所有状态为新循环做准备");

                // 重置引脚计数器
                _currentPinIndex = 0;
                NotifyLog("🔢 已重置引脚计数器");

                // 重置检测计数器和状态
                CheckCount = 0;
                DetectionStatus = "新循环开始...";
                PendingResultCount = 0;
                IsRunning = false;
                _state.IsRunning = false;
                PassCount = 0;
                FailCount = 0;
                NotifyLog("📊 已重置检测计数器和状态");

                // 清空当前检测结果显示
                ObservableReportData = null;
                NotifyLog("📋 已清空检测结果显示");

                // 重置饼图
                UpdatePieChart();
                NotifyLog("📈 已重置饼图显示");

                // 重置步骤切换相关标志
                _isProcessingStepChange = false;
                _lastStepChangeTime = DateTime.MinValue;
                NotifyLog("🔄 已重置步骤切换标志");

                NotifyLog("✅ 所有状态已重置，准备开始新循环");
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 重置状态时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 如果配置启用，生成MES文件
        /// </summary>
        private async Task GenerateMESFilesIfEnabled()
        {
            try
            {
                // 检查配置是否启用MES输出
                if (!_state.AppConfig.EnableMESOutput)
                {
                    NotifyLog("📋 MES输出未启用，跳过MES文件生成");
                    return;
                }

                // 检查MES输出文件夹配置
                string mesOutputFolder = _state.AppConfig.MESOutputFolder;
                if (string.IsNullOrEmpty(mesOutputFolder))
                {
                    // 如果未配置输出文件夹，使用默认路径（exe同级目录下的mesdata文件夹）
                    mesOutputFolder = Common.FileHelper.ConcatFile(AppDomain.CurrentDomain.BaseDirectory, "mesdata");
                    NotifyLog($"⚠️ 未配置MES输出文件夹，使用默认路径: {mesOutputFolder}");
                }

                NotifyLog($"📁 开始生成MES文件到文件夹: {mesOutputFolder}");

                // 获取当前产品的所有检测结果
                var allReports = await GetCurrentProductReports();
                if (allReports == null || allReports.Count == 0)
                {
                    NotifyLog("⚠️ 没有找到当前产品的检测结果数据，无法生成MES文件");
                    return;
                }

                NotifyLog($"📊 找到 {allReports.Count} 条检测结果数据");

                // 获取当前产品信息
                var currentProduct = _state.CurrentProduct;
                if (currentProduct == null)
                {
                    NotifyLog("⚠️ 当前产品信息为空，无法生成MES文件");
                    return;
                }

                // 将ObservableProductModel转换为ProductParamModel
                var productParamModel = currentProduct.MapTo<ObservableProductModel, ProductParamModel>();

                // 批量生成MES XML文件
                var generatedFiles = MESHelper.GenerateBatchMESXml(allReports, productParamModel, mesOutputFolder);
                
                if (generatedFiles != null && generatedFiles.Count > 0)
                {
                    NotifyLog($"✅ MES文件生成成功，共生成 {generatedFiles.Count} 个XML文件:");
                    foreach (var filePath in generatedFiles)
                    {
                        NotifyLog($"   📄 {Path.GetFileName(filePath)}");
                    }
                    
                    NotifyLog($"📂 MES文件已保存到: {mesOutputFolder}");
                }
                else
                {
                    NotifyLog("❌ MES文件生成失败，未生成任何文件");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 生成MES文件时发生异常: {ex.Message}");
                _logHelper.LogError($"生成MES文件异常: {ex}");
            }
        }

        /// <summary>
        /// 获取当前产品的检测结果报告
        /// </summary>
        /// <returns>检测结果列表</returns>
        private async Task<List<ReportModel>> GetCurrentProductReports()
        {
            try
            {
                // 检查必要的条件
                var productNumber = _state.CurrentProduct?.ProductNumber;
                var serialNumber = SerialNumber;
                
                if (string.IsNullOrEmpty(productNumber) || string.IsNullOrEmpty(serialNumber))
                {
                    NotifyLog("⚠️ 产品编号或序列号为空，无法查询检测结果");
                    return new List<ReportModel>();
                }

                // 根据当前产品和序列号查询检测结果
                var reports = await _reportService.getByWhere(r => 
                    r.ProductNumber == productNumber &&
                    r.SerialNumber == serialNumber &&
                    r.Status != null &&
                    r.Status != "" &&
                    r.Status != "WAITING");

                return reports;
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 获取当前产品检测结果时异常: {ex.Message}");
                return new List<ReportModel>();
            }
        }

        // 初始化到入口位置
        private async void InitPosition()
        {
            
            await _equipmentService.SetT(0);
            await _equipmentService.SetR(55);
            _equipmentService.SetZAsync(0);
           await _equipmentService.WriteXYArray(new float[]{ -93  },new float[] { -250 },1);
        }

        /// <summary>
        /// 运行时动态计算包含加速和减速点的坐标数组
        /// </summary>
        /// <param name="pinPositions">原始pin坐标列表</param>
        /// <returns>包含加速减速点的X和Y坐标数组</returns>
        private (float[] xArray, float[] yArray) CalculateCoordinatesWithAccelDecelForRuntime(ObservableCollection<ObservablePinPosition> pinPositions)
        {
            const float ACCEL_DECEL_DISTANCE = 15.0f; // 加速减速距离15mm

            try
            {
                if (pinPositions == null || pinPositions.Count == 0)
                {
                    return (new float[0], new float[0]);
                }

                // 如果只有一个点，不需要加速减速点
                if (pinPositions.Count == 1)
                {
                    return (new float[] { pinPositions[0].X }, new float[] { pinPositions[0].Y });
                }

                // 按照Index排序确保顺序正确
                var sortedPins = pinPositions.OrderBy(p => p.Index).ToList();
                var firstPin = sortedPins.First();
                var lastPin = sortedPins.Last();

                // 计算边的方向（基于EdgeNumber）
                var direction = GetEdgeDirection(firstPin.EdgeNumber);

                // 计算加速点（在第一个pin点之前）
                float accelX = firstPin.X - direction.deltaX * ACCEL_DECEL_DISTANCE;
                float accelY = firstPin.Y - direction.deltaY * ACCEL_DECEL_DISTANCE;

                // 计算减速点（在最后一个pin点之后）
                float decelX = lastPin.X + direction.deltaX * ACCEL_DECEL_DISTANCE;
                float decelY = lastPin.Y + direction.deltaY * ACCEL_DECEL_DISTANCE;

                // 构建完整的坐标数组：加速点 + 所有pin点 + 减速点
                var xList = new List<float>();
                var yList = new List<float>();

                // 添加加速点
                xList.Add(accelX);
                yList.Add(accelY);

                // 添加所有pin点
                foreach (var pin in sortedPins)
                {
                    xList.Add(pin.X);
                    yList.Add(pin.Y);
                }

                // 添加减速点
                xList.Add(decelX);
                yList.Add(decelY);

                NotifyLog($"运行时计算坐标数组: 原始{pinPositions.Count}个点，添加加速减速点后共{xList.Count}个点");
                NotifyLog($"加速点: ({accelX:F2}, {accelY:F2})");
                NotifyLog($"减速点: ({decelX:F2}, {decelY:F2})");

                return (xList.ToArray(), yList.ToArray());
            }
            catch (Exception ex)
            {
                NotifyLog($"运行时计算加速减速坐标时发生异常: {ex.Message}");
                // 发生异常时返回原始坐标
                var xArray = pinPositions.Select(p => p.X).ToArray();
                var yArray = pinPositions.Select(p => p.Y).ToArray();
                return (xArray, yArray);
            }
        }

        /// <summary>
        /// 获取边的方向向量
        /// </summary>
        /// <param name="edgeNumber">边的编号</param>
        /// <returns>方向向量</returns>
        private (int deltaX, int deltaY) GetEdgeDirection(int edgeNumber)
        {
            switch (edgeNumber)
            {
                case 1: return (0, 1);   // 上边：Y轴正方向
                case 2: return (1, 0);   // 右边：X轴正方向
                case 3: return (0, -1);  // 下边：Y轴负方向
                case 4: return (-1, 0);  // 左边：X轴负方向
                default: return (0, 0);
            }
        }

        #endregion

        #region 资源清理机制

        /// <summary>
        /// 照片保存后的强制资源清理 - 防止第二轮闪退的关键方法
        /// </summary>
        private async Task ForceResourceCleanupAfterPhotoSave()
        {
            try
            {
                _logHelper.LogInfo("🧹 开始照片保存后资源清理...");

                // 1. 强制垃圾回收（连续3次，确保彻底清理）
                for (int i = 0; i < 3; i++)
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    await Task.Delay(50);
                }

                // 2. 清理图像相关资源
                try
                {
                    // 清理可能的图像缓存（移除不存在的CameraImage引用）
                    // 图像资源会通过垃圾回收自动清理
                    await Task.Delay(50); // 给图像处理一些时间
                }
                catch (Exception imgEx)
                {
                    _logHelper.LogError($"图像资源清理异常: {imgEx.Message}");
                }

                // 3. 清理照片处理队列
                try
                {
                    lock (_photoQueueLock)
                    {
                        // 不清空队列，但确保没有死锁
                        if (_photoProcessingQueue.Count > 10)
                        {
                            _logHelper.LogInfo($"⚠️ 照片处理队列过长: {_photoProcessingQueue.Count}，可能存在积压");
                        }
                    }
                }
                catch (Exception queueEx)
                {
                    _logHelper.LogError($"队列清理异常: {queueEx.Message}");
                }

                // 4. 等待文件系统操作完成
                await Task.Delay(200);

                // 5. 最后一次垃圾回收
                GC.Collect();

                _logHelper.LogInfo("✅ 照片保存后资源清理完成");
            }
            catch (Exception ex)
            {
                _logHelper.LogError($"❌ 照片保存后资源清理异常: {ex.Message}");
            }
        }

        #endregion

        #region 数据库操作重试机制

        /// <summary>
        /// 使用重试机制保存报告数据到数据库，防止SQLite并发冲突导致闪退
        /// </summary>
        /// <param name="reportModel">要保存的报告数据</param>
        /// <param name="fileName">文件名</param>
        /// <returns>是否保存成功</returns>
        private async Task<bool> SaveReportDataWithRetry(ReportModel reportModel, string fileName)
        {
            const int maxRetries = 5;
            const int baseDelayMs = 100;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    bool success = await _reportService.Add(reportModel);
                    if (success)
                    {
                        _logHelper.LogInfo($"✅ 数据库保存成功 - 照片:{fileName}, 尝试次数:{attempt}");
                        return true;
                    }
                    else
                    {
                        _logHelper.LogError($"⚠️ 数据库保存返回false - 照片:{fileName}, 尝试次数:{attempt}");
                        if (attempt < maxRetries)
                        {
                            await Task.Delay(baseDelayMs * attempt);
                            continue;
                        }
                        return false;
                    }
                }
                catch (Exception saveEx)
                {
                    string errorMsg = saveEx.Message?.ToLower() ?? "";
                    bool isRetryable = errorMsg.Contains("database is locked") ||
                                      errorMsg.Contains("busy") ||
                                      errorMsg.Contains("timeout") ||
                                      errorMsg.Contains("集合已修改") ||
                                      saveEx is InvalidOperationException;

                    if (isRetryable && attempt < maxRetries)
                    {
                        // 指数退避延迟
                        int delay = baseDelayMs * (int)Math.Pow(2, attempt - 1) + new Random().Next(0, 100);
                        _logHelper.LogError($"⚠️ 数据库保存失败，第{attempt}次重试 - 照片:{fileName}, 错误:{saveEx.Message}, {delay}ms后重试");

                        await Task.Delay(delay);
                        continue;
                    }
                    else
                    {
                        _logHelper.LogError($"❌ 数据库保存最终失败 - 照片:{fileName}, 尝试次数:{attempt}, 错误:{saveEx.Message}");
                        _logHelper.LogError($"   异常详情: {saveEx.StackTrace}");
                        return false;
                    }
                }
            }

            return false;
        }

        #endregion

        #region 接口调用
        private async Task<bool> DiffMarkPoint(Camera.Com.PictureModel obj)
        {
            bool isOk = false;
            try
            {
                var result = await _apiService.FetchMarkPoint(obj.FileFullPath);
                if (result.Status == 200 && result.Data != null && result.Data.Length > 0)
                {
                    var temp = result.Data[0];
                    var temp2 = CalculateIndependentMovements(temp.Diff_X, temp.Diff_Y, _state.AppConfig.DiffRotate);
                    diffXLength = (float)temp2.xDistance;
                    diffYLength = (float)temp2.yDistance;
                    isOk = true;
                    NotifyLog($"原点识别成功！偏移量:{JsonConvert.SerializeObject(result.Data[0])}");
                }
                else
                {
                    isOk = false;
                    diffXLength = 0;
                    diffYLength = 0;
                    NotifyLog($"Mark点识别失败，状态码: {result.Status}");

                    // **新增：Mark点识别失败时的完整处理逻辑**
                    await HandleMarkDetectionFailure();
                }
            }
            catch (Exception ex)
            {
                isOk = false;
                diffXLength = 0;
                diffYLength = 0;
                NotifyLog($"❌ Mark点识别处理异常: {ex.Message}");

                // **新增：异常情况下也执行失败处理逻辑**
                await HandleMarkDetectionFailure();
            }

            return isOk;
        }

        /// <summary>
        /// 处理Mark点识别失败的完整逻辑
        /// </summary>
        private async Task HandleMarkDetectionFailure()
        {
            try
            {
                NotifyLog("🛑 Mark点识别失败，开始执行失败处理流程...");

                // 1. 停止自动运行模式
                if (IsRunning)
                {
                    NotifyLog("🔄 停止自动运行模式...");
                    _state.IsRunning = false;
                    IsRunning = false;
                    NotifyLog("✅ 自动运行模式已停止");
                }

                // 2. 设备回到初始位置
                NotifyLog("📍 设备回到初始位置...");
                await Task.Run(() => InitPosition());
                NotifyLog("✅ 设备已回到初始位置");

                // 3. 显示错误提示给用户
                System.Windows.Application.Current.Dispatcher.Invoke(() => {
                    try
                    {
                        HandyControl.Controls.MessageBox.Error(
                            "Mark点识别失败！\n\n请检查以下项目后重新尝试：\n" +
                            "1. 确认Mark点识别服务已启用\n" +
                            "2. 检查图像质量和Mark点清晰度\n" +
                            "3. 确认相机位置和焦距设置\n" +
                            "4. 检查网络连接和AI服务状态",
                            "Mark点识别失败");
                    }
                    catch (Exception msgEx)
                    {
                        NotifyLog($"❌ 显示Mark点识别失败对话框异常: {msgEx.Message}");
                    }
                });

                NotifyLog("✅ Mark点识别失败处理流程完成");
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ Mark点识别失败处理流程异常: {ex.Message}");
            }
        }


        /// <summary>
        /// 异步裁剪图片方法 - 使用临时目录避免检测混乱
        /// </summary>
        /// <param name="obj"></param>
        /// <returns>返回裁剪后的文件路径数组</returns>
        private async Task<string[]> CropImageAsync(Camera.Com.PictureModel obj)
        {
            try
            {
                string[] fileFullPaths = obj.FileFullPath.Split(',');
                string[] fileNames = obj.FileName.Split(',');

                // 添加调试日志：检查输入参数
                NotifyLog($"开始图片裁剪处理 - 输入文件数量:{fileNames.Length}");
                NotifyLog($"输入文件路径: {obj.FileFullPath}");

                // 检查输入文件是否存在
                foreach (var filePath in fileFullPaths)
                {
                    if (!Common.FileHelper.FileIsExists(filePath))
                    {
                        NotifyLog($"输入文件不存在: {filePath}");
                        throw new Exception($"输入文件不存在: {filePath}");
                    }
                }

                // 直接使用配置的source文件夹，便于AI服务检测
                string sourceCropFolder = Common.FileHelper.GetFolderName(_state.AppConfig.AiSource);

                // 确保source目录存在
                Common.FileHelper.CreateFolder(sourceCropFolder);
                NotifyLog($"使用配置的source文件夹进行裁剪: {sourceCropFolder}");

                // 构建输出路径数组 - 使用source文件夹
                List<string> outputPaths = new List<string>();
                for (int i = 0; i < fileFullPaths.Length; i++)
                {
                    outputPaths.Add(Common.FileHelper.ConcatFile(sourceCropFolder, fileNames[i]));
                }

                // 将输出路径数组拼接成字符串
                string outputPathString = string.Join(",", outputPaths);

                // 添加调试日志：显示请求参数
                NotifyLog($"临时裁剪输出路径: {outputPathString}");
                NotifyLog($"开始调用AI裁剪接口...");

                // 传入拼接后的输入和输出路径
                var result = await _apiService.CropImageAsync(obj.FileFullPath, outputPathString);
                if (result.Status == 200)
                {
                    NotifyLog($"批次照片裁剪成功！source文件夹:{sourceCropFolder}, 文件数量:{fileNames.Length}");
                    return outputPaths.ToArray(); // 返回裁剪后的文件路径数组
                }
                else
                {
                    NotifyLog($"AI裁剪接口返回异常状态码: {result.Status}, 消息: {result.Message}");
                    throw new Exception($"图片裁剪失败，状态码: {result.Status}, 消息: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"裁剪异常:{ex}" );

                // 添加更详细的异常信息
                if (ex.InnerException != null)
                {
                    NotifyLog($"内部异常详情: {ex.InnerException.Message}");
                }

                // 检查网络连接
                try
                {
                    using (var client = new System.Net.Http.HttpClient())
                    {
                        client.Timeout = TimeSpan.FromSeconds(5);
                        var testResponse = await client.GetAsync("http://localhost:5000");
                        NotifyLog($"AI服务连接测试结果: {testResponse.StatusCode}");
                    }
                }
                catch (Exception netEx)
                {
                    NotifyLog($"AI服务连接失败: {netEx.Message}");
                }
                throw;
            }
        }


        private async Task<bool> GetQRCode(Camera.Com.PictureModel obj)
        {
            bool isOk = false;
            var result = await _apiService.QrDetectAsync(obj.FileFullPath);
            if (result.Status == 200 && result.Data != null && result.Data.Length > 0)
            {
                isOk = true;
                NotifyLog($"识别二维码信息:{result.Data[0]}");
            }
            else
            {
                isOk = false;
                NotifyLog($"未识别到二维码信息!");
                _state.IsRunning = false;
            }

            return isOk;
        }

        /// <summary>
        /// 执行单个pin脚拍照 - 新增：从实时画面流直接获取一帧进行完整处理
        /// **不修改现有逻辑，新增功能：直接从实时画面获取帧→存储→旋转→裁剪→检测**
        /// </summary>
        private async Task ExecuteSinglePinPhotoCapture()
        {
            try
            {
                NotifyLog($"📌 开始执行单个pin脚拍照 - Pin索引: {CurrentPinPosition.Index}, 位置: ({CurrentPinPosition.X}, {CurrentPinPosition.Y})");

                // 设置设备参数到当前步骤的参数
                await _equipmentService.SetT(CurrentProcess.EquipmentPara.T);
                _equipmentService.SetZAsync(CurrentProcess.EquipmentPara.Z);
                await _equipmentService.SetR(CurrentProcess.EquipmentPara.R);

                // 移动到选中的pin脚位置
                await _equipmentService.WriteXYArray(new float[] { CurrentPinPosition.X }, new float[] { CurrentPinPosition.Y }, 1);

                NotifyLog($"📍 设备已移动到pin脚位置 - Pin索引: {CurrentPinPosition.Index}");

                // 等待设备移动稳定
                await Task.Delay(500);

                // 根据步骤类型执行相应的拍照逻辑
                if (CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE)
                {
                    if (CurrentProcess.CameraType == CameraTypeEnum.Main)
                    {
                        // **新增功能：OPT相机从实时画面流直接获取帧进行完整处理**
                        string folderName = CheckFolder();
                        string basePictureName = CheckPictureName();

                        // 为单个pin脚生成特殊的文件名
                        string pinPictureName = $"{basePictureName}_Pin{CurrentPinPosition.Index}_Manual";

                        // **关键改进：使用新的从实时画面流获取帧的方法**
                        bool captureSuccess = _optVisionService.CaptureSingleFrameFromStream(folderName, pinPictureName);
                        
                        if (captureSuccess)
                        {
                            NotifyLog($"🎯 OPT相机单个pin脚拍照已启动 - Pin索引: {CurrentPinPosition.Index}, 文件名: {pinPictureName}");
                            NotifyLog($"📝 图片将自动进行：存储→复制→旋转→裁剪→检测 的完整处理流程");
                        }
                        else
                        {
                            NotifyLog($"❌ OPT相机单个pin脚拍照失败 - Pin索引: {CurrentPinPosition.Index}");
                            MessageBox.Error("从实时画面获取帧失败，请检查相机连接状态！", "拍照失败");
                        }
                    }
                    else if (CurrentProcess.CameraType == CameraTypeEnum.Child)
                    {
                        // HK相机拍照
                        string folderName = CheckFolder();
                        string pinPictureName = $"{CheckPictureName()}_Pin{CurrentPinPosition.Index}_Manual";

                        _hkVisionService.TakePicture(folderName, pinPictureName);
                        NotifyLog($"📷 HK相机单个pin脚拍照已触发 - Pin索引: {CurrentPinPosition.Index}, 文件名: {pinPictureName}");
                    }
                }
                else
                {
                    NotifyLog($"⚠️ 当前步骤类型 {CurrentProcess.ProcessType} 不支持单个pin脚拍照");
                    MessageBox.Warning($"当前步骤类型 {CurrentProcess.ProcessType} 不支持单个pin脚拍照！", "警告");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 执行单个pin脚拍照异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 执行整个步骤拍照 - 按照步骤逻辑完整执行
        /// </summary>
        private async Task ExecuteStepPhotoCapture()
        {
            try
            {
                NotifyLog($"🎯 开始执行步骤拍照 - 步骤: {CurrentProcess.ParamName}, 类型: {CurrentProcess.ProcessType}");

                // 根据步骤类型执行不同的逻辑
                switch (CurrentProcess.ProcessType)
                {
                    case ProcessTypeEnum.TAKEPICTURE:
                        await ExecuteTakePictureStep();
                        break;

                    case ProcessTypeEnum.POINT:
                        await ExecuteMarkPointStep();
                        break;

                    case ProcessTypeEnum.QRCODE:
                        await ExecuteQRCodeStep();
                        break;

                    default:
                        NotifyLog($"⚠️ 不支持的步骤类型: {CurrentProcess.ProcessType}");
                        MessageBox.Warning($"不支持的步骤类型: {CurrentProcess.ProcessType}！", "警告");
                        break;
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 执行步骤拍照异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 执行拍照步骤
        /// </summary>
        private async Task ExecuteTakePictureStep()
        {
            try
            {
                // 设置设备参数
                await _equipmentService.SetT(CurrentProcess.EquipmentPara.T);
                _equipmentService.SetZAsync(CurrentProcess.EquipmentPara.Z);
                await _equipmentService.SetR(CurrentProcess.EquipmentPara.R);

                if (CurrentProcess.PinPositions != null && CurrentProcess.PinPositions.Count > 0)
                {
                    // 动态计算包含加速减速点的坐标数组
                    var (xArrayWithAccelDecel, yArrayWithAccelDecel) = CalculateCoordinatesWithAccelDecelForRuntime(CurrentProcess.PinPositions);
                    await _equipmentService.WriteXYArray(xArrayWithAccelDecel, yArrayWithAccelDecel, xArrayWithAccelDecel.Length);

                    NotifyLog($"📍 设备已移动到拍照步骤位置 - 包含{CurrentProcess.PinPositions.Count}个pin点");

                    // 生成文件夹和文件名
                    string folderName = CheckFolder();
                    string basePictureName = CheckPictureName();

                    if (CurrentProcess.CameraType == CameraTypeEnum.Main)
                    {
                        // OPT相机拍照 - 对每个pin脚拍照
                        NotifyLog($"📷 开始OPT相机拍照 - 共{CurrentProcess.PinPositions.Count}个pin脚");

                        for (int i = 0; i < CurrentProcess.PinPositions.Count; i++)
                        {
                            var pin = CurrentProcess.PinPositions.OrderBy(p => p.Index).ElementAt(i);
                            string pinPictureName = $"{basePictureName}_Pin{pin.Index}_Manual";

                            _optVisionService.TakePicture(folderName, pinPictureName);
                            NotifyLog($"📷 OPT相机拍照 {i + 1}/{CurrentProcess.PinPositions.Count} - Pin索引: {pin.Index}");

                            // 拍照间隔，避免过快
                            if (i < CurrentProcess.PinPositions.Count - 1)
                            {
                                await Task.Delay(500); // 500ms间隔
                            }
                        }
                    }
                                         else if (CurrentProcess.CameraType == CameraTypeEnum.Child)
                     {
                         // HK相机拍照
                         string stepPictureName = $"{basePictureName}_Step_Manual";
                         _hkVisionService.TakePicture(folderName, stepPictureName);
                         NotifyLog($"📷 HK相机步骤拍照已触发 - 文件名: {stepPictureName}");
                     }
                }
                else
                {
                    NotifyLog($"⚠️ 拍照步骤 {CurrentProcess.ParamName} 没有pin脚位置数据");
                    MessageBox.Warning($"拍照步骤 {CurrentProcess.ParamName} 没有pin脚位置数据！", "警告");
                }
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 执行拍照步骤异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 执行Mark点识别步骤
        /// </summary>
        private async Task ExecuteMarkPointStep()
        {
            try
            {
                // 设置设备参数
                await _equipmentService.SetT(CurrentProcess.EquipmentPara.T);
                _equipmentService.SetZAsync(CurrentProcess.EquipmentPara.Z);
                await _equipmentService.SetR(CurrentProcess.EquipmentPara.R);

                // 移动到Mark点位置
                await _equipmentService.WriteXYArray(new float[] { CurrentProcess.EquipmentPara.X }, new float[] { CurrentProcess.EquipmentPara.Y }, 1);

                NotifyLog($"📍 设备已移动到Mark点位置 - ({CurrentProcess.EquipmentPara.X}, {CurrentProcess.EquipmentPara.Y})");

                // HK相机拍照进行Mark点识别
                string folderName = CheckFolder();
                folderName = Common.FileHelper.ConcatFile(folderName, "point_picture");
                Common.FileHelper.CreateFolder(folderName);

                string pictureName = $"{CheckPictureName()}_MarkPoint_Manual";
                _hkVisionService.TakePicture(folderName, pictureName);

                NotifyLog($"📷 HK相机Mark点识别拍照已触发 - 文件名: {pictureName}");
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 执行Mark点识别步骤异常: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 执行二维码识别步骤
        /// </summary>
        private async Task ExecuteQRCodeStep()
        {
            try
            {
                // 设置设备参数
                await _equipmentService.SetT(CurrentProcess.EquipmentPara.T);
                _equipmentService.SetZAsync(CurrentProcess.EquipmentPara.Z);
                await _equipmentService.SetR(CurrentProcess.EquipmentPara.R);

                // 移动到二维码位置
                await _equipmentService.WriteXYArray(new float[] { CurrentProcess.EquipmentPara.X }, new float[] { CurrentProcess.EquipmentPara.Y }, 1);

                NotifyLog($"📍 设备已移动到二维码位置 - ({CurrentProcess.EquipmentPara.X}, {CurrentProcess.EquipmentPara.Y})");

                // HK相机拍照进行二维码识别
                string folderName = CheckFolder();
                string pictureName = $"{CheckPictureName()}_QRCode_Manual";
                _hkVisionService.TakePicture(folderName, pictureName);

                NotifyLog($"📷 HK相机二维码识别拍照已触发 - 文件名: {pictureName}");
            }
            catch (Exception ex)
            {
                NotifyLog($"❌ 执行二维码识别步骤异常: {ex.Message}");
                throw;
            }
        }


        #endregion

        #region 文件操作
        private string CheckFolder()
        {
            string folderName = _state.AppConfig.SaveFolder;
            var temp = TranslateFolderName();
            folderName = Common.FileHelper.ConcatFile(folderName, temp);
            Common.FileHelper.CreateFolder(folderName);
            return folderName;
        }

        private string CheckPictureName()
        {

            return TranslatePictureName();
        }

        private string TranslatePictureName()
        {
            string result = string.Empty;
            var pictureNameList = _state.AppConfig.PictureList.OrderBy(item => item.Index).ToList();
            if (pictureNameList.Count == 0) return result;
            List<string> names = TranslateName(pictureNameList);
            return string.Join("", names);
        }

        private string TranslateWaterName()
        {
            var pictureNameList = _state.AppConfig.PictureList.OrderBy(item => item.Index).ToList();
            if (pictureNameList.Count == 0) return string.Empty;
            string result = string.Empty;
            List<string> names = TranslateName(pictureNameList);
            return string.Join("", names);
        }

        private string TranslateFolderName()
        {
            var pictureNameList = _state.AppConfig.FolderNameList.OrderBy(item => item.Index).ToList();
            string result = string.Empty;
            if (pictureNameList.Count == 0) return string.Empty;
            List<string> names = TranslateName(pictureNameList);
            switch (_state.AppConfig.FolderNameType)
            {
                case FolderNameTypeEnum.SAMELEVEL:
                    result = string.Join("", names);
                    break;
                case FolderNameTypeEnum.OTHERLEVEL:
                    result = string.Join("\\", names);
                    break;
            }
            return result;
        }

        private List<string> TranslateName(List<ObservableCustomNameModel> model)
        {
            List<string> names = new List<string>();
            model.ForEach(item =>
            {
                var types = Enum.Parse(typeof(NameEnum), item.NameType);
                switch (types)
                {
                    case NameEnum.DATA:
                        names.Add(DateTime.Now.Day.ToString());
                        break;
                    case NameEnum.MINUTES:
                        names.Add(DateTime.Now.Minute.ToString());
                        break;
                    case NameEnum.SECONDS:
                        names.Add(DateTime.Now.Second.ToString());
                        break;
                    case NameEnum.PRODUCTNAME:
                        names.Add(_state.CurrentProduct == null ? "" : _state.CurrentProduct.ProductName);
                        break;
                    case NameEnum.MOUNTH:
                        names.Add(DateTime.Now.Month.ToString());
                        break;
                    case NameEnum.CUSTOM:
                        names.Add(item.Content);
                        break;
                    case NameEnum.YEAR:
                        names.Add(DateTime.Now.Year.ToString());
                        break;
                    case NameEnum.STEPNAME:
                        names.Add(_state.CurrentProcessModel == null ? "" : _state.CurrentProcessModel.ParamName);
                        break;
                    case NameEnum.WEIGHT:
                        names.Add(_state.Weight);
                        break;
                    case NameEnum.PRODUCTNUMBER:
                        names.Add(_state.CurrentProduct == null ? "" : _state.CurrentProduct.ProductNumber);
                        break;
                    case NameEnum.SERIALNUMBER:
                        names.Add(SerialNumber);
                        break;
                }
            });
            return names;
        }
        #endregion

        #region 查询近期拍照
        private async void GetTopSixPicture()
        {
            var reports = await _reportService.TopData(8);
            // 在UI线程上更新ReportList
            System.Windows.Application.Current.Dispatcher.Invoke(() => {
                ReportList = reports;
            });
        }
        #endregion

        #region 日志记录
        /// <summary>
        /// 记录日志 - 修复重复日志问题
        /// 只输出到日志系统，不通过HObservable发送（避免InfoControlViewModel重复记录）
        /// </summary>
        private void NotifyLog(string message, HEventCode eventCode = HEventCode.SUCCESS)
        {
            // **修复重复日志问题：只输出到日志系统，不通过HObservable发送**
            // 原因：InfoControlViewModel会监听HObservable事件并再次调用_logger.LogInfo，导致重复记录
            switch (eventCode)
            {
                case HEventCode.ERROR:
                    _logHelper.LogError(message);
                    break;
                case HEventCode.SUCCESS:
                    _logHelper.LogInfo(message);
                    break;
            }
        }

        /// <summary>
        /// **新方式：直接处理OPT照片 - 立即复制、旋转、保存、裁剪，无队列延迟**
        /// </summary>
        /// <param name="obj">OPT相机照片模型</param>
        private async Task ProcessOptImageDirectly(PictureModel obj)
        {
            try
            {
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"🚀 开始直接处理OPT照片 - {obj.FileName}"));

                // **步骤1：立即复制到waitingImage文件夹 - 使用File.Copy**
                string waitingImagePath = await CopyToWaitingImageDirectly(obj);
                if (string.IsNullOrEmpty(waitingImagePath))
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 复制失败 - {obj.FileName}"));
                    return;
                }

                // **步骤2：立即旋转原图 - 使用Image.FromFile + RotateFlip**
                await RotateImageDirectly(obj);

                // **步骤3：保存到数据库（旋转后的图片绝对路径）**
                await SaveToDatabaseDirectly(obj);

                // **步骤4：立即单张裁剪**
                string fileName = Path.GetFileName(obj.FileFullPath);
                await CallSingleCropDirectly(waitingImagePath, fileName);

                // **步骤5：立即单张检测 - 防闪退保护**
                if (IsDetectionEnabled)
                {
                    try
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"🎯 检验已启用，准备执行检测 - {obj.FileName}"));
                        
                        // **步骤5：立即单张检测**
                        await CallSingleDetectionDirectly(obj.FileFullPath, fileName);
                    }
                    catch (Exception detectionEx)
                    {
                        // **检测异常的额外保护层**
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"💥 检测调用保护性异常处理 - {obj.FileName}: {detectionEx.Message}"));
                        _logHelper.LogError($"检测调用保护性异常（{obj.FileName}）: {detectionEx}");
                    }
                }
                else
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"⏭️ 检验已禁用，跳过检测 - {obj.FileName}"));
                }

                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"✅ 直接处理完成 - {obj.FileName}"));
            }
            catch (Exception ex)
            {
                Application.Current?.Dispatcher.Invoke(() =>
                    NotifyLog($"❌ 直接处理异常 - {obj.FileName}: {ex.Message}"));
            }
        }

        /// <summary>
        /// 直接复制到waitingImage文件夹 - 使用File.Copy，一瞬间完成
        /// </summary>
        private async Task<string> CopyToWaitingImageDirectly(PictureModel obj)
        {
            return await Task.Run(() =>
            {
                try
                {
                    string sourceDir = Path.GetDirectoryName(obj.FileFullPath);
                    string waitingImageDir = Path.Combine(sourceDir, "waiting_image");

                    // 确保文件夹存在
                    if (!Directory.Exists(waitingImageDir))
                    {
                        Directory.CreateDirectory(waitingImageDir);
                    }

                    string targetPath = Path.Combine(waitingImageDir, obj.FileName);

                    // **直接使用File.Copy - 应该是一瞬间**
                    if (File.Exists(obj.FileFullPath))
                    {
                        if (File.Exists(targetPath))
                        {
                            File.Delete(targetPath);
                        }
                        File.Copy(obj.FileFullPath, targetPath);
                        
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"📋 复制完成 - {obj.FileName}"));
                        
                        return targetPath;
                    }
                    else
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ 源文件不存在 - {obj.FileFullPath}"));
                        return null;
                    }
                }
                catch (Exception ex)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 复制异常 - {obj.FileName}: {ex.Message}"));
                    return null;
                }
            });
        }

        /// <summary>
        /// 直接旋转图片 - 使用Image.FromFile + RotateFlip，类似Windows旋转
        /// </summary>
        private async Task RotateImageDirectly(PictureModel obj)
        {
            await Task.Run(() =>
            {
                try
                {
                    if (!File.Exists(obj.FileFullPath))
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ 旋转失败，文件不存在 - {obj.FileFullPath}"));
                        return;
                    }

                    string tempPath = obj.FileFullPath + ".tmp";

                    // **直接使用Image.FromFile + RotateFlip - 类似Windows旋转**
                    using (var image = System.Drawing.Image.FromFile(obj.FileFullPath))
                    {
                        // 逆时针90度旋转
                        image.RotateFlip(System.Drawing.RotateFlipType.Rotate270FlipNone);
                        
                        // 保存到临时文件
                        image.Save(tempPath, System.Drawing.Imaging.ImageFormat.Jpeg);
                    }

                    // 原子操作替换原文件
                    if (File.Exists(obj.FileFullPath))
                    {
                        File.Delete(obj.FileFullPath);
                    }
                    File.Move(tempPath, obj.FileFullPath);

                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"🔄 旋转完成 - {obj.FileName}"));
                }
                catch (Exception ex)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 旋转异常 - {obj.FileName}: {ex.Message}"));
                }
            });
        }

        /// <summary>
        /// 直接保存到数据库 - 将旋转后图片的绝对路径存入数据库
        /// </summary>
        private async Task SaveToDatabaseDirectly(PictureModel obj)
        {
            await Task.Run(async () =>
            {
                try
                {
                    // 创建报告记录
                    var reportModel = new ReportModel
                    {
                        // 不能为空的字段，添加安全默认值
                        PictureName = obj.FileName ?? "未知图片名称", // 确保不为空
                        PicturePath = obj.FileFullPath ?? "", // 确保不为空，至少是空字符串
                        CreateTime = DateTime.Now, // 不会为空
                        OperatorName = _state.LoginUser?.UserName ?? "System", // 确保不为空
                        CheckFilePath = "", // 按用户要求，可以为空字符串但不能为null
                        Status = "WAITING", // 初始状态为等待检测，不会为空
                        CheckTime = DateTime.Now, // 不会为空
                        
                        // 可以为空的字段
                        OrderNumber = OrderNumber ?? "",
                        SerialNumber = SerialNumber ?? "",
                        ProductNumber = _state.CurrentProduct?.ProductNumber ?? "",
                        ProcessId = _state.CurrentProcessModel?.Id ?? "",
                        ProcessName = _state.CurrentProcessModel?.ParamName ?? "",
                        Weight = _state.Weight ?? "",
                        WorkStation = _state.AppConfig?.WorkStation ?? ""
                    };

                    // **修复：使用重试机制保存到数据库**
                    bool success = await SaveReportDataWithRetry(reportModel, obj.FileName);

                    if (success)
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"💾 数据库保存完成 - {obj.FileName} (路径: {obj.FileFullPath})"));
                    }
                    else
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ 数据库保存失败 - {obj.FileName}, 已重试多次"));
                    }
                }
                catch (Exception ex)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 数据库保存异常 - {obj.FileName}: {ex.Message}"));
                    
                    // 记录详细错误日志
                    _logHelper.LogError($"保存照片到数据库异常: {ex}");
                }
            });
        }


        /// <summary>
        /// 直接调用裁剪接口 - 使用waitingImage文件夹中的照片，输出到配置的AiSource文件夹
        /// </summary>
        /// <param name="waitingImagePath">waitingImage文件夹中照片的绝对路径</param>
        /// <param name="fileName">文件名</param>
        private async Task CallCropInterfaceDirectly(string waitingImagePath, string fileName)
        {
            await Task.Run(async () =>
            {
                try
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"✂️ 开始调用裁剪接口（使用waitingImage中的照片）: {fileName}"));

                    // 检查waitingImage文件夹中的照片是否存在
                    if (!File.Exists(waitingImagePath))
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ waitingImage文件夹中的源文件不存在: {waitingImagePath}"));
                        return;
                    }

                    // 获取配置的AiSource文件夹路径
                    string aiSourceFolder = _state?.AppConfig?.AiSource ?? @"C:\AIImages\source";
                    
                    // 确保AiSource目录存在
                    if (!Directory.Exists(aiSourceFolder))
                    {
                        Directory.CreateDirectory(aiSourceFolder);
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"📁 创建AiSource文件夹: {aiSourceFolder}"));
                    }

                    // 构建输出路径
                    string outputPath = Path.Combine(aiSourceFolder, fileName);

                    // **新增功能：详细记录单个裁剪接口参数日志**
                    Application.Current?.Dispatcher.Invoke(() =>
                    {
                        NotifyLog($"📊 单个裁剪接口参数详情：");
                        NotifyLog($"   📈 图片数量: 1 张");
                        NotifyLog($"   📋 图片完整路径:");
                        NotifyLog($"      [01] {waitingImagePath}");
                        NotifyLog($"   📂 输出文件夹: {aiSourceFolder}");
                        NotifyLog($"   🎯 输出文件路径: {outputPath}");
                        NotifyLog($"🚀 开始调用单个裁剪接口...");
                    });

                    // 调用裁剪接口（传递waitingImage文件夹中照片的绝对路径）
                    var result = await _apiService.CropImageAsync(waitingImagePath, outputPath);
                    
                    if (result?.Status == 200)
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"✅ 裁剪接口调用成功: 从waitingImage/{fileName} → {aiSourceFolder}"));
                    }
                    else
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ 裁剪接口调用失败: waitingImage/{fileName} - 状态码: {result?.Status}, 消息: {result?.Message}"));
                    }
                }
                catch (Exception ex)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 裁剪接口调用异常: waitingImage/{fileName} - {ex.Message}"));
                    
                    // 记录详细错误日志
                    _logHelper.LogError($"裁剪接口调用异常（waitingImage/{fileName}）: {ex}");
                }
            });
        }

        /// <summary>
        /// 立即单张裁剪 - 调用裁剪接口处理单张照片
        /// </summary>
        /// <param name="waitingImagePath">waitingImage文件夹中照片的绝对路径</param>
        /// <param name="fileName">文件名</param>
        private async Task CallSingleCropDirectly(string waitingImagePath, string fileName)
        {
            await Task.Run(async () =>
            {
                try
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"✂️ 开始单张裁剪: {fileName}"));

                    if (!File.Exists(waitingImagePath))
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ waitingImage文件不存在: {waitingImagePath}"));
                        return;
                    }

                    string aiSourceFolder = _state?.AppConfig?.AiSource ?? @"C:\AIImages\source";
                    if (!Directory.Exists(aiSourceFolder))
                    {
                        Directory.CreateDirectory(aiSourceFolder);
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"📁 创建AiSource文件夹: {aiSourceFolder}"));
                    }

                    string outputPath = Path.Combine(aiSourceFolder, fileName);

                    // **单张裁剪接口参数日志**
                    Application.Current?.Dispatcher.Invoke(() =>
                    {
                        NotifyLog($"📊 单张裁剪接口参数：");
                        NotifyLog($"   📋 输入文件: {waitingImagePath}");
                        NotifyLog($"   🎯 输出文件: {outputPath}");
                    });

                    // **调用单张裁剪接口 - 不加逗号**
                    var result = await _apiService.CropImageAsync(waitingImagePath, outputPath);
                    
                    if (result?.Status == 200)
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"✅ 单张裁剪成功: {fileName}"));
                    }
                    else
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ 单张裁剪失败: {fileName} - 状态码: {result?.Status}, 消息: {result?.Message}"));
                    }
                }
                catch (Exception ex)
                {
                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"❌ 单张裁剪异常: {fileName} - {ex.Message}"));
                    _logHelper.LogError($"单张裁剪异常（{fileName}）: {ex}");
                }
            });
        }

        /// <summary>
        /// 立即单张检测 - 防闪退版本，增强异常保护和详细日志
        /// </summary>
        /// <param name="imagePath">照片绝对路径</param>
        /// <param name="fileName">文件名</param>
        private async Task CallSingleDetectionDirectly(string imagePath, string fileName)
        {
            await Task.Run(async () =>
            {
                bool isSuccess = false;
                string errorDetail = "";
                string batchId = "";
                
                try
                {
                    // **检查输入参数**
                    if (string.IsNullOrEmpty(imagePath) || string.IsNullOrEmpty(fileName))
                    {
                        errorDetail = "检测参数无效（路径或文件名为空）";
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ {errorDetail}: 路径={imagePath}, 文件名={fileName}"));
                        _logHelper.LogError($"单张检测参数异常: {errorDetail}");
                        return;
                    }

                    // **检查文件是否存在**
                    if (!File.Exists(imagePath))
                    {
                        errorDetail = "检测文件不存在";
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ {errorDetail}: {imagePath}"));
                        _logHelper.LogError($"单张检测文件不存在: {imagePath}");
                        return;
                    }

                    Application.Current?.Dispatcher.Invoke(() =>
                        NotifyLog($"🔍 开始单张检测: {fileName} (路径: {imagePath})"));

                    batchId = $"SINGLE_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
                    _logHelper.LogInfo($"🎯 生成单张检测批次ID: {batchId}，文件: {fileName}");
                    
                    // **调用单张检测接口 - 增强保护**
                    try
                    {
                        if (_checkService == null)
                        {
                            errorDetail = "检测服务未初始化";
                            Application.Current?.Dispatcher.Invoke(() =>
                                NotifyLog($"❌ {errorDetail}"));
                            _logHelper.LogError($"单张检测服务异常: {errorDetail}");
                            return;
                        }

                        _logHelper.LogInfo($"📡 准备调用检测接口，批次: {batchId}，文件: {fileName}");
                        _checkService.SendToCheckSafely(new string[] { imagePath }, batchId);
                        
                        isSuccess = true;
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"✅ 单张检测已提交: {fileName} - 批次ID: {batchId}"));
                        _logHelper.LogInfo($"🎉 单张检测提交成功，批次: {batchId}，文件: {fileName}");
                    }
                    catch (Exception serviceEx)
                    {
                        errorDetail = $"检测服务调用异常: {serviceEx.Message}";
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"❌ {errorDetail}: {fileName}"));
                        _logHelper.LogError($"单张检测服务调用异常（{fileName}）: {serviceEx}");
                    }
                }
                catch (Exception ex)
                {
                    // **最外层异常保护，防止闪退**
                    errorDetail = $"单张检测系统异常: {ex.Message}";
                    
                    try
                    {
                        Application.Current?.Dispatcher.Invoke(() =>
                            NotifyLog($"💥 {errorDetail}: {fileName}"));
                    }
                    catch (Exception dispatcherEx)
                    {
                        // 如果连UI线程都有问题，只记录日志
                        _logHelper.LogError($"UI线程异常: {dispatcherEx.Message}");
                    }
                    
                    _logHelper.LogError($"单张检测严重异常（{fileName}）: {ex}");
                    _logHelper.LogError($"异常堆栈: {ex.StackTrace}");
                }
                
                // **最终结果日志**
                if (isSuccess)
                {
                    _logHelper.LogInfo($"🏆 单张检测完整成功，文件: {fileName}，批次: {batchId}");
                }
                else
                {
                    _logHelper.LogError($"🚫 单张检测最终失败，文件: {fileName}，原因: {errorDetail}");
                }
            });
        }

        /// <summary>
        /// 写入自动运行异常日志 - 专门用于诊断自动运行模式下的闪退问题
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <param name="context">异常发生的上下文</param>
        private void WriteAutoRunExceptionLog(Exception ex, string context)
        {
            try
            {
                string logDirectory = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "AutoRunCrashLogs");
                if (!System.IO.Directory.Exists(logDirectory))
                {
                    System.IO.Directory.CreateDirectory(logDirectory);
                }

                var logContent = new System.Text.StringBuilder();
                logContent.AppendLine("=== 自动运行模式异常日志 ===");
                logContent.AppendLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                logContent.AppendLine($"上下文: {context}");
                logContent.AppendLine($"当前运行状态: {IsRunning}");
                logContent.AppendLine($"当前步骤: {CurrentProcess?.ParamName ?? "null"}");
                logContent.AppendLine($"步骤类型: {CurrentProcess?.ProcessType}");
                logContent.AppendLine($"相机类型: {CurrentProcess?.CameraType}");
                logContent.AppendLine($"是否处理步骤切换中: {_isProcessingStepChange}");
                logContent.AppendLine($"检测计数: {CheckCount}");
                logContent.AppendLine($"照片队列长度: {PhotoQueueLength}");
                logContent.AppendLine();

                // 内存信息
                try
                {
                    var process = System.Diagnostics.Process.GetCurrentProcess();
                    logContent.AppendLine("=== 内存状态 ===");
                    logContent.AppendLine($"工作集: {process.WorkingSet64 / 1024 / 1024:F1} MB");
                    logContent.AppendLine($"私有内存: {process.PrivateMemorySize64 / 1024 / 1024:F1} MB");
                    logContent.AppendLine($"GC总内存: {GC.GetTotalMemory(false) / 1024 / 1024:F1} MB");
                    logContent.AppendLine($"GC第0代回收次数: {GC.CollectionCount(0)}");
                    logContent.AppendLine($"GC第1代回收次数: {GC.CollectionCount(1)}");
                    logContent.AppendLine($"GC第2代回收次数: {GC.CollectionCount(2)}");
                }
                catch { }

                logContent.AppendLine();
                logContent.AppendLine("=== 异常详情 ===");
                logContent.AppendLine($"异常类型: {ex.GetType().FullName}");
                logContent.AppendLine($"异常消息: {ex.Message}");
                logContent.AppendLine();
                logContent.AppendLine("=== 堆栈跟踪 ===");
                logContent.AppendLine(ex.StackTrace ?? "无堆栈跟踪");

                if (ex.InnerException != null)
                {
                    logContent.AppendLine();
                    logContent.AppendLine("=== 内部异常 ===");
                    logContent.AppendLine($"类型: {ex.InnerException.GetType().FullName}");
                    logContent.AppendLine($"消息: {ex.InnerException.Message}");
                    logContent.AppendLine($"堆栈: {ex.InnerException.StackTrace}");
                }

                logContent.AppendLine("========================");

                string logFileName = $"AutoRun_{DateTime.Now:yyyyMMdd_HHmmss}_{context.Replace(" ", "_")}_{Guid.NewGuid():N[..8]}.log";
                string logFilePath = System.IO.Path.Combine(logDirectory, logFileName);

                System.IO.File.WriteAllText(logFilePath, logContent.ToString(), System.Text.Encoding.UTF8);

                NotifyLog($"📝 自动运行异常日志已保存: {logFileName}");
            }
            catch (Exception logEx)
            {
                // 写入日志失败，至少记录到普通日志中
                _logHelper.LogError($"写入自动运行异常日志失败: {logEx.Message}");
            }
        }



        #endregion


    }
}