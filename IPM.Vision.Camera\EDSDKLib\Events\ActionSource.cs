﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Events
{
    public class ActionSource
    {
        private List<ActionListener> listeners = new List<ActionListener>();

        public void AddActionListener(ref ActionListener listener)
        {
            listeners.Add(listener);
        }

        public void RemoveActionListener(ref ActionListener listener)
        {
            listeners.Remove(listener);
        }

        public void ClearActionListener()
        {
            listeners.Clear();
        }

        public void FireEvent(ActionEvent.Command command, IntPtr arg)
        {
            ActionEvent e = new ActionEvent(command, arg);
            for (int i = 0; i < listeners.Count; i++)
            {
                listeners[i].ActionPerformed(e);
            }
        }
    }
}
