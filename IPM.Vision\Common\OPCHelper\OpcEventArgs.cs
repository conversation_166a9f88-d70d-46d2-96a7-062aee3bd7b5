﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common.OPCHelper
{
    public class OpcEventArgs: EventArgs
    {
        public string Message { get; set; }

        public OpcCode Code { get; set; }

        public string Value { get; set; }
        public bool IsError { get; set; } = false;
    }

    public enum OpcCode
    {
        Error = -1, // 0xFFFFFFFF
        None = 0,
        Working = 1,
        Waiting = 2,
        Stop = 3,
        McError = 4,
        Connected = 999, // 0x000003E7
    }
}
