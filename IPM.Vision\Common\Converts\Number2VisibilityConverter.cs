using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;

namespace IPM.Vision.Common.Converts
{
    public class Number2VisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            var result = Visibility.Collapsed;
            
            if (value != null)
            {
                // 尝试将值转换为数字
                if (value is int intValue)
                {
                    result = intValue > 0 ? Visibility.Visible : Visibility.Collapsed;
                }
                else if (double.TryParse(value.ToString(), out double doubleValue))
                {
                    result = doubleValue > 0 ? Visibility.Visible : Visibility.Collapsed;
                }
            }
            
            return result;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 