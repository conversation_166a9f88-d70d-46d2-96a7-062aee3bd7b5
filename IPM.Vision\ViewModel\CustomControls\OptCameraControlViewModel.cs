﻿using CommunityToolkit.Mvvm.Input;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.LEvents;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using IPM.Vision.Common;
using System.Configuration;

namespace IPM.Vision.ViewModel.CustomControls
{
    /// <summary>
    /// OPT相机控制ViewModel - 专注于UI状态管理和显示控制
    /// 队列处理逻辑已移至OptVisionService，避免重复设计
    /// </summary>
    public class OptCameraControlViewModel:ViewModelBase, IDisposable
    {
        private readonly OptVisionService _optVisionService;
        private readonly EquipmentService _equipmentService;
        private readonly ObservableGlobalState _globalState;
        private readonly NLogHelper _logger = new NLogHelper(); // 专用日志记录器

        // 从配置读取的旋转角度（度）
        private readonly int _rotSide1;
        private readonly int _rotSide2;
        private readonly int _rotSide3;
        private readonly int _rotSide4;

        // 图像显示变换参数
        private float _scaleAX = 1;  // 修复：初始值改为1，避免图像被缩放为0
        private float _scaleAY = 1;  // 修复：初始值改为1，避免图像被缩放为0
        private float _scaleBX = 1; // 修复：初始值改为1，避免图像被缩放为0
        private float _scaleBY = 1; // 修复：初始值改为1，避免图像被缩放为0
        private float _rotate = 0;
        private double _marginLeft = 0;
        private bool _isSetByUser = false;
        
        // 连接管理
        private const int MAX_RETRY_ATTEMPTS = 5;
        private int _connectionAttempts = 0;
        private System.Threading.CancellationTokenSource _cancellationTokenSource = new System.Threading.CancellationTokenSource();
        
        public OptCameraControlViewModel(OptVisionService optVisionService,IEquipmentService equipmentService,ObservableGlobalState globalState)
        {
            _optVisionService = optVisionService;
            _globalState = globalState;
            _equipmentService = (EquipmentService)equipmentService;
            
            // 订阅相机事件 - 确保画面更新事件正确订阅
            _optVisionService.NewCameraAddEvent += _optVisionService_NewCameraAddEvent;
            _optVisionService.UpdateGrappingBitmapSourceEvent += _optVisionService_UpdateGrappingBitmapSourceEvent;
            _optVisionService.CameraDisConnectedEvent += _optVisionService_CameraDisConnectedEvent;
            _equipmentService.McDataNotify += _equipmentService_McDataNotify;

            // ===== 读取旋转角度配置 =====
            _rotSide1 = GetRotationFromConfig("CameraRotation.Side1", 90);
            _rotSide2 = GetRotationFromConfig("CameraRotation.Side2", 0);
            _rotSide3 = GetRotationFromConfig("CameraRotation.Side3", 90);
            _rotSide4 = GetRotationFromConfig("CameraRotation.Side4", 180);

            // **关键修复：确保在初始化时立即同步服务状态到ViewModel**
            bool actualServiceStatus = _optVisionService.IsConnect;
            IsConnect = actualServiceStatus;
            UpdateConnectionStatus();
            
            // 强制触发属性变化通知，确保UI立即更新
            OnPropertyChanged(nameof(IsConnect));
            OnPropertyChanged(nameof(ConnectionStatus));
            OnPropertyChanged(nameof(RenderImageData));
        }

        #region 图像变换属性

        public float ScaleAX
        {
            get => _scaleAX;
            set => SetProperty(ref _scaleAX, value);
        }

        public float ScaleAY
        {
            get => _scaleAY;
            set => SetProperty(ref _scaleAY, value);
        }
        public float ScaleBX
        {
            get => _scaleBX;
            set => SetProperty(ref _scaleBX, value);
        }
        public float ScaleBY
        {
            get => _scaleBY;
            set => SetProperty(ref _scaleBY, value);
        }

        public float RotateAngle
        {
            get => _rotate;
            set => SetProperty(ref _rotate, value);
        }

        public double MarginLeft
        {
            get => _marginLeft;
            set => SetProperty(ref _marginLeft, value);
        }

        public bool IsSetByUser
        {
            get => _isSetByUser;
            set => SetProperty(ref _isSetByUser, value);
        }

        #endregion

        #region 设备角度监听

        /// <summary>
        /// 监听设备角度变化，自动调整图像显示变换
        /// </summary>
        private void _equipmentService_McDataNotify(int arg1, float arg2)
        {
            if (!IsSetByUser)
            {
                // 根据角度范围判断当前是哪个side，并设置对应的旋转角度
                // side1逆向转270度（等价于顺时针转90度）
                // side2正常正向（0度）
                // side3需要转90度
                // side4需要转180度

                if (arg2 >= 135 && arg2 < 225)
                {
                    // Side1
                    ApplyRotation(_rotSide1);
                }
                else if (arg2 >= 225 && arg2 < 315)
                {
                    // Side2
                    ApplyRotation(_rotSide2);
                }
                else if (arg2 >= 315 || arg2 < 45)
                {
                    // Side3
                    ApplyRotation(_rotSide3);
                }
                else if (arg2 >= 45 && arg2 < 135)
                {
                    // Side4
                    ApplyRotation(_rotSide4);
                }
                else
                {
                    // 默认：0°
                    ApplyRotation(0);
                }
            }
        }

        /// <summary>
        /// 根据配置角度应用旋转，保持缩放系数为1
        /// 统一逆时针旋转90度（旋转控件，不是旋转图像）
        /// </summary>
        private void ApplyRotation(int angle)
        {
            ScaleAX = 1;
            ScaleAY = 1;
            ScaleBX = 1;
            ScaleBY = 1;
            
            RotateAngle = -90;
        }

        /// <summary>
        /// 从App.config读取旋转角度，若解析失败则返回默认值
        /// </summary>
        private int GetRotationFromConfig(string key, int defaultVal)
        {
            try
            {
                var val = ConfigurationManager.AppSettings[key];
                if (int.TryParse(val, out int result))
                {
                    return result;
                }
            }
            catch { }
            return defaultVal;
        }

        #endregion

        #region 显示状态属性

        private BitmapSource _imageData;
        private bool _isConnect = false;
        private bool _isConnecting = false;
        private string _connectionStatus = "未连接";

        public BitmapSource RenderImageData
        {
            get => _imageData;
            set => SetProperty(ref _imageData, value);
        }

        public bool IsConnect
        {
            get => _isConnect;
            set => SetProperty(ref _isConnect, value);
        }
        
        public bool IsConnecting
        {
            get => _isConnecting;
            set => SetProperty(ref _isConnecting, value);
        }
        
        public string ConnectionStatus
        {
            get => _connectionStatus;
            set => SetProperty(ref _connectionStatus, value);
        }

        #endregion

        #region 相机事件处理

        /// <summary>
        /// 相机断开连接事件处理
        /// </summary>
        private void _optVisionService_CameraDisConnectedEvent()
        {
            IsConnect = false;
            UpdateConnectionStatus();
            
            // 清理画面数据
            Application.Current.Dispatcher.Invoke(() =>
            {
                RenderImageData = null;
            });
            
            // 自动尝试重新连接（如果不是用户主动断开）
            if (!IsConnecting)
            {
                Debug.WriteLine("相机连接断开，准备自动重连...");
                Task.Delay(2000).ContinueWith(t =>
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        TryConnect();
                    });
                });
            }
        }

        /// <summary>
        /// 处理画面更新事件 - 专注于UI显示更新
        /// **关键修复：增加画面来源验证，确保只显示OPT相机的画面**
        /// </summary>
        /// <param name="bitmap">从相机获取的位图数据</param>
        private void _optVisionService_UpdateGrappingBitmapSourceEvent(BitmapSource bitmap)
        {
            try
            {
                if (bitmap == null)
                {
                    Debug.WriteLine("[OPT画面更新] 收到空的位图数据");
                    return;
                }

                // **关键修复：验证画面来源，确保是OPT相机的画面**
                if (!_optVisionService.IsConnect)
                {
                    Debug.WriteLine("[OPT画面更新] ⚠️ OPT服务未连接，但收到画面数据，可能是错误的画面源");
                    return; // 如果OPT服务未连接，不应该收到画面数据
                }

                // **关键修复：增加画面数据验证**
                if (bitmap.PixelWidth <= 0 || bitmap.PixelHeight <= 0)
                {
                    Debug.WriteLine("[OPT画面更新] ⚠️ 收到无效尺寸的画面数据");
                    return;
                }

                Debug.WriteLine($"[OPT画面更新] ✅ 收到有效OPT画面: {bitmap.PixelWidth}x{bitmap.PixelHeight}");

                // 获取到图像表示相机已连接成功
                if (!IsConnect)
                {
                    IsConnect = true;
                    IsConnecting = false;
                    UpdateConnectionStatus();
                    _connectionAttempts = 0;
                    Debug.WriteLine("[OPT连接状态] 相机连接状态已更新为已连接");
                }

                // 直接更新UI显示
                UpdateRealTimeDisplay(bitmap);

                // 如果处于硬触发模式，记录收到帧的日志，便于排查遗漏
                if (_optVisionService.IsTriggerMode)
                {
                    _logger.LogInfo($"[OPT触发帧] 收到触发帧，尺寸: {bitmap.PixelWidth}x{bitmap.PixelHeight}, 时间: {DateTime.UtcNow:o}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[OPT画面更新异常] {ex.Message}");
            }
        }
        
        /// <summary>
        /// 更新实时显示 - 优化版本，解决内存泄漏问题
        /// </summary>
        /// <param name="bitmap">要显示的位图</param>
        private void UpdateRealTimeDisplay(BitmapSource bitmap)
        {
            try
            {
                if (bitmap == null) return;
                
                // **关键修复：确保位图已冻结，避免后续修改导致内存问题**
                if (!bitmap.IsFrozen)
                {
                    bitmap.Freeze();
                }
                
                // **关键修复：在更新前先清理旧的引用，帮助GC回收**
                BitmapSource oldBitmap = null;
                
                // 确保在UI线程中更新
                if (Application.Current.Dispatcher.CheckAccess())
                {
                    // 已在UI线程中
                    oldBitmap = RenderImageData;
                    RenderImageData = bitmap;
                    OnPropertyChanged(nameof(RenderImageData));
                    
                    if (_debugFrameCount % 90 == 0)
                    {
                        Debug.WriteLine($"[UI更新] 直接在UI线程更新RenderImageData - 第{_debugFrameCount}帧");
                    }
                }
                else
                {
                    // 需要切换到UI线程
                    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            oldBitmap = RenderImageData;
                            RenderImageData = bitmap;
                            OnPropertyChanged(nameof(RenderImageData));
                            
                            if (_debugFrameCount % 90 == 0)
                            {
                                Debug.WriteLine($"[UI更新] 通过Dispatcher更新RenderImageData - 第{_debugFrameCount}帧");
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"[UI更新内部异常] {ex.Message}");
                        }
                    }), System.Windows.Threading.DispatcherPriority.Background);
                }
                
                // **关键修复：显式清空旧位图引用，帮助GC回收大对象**
                if (oldBitmap != null)
                {
                    oldBitmap = null;
                    
                    // **关键修复：定期触发垃圾回收，清理累积的图像内存**
                    if (_debugFrameCount % 150 == 0)
                    {
                        GC.Collect();
                        GC.WaitForPendingFinalizers();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[UI更新异常] {ex.Message}");
            }
        }

        private int _debugFrameCount = 0;

        /// <summary>
        /// 相机添加事件处理
        /// </summary>
        private void _optVisionService_NewCameraAddEvent()
        {
            // 相机连接成功
            IsConnect = true;
            IsConnecting = false;
            UpdateConnectionStatus();
            
            // 重置连接尝试计数器
            _connectionAttempts = 0;
            
            Debug.WriteLine("相机添加事件触发 - 连接状态已更新");
        }

        #endregion

        #region 连接管理

        /// <summary>
        /// 连接相机
        /// </summary>
        private async void ConnectCamera()
        {
            if (IsConnecting) return;
            
            try
            {
                IsConnecting = true;
                UpdateConnectionStatus();
                
                // 调用服务层的连接方法
                await Task.Run(() => _optVisionService.RefreshCamera());
                
                // 连接状态会通过事件更新，不需要在这里设置
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"连接相机异常: {ex.Message}");
                IsConnecting = false;
                UpdateConnectionStatus();
            }
        }

        /// <summary>
        /// 尝试连接相机（带重试机制）
        /// </summary>
        private async void TryConnect()
        {
            if (IsConnecting) return;
            
            // 增加连接尝试计数
            _connectionAttempts++;
            
            try
            {
                IsConnecting = true;
                UpdateConnectionStatus();
                
                // 如果尝试次数超过最大值，延长等待时间
                int delayTime = _connectionAttempts > MAX_RETRY_ATTEMPTS ? 5000 : 1000;
                
                // 显示正在尝试连接的状态
                Debug.WriteLine($"正在尝试连接相机 (第{_connectionAttempts}次尝试)...");
                
                // 延迟一段时间再连接，避免频繁重试
                await Task.Delay(delayTime);
                
                // 调用服务层的连接方法
                await Task.Run(() => _optVisionService.RefreshCamera());
                
                // 连接状态会通过事件更新，不需要在这里设置
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"尝试连接相机异常: {ex.Message}");
                IsConnecting = false;
                UpdateConnectionStatus();
                
                // 如果连接失败且尝试次数不多，自动重试
                if (_connectionAttempts < MAX_RETRY_ATTEMPTS)
                {
                    int retryDelay = 2000 * _connectionAttempts; // 逐渐增加重试间隔
                    Debug.WriteLine($"将在 {retryDelay/1000} 秒后重试连接...");
                    
                    await Task.Delay(retryDelay);
                    TryConnect(); // 递归调用自身进行重试
                }
                else
                {
                    Debug.WriteLine($"已达到最大重试次数 ({MAX_RETRY_ATTEMPTS})，停止自动重连");
                }
            }
        }

        /// <summary>
        /// 更新连接状态文本
        /// </summary>
        private void UpdateConnectionStatus()
        {
            if (IsConnecting)
            {
                ConnectionStatus = "正在连接...";
            }
            else if (IsConnect)
            {
                ConnectionStatus = "已连接";
            }
            else
            {
                ConnectionStatus = "未连接";
            }
        }

        /// <summary>
        /// 同步服务层状态到ViewModel
        /// </summary>
        private void SyncDisplayStateFromService()
        {
            try
            {
                // 同步连接状态
                IsConnect = _optVisionService.IsConnect;
                
                // 更新状态文本
                UpdateConnectionStatus();
                
                // 强制触发属性变化通知
                OnPropertyChanged(nameof(IsConnect));
                OnPropertyChanged(nameof(ConnectionStatus));
                
                Debug.WriteLine($"同步显示状态 - IsConnect: {IsConnect}, ConnectionStatus: {ConnectionStatus}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"同步显示状态异常: {ex.Message}");
            }
        }

        #endregion

        #region 命令

        /// <summary>
        /// 卸载命令 - 优化版本，增强资源清理
        /// </summary>
        public IRelayCommand UnLoadCommand => new RelayCommand(() =>
        {
            try
            {
                Debug.WriteLine("=== UnLoadCommand 执行开始 ===");
                
                // 取消订阅事件
                _optVisionService.NewCameraAddEvent -= _optVisionService_NewCameraAddEvent;
                _optVisionService.UpdateGrappingBitmapSourceEvent -= _optVisionService_UpdateGrappingBitmapSourceEvent;
                _optVisionService.CameraDisConnectedEvent -= _optVisionService_CameraDisConnectedEvent;
                _equipmentService.McDataNotify -= _equipmentService_McDataNotify;
                
                // **关键修复：彻底清理UI资源**
                CleanupUIResources();
                
                // **关键修复：重置所有状态变量**
                _debugFrameCount = 0;
                IsConnect = false;
                IsConnecting = false;
                UpdateConnectionStatus();
                
                // **关键修复：强制垃圾回收，清理累积的图像内存**
                GC.Collect();
                GC.WaitForPendingFinalizers();
                
                Debug.WriteLine("=== UnLoadCommand 执行完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"卸载控件异常: {ex.Message}");
            }
        });

        /// <summary>
        /// 加载命令
        /// </summary>
        public IRelayCommand LoadCommand => new RelayCommand(async () => {
            try
            {
                Debug.WriteLine("=== LoadCommand 执行开始 ===");
                
                // 确保之前的资源已清理
                CleanupUIResources();
                
                // 重新订阅事件（以防万一）
                _optVisionService.NewCameraAddEvent -= _optVisionService_NewCameraAddEvent;
                _optVisionService.UpdateGrappingBitmapSourceEvent -= _optVisionService_UpdateGrappingBitmapSourceEvent;
                _optVisionService.CameraDisConnectedEvent -= _optVisionService_CameraDisConnectedEvent;
                
                _optVisionService.NewCameraAddEvent += _optVisionService_NewCameraAddEvent;
                _optVisionService.UpdateGrappingBitmapSourceEvent += _optVisionService_UpdateGrappingBitmapSourceEvent;
                _optVisionService.CameraDisConnectedEvent += _optVisionService_CameraDisConnectedEvent;
                
                Debug.WriteLine("事件重新订阅完成");
                
                // **关键修复：强制同步服务状态到ViewModel**
                SyncDisplayStateFromService();
                
                Debug.WriteLine($"当前状态同步完成 - IsConnect: {IsConnect}, Service.IsConnect: {_optVisionService.IsConnect}");
                
                // **关键修复：检查服务层连接状态，决定是否需要连接**
                if (!_optVisionService.IsConnect && !IsConnecting)
                {
                    Debug.WriteLine("服务层相机未连接，准备尝试连接...");
                    // 使用延迟连接，避免UI阻塞
                    await Task.Delay(500);
                    TryConnect();
                }
                else if (_optVisionService.IsConnect)
                {
                    Debug.WriteLine("服务层相机已连接，更新UI状态");
                    // 如果服务层已连接但UI状态未同步，强制更新
                    if (!IsConnect)
                    {
                        IsConnect = true;
                        IsConnecting = false;
                        UpdateConnectionStatus();
                    }
                }
                else
                {
                    Debug.WriteLine($"当前状态: IsConnect={IsConnect}, IsConnecting={IsConnecting}");
                }
                
                Debug.WriteLine("=== LoadCommand 执行完成 ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载控件异常: {ex.Message}");
                Debug.WriteLine($"异常堆栈: {ex.StackTrace}");
                IsConnecting = false;
                UpdateConnectionStatus();
            }
        });

        /// <summary>
        /// 重连命令
        /// </summary>
        public IRelayCommand ReconnectCommand => new RelayCommand(() =>
        {
            if (!IsConnecting)
            {
                TryConnect();
            }
        });

        #endregion

        #region 资源清理

        /// <summary>
        /// 清理UI资源 - 优化版本，更彻底的内存清理
        /// </summary>
        private void CleanupUIResources()
        {
            try
            {
                Debug.WriteLine("开始清理OptCameraControl UI资源...");
                
                // **关键修复：在UI线程中彻底清理图像资源**
                Application.Current.Dispatcher.Invoke(() =>
                {
                    // 先保存旧引用
                    var oldBitmap = RenderImageData;
                    
                    // 清空当前引用
                    RenderImageData = null;
                    
                    // 强制触发属性变化通知
                    OnPropertyChanged(nameof(RenderImageData));
                    
                    // 显式清空旧引用
                    if (oldBitmap != null)
                    {
                        oldBitmap = null;
                    }
                });
                
                // **关键修复：重置帧计数器**
                _debugFrameCount = 0;
                
                Debug.WriteLine("OptCameraControl UI资源清理完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"清理UI资源异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理所有资源
        /// </summary>
        private void CleanupResources()
        {
            try
            {
                // 取消订阅事件
                _optVisionService.NewCameraAddEvent -= _optVisionService_NewCameraAddEvent;
                _optVisionService.UpdateGrappingBitmapSourceEvent -= _optVisionService_UpdateGrappingBitmapSourceEvent;
                _optVisionService.CameraDisConnectedEvent -= _optVisionService_CameraDisConnectedEvent;
                _equipmentService.McDataNotify -= _equipmentService_McDataNotify;
                
                // 取消所有挂起的任务
                _cancellationTokenSource.Cancel();
                
                // 清理UI资源
                CleanupUIResources();
                
                // **关键修复：强制垃圾回收**
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"清理资源异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            CleanupResources();
            GC.SuppressFinalize(this);
        }

        #endregion
    }
}
