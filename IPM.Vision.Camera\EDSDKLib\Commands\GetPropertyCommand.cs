﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class GetPropertyCommand : BasicCommand
    {
        private uint _propertyID;
        public GetPropertyCommand(ref CanonCameraModel model, uint propertyID) : base(ref model)
        {
            _propertyID = propertyID;
        }
        public override bool Execute()
        {
            uint num = 0u;
            num = GetProperty(_propertyID);
            switch (num)
            {
                case 129u:
                    {
                        CameraEvent e2 = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e2);
                        return false;
                    }
                default:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e);
                        break;
                    }
                case 0u:
                    break;
            }

            return true;
        }

        private uint GetProperty(uint propertyID)
        {
            uint num = 0u;
            EDSDK.EdsDataType outDataType = EDSDK.EdsDataType.Unknown;
            int outSize = 0;
            if (propertyID == 65535)
            {
                if (num == 0)
                {
                    num = GetProperty(1078u);
                }

                if (num == 0)
                {
                    num = GetProperty(1025u);
                }

                if (num == 0)
                {
                    num = GetProperty(262u);
                }

                if (num == 0)
                {
                    num = GetProperty(1030u);
                }

                if (num == 0)
                {
                    num = GetProperty(1029u);
                }

                if (num == 0)
                {
                    num = GetProperty(1026u);
                }

                if (num == 0)
                {
                    num = GetProperty(1027u);
                }

                if (num == 0)
                {
                    num = GetProperty(1031u);
                }

                if (num == 0)
                {
                    num = GetProperty(256u);
                }

                if (num == 0)
                {
                    num = GetProperty(1034u);
                }

                if (num == 0)
                {
                    num = GetProperty(8u);
                }

                if (num == 0)
                {
                    num = GetProperty(16778261u);
                }

                if (num == 0)
                {
                    num = GetProperty(16778275u);
                }

                if (num == 0)
                {
                    num = GetProperty(276u);
                }

                if (num == 0)
                {
                    num = GetProperty(16778333u);
                }

                if (num == 0)
                {
                    num = GetProperty(16778502u);
                }

                if (num == 0)
                {
                    num = GetProperty(16778274u);
                }

                if (num == 0)
                {
                    num = GetProperty(16778296u);
                }

                if (num == 0)
                {
                    num = GetProperty(16778273u);
                }

                return num;
            }

            num = EDSDK.EdsGetPropertySize(_model.Camera, propertyID, 0, out outDataType, out outSize);
            if (num == 0)
            {
                switch (outDataType)
                {
                    case EDSDK.EdsDataType.UInt32:
                        {
                            num = EDSDK.EdsGetPropertyData(_model.Camera, propertyID, 0, out uint outPropertyData2);
                            if (num == 0)
                            {
                                _model.SetPropertyUInt32(propertyID, outPropertyData2);
                            }

                            break;
                        }
                    case EDSDK.EdsDataType.Int32:
                        {
                            num = EDSDK.EdsGetPropertyData(_model.Camera, propertyID, 0, out uint outPropertyData4);
                            if (num == 0)
                            {
                                _model.SetPropertyInt32(propertyID, outPropertyData4);
                            }

                            break;
                        }
                    case EDSDK.EdsDataType.String:
                        {
                            num = EDSDK.EdsGetPropertyData(_model.Camera, propertyID, 0, out string outPropertyData5);
                            if (num == 0)
                            {
                                _model.SetPropertyString(propertyID, outPropertyData5);
                            }

                            break;
                        }
                    case EDSDK.EdsDataType.FocusInfo:
                        {
                            num = EDSDK.EdsGetPropertyData(_model.Camera, propertyID, 0, out EDSDK.EdsFocusInfo outPropertyData3);
                            if (num == 0)
                            {
                                _model.SetPropertyFocusInfo(propertyID, outPropertyData3);
                            }

                            break;
                        }
                    case EDSDK.EdsDataType.ByteBlock:
                        {
                            byte[] outPropertyData = new byte[outSize];
                            num = EDSDK.EdsGetPropertyData(_model.Camera, propertyID, 0, out outPropertyData);
                            if (num == 0)
                            {
                                _model.SetPropertyByteBlock(propertyID, outPropertyData);
                            }

                            break;
                        }
                }
            }
            else if (outDataType == EDSDK.EdsDataType.FocusInfo || num == 80)
            {
                _model.FocusInfo = default(EDSDK.EdsFocusInfo);
                num = 0u;
            }

            if (num == 0)
            {
                CameraEvent e = new CameraEvent(CameraEvent.Type.PROPERTY_CHANGED, (IntPtr)propertyID);
                _model.NotifyObservers(e);
            }

            return num;
        }
    }
}
