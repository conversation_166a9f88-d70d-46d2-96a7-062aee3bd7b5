﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.Dialogs;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.Dialogs;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.Pages
{
    public class BarcodeConfigPageViewModel:ViewModelBase
    {
        private readonly BarcodeRuleService _barcodeRuleService;
        private ObservableCollection<ObservableBarcodeRuleModel> _dataList = new ObservableCollection<ObservableBarcodeRuleModel>();

        public BarcodeConfigPageViewModel(IBarcodeRuleService barcodeRuleService)
        {
            _barcodeRuleService = (BarcodeRuleService)barcodeRuleService;
        }

        public ObservableCollection<ObservableBarcodeRuleModel> DataList
        {
            get => _dataList;
            set => SetProperty(ref _dataList, value);
        }



        public IRelayCommand AddRuleCommand => new RelayCommand(async () =>
        {
            var result = await Dialog.Show<BarcodeRuleDoialog>()
             .Initialize<BarcodeRuleDialogViewModel>(vm =>
             {
                 vm.Title = "新增条码信息";
                 vm.RuleModel = new ObservableBarcodeRuleModel();
             })
             .GetResultAsync<bool>();
            if(result) RefreshEvent();
        });

        public IRelayCommand<ObservableBarcodeRuleModel> ModifyRuleCommand => new RelayCommand<ObservableBarcodeRuleModel>(async (model) =>
        {
            var result = await Dialog.Show<BarcodeRuleDoialog>()
             .Initialize<BarcodeRuleDialogViewModel>(vm =>
             {
                 vm.Title = "修改条码信息";
                 vm.RuleModel = model;
             })
             .GetResultAsync<bool>();
            if(result) RefreshEvent();
        });

        public IRelayCommand<ObservableBarcodeRuleModel> DeleteCommand => new RelayCommand<ObservableBarcodeRuleModel>(async (model) =>
        {
            var result = MessageBox.Show($"确认删除当前[{model.Rule}]条码规则?","警告",System.Windows.MessageBoxButton.YesNo,System.Windows.MessageBoxImage.Question);
            if(result == System.Windows.MessageBoxResult.Yes)
            {
                var tempData = model.MapTo<ObservableBarcodeRuleModel, BarcodeRuleModel>();
                var delResult = await _barcodeRuleService.Delete<string>(tempData.Id);
                if(delResult)  RefreshEvent();
            }
        });

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            RefreshEvent();
        });

        private async void RefreshEvent()
        {
            var temp = await _barcodeRuleService.getAll();
            if(temp != null) DataList = temp.MapTo<List<BarcodeRuleModel>,ObservableCollection<ObservableBarcodeRuleModel>>();
        }
    }
}
