﻿using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using HandyControl.Controls;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace IPM.Vision.ViewModel.Pages
{
    public class SettingPageViewModel:ViewModelBase,IDropTarget
    {
        private ObservableConfigModel _config;
        private NLogHelper _logger;
        private ObservableGlobalState _globalState;
        private string _currentName;

        public SettingPageViewModel(NLogHelper logger, ObservableGlobalState globalState)
        {
            _logger = logger;
            _globalState = globalState;
        }

        public List<EnumItem> PortItem { get => EnumHelper.GetEnumList<SerialPortEnum>(); }
        public List<EnumItem> ConnectItem { get => EnumHelper.GetEnumList<ConnectEnum>(); }
        public List<EnumItem> OpenItem { get => EnumHelper.GetEnumList<OpenEnum>(); }
        public List<EnumItem> CameraItem { get => EnumHelper.GetEnumList<CameraEnum>(); }
        public List<EnumItem> FolderItem { get => EnumHelper.GetEnumList<FolderEnum>(); }
        public List<EnumItem> FolderNameTypeItem { get => EnumHelper.GetEnumList<FolderNameTypeEnum>(); }

        public List<EnumItem> NameItem { get => EnumHelper.GetEnumList<NameEnum>(); }

        public ObservableConfigModel Config
        {
            get => _config;
            set => SetProperty(ref _config, value);
        }

        public IRelayCommand<string> ChoseFolderCommand => new RelayCommand<string>((param) =>
        {
            using (System.Windows.Forms.FolderBrowserDialog folderDialog = new System.Windows.Forms.FolderBrowserDialog())
            {
                // 设定对话框的说明
                folderDialog.Description = "请选择一个文件夹";

                // 打开对话框并检查用户是否点击了“确定”
                if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    switch (param)
                    {
                        case "0":
                            // 获取选择的文件夹路径
                            Config.SaveFolder = folderDialog.SelectedPath;
                            break;
                        case "1":
                            Config.AiSource = folderDialog.SelectedPath;
                            break;
                        case "2":
                            Config.AiResult = folderDialog.SelectedPath;
                            break;
                        case "3":
                            Config.MESOutputFolder = folderDialog.SelectedPath;
                            break;
                    }
                    
                }
            }
        });

        /// <summary>
        /// 重置MES输出文件夹为默认路径命令
        /// </summary>
        public IRelayCommand ResetMESOutputFolderCommand => new RelayCommand(() =>
        {
            // 设置为默认路径：exe同级目录下的mesdata文件夹
            Config.MESOutputFolder = FileHelper.ConcatFile(System.AppDomain.CurrentDomain.BaseDirectory, "mesdata");
            
            // 确保文件夹存在
            ValidateAndCreateMESFolder();
        });

        /// <summary>
        /// 验证MES输出文件夹命令，如果不存在则创建
        /// </summary>
        public IRelayCommand ValidateMESFolderCommand => new RelayCommand(() =>
        {
            ValidateAndCreateMESFolder();
        });

        /// <summary>
        /// 验证并创建MES输出文件夹
        /// </summary>
        private void ValidateAndCreateMESFolder()
        {
            try
            {
                if (string.IsNullOrEmpty(Config.MESOutputFolder))
                {
                    HandyControl.Controls.MessageBox.Warning("MES输出文件夹路径为空！", "警告");
                    return;
                }

                // 创建文件夹（如果不存在）
                FileHelper.CreateFolder(Config.MESOutputFolder);
                
                // 检查是否创建成功
                if (System.IO.Directory.Exists(Config.MESOutputFolder))
                {
                    HandyControl.Controls.MessageBox.Success($"MES输出文件夹验证成功！\n路径：{Config.MESOutputFolder}", "验证成功");
                    _logger.LogInfo($"MES输出文件夹验证成功：{Config.MESOutputFolder}");
                }
                else
                {
                    HandyControl.Controls.MessageBox.Error($"MES输出文件夹创建失败！\n路径：{Config.MESOutputFolder}", "创建失败");
                    _logger.LogError($"MES输出文件夹创建失败：{Config.MESOutputFolder}");
                }
            }
            catch (Exception ex)
            {
                HandyControl.Controls.MessageBox.Error($"验证MES输出文件夹时发生异常：{ex.Message}", "异常");
                _logger.LogError($"验证MES输出文件夹异常：{ex}");
            }
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            string configFile = FileHelper.ConcatFile(GlobalConstants.BASEDIC, GlobalConstants.CONFIGNAME);
            string content = FileHelper.ReadFile(configFile);
            var config = JsonConvert.DeserializeObject<ConfigModel>(content);
            if (config != null) Config = config.MapTo<ConfigModel, ObservableConfigModel>();
        });

        public IRelayCommand<string> AddRuleCommand => new RelayCommand<string>((param) => {
            switch (param)
            {
                case "folder":
                    if (Config.FolderNameList == null) Config.FolderNameList = new ObservableCollection<ObservableCustomNameModel>();
                    if (Config.FolderNameList.Count == 0)
                        Config.FolderNameList.Add(new ObservableCustomNameModel() { Index = 1 });
                    else
                    {
                        var index = Config.FolderNameList.Count;
                        index++;
                        Config.FolderNameList.Add(new ObservableCustomNameModel() { Index = index });
                    }
                    break;
                case "pictureName":
                    if (Config.PictureList == null) Config.PictureList = new ObservableCollection<ObservableCustomNameModel>();
                    if (Config.PictureList.Count == 0)
                        Config.PictureList.Add(new ObservableCustomNameModel() { Index = 1 });
                    else
                    {
                        var index = Config.PictureList.Count;
                        index++;
                        Config.PictureList.Add(new ObservableCustomNameModel() { Index = index });
                    }
                    break;
                case "watermark":
                    if (Config.WatermarkList == null) Config.WatermarkList = new ObservableCollection<ObservableCustomNameModel>();
                    if (Config.WatermarkList.Count == 0)
                        Config.WatermarkList.Add(new ObservableCustomNameModel() { Index = 1 });
                    else
                    {
                        var index = Config.WatermarkList.Count;
                        index++;
                        Config.WatermarkList.Add(new ObservableCustomNameModel() { Index = index });
                    }
                    break;
            }

        });

        public IRelayCommand UnLoadCommand => new RelayCommand(() =>
        {
            Task.Factory.StartNew(() =>
            {
                SaveConfig();
            });
        });

        private void SaveConfig()
        {
            string configFile = FileHelper.ConcatFile(GlobalConstants.BASEDIC, GlobalConstants.CONFIGNAME);
            var result = Config.MapTo<ObservableConfigModel, ConfigModel>();
            FileHelper.WriteFile(configFile, JsonConvert.SerializeObject(result,Formatting.Indented));
            _globalState.AppConfig = Config;
        }

        public IRelayCommand<ObservableCustomNameModel> DelWaterCommand => new RelayCommand<ObservableCustomNameModel>((data) => {
            if (data == null) return;
            Config.WatermarkList.Remove(data);
            int index = 1;
            foreach (var item in Config.WatermarkList)
            {
                item.Index = index;
                index++;
            }

        });

        public IRelayCommand<ObservableCustomNameModel> DelFolderCommand => new RelayCommand<ObservableCustomNameModel>((data) => {
            if (data == null) return;
            Config.FolderNameList.Remove(data);
            int index = 1;
            foreach (var item in Config.FolderNameList)
            {
                item.Index = index;
                index++;
            }
        });

        public IRelayCommand<ObservableCustomNameModel> DelFileNameCommand => new RelayCommand<ObservableCustomNameModel>((data) => {
            if (data == null) return;
            Config.PictureList.Remove(data);
            int index = 1;
            foreach (var item in Config.PictureList)
            {
                item.Index = index;
                index++;
            }

        });

        public IAsyncRelayCommand<RoutedEventArgs> ImageSelectedCommand => new AsyncRelayCommand<RoutedEventArgs>(ImageSelectedChangedEvent);
        private async Task ImageSelectedChangedEvent(RoutedEventArgs args)
        {
            var temp = args.Source as ImageSelector;
            try
            {
                if (temp != null)
                {
                    if (string.IsNullOrEmpty(temp.Uri.OriginalString)) return;
                    string fileName = FileHelper.GetFileName(temp.Uri.OriginalString);
                    FileHelper.CopyFile(temp.Uri.OriginalString, FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER, fileName));
                    Config.LogoPath = FileHelper.ConcatFile(GlobalConstants.CONFIGFOLDER, fileName);
                }
            }
            catch (Exception ex) 
            {
                HandyControl.Controls.MessageBox.Show(ex.ToString());

            }
            finally
            {
                await Task.CompletedTask;
            }
            
            
        }

        #region 拖拽处理

        public void DragEnter(IDropInfo dropInfo)
        {
            var source = dropInfo.Data as ObservableCustomNameModel;
            var target = dropInfo.TargetItem as ObservableCustomNameModel;
            if(source != null && target != null)
            {
                if (dropInfo.VisualTarget is DataGrid dataGrid)
                {
                    
                    // Determine which DataGrid is being targeted
                    if (dataGrid.Name == "FolderNameDataGrid") // Replace with actual name
                    {
                        _currentName = dataGrid.Name;
                        // Handle FolderNameList drag-and-drop logic
                        dropInfo.Effects = DragDropEffects.Move;
                        dropInfo.EffectText = "移动到当前位置！";
                        dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    }
                    else if (dataGrid.Name == "PictureNameDataGrid") // Replace with actual name
                    {

                        _currentName = dataGrid.Name;
                        // Handle PictureList drag-and-drop logic
                        dropInfo.Effects = DragDropEffects.Move;
                        dropInfo.EffectText = "移动到当前位置！";
                        dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    }
                    else if (dataGrid.Name == "WatermarkNameDataGrid") // Replace with actual name
                    {
                        _currentName = dataGrid.Name;
                        // Handle WatermarkList drag-and-drop logic
                        dropInfo.Effects = DragDropEffects.Move;
                        dropInfo.EffectText = "移动到当前位置！";
                        dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    }

                    if (dropInfo.DragInfo.VisualSource is DataGrid tdataGrid && (tdataGrid.Name != _currentName))
                    {
                        dropInfo.Effects = DragDropEffects.None;
                        dropInfo.EffectText = "不是当前目标列表！";
                        dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
                    }
                }
            }
        }

        public void DragOver(IDropInfo dropInfo)
        {
            DragEnter(dropInfo);
        }

        public void DragLeave(IDropInfo dropInfo)
        {
           
        }

        public void Drop(IDropInfo dropInfo)
        {
            if (dropInfo.DragInfo.VisualSource is DataGrid dataGrid && (dataGrid.Name == _currentName))
            {
                if (dropInfo.Data is ObservableCustomNameModel source && dropInfo.TargetItem is ObservableCustomNameModel targetItem)
                {

                    var targetList = GetTargetList(dropInfo);
                    int targetIndex = targetList.IndexOf(targetItem);
                    targetList.Remove(source);
                    targetList.Insert(targetIndex, source);

                    // 如有必要，更新列表中所有项的索引
                    UpdateIndices(targetList);
                }
            }
           
        }
        private ObservableCollection<ObservableCustomNameModel> GetTargetList(IDropInfo dropInfo)
        {
            if (dropInfo.VisualTarget is DataGrid dataGrid)
            {
                if (dataGrid.Name == "FolderNameDataGrid")
                {
                    return Config.FolderNameList; // Replace with your actual list for FolderName
                }
                else if (dataGrid.Name == "PictureNameDataGrid")
                {
                    return Config.PictureList; // Replace with your actual list for PictureName
                }
                else if (dataGrid.Name == "WatermarkNameDataGrid")
                {
                    return Config.WatermarkList; // Replace with your actual list for WatermarkName
                }
            }
            return null; // Handle this case appropriately
        }

        private void UpdateIndices(ObservableCollection<ObservableCustomNameModel> targetList)
        {
            for (int i = 0; i < targetList.Count; i++)
            {
                targetList[i].Index = i + 1; // Set Index based on the new order
            }
        }

        #endregion
    }
}
