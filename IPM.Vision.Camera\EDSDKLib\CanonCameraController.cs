﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Commands;
using IPM.Vision.Camera.EDSDKLib.Events;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib
{
    public class CameraController : ActionListener
    {
        private CanonCameraModel _model;

        private CommandProcessor _processor = new CommandProcessor();

        private static EDSDK.EdsPropertyEventHandler propertyEventHandler;

        private static EDSDK.EdsObjectEventHandler objectEventHandler;

        private static EDSDK.EdsStateEventHandler stateEventHandler;

        public int ClickType = 0;

        public CameraController(ref CanonCameraModel model)
        {
            _model = model;
            GCHandle value = GCHandle.Alloc(this);
            IntPtr inContext = GCHandle.ToIntPtr(value);
            propertyEventHandler = CameraEventListener.HandlePropertyEvent;
            objectEventHandler = CameraEventListener.HandleObjectEvent;
            stateEventHandler = CameraEventListener.HandleStateEvent;
            EDSDK.EdsSetPropertyEventHandler(_model.Camera, 256u, propertyEventHandler, inContext);
            EDSDK.EdsSetObjectEventHandler(_model.Camera, 512u, objectEventHandler, inContext);
            EDSDK.EdsSetCameraStateEventHandler(_model.Camera, 768u, stateEventHandler, inContext);
        }

        public void Run()
        {
            _processor.Start();
            _processor.PostCommand(new OpenSessionCommand(ref _model));
            _processor.PostCommand(new GetPropertyCommand(ref _model, 2u));
            _processor.PostCommand(new GetPropertyCommand(ref _model, 8u));
        }

        public CanonCameraModel GetModel()
        {
            return _model;
        }

        public override void ActionPerformed(ActionEvent e)
        {
            ActionEvent.Command actionCommand = e.GetActionCommand();
            switch (actionCommand)
            {
                case ActionEvent.Command.GET_PROPERTY:
                    {
                        uint propertyID = (uint)(int)e.GetArg();
                        _processor.PostCommand(new GetPropertyCommand(ref _model, propertyID));
                        break;
                    }
                case ActionEvent.Command.GET_PROPERTYDESC:
                    {
                        uint propertyID = (uint)(int)e.GetArg();
                        _processor.PostCommand(new GetPropertyDescCommand(ref _model, propertyID));
                        break;
                    }
                case ActionEvent.Command.TAKE_PICTURE:
                    _processor.PostCommand(new TakePictureCommand(ref _model));
                    break;
                case ActionEvent.Command.UI_LOCKED:
                    _processor.PostCommand(new UILockedCommand(ref _model, 0u));
                    break;
                case ActionEvent.Command.UI_UNLOCKED:
                    _processor.PostCommand(new UIUnlockedCommand(ref _model, 0u));
                    break;
                case ActionEvent.Command.PRESS_COMPLETELY:
                    _processor.PostCommand(new PressShutterCommand(ref _model, 3u));
                    break;
                case ActionEvent.Command.PRESS_HALFWAY:
                    _processor.PostCommand(new PressShutterCommand(ref _model, 1u));
                    break;
                case ActionEvent.Command.PRESS_OFF:
                    _processor.PostCommand(new PressShutterCommand(ref _model, 0u));
                    break;
                case ActionEvent.Command.SET_CAMERATIMER:
                    {
                        DateTime dateTime = DateTime.Now;
                        DateTime maxDate = _model.MaxDate;
                        DateTime minDate = _model.MinDate;
                        if (dateTime > maxDate)
                        {
                            dateTime = maxDate;
                        }

                        if (dateTime < minDate)
                        {
                            dateTime = minDate;
                        }

                        EDSDK.EdsTime edsTime = default(EDSDK.EdsTime);
                        edsTime.Year = dateTime.Year;
                        edsTime.Month = dateTime.Month;
                        edsTime.Day = dateTime.Day;
                        edsTime.Hour = dateTime.Hour;
                        edsTime.Minute = dateTime.Minute;
                        edsTime.Second = dateTime.Second;
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 6u, edsTime));
                        break;
                    }
                case ActionEvent.Command.START_EVF:
                    _model.isEvfEnable = true;
                    _processor.PostCommand(new StartEvfCommand(ref _model));
                    break;
                case ActionEvent.Command.END_EVF:
                    _model.isEvfEnable = false;
                    _processor.PostCommand(new EndEvfCommand(ref _model));
                    break;
                case ActionEvent.Command.DOWNLOAD_EVF:
                    _processor.PostCommand(new DownloadEvfCommand(ref _model));
                    break;
                case ActionEvent.Command.SET_AF_MODE:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1028u, num));
                        break;
                    }
                case ActionEvent.Command.SET_AE_MODE:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1078u, num));
                        break;
                    }
                case ActionEvent.Command.SET_AV:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1029u, num));
                        break;
                    }
                case ActionEvent.Command.SET_TV:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1030u, num));
                        break;
                    }
                case ActionEvent.Command.SET_ISO_SPEED:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1026u, num));
                        break;
                    }
                case ActionEvent.Command.SET_METERING_MODE:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1027u, num));
                        break;
                    }
                case ActionEvent.Command.SET_DRIVE_MODE:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1025u, num));
                        break;
                    }
                case ActionEvent.Command.SET_WHITE_BALANCE:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 262u, num));
                        break;
                    }
                case ActionEvent.Command.SET_EXPOSURE_COMPENSATION:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1031u, num));
                        break;
                    }
                case ActionEvent.Command.SET_IMAGEQUALITY:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 256u, num));
                        break;
                    }
                case ActionEvent.Command.SET_EVF_AFMODE:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1294u, num));
                        break;
                    }
                case ActionEvent.Command.SET_ZOOM:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1536u, num));
                        break;
                    }
                case ActionEvent.Command.SET_FLASH_MODE:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1537u, num));
                        break;
                    }
                case ActionEvent.Command.SET_MOVIEQUALITY:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 16778275u, num));
                        break;
                    }
                case ActionEvent.Command.SET_MOVIE_HFR:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 16778333u, num));
                        break;
                    }
                case ActionEvent.Command.SET_PICTURESTYLE:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 276u, num));
                        break;
                    }
                case ActionEvent.Command.SET_ASPECT:
                    {
                        uint num = (uint)(int)e.GetArg();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 16778289u, num));
                        break;
                    }
                case ActionEvent.Command.CLICK_WB:
                    ClickType = 1;
                    break;
                case ActionEvent.Command.CLICK_MOUSE_WB:
                    break;
                case ActionEvent.Command.CLICK_AF_POINT:
                    ClickType = 2;
                    break;
                case ActionEvent.Command.CLICK_MOUSE_AF:
                    break;
                case ActionEvent.Command.FOCUS_NEAR1:
                    _processor.PostCommand(new DriveLensCommand(ref _model, 1u));
                    break;
                case ActionEvent.Command.FOCUS_NEAR2:
                    _processor.PostCommand(new DriveLensCommand(ref _model, 2u));
                    break;
                case ActionEvent.Command.FOCUS_NEAR3:
                    _processor.PostCommand(new DriveLensCommand(ref _model, 3u));
                    break;
                case ActionEvent.Command.FOCUS_FAR1:
                    _processor.PostCommand(new DriveLensCommand(ref _model, 32769u));
                    break;
                case ActionEvent.Command.FOCUS_FAR2:
                    _processor.PostCommand(new DriveLensCommand(ref _model, 32770u));
                    break;
                case ActionEvent.Command.FOCUS_FAR3:
                    _processor.PostCommand(new DriveLensCommand(ref _model, 32771u));
                    break;
                case ActionEvent.Command.DOWNLOAD:
                    {
                        IntPtr inRef = e.GetArg();
                        _processor.PostCommand(new DownloadCommand(ref _model, ref inRef));
                        break;
                    }
                case ActionEvent.Command.CLOSING:
                    _processor.PostCommand(new CloseSessionCommand(ref _model));
                    _processor.Stop();
                    break;
                case ActionEvent.Command.SHUT_DOWN:
                    {
                        _processor.Stop();
                        CameraEvent e2 = new CameraEvent(CameraEvent.Type.SHUT_DOWN, IntPtr.Zero);
                        _model.NotifyObservers(e2);
                        break;
                    }
                case ActionEvent.Command.EVF_AF_ON:
                    if (_model.isEvfEnable)
                    {
                        _processor.PostCommand(new DoEvfAFCommand(ref _model, 1u));
                    }

                    break;
                case ActionEvent.Command.EVF_AF_OFF:
                    _processor.PostCommand(new DoEvfAFCommand(ref _model, 0u));
                    break;
                case ActionEvent.Command.ZOOM_FIT:
                    _processor.PostCommand(new SetPropertyCommand(ref _model, 1287u, 1));
                    break;
                case ActionEvent.Command.ZOOM_ZOOM:
                    _processor.PostCommand(new SetPropertyCommand(ref _model, 1287u, 5));
                    break;
                case ActionEvent.Command.POSITION_POINT:
                    {
                        EDSDK.EdsPoint zoomPosition = _model.GetZoomPosition();
                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1288u, zoomPosition));
                        break;
                    }
                case ActionEvent.Command.POSITION_UP:
                case ActionEvent.Command.POSITION_DOWN:
                    {
                        EDSDK.EdsPoint zoomPosition = _model.GetZoomPosition();
                        if (actionCommand == ActionEvent.Command.POSITION_UP)
                        {
                            zoomPosition.y -= 128;
                            if (zoomPosition.y < 0)
                            {
                                zoomPosition.y = 0;
                            }
                        }

                        if (actionCommand == ActionEvent.Command.POSITION_DOWN)
                        {
                            zoomPosition.y += 128;
                        }

                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1288u, zoomPosition));
                        break;
                    }
                case ActionEvent.Command.POSITION_LEFT:
                case ActionEvent.Command.POSITION_RIGHT:
                    {
                        EDSDK.EdsPoint zoomPosition = _model.GetZoomPosition();
                        if (actionCommand == ActionEvent.Command.POSITION_LEFT)
                        {
                            zoomPosition.x -= 128;
                            if (zoomPosition.x < 0)
                            {
                                zoomPosition.x = 0;
                            }
                        }

                        if (actionCommand == ActionEvent.Command.POSITION_RIGHT)
                        {
                            zoomPosition.x += 128;
                        }

                        _processor.PostCommand(new SetPropertyCommand(ref _model, 1288u, zoomPosition));
                        break;
                    }
                case ActionEvent.Command.REMOTESHOOTING_START:
                    _processor.PostCommand(new SetRemoteShootingCommand(ref _model, (uint)EDSDKLib.EDSDK.DcRemoteShootingMode.DcRemoteShootingModeStart));
                    break;
                case ActionEvent.Command.REMOTESHOOTING_STOP:
                    _processor.PostCommand(new SetRemoteShootingCommand(ref _model, (uint)EDSDKLib.EDSDK.DcRemoteShootingMode.DcRemoteShootingModeStop));
                    break;
                case ActionEvent.Command.PRESS_STILL:
                    break;
                case ActionEvent.Command.PRESS_MOVIE:
                    break;
                case ActionEvent.Command.REC_START:
                    break;
                case ActionEvent.Command.REC_END:
                    break;
                case ActionEvent.Command.MIRRORUP_ON:
                    break;
                case ActionEvent.Command.MIRRORUP_OFF:
                    break;
                case ActionEvent.Command.ROLLPITCH:
                    if (_model.isEvfEnable)
                    {
                        if (_model.RollPitch == 0)
                        {
                            _model.RollPitch = 1u;
                        }
                        else
                        {
                            _model.RollPitch = 0u;
                        }
                    }

                    break;
                case ActionEvent.Command.END_ROLLPITCH:
                    _model.RollPitch = 1u;
                    break;
                case ActionEvent.Command.SET_CAMERASETTING:
                case ActionEvent.Command.CLICK_FBS:
                    break;
                case ActionEvent.Command.GET_ALL_PROPERTY:
                    GetAllProperty();
                    break;
                case ActionEvent.Command.GET_ALL_PROPERTYDESC:
                    GetAllPropertyDESC();
                    break;
            }
        }

        private void GetAllProperty()
        {
            _processor.PostCommand(new GetPropertyCommand(ref _model, 65535));
        }

        private void GetAllPropertyDESC()
        {
            _processor.PostCommand(new GetPropertyDescCommand(ref _model, 65535));
        }

        public void DownloadFile(IntPtr volume)
        {
            _processor.PostCommand(new DownloadAllFilesCommand(ref _model, ref volume));
        }

        public void EDSDKRelease()
        {
            EDSDK.EdsRelease(_model.Camera);
            _model.Camera = IntPtr.Zero;
        }
    }
}
