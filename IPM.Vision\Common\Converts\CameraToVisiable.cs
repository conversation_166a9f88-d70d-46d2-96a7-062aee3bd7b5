﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Data;

namespace IPM.Vision.Common.Converts
{
    public class CameraToVisible : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is CameraEnum intValue)
            {
                // 如果 int 值为 0，返回 Visible，否则返回 Collapsed
                return intValue == CameraEnum.OPT ? Visibility.Visible : Visibility.Collapsed;
            }

            throw new InvalidOperationException("The value must be an int.");
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
