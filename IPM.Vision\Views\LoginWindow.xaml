﻿<Window
    x:Class="IPM.Vision.Views.LoginWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:IPM.Vision.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="LoginWindow"
    Width="800"
    Height="550"
    AllowsTransparency="True"
    Background="Transparent"
    DataContext="{Binding LoginWindowViewModel, Source={StaticResource Locator}}"
    Style="{x:Null}"
    WindowStartupLocation="CenterScreen"
    WindowStyle="None"
    mc:Ignorable="d">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadedCommand}" />
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border hc:WindowAttach.IsDragElement="True" Style="{StaticResource Body}">
        <Border.Background>
            <ImageBrush ImageSource="pack://application:,,,/Assets/login.jpg" Stretch="UniformToFill" />
        </Border.Background>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <StackPanel
                Grid.Row="0"
                Height="40"
                HorizontalAlignment="Right"
                Orientation="Horizontal">
                <Button
                    Width="40"
                    Height="40"
                    Command="{Binding CloseCommand}"
                    Content="&#xf00d;"
                    IsEnabled="{Binding IsLoaded}"
                    Template="{StaticResource CloseTemplate}" />
            </StackPanel>
            <Border
                Grid.Row="1"
                Width="350"
                Height="450"
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                Background="#90000000"
                CornerRadius="5">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="3*" />
                        <RowDefinition Height="1.5*" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <!--  登录logo  -->
                    <Border Grid.Row="0" Padding="10">
                        <Grid HorizontalAlignment="Center" VerticalAlignment="Center">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <hc:Gravatar
                                Grid.Row="0"
                                Width="140"
                                Height="140"
                                Id="logo"
                                Style="{StaticResource GravatarCircleImg}">
                                <Image Source="{Binding ObConfig.LogoPath, FallbackValue='pack://application:,,,/Assets/logo.png'}" />
                            </hc:Gravatar>
                            <TextBlock
                                Grid.Row="1"
                                Margin="0,10,0,0"
                                FontSize="20"
                                Foreground="White"
                                Text="CQFP封装器件引脚后润湿自动检查" />
                            <TextBlock
                                Grid.Row="2"
                                Margin="0,10,0,0"
                                FontSize="20"
                                Foreground="White"
                                Text="C-986"
                                TextAlignment="Center" />
                        </Grid>
                    </Border>
                    <Border
                        Grid.Row="1"
                        Padding="10,0,20,0"
                        VerticalAlignment="Center"
                        Visibility="{Binding IsLoaded, Converter={StaticResource BoolToVisibilityConverter}}">
                        <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <TextBlock
                                    Width="24"
                                    VerticalAlignment="Center"
                                    FontFamily="{StaticResource FontAwesome}"
                                    FontSize="22"
                                    Foreground="White"
                                    Text="&#xf007;"
                                    TextAlignment="Left" />
                                <hc:TextBox
                                    Grid.Column="1"
                                    hc:InfoElement.Placeholder="请输入账号..."
                                    Style="{StaticResource TextBoxExtend}"
                                    Text="{Binding LoginUser.Account, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                            </Grid>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <TextBlock
                                    Width="24"
                                    VerticalAlignment="Center"
                                    FontFamily="{StaticResource FontAwesome}"
                                    FontSize="22"
                                    Foreground="White"
                                    Text="&#xf023;"
                                    TextAlignment="Left" />
                                <hc:PasswordBox
                                    Grid.Column="1"
                                    hc:InfoElement.Placeholder="请输入账号..."
                                    IsSafeEnabled="False"
                                    ShowEyeButton="True"
                                    Style="{StaticResource PasswordBoxPlusBaseStyle}"
                                    UnsafePassword="{Binding LoginUser.Password, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                            </Grid>

                            <CheckBox
                                Content="记住账号和密码"
                                Foreground="White"
                                IsChecked="{Binding LoginUser.IsRemeber, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}" />
                        </hc:UniformSpacingPanel>
                    </Border>
                    <Border Grid.Row="2" Visibility="{Binding IsLoaded, Converter={StaticResource BoolToVisibilityConverter}}">
                        <Button
                            Width="200"
                            Height="32"
                            VerticalAlignment="Center"
                            Command="{Binding LoginCommand}"
                            Content="登 录"
                            FontSize="16"
                            FontWeight="Bold"
                            Style="{StaticResource ButtonPrimary}" />
                    </Border>
                    <Border Grid.Row="1" Visibility="{Binding IsLoaded, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=True}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*" />
                                <RowDefinition Height="Auto" />
                            </Grid.RowDefinitions>
                            <hc:LoadingCircle
                                Grid.Row="0"
                                Height="80"
                                Style="{StaticResource LoadingCircleLarge}" />
                            <hc:SimpleText
                                Grid.Row="1"
                                Margin="0,10,0,0"
                                HorizontalAlignment="Center"
                                FontSize="12"
                                Foreground="White"
                                Text="{Binding Message}" />
                        </Grid>
                    </Border>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
