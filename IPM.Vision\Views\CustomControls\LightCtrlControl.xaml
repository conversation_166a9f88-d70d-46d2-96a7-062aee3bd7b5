﻿<UserControl x:Class="IPM.Vision.Views.CustomControls.LightCtrlControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.CustomControls"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             DataContext="{Binding LightCtrlControlViewModel, Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
        <hc:EventTrigger EventName="Unloaded">
            <hc:EventToCommand Command="{Binding UnLoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Background="White" CornerRadius="5" Width="720">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Border Grid.Row="0" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <hc:UniformSpacingPanel Margin="0 0 10 0" Orientation="Vertical" Spacing="10"  Grid.Column="0">
                        <hc:TextBox
                            FontSize="14"
                            hc:InfoElement.Title="参数名称"
                            IsEnabled="False"
                            BorderBrush="Black"
                            Text="{Binding LightModel.ParamName,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"/>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源1"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LOneLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LOneLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="1" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源2"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LTwoLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LTwoLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="2" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源3"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LThreeLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LThreeLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="3" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>

                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="副相机光源"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LNineLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LNineLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="9" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                    </hc:UniformSpacingPanel>
                    <hc:UniformSpacingPanel Margin="10 0 0 0" Orientation="Vertical" Spacing="10" Grid.Column="1">
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源4"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LFourLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LFourLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="4" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源5"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LFiveLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LFiveLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="5" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源6"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LSixLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LSixLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="6" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源7"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LSevenLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LSevenLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="7" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                        <hc:NumericUpDown
                            FontSize="14"
                            hc:InfoElement.Title="光源8"
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LEightLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            Style="{StaticResource NumericUpDownExtend}"/>
                        <hc:PreviewSlider
                            Minimum="0"
                            Maximum="255"
                            Value="{Binding LightModel.LEightLuminance,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                            IsSnapToTickEnabled="True"
                            VerticalAlignment="Bottom">
                            <hc:Interaction.Triggers>
                                <hc:EventTrigger EventName="LostMouseCapture">
                                    <hc:EventToCommand Command="{Binding MainLightChangedCommand}" CommandParameter="8" />
                                </hc:EventTrigger>
                            </hc:Interaction.Triggers>
                            <hc:PreviewSlider.PreviewContent>
                                <Label Style="{StaticResource LabelPrimary}" Content="{Binding Path=(hc:PreviewSlider.PreviewPosition),RelativeSource={RelativeSource Self}}" ContentStringFormat="#0.00"/>
                            </hc:PreviewSlider.PreviewContent>
                        </hc:PreviewSlider>
                    </hc:UniformSpacingPanel>
                </Grid>
            </Border>
            <Border Grid.Row="1" Margin="10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Button Grid.Column="1"  Height="35" Width="220" Style="{StaticResource ButtonPrimary}" Content="保存" Command="{Binding SaveCommand}"/>
                </Grid>
            </Border>
        </Grid>
    </Border>
</UserControl>
