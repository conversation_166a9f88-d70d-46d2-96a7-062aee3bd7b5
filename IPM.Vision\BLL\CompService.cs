﻿using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common.DBCommon;
using IPM.Vision.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.BLL
{
    public class CompService : BaseService<CompPointModel>, ICompService
    {
        public CompService(IBaseRepository<CompPointModel> baseRepository) : base(baseRepository)
        {
        }
    }
}
