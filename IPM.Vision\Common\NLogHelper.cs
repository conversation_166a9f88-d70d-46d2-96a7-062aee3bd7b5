﻿using NLog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common
{
    public class NLogHelper
    {
        private static readonly Logger logger = LogManager.GetCurrentClassLogger();

        public void LogError(Exception ex) => NLogHelper.logger.Error<Exception>(ex);

        public void LogError(string message) => NLogHelper.logger.Error(message);

        public void LogError(string message, Exception ex) => NLogHelper.logger.Error(string.Format("{0}:{1}", (object)message, (object)ex));

        public void LogInfo(string message) => NLogHelper.logger.Info(message);

    }
}
