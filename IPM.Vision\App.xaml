﻿<Application x:Class="IPM.Vision.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:locator="clr-namespace:IPM.Vision.ViewModel"
             xmlns:convert="clr-namespace:IPM.Vision.Comm.Converts"
             xmlns:local="clr-namespace:IPM.Vision"
             xmlns:converts="clr-namespace:IPM.Vision.Common.Converts"
             StartupUri="./Views/LoginWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/SkinDefault.xaml"/>
                <ResourceDictionary Source="pack://application:,,,/HandyControl;component/Themes/Theme.xaml"/>
                <ResourceDictionary Source="./Styles/FontResource.xaml"/>
                <ResourceDictionary Source="./Styles/BaseStyle.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <locator:ViewModelLocator x:Key="Locator"/>
            <converts:BoolToVisibilityConvert x:Key="BoolToVisibilityConverter"/>
            <converts:ListToStringConverter x:Key="List2StringConverter"/>
            <converts:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
            <converts:EnumToVisibleConvert x:Key="EnumToVisibleConvert"/>
            <converts:UriToBitmapConverter x:Key="UriToBitmapConverter"/>
            <converts:NullToStringConvert x:Key="NullToStringConvert"/>
            <converts:HalfConverter x:Key="HalfConverter"/>
            <converts:CenterXConverter x:Key="CenterXConverter"/>
            <converts:CenterYConverter x:Key="CenterYConverter"/>
            <converts:Half2Converter x:Key="Half2Converter"/>
            <converts:CheckValueConvert x:Key="CheckValueConvert"/>
            <converts:StatusTextConvert x:Key="StatusTextConvert"/>
            <converts:ManualReviewResultConverter x:Key="ManualReviewResultConverter"/>
            <converts:Number2VisibilityConverter x:Key="Number2VisibilityConverter"/>
            <convert:BooleanInvertConverter x:Key="BooleanInvertConverter"/>
        </ResourceDictionary>

    </Application.Resources>
</Application>
