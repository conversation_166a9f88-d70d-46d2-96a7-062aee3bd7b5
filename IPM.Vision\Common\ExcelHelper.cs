﻿using CsvHelper.Configuration;
using CsvHelper;
using DocumentFormat.OpenXml.Spreadsheet;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common
{
    public class ExcelHelper
    {
        /// <summary>
        /// 读取 CSV 文件并返回数据列表。
        /// </summary>
        /// <param name="filePath">CSV 文件路径</param>
        /// <returns>返回一个包含每行数据的字典列表</returns>
        public List<List<string>> ReadCsvFile(string filePath)
        {
            try
            {
                var dataList = new List<List<string>>();

                // **修复：尝试多种编码方式读取CSV文件**
                Encoding[] encodings = {
                    Encoding.UTF8,
                    Encoding.GetEncoding("GB2312"),
                    Encoding.GetEncoding("GBK"),
                    Encoding.Default
                };

                foreach (var encoding in encodings)
                {
                    try
                    {
                        using (var reader = new StreamReader(filePath, encoding))
                        using (var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
                        {
                            HasHeaderRecord = false // 没有表头，第一行作为数据行
                        }))
                        {
                            dataList.Clear(); // 清空之前的数据

                            // 读取每一行数据
                            while (csv.Read())
                            {
                                var rowData = new List<string>();

                                for (int i = 0; i < csv.ColumnCount; i++)
                                {
                                    var cellValue = csv.GetField(i); // 获取当前行的字段值
                                    rowData.Add(cellValue ?? ""); // 将列名和对应的值添加到字典
                                }

                                dataList.Add(rowData); // 将字典添加到结果列表
                            }
                        }

                        // **验证读取结果：检查是否有乱码**
                        if (dataList.Count > 0 && dataList[0].Count > 0)
                        {
                            string firstCell = dataList[0][0];
                            // 如果第一个单元格包含明显的乱码字符，尝试下一种编码
                            if (ContainsGarbledText(firstCell))
                            {
                                continue; // 尝试下一种编码
                            }
                        }

                        // 读取成功，返回结果
                        return dataList;
                    }
                    catch (Exception encodingEx)
                    {
                        // 当前编码失败，尝试下一种
                        continue;
                    }
                }

                // 所有编码都失败，返回null
                return null;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// 检查文本是否包含乱码字符
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <returns>是否包含乱码</returns>
        private bool ContainsGarbledText(string text)
        {
            if (string.IsNullOrEmpty(text))
                return false;

            // 检查是否包含常见的乱码字符
            char[] garbledChars = { '�', '?', 'ͼ', 'ǩ', 'Ŷ' };

            foreach (char c in text)
            {
                if (Array.IndexOf(garbledChars, c) >= 0)
                    return true;

                // 检查是否为控制字符（除了常见的空白字符）
                if (char.IsControl(c) && c != '\t' && c != '\r' && c != '\n')
                    return true;
            }

            return false;
        }
    }
}
