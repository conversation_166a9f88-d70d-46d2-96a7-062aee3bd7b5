﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class TakePictureCommand : BasicCommand
    {
        public TakePictureCommand(ref CanonCameraModel model)
            : base(ref model)
        {
        }

        public override bool Execute()
        {
            uint num = EDSDK.EdsSendCommand(_model.Camera, 4u, 3);
            num = EDSDK.EdsSendCommand(_model.Camera, 4u, 0);
            switch (num)
            {
                case 129u:
                    {
                        CameraEvent e2 = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e2);
                        return true;
                    }
                default:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e);
                        return true;
                    }
                case 0u:
                    _model.canDownloadImage = true;
                    return true;
            }
        }
    }
}
