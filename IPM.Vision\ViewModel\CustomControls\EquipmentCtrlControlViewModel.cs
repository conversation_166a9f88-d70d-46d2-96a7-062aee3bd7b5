﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;

namespace IPM.Vision.ViewModel.CustomControls
{
    public class EquipmentCtrlControlViewModel : ViewModelBase
    {
        private readonly EquipmentService _equipmentService;
        private ObservableEquipmentModel _equipmentModel;
        private ObservableGlobalState _globalState;
        public EquipmentCtrlControlViewModel(IEquipmentService equipmentService, ObservableGlobalState globalState)
        {
            _equipmentService = (EquipmentService)equipmentService;
            _globalState = globalState;
            _globalState.ProcessChangedEvent += _globalState_ProcessChangedEvent;
        }

        private void _globalState_ProcessChangedEvent(ObservableProcessModel obj)
        {
            if (obj != null && obj.EquipmentPara != null)
            {
                EquipmentModel = obj.EquipmentPara;
            }
            else
            {
                EquipmentModel = new ObservableEquipmentModel();
            }
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            if (_globalState.CurrentProcessModel != null && _globalState.CurrentProcessModel.EquipmentPara != null)
            {
                EquipmentModel = _globalState.CurrentProcessModel.EquipmentPara;
            }
            else
            {
                EquipmentModel = new ObservableEquipmentModel();
            }
        });

        public IRelayCommand UnLoadCommand => new RelayCommand(() =>
        {
            _globalState.ProcessChangedEvent -= _globalState_ProcessChangedEvent;
        });

        public PopupWindow ShowControl { get; set; }

        public ObservableEquipmentModel EquipmentModel
        {
            get => _equipmentModel;
            set => SetProperty(ref _equipmentModel, value);
        }

        public IRelayCommand SaveCommand => new RelayCommand(async () =>
        {
            if (!string.IsNullOrEmpty(EquipmentModel.Id))
            {
                await _equipmentService.Update(EquipmentModel.MapTo<ObservableEquipmentModel, EquipmentModel>());
            }
            if (ShowControl != null) ShowControl.Close();
        });
        public IRelayCommand<string> ResetSingleCommand => new RelayCommand<string>((args) =>
        {
            switch (args)
            {
                case "XY":
                    _equipmentService.ReSetXY();
                    EquipmentModel.X = 0;
                    EquipmentModel.Y = 0;
                    break;
                case "Z":
                    _equipmentService.ReSetZ();
                    EquipmentModel.Z = 0;
                    break;
                case "O":
                    _equipmentService.ReSetO();
                    EquipmentModel.O = 0;
                    break;
                case "R":
                    _equipmentService.ReSetR();
                    EquipmentModel.R = 0;
                    break;
                case "T":
                    _equipmentService.ReSetT();
                    EquipmentModel.T = 0;
                    break;
            }
        });

        public IRelayCommand<string> MoveCommand => new RelayCommand<string>((args) =>
        {
            MoveEvent(args);
        });

        private void MoveEvent(string args)
        {
            switch (args)
            {
                case "XY":
                    //_equipmentService.SetXY(EquipmentModel.X, EquipmentModel.Y);
                    _equipmentService.WriteXYArray(new float[] { EquipmentModel.X }, new float[] { EquipmentModel.Y }, 1);
                    break;
                case "Z":
                    _equipmentService.SetZAsync(EquipmentModel.Z);
                    break;
                case "O":
                    _equipmentService.SetO(EquipmentModel.O);
                    break;
                case "R":
                    _equipmentService.SetR(EquipmentModel.R);
                    break;
                case "T":
                    _equipmentService.SetT(EquipmentModel.T);
                    break;
            }
        }

        public IRelayCommand<KeyEventArgs> XYKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("XY");
            }
        });
        public IRelayCommand<KeyEventArgs> ZKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("Z");
            }
        });
        public IRelayCommand<KeyEventArgs> RKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("R");
            }
        });
        public IRelayCommand<KeyEventArgs> TKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("T");
            }
        });
        public IRelayCommand<KeyEventArgs> OKeyDownCommand => new RelayCommand<KeyEventArgs>((e) =>
        {
            if (e.Key == Key.Enter)
            {
                MoveEvent("O");
            }
        });

    }
}
