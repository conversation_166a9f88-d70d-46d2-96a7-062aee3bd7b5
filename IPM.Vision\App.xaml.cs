﻿using IPM.Vision.Views;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;

namespace IPM.Vision
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private bool _isShutdownInitiated = false;
        
        protected override void OnStartup(StartupEventArgs e)
        {
            // 注册应用程序退出事件
            this.Exit += App_Exit;
            this.SessionEnding += App_SessionEnding;
            
            // 注册未处理异常事件
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            
            base.OnStartup(e);
        }
        
        private void App_Exit(object sender, ExitEventArgs e)
        {
            if (!_isShutdownInitiated)
            {
                _isShutdownInitiated = true;
                try
                {
                    // 获取主窗口的ViewModel进行资源清理
                    if (MainWindow?.DataContext is IPM.Vision.ViewModel.MainWindowViewModel mainViewModel)
                    {
                        // 检查并清理裁剪任务状态
                        try
                        {
                            if (mainViewModel.GlobalState?.HasCroppingTasks == true)
                            {
                                System.Diagnostics.Debug.WriteLine($"应用程序退出时仍有 {mainViewModel.GlobalState.WaitingCropCount} 个裁剪任务未完成");
                                // 清除状态，避免下次启动时出现问题
                                mainViewModel.GlobalState.ClearCroppingStatus();
                            }
                        }
                        catch (Exception cleanupEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"清理裁剪状态异常: {cleanupEx.Message}");
                        }
                        
                        // 强制清理相机资源
                        System.Threading.Tasks.Task.Run(() =>
                        {
                            try
                            {
                                // 这里可以调用清理方法，但由于是私有方法，我们通过其他方式
                                System.Threading.Thread.Sleep(1000); // 给一点时间让资源释放
                            }
                            catch { }
                        }).Wait(3000); // 最多等待3秒
                    }
                }
                catch { }
            }
        }
        
        private void App_SessionEnding(object sender, SessionEndingCancelEventArgs e)
        {
            // 系统会话结束时的清理
            if (!_isShutdownInitiated)
            {
                _isShutdownInitiated = true;
                try
                {
                    // 快速清理关键资源
                    System.Threading.Tasks.Task.Run(() =>
                    {
                        System.Threading.Thread.Sleep(500);
                    }).Wait(1000);
                }
                catch { }
            }
        }
        
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                // **增强异常处理：记录详细的崩溃信息到文件**
                string crashInfo = GenerateCrashReport(e.ExceptionObject as Exception, "UnhandledException", e.IsTerminating);
                WriteCrashLogToFile(crashInfo);
                
                System.Diagnostics.Debug.WriteLine($"未处理的异常: {e.ExceptionObject}");
                
                if (!_isShutdownInitiated && e.IsTerminating)
                {
                    _isShutdownInitiated = true;
                    // 在程序即将崩溃时进行紧急清理
                    EmergencyCleanup();
                }
            }
            catch { }
        }
        
        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                // **增强异常处理：记录详细的UI异常信息**
                string crashInfo = GenerateCrashReport(e.Exception, "DispatcherUnhandledException", false);
                WriteCrashLogToFile(crashInfo);
                
                System.Diagnostics.Debug.WriteLine($"UI线程未处理异常: {e.Exception}");
                
                // **增强异常分类处理**
                bool isCriticalException = IsCriticalException(e.Exception);
                
                if (!isCriticalException)
                {
                    e.Handled = true; // 非关键异常，标记为已处理
                    System.Diagnostics.Debug.WriteLine("非关键异常已处理，程序继续运行");
                }
                else
                {
                    // 关键异常，进行紧急清理
                    if (!_isShutdownInitiated)
                    {
                        _isShutdownInitiated = true;
                        EmergencyCleanup();
                    }
                    System.Diagnostics.Debug.WriteLine("关键异常检测到，执行紧急清理");
                }
            }
            catch { }
        }
        
        /// <summary>
        /// 生成详细的崩溃报告
        /// </summary>
        private string GenerateCrashReport(Exception ex, string exceptionType, bool isTerminating)
        {
            try
            {
                var report = new System.Text.StringBuilder();
                report.AppendLine("=== IPM.Vision 应用程序崩溃报告 ===");
                report.AppendLine($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                report.AppendLine($"异常类型: {exceptionType}");
                report.AppendLine($"是否终止: {isTerminating}");
                report.AppendLine();

                // 系统信息
                report.AppendLine("=== 系统信息 ===");
                report.AppendLine($"操作系统: {Environment.OSVersion}");
                report.AppendLine($"CLR版本: {Environment.Version}");
                report.AppendLine($"工作集内存: {Environment.WorkingSet / 1024 / 1024:F1} MB");
                
                // 内存使用情况
                try
                {
                    var process = System.Diagnostics.Process.GetCurrentProcess();
                    report.AppendLine($"私有内存: {process.PrivateMemorySize64 / 1024 / 1024:F1} MB");
                    report.AppendLine($"虚拟内存: {process.VirtualMemorySize64 / 1024 / 1024:F1} MB");
                    report.AppendLine($"GC总内存: {GC.GetTotalMemory(false) / 1024 / 1024:F1} MB");
                }
                catch { }
                
                report.AppendLine();

                // 异常详情
                if (ex != null)
                {
                    report.AppendLine("=== 异常详情 ===");
                    report.AppendLine($"异常类型: {ex.GetType().FullName}");
                    report.AppendLine($"异常消息: {ex.Message}");
                    report.AppendLine();
                    report.AppendLine("=== 堆栈跟踪 ===");
                    report.AppendLine(ex.StackTrace ?? "无堆栈跟踪");
                    
                    // 内部异常
                    if (ex.InnerException != null)
                    {
                        report.AppendLine();
                        report.AppendLine("=== 内部异常 ===");
                        report.AppendLine($"类型: {ex.InnerException.GetType().FullName}");
                        report.AppendLine($"消息: {ex.InnerException.Message}");
                        report.AppendLine($"堆栈: {ex.InnerException.StackTrace}");
                    }
                }

                report.AppendLine();
                report.AppendLine("=== 线程信息 ===");
                report.AppendLine($"当前线程ID: {System.Threading.Thread.CurrentThread.ManagedThreadId}");
                report.AppendLine($"是否UI线程: {Current.Dispatcher.CheckAccess()}");
                
                report.AppendLine("========================");
                return report.ToString();
            }
            catch
            {
                return $"生成崩溃报告失败 - {DateTime.Now:yyyy-MM-dd HH:mm:ss} - {ex?.Message ?? "未知异常"}";
            }
        }

        /// <summary>
        /// 将崩溃日志写入文件
        /// </summary>
        private void WriteCrashLogToFile(string crashInfo)
        {
            try
            {
                string logDirectory = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "CrashLogs");
                if (!System.IO.Directory.Exists(logDirectory))
                {
                    System.IO.Directory.CreateDirectory(logDirectory);
                }

                string logFileName = $"Crash_{DateTime.Now:yyyyMMdd_HHmmss}_{Guid.NewGuid():N}.log";
                string logFilePath = System.IO.Path.Combine(logDirectory, logFileName);

                System.IO.File.WriteAllText(logFilePath, crashInfo, System.Text.Encoding.UTF8);
                
                // 限制崩溃日志文件数量（保留最近20个）
                CleanupOldCrashLogs(logDirectory, 20);
            }
            catch
            {
                // 写入崩溃日志失败，至少确保Debug输出
                System.Diagnostics.Debug.WriteLine("写入崩溃日志失败");
            }
        }

        /// <summary>
        /// 清理旧的崩溃日志文件
        /// </summary>
        private void CleanupOldCrashLogs(string logDirectory, int maxLogFiles)
        {
            try
            {
                var logFiles = System.IO.Directory.GetFiles(logDirectory, "Crash_*.log")
                    .Select(f => new System.IO.FileInfo(f))
                    .OrderByDescending(f => f.CreationTime)
                    .ToArray();

                if (logFiles.Length > maxLogFiles)
                {
                    for (int i = maxLogFiles; i < logFiles.Length; i++)
                    {
                        try
                        {
                            logFiles[i].Delete();
                        }
                        catch { }
                    }
                }
            }
            catch { }
        }

        /// <summary>
        /// 判断是否为关键异常
        /// </summary>
        private bool IsCriticalException(Exception ex)
        {
            if (ex == null) return false;

            // 关键异常类型
            if (ex is OutOfMemoryException ||
                ex is StackOverflowException ||
                ex is AccessViolationException ||
                ex is System.Runtime.InteropServices.SEHException)
            {
                return true;
            }

            // 关键异常消息模式
            string message = ex.Message?.ToLower() ?? "";
            if (message.Contains("相机") || message.Contains("camera") ||
                message.Contains("设备") || message.Contains("device") ||
                message.Contains("memory") || message.Contains("内存") ||
                message.Contains("数据库") || message.Contains("database") ||
                message.Contains("sqlite"))
            {
                return true;
            }

            return false;
        }

        private void EmergencyCleanup()
        {
            try
            {
                // 紧急情况下的简单清理
                System.Threading.Tasks.Task.Run(() =>
                {
                    try
                    {
                        // 尝试强制结束可能的相机进程
                        var processes = System.Diagnostics.Process.GetProcessesByName("IPM.Vision");
                        foreach (var process in processes)
                        {
                            if (process.Id != System.Diagnostics.Process.GetCurrentProcess().Id)
                            {
                                try
                                {
                                    process.Kill();
                                }
                                catch { }
                            }
                        }
                    }
                    catch { }
                }).Wait(1000);
            }
            catch { }
        }
    }
}
