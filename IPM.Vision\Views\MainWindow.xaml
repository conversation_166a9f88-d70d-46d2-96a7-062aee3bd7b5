﻿<Window x:Class="IPM.Vision.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:IPM.Vision.Views"
        WindowStyle="None"
        xmlns:layout="clr-namespace:IPM.Vision.Layout"
        xmlns:hc="https://handyorg.github.io/handycontrol"
        DataContext="{Binding MainWindowViewModel ,Source={StaticResource Locator}}"
        mc:Ignorable="d"
        Style="{x:Null}"
        AllowsTransparency="True"
        Width="1590"
        Height="1080"
        x:Name="mainWindow"
        Background="Transparent">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <hc:DialogContainer hc:Dialog.Token="main">
        <Border hc:WindowAttach.IsDragElement="True"  Style="{StaticResource Body}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="45"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Border Grid.Row="0" BorderBrush="Gray" BorderThickness="0 0 0 0.3">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <hc:SimpleText Grid.Column="0" FontSize="18" FontWeight="Bold"  Text="CQFP封装器件引脚后润湿自动检查设备 C-986" VerticalAlignment="Center" Margin="15 0 0 0"/>
                        <Grid Grid.Column="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <hc:UniformSpacingPanel HorizontalAlignment="Right" Grid.Column="0" Orientation="Horizontal" Spacing="5" VerticalAlignment="Center">
                                <hc:SimpleText FontWeight="Bold" Text="工位:" FontSize="16" VerticalAlignment="Center"/>
                                <hc:SimpleText Text="{Binding ObConfigModel.WorkStation,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}" FontSize="16" VerticalAlignment="Center"/>
                            </hc:UniformSpacingPanel>
                            <hc:UniformSpacingPanel Grid.Column="1" Margin="20,0,0,0" Orientation="Horizontal" Spacing="5" VerticalAlignment="Center">
                                <hc:SimpleText FontWeight="Bold" Text="用户名:" FontSize="16" VerticalAlignment="Center"/>
                                <hc:SimpleText Text="{Binding UserInfoModel.UserName}" FontSize="16" VerticalAlignment="Center"/>
                            </hc:UniformSpacingPanel>
                            <Button  Width="40" Height="40" Grid.Column="2"  VerticalAlignment="Center" Margin="30 0 5 0" Style="{StaticResource ButtonPrimary}" Command="{Binding MinCommand}" CommandParameter="{Binding ElementName=mainWindow}">
                                <Button.Content>
                                    <TextBlock Text="&#xf2d1;" FontFamily="{StaticResource FontAwesome}"/>
                                </Button.Content>
                            </Button>
                            <Button  Width="40" Height="40" Grid.Column="3"  VerticalAlignment="Center" Margin="5 0 5 0" Style="{StaticResource ButtonDanger}" Command="{Binding CloseCommand}">
                                <Button.Content>
                                    <TextBlock Text="&#xf00d;" FontFamily="{StaticResource FontAwesome}"/>
                                </Button.Content>
                            </Button>
                        </Grid>
                    </Grid>
                </Border>

                <Border Grid.Row="1" Background="#f5f5f5">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="95"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Border Grid.Column="0"
                                BorderThickness="0.3"
                                Margin="10 10 0 10"
                                Background="White"
                                BorderBrush="Gray"
                                CornerRadius="5">
                            <Border.Effect>
                                <DropShadowEffect BlurRadius="5" Direction="330" ShadowDepth="0.5" Color="Gray" />
                            </Border.Effect>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="80"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Border Grid.Row="0">
                                    <Image Height="60" Width="60" VerticalAlignment="Center" HorizontalAlignment="Center" Source="{Binding ObConfigModel.LogoPath, FallbackValue='pack://application:,,,/Assets/logo.png'}"/>
                                </Border>
                                <Border Grid.Row="1" >
                                    <layout:MenuControl  IsEnabled="{Binding IsRunning,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged,Converter={StaticResource Boolean2BooleanReConverter}}"/>
                                </Border>
                            </Grid>
                        </Border>
                        <Border Grid.Column="1"  Padding="5">
                            <Frame Content="{Binding CurrentPage}" NavigationUIVisibility="Hidden" BorderBrush="Transparent"/>
                        </Border>
                    </Grid>
                </Border>

            </Grid>
        </Border>
    </hc:DialogContainer>

</Window>
