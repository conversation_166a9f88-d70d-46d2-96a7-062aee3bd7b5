﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class PressShutterCommand : BasicCommand
    {
        private uint _status;

        public PressShutterCommand(ref CanonCameraModel model, uint status)
            : base(ref model)
        {
            _status = status;
        }

        public override bool Execute()
        {
            uint num = EDSDK.EdsSendCommand(_model.Camera, 4u, (int)_status);
            switch (num)
            {
                case 129u:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e);
                        return true;
                    }
                default:
                    {
                        CameraEvent e2 = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e2);
                        return true;
                    }
                case 0u:
                    if (_status == 3 && _model.DriveMode != 1 && _model.DriveMode != 4)
                    {
                        _model.canDownloadImage = true;
                    }

                    return true;
            }
        }
    }
}
