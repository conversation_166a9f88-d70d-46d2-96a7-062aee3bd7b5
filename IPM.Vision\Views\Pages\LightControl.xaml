﻿<UserControl x:Class="IPM.Vision.Views.Pages.LightControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.Pages"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             DataContext="{Binding LightControlViewModel, Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}"/>
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border Padding="10">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid Grid.Row="0">
                <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10" Grid.Row="0">
                    <hc:TextBox
                        Height="35"
                        hc:TitleElement.Title="参数名称"
                        Width="380"
                        Text="{Binding ParamName,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                        hc:TitleElement.TitlePlacement="Left">
                        <hc:Interaction.Triggers>
                            <hc:EventTrigger EventName="TextChanged">
                                <hc:EventToCommand Command="{Binding SearchCommand}"/>
                            </hc:EventTrigger>
                        </hc:Interaction.Triggers>
                    </hc:TextBox>
                </hc:UniformSpacingPanel>
                <Button Grid.Row="1"
                        Margin="0 10 0 5"
                        Content="&#xf067; 新增参数"
                        FontFamily="{StaticResource FontAwesome}"
                        Height="35"
                        Width="100"
                        FontSize="14"
                        HorizontalAlignment="Right"
                        Command="{Binding AddParamCommand}"
                        Style="{StaticResource ButtonPrimary}"/>
            </Grid>
            <DataGrid Margin="0 10 0 0"
                      Grid.Row="1"
                      Style="{StaticResource DataGridBaseStyle}"
                      AutoGenerateColumns="False"
                      hc:DataGridAttach.ShowSelectAllButton="True"
                      ItemsSource="{Binding DataList}"
                      RowHeaderWidth="60"
                      HeadersVisibility="All"
                      hc:DataGridAttach.CanUnselectAllWithBlankArea="True"
                      hc:DataGridAttach.ShowRowNumber="True"
                      hc:DataGridAttach.ApplyDefaultStyle="True"
                      RowDetailsVisibilityMode="Collapsed"
                      CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                      ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}">
                <DataGrid.Resources>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Visibility" Value="Visible"/>
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding DataList.Count}" Value="0">
                                <Setter Property="Visibility" Value="Collapsed"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.Resources>
                <DataGrid.Columns>
                    <DataGridTextColumn Header="参数名称" Width="*" Binding="{Binding ParamName}"/>
                    <DataGridTextColumn Header="1号" Width="*"  Binding="{Binding LOneLuminance}"/>
                    <DataGridTextColumn Header="2号" Width="*"  Binding="{Binding LTwoLuminance}"/>
                    <DataGridTextColumn Header="3号" Width="*"  Binding="{Binding LThreeLuminance}"/>
                    <DataGridTextColumn Header="4号" Width="*" Binding="{Binding LFourLuminance}"/>
                    <DataGridTextColumn Header="5号" Width="*" Binding="{Binding LFiveLuminance}"/>
                    <DataGridTextColumn Header="6号" Width="*" Binding="{Binding LSixLuminance}"/>
                    <DataGridTextColumn Header="7号" Width="*" Binding="{Binding LSevenLuminance}"/>
                    <DataGridTextColumn Header="8号" Width="*" Binding="{Binding LEightLuminance}"/>
                    <DataGridTextColumn Header="环形光源" Width="*" Binding="{Binding LNineLuminance}"/>
                    <DataGridTextColumn Header="创建人" Width="*" Binding="{Binding Operator}"/>
                    <DataGridTextColumn Header="创建时间" Width="200" Binding="{Binding CreateTime}"/>
                    <DataGridTemplateColumn IsReadOnly="True" Width="120" CanUserResize="False" Header="操作">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="&#xf044;"
                                            FontFamily="{StaticResource FontAwesome}"
                                            Style="{StaticResource ButtonInfo}"
                                            Command="{Binding DataContext.ModifyParamCommand,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=DataGrid}}"
                                            CommandParameter="{Binding}"/>
                                    <Button Content="&#xf1f8;"
                                            Style="{StaticResource ButtonDanger}"
                                            Margin="5 0 0 0"
                                            FontFamily="{StaticResource FontAwesome}"
                                            Command="{Binding DataContext.DeleteParamCommand,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=DataGrid}}"
                                            CommandParameter="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
            <hc:Dialog Grid.Row="2" Visibility="{Binding IsLoading,Converter={StaticResource BoolToVisibilityConverter}}">
                <hc:Dialog.Content>
                    <hc:UniformSpacingPanel Orientation="Vertical" Spacing="10" HorizontalAlignment="Center">
                        <hc:LoadingCircle Style="{StaticResource LoadingCircleLarge}"/>
                        <TextBlock Text="查询中..." HorizontalAlignment="Center" FontSize="22" TextAlignment="Center" Foreground="{StaticResource PrimaryBrush}"/>
                    </hc:UniformSpacingPanel>
                </hc:Dialog.Content>
            </hc:Dialog>
            <TextBlock Grid.Row="2"
                       FontSize="22"
                       Visibility="{Binding DataList.Count,Converter={StaticResource NullToVisibilityConverter}}"
                       Text="暂未查询到数据"
                       HorizontalAlignment="Center"
                       VerticalAlignment="Center"/>
        </Grid>
    </Border>
</UserControl>
