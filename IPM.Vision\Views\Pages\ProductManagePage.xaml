﻿<Page
    x:Class="IPM.Vision.Views.Pages.ProductManagePage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:IPM.Vision.Views.Pages"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="450"
    d:DesignWidth="800"
    DataContext="{Binding ProductManagePageViewModel, Source={StaticResource Locator}}"
    mc:Ignorable="d">
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}" />
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>
            <hc:UniformSpacingPanel
                Grid.Row="0"
                Orientation="Horizontal"
                Spacing="10">
                <hc:TextBox
                    Width="380"
                    Height="35"
                    hc:InfoElement.ShowClearButton="True"
                    hc:TitleElement.Title="产品编号"
                    hc:TitleElement.TitlePlacement="Left"
                    FontSize="16"
                    Text="{Binding ProductNumber, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}">
                    <hc:Interaction.Triggers>
                        <hc:EventTrigger EventName="TextChanged">
                            <hc:EventToCommand Command="{Binding SearchCommand}" />
                        </hc:EventTrigger>
                    </hc:Interaction.Triggers>
                </hc:TextBox>
            </hc:UniformSpacingPanel>
            <Border
                Grid.Row="1"
                Padding="10"
                Style="{StaticResource Body}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Button
                        Grid.Row="0"
                        Width="100"
                        Height="35"
                        HorizontalAlignment="Left"
                        Command="{Binding AddProductCommand}"
                        Content="&#xf067; 新增产品"
                        FontFamily="{StaticResource FontAwesome}"
                        FontSize="14"
                        Style="{StaticResource ButtonPrimary}" />
                    <DataGrid
                        Grid.Row="1"
                        Margin="0,10,0,0"
                        hc:DataGridAttach.ApplyDefaultStyle="True"
                        hc:DataGridAttach.CanUnselectAllWithBlankArea="True"
                        hc:DataGridAttach.ShowRowNumber="True"
                        AutoGenerateColumns="False"
                        CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                        ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}"
                        HeadersVisibility="All"
                        ItemsSource="{Binding ProductModelList}"
                        RowDetailsVisibilityMode="Collapsed"
                        RowHeaderWidth="60"
                        Style="{StaticResource DataGridBaseStyle}">
                        <DataGrid.Resources>
                            <Style TargetType="DataGridRow">
                                <Setter Property="Visibility" Value="Visible" />
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding ProductModelList.Count}" Value="0">
                                        <Setter Property="Visibility" Value="Collapsed" />
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.Resources>
                        <DataGrid.Columns>
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding ProductNumber}"
                                Header="产品编号"
                                IsReadOnly="True" />
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding ProductName}"
                                Header="产品名称"
                                IsReadOnly="True" />
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding Operator}"
                                Header="创建人"
                                IsReadOnly="True" />
                            <DataGridTextColumn
                                Width="*"
                                Binding="{Binding CreateTime}"
                                Header="创建时间"
                                IsReadOnly="True" />
                            <DataGridTemplateColumn
                                Width="220"
                                CanUserResize="False"
                                Header="操作"
                                IsReadOnly="True">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button
                                                Command="{Binding DataContext.ModifyProductCommand, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                Content="&#xf044;"
                                                FontFamily="{StaticResource FontAwesome}"
                                                Style="{StaticResource ButtonInfo}" />
                                            <Button
                                                Margin="5,0,0,0"
                                                Command="{Binding DataContext.DeleteCommand, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"
                                                Content="&#xf1f8;"
                                                FontFamily="{StaticResource FontAwesome}"
                                                Style="{StaticResource ButtonDanger}" />
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                    <hc:Dialog
                        Grid.Row="2"
                        Margin="0,10,0,0"
                        Visibility="Collapsed">
                        <hc:Dialog.Content>
                            <hc:UniformSpacingPanel
                                HorizontalAlignment="Center"
                                Orientation="Vertical"
                                Spacing="10">
                                <hc:LoadingCircle Style="{StaticResource LoadingCircleLarge}" />
                                <TextBlock
                                    HorizontalAlignment="Center"
                                    FontSize="22"
                                    Foreground="{StaticResource PrimaryBrush}"
                                    Text="查询中..."
                                    TextAlignment="Center" />
                            </hc:UniformSpacingPanel>
                        </hc:Dialog.Content>
                    </hc:Dialog>
                    <TextBlock
                        Grid.Row="2"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        FontSize="22"
                        Text="暂未查询到数据"
                        Visibility="Collapsed" />
                </Grid>

            </Border>

        </Grid>
    </Border>
</Page>
