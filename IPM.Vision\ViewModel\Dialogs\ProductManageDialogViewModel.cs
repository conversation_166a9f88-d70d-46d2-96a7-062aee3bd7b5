using CommunityToolkit.Mvvm.Input;
using GongSolutions.Wpf.DragDrop;
using HandyControl.Controls;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Common;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Views.Dialogs;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Forms;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class ProductManageDialogViewModel : ViewModelBase, IDialogResultable<bool>,GongSolutions.Wpf.DragDrop.IDropTarget
    {
        private bool _result = false;
        private string _title = string.Empty;
        private bool _isInsert = false;
        private readonly ProductParaService _productService;
        private readonly ProcessParaService _processParaService;
        private ObservableProductModel _productModel;
        private readonly MainCameraParaService _mainCameraService;
        private readonly EquipmentService _equipmentService;
        private readonly LightParamService _lightParamService;
        private readonly CompService _compService;
        private ObservableGlobalState _globalState;
        private readonly NLogHelper _logger;
        private readonly CompInfoService _compInfoService;
        private ObservableCollection<ObservableProcessModel> _processModels = new ObservableCollection<ObservableProcessModel>();
        private ObservableCollection<ObservableEquipmentModel> _equipmentModels = new ObservableCollection<ObservableEquipmentModel>();
        private ObservableCollection<ObservableCompPointModel> _compPointsModels;

        public List<EnumItem> CameraTypes
        {
            get
            {
                List<EnumItem> result = new List<EnumItem>();
                var temp = EnumHelper.GetEnumList<CameraTypeEnum>();
                var checkTemp = _globalState.AppConfig.HaveMarkCamera == OpenEnum.OPEN;
                if (!checkTemp) return temp.Where(item => (CameraTypeEnum)item.Value != CameraTypeEnum.Child).ToList();
                else return EnumHelper.GetEnumList<CameraTypeEnum>();
            }
        }
        public List<EnumItem> ProcessTypes
        {
            get
            {
                return EnumHelper.GetEnumList<ProcessTypeEnum>();
            }
        }
        public List<EnumItem> TakePictureTypes { get => EnumHelper.GetEnumList<PictureType>(); }
        public List<EnumItem> PictureLayoutTypes { get => EnumHelper.GetEnumList<PictureLayoutEnum>(); }

        public List<EnumItem> PinTypeEnumTypes { get => EnumHelper.GetEnumList<PinTypeEnum>(); }

        public ObservableCollection<ObservableEquipmentModel> ObservableEquipmentModels
        {
            get => _equipmentModels;
            set => SetProperty(ref _equipmentModels, value);
        }

        public ObservableCollection<ObservableCompPointModel> CompPointsModels
        {
            get => _compPointsModels;
            set => SetProperty(ref _compPointsModels, value);
        }


        public bool Result
        {
            get => _result;
            set => SetProperty(ref _result, value);
        }

        public ObservableProductModel ProductModel
        {
            get => _productModel;
            set => SetProperty(ref _productModel, value);
        }

        public ObservableCollection<ObservableProcessModel> ProcessModels
        {
            get => _processModels;
            set => SetProperty(ref _processModels, value);
        }

        public bool IsInsert
        {
            get => _isInsert;
            set => SetProperty(ref _isInsert, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }



        

        public ProductManageDialogViewModel(IProductParaService productParaService, 
            ObservableGlobalState globalState, 
            IMainCameraParaService mainCameraService, 
            IEquipmentService equipmentService, 
            ILightParamService lightParamService,
            IProcessParaService processParaService,
            ICompService compService,
            ICompInfoService compInfoService,
            NLogHelper logger)
        {
            _productService = (ProductParaService)productParaService;
            _globalState = globalState;
            _mainCameraService = (MainCameraParaService)mainCameraService;
            _equipmentService = (EquipmentService)equipmentService;
            _lightParamService = (LightParamService)lightParamService;
            _logger = logger;
            _compInfoService = (CompInfoService)compInfoService;
            _compService = (CompService)compService;
            _processParaService = (ProcessParaService)processParaService;
            
        }

        public Action CloseAction { get; set; }

        public IRelayCommand AddProcessCommand => new RelayCommand(async () =>
        {
            var temp = await Dialog.Show<ProcessManageDialog>()
                .Initialize<ProcessManageDialogViewModel>(vm =>
                {
                    vm.Title = "新增步骤";
                    vm.CurrentProcessModel = new ObservableProcessModel();
                    vm.IsInsert = true;
                    vm.SaveSucceEvent += Vm_SaveSucceEvent;
                }).GetResultAsync<bool>();
        });

        public IRelayCommand AddNewProcessAndOtherCommand => new RelayCommand(async () =>
        {
            int number = 1;
            if (ProcessModels.Count > 0)
            {
                number = ProcessModels.Max(item => item.ProcessNumber);
                number++;
            }
            var temp = await Dialog.Show<ProcessManageDialog>()
                .Initialize<ProcessManageDialogViewModel>(vm =>
                {
                    vm.Title = "新增步骤";
                    vm.CurrentProcessModel = new ObservableProcessModel()
                    {
                        ProcessNumber = number,
                        ParamName = $"Side{number}",
                        CameraType = CameraTypeEnum.Main,
                        TakeType = PictureType.SINGLE,
                        PictureLayout = PictureLayoutEnum.ONE,
                        ProcessType = ProcessTypeEnum.TAKEPICTURE,
                    };
                    vm.IsInsert = true;
                    vm.SaveSucceEvent += Vm_SaveSucceEvent;
                }).GetResultAsync<bool>();

        });

        private void Vm_SaveSucceEvent(ObservableProcessModel obj)
        {
            RefreshEquipment();
            ProcessModels.Add(obj);
        }
        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            Result = false;
            CloseAction?.Invoke();
        });

        public IRelayCommand<ObservableProcessModel> ModifyProcessCommand => new RelayCommand<ObservableProcessModel>(async (model) =>
        {
            var temp = await Dialog.Show<ProcessManageDialog>()
                .Initialize<ProcessManageDialogViewModel>(vm =>
                {
                    vm.Title = "修改步骤";
                    vm.CurrentProcessModel = model;
                    vm.IsInsert = false;
                }).GetResultAsync<bool>();

            if (temp)
            {
                RefreshEquipment();
            }
            
        });

        public IRelayCommand SaveCommand => new RelayCommand(async () =>
        {
            try
            {
                
                if (IsInsert)
                {
                    Result = await InsertProduct();
                }
                else
                {
                   Result = await ModifyProduct();
                }
                SaveComp();
                if (Result) CloseAction?.Invoke();
            }
            catch (Exception ex) { 
            
                Result = false;
                _logger.LogError(ex);
            }
            
        });
        public IRelayCommand LoadCommand => new RelayCommand(async () =>
        {
            
            LoadComp();
            if (!IsInsert) {
                var _processTempData = await _processParaService.getByWhere(item => item.Id != null && ProductModel.ProcessIds.ToList().Contains(item.Id), isOrderBy:true,src=>src.ProcessNumber);
                ProcessModels = _processTempData.MapTo<List<ProcessModel>,ObservableCollection<ObservableProcessModel>>();
            }
            RefreshEquipment();

        });


        private async Task<bool> InsertProduct()
        {
            if (string.IsNullOrWhiteSpace(ProductModel.ProductNumber))
            {
                HandyControl.Controls.MessageBox.Error("产品编号不能为空！");
                return false;
            }
            var checkTemp= await _productService.getIsAny(item=>item.ProductNumber == ProductModel.ProductNumber);

            if (checkTemp)
            {
                HandyControl.Controls.MessageBox.Error("产品编号已存在，无法重复创建！");
                return false;
            }
            List<string> processIds = new List<string>();
            if (ProcessModels != null && ProcessModels.Count > 0)
            {
                var processTemp = ProcessModels.MapTo<ObservableCollection<ObservableProcessModel>, List<ProcessModel>>();
                foreach (var item in processTemp)
                {
                   if(string.IsNullOrEmpty(item.Id))item.Id = Guid.NewGuid().ToString();
                }
                var s = await _processParaService.AddRange(processTemp);
                if (!s)
                {
                    _logger.LogInfo("步骤过程插入失败");
                    return false;
                }
                processIds = processTemp.Select(item => item.Id).ToList();
                ProductModel.ProcessIds = new ObservableCollection<string>(processIds);
            }
            if (string.IsNullOrEmpty(ProductModel.Id))
            {
                ProductModel.Id = Guid.NewGuid().ToString();
                ProductModel.Operator = _globalState.LoginUser.Account;
                ProductModel.CreateTime = DateTime.Now;
                var temp = ProductModel.MapTo<ObservableProductModel, ProductParamModel>();
                return await _productService.Add(temp);
            }
            else
            {
                ProductModel.ProcessIds = new ObservableCollection<string>(processIds);
                var temp = ProductModel.MapTo<ObservableProductModel, ProductParamModel>();
                return await _productService.Update(temp);
            }
        }

        private async Task<bool> ModifyProduct()
        {
            ProductModel.CreateTime = DateTime.Now;
            List<string> processIds = new List<string>();
            if (ProcessModels.Count > 0)
            {
                var processTemp = ProcessModels.MapTo<ObservableCollection<ObservableProcessModel>, List<ProcessModel>>();
                List<ProcessModel> insertData = processTemp.Where(item=>string.IsNullOrEmpty(item.Id)).ToList();
                List<ProcessModel> updateData = processTemp.Where(item => !string.IsNullOrEmpty(item.Id)).ToList();
                foreach (var item in insertData)
                {
                    item.Id = Guid.NewGuid().ToString();
                }
                
                var s = await _processParaService.AddRange(insertData);
                var u = await _processParaService.UpdateRange(updateData);
                if (insertData.Count != 0 && !s)
                {
                    _logger.LogInfo("步骤过程插入失败");
                    return false;
                }
                if (updateData.Count != 0 && !u)
                {
                    _logger.LogInfo("步骤过程更新失败");
                    return false;
                }
                var temp2 = insertData.Select(item => item.Id).ToList();
                processIds = temp2.Concat(updateData.Select(item => item.Id).ToList()).ToList();
            }
            ProductModel.ProcessIds = new ObservableCollection<string>(processIds);
            var temp = ProductModel.MapTo<ObservableProductModel, ProductParamModel>();
            return await _productService.Update(temp);
        }

        public IRelayCommand AddNewProcessCommand => new RelayCommand(() =>
        {
            if (ProcessModels == null) ProcessModels = new ObservableCollection<ObservableProcessModel>();
            int number = 1;
            if (ProcessModels.Count > 0)
            {
                number = ProcessModels.Max(item => item.ProcessNumber);
                number++;
            }
            ProcessModels.Add(new ObservableProcessModel() {
                ProcessNumber = number,
                ParamName = $"Side{number}",
                CameraType = CameraTypeEnum.Main,
                TakeType = PictureType.SINGLE,
                PictureLayout = PictureLayoutEnum.ONE,
                ProcessType = ProcessTypeEnum.TAKEPICTURE,
            });
        });

        public IRelayCommand AddEquipmentCommand => new RelayCommand(async () =>
        {
            var result = await Dialog.Show<EquipmentCtrlDialog>()
             .Initialize<EquipmentCtrlDialogViewModel>(vm =>
             {
                 vm.Title = "新增设备参数";
                 vm.EquipmentModel = new ObservableEquipmentModel();
                 vm.IsInsert = true;
             })
             .GetResultAsync<bool>();
            if (result) RefreshEquipment();
        });

        public IRelayCommand<ObservableProcessModel> DeleteCommand => new RelayCommand<ObservableProcessModel>(async (model) =>
        {
            if (!string.IsNullOrEmpty(model.Id))
            {
                await _processParaService.DeleteWhere(item=>item.Id == model.Id);
                ProductModel.ProcessIds.Remove(model.Id);
            }
            ProcessModels.Remove(model);
            int index = 1;
            foreach (var item in ProcessModels)
            {
                item.ProcessNumber = index;
                index++;
            }
        });

        private async void RefreshEquipment()
        {
            var temp = await _equipmentService.getAll();

            if (ProcessModels == null) return;
            // 保存每个 ProcessModel 当前选中的 EquipmentParaId
            var selectedIds = ProcessModels
                .Where(pm => !string.IsNullOrEmpty(pm.EquipmentParaId))
                .GroupBy(pm => pm.EquipmentParaId)
                .ToDictionary(pm => pm.Key, pm => pm.ToList());

            // 保持对象引用一致性，避免重新创建实例
            foreach (var equipment in temp.MapTo<List<EquipmentModel>, ObservableCollection<ObservableEquipmentModel>>())
            {
                var existingItem = ObservableEquipmentModels.FirstOrDefault(e => e.Id == equipment.Id);
                if (existingItem != null)
                {
                    // 更新现有对象的属性而不是替换它
                    existingItem.ParamName = equipment.ParamName;
                    // 其他需要更新的属性
                }
                else
                {
                    // 如果不存在则添加
                    ObservableEquipmentModels.Add(equipment);
                }
            }

            // 重新设置选中的值
            foreach (var item in ProcessModels)
            {
                if (!string.IsNullOrEmpty(item.EquipmentParaId) && selectedIds.TryGetValue(item.EquipmentParaId, out var processModel))
                {
                    item.EquipmentParaId = processModel[0].EquipmentParaId;
                }
            }
        }

        /// <summary>
        /// 导入excel文件
        /// </summary>
        public IRelayCommand ImportCommand => new RelayCommand(async () =>
        {
            var result = await Dialog.Show<ImportPinCoordinatesDialog>()
                .Initialize<ImportPinCoordinatesDialogViewModel>(vm =>
                {
                    vm.Title = "导入坐标";
                    vm.SaveProcessedEvent += Vm_ImportSaveSuccessedEvent;
                })
                .GetResultAsync<bool>();
            if (result) RefreshEquipment();
        });

        private void Vm_ImportSaveSuccessedEvent(ObservableCollection<ObservableProcessModel> processModels)
        {
            // 将生成的步骤添加到当前产品的步骤列表中
            if (processModels != null && processModels.Count > 0)
            {
                foreach (var item in processModels)
                {
                    if (string.IsNullOrEmpty(item.Id))
                    {
                        item.Id = Guid.NewGuid().ToString();
                    }
                    ProcessModels.Add(item);
                    if (ProductModel.ProcessIds == null)
                    {
                        ProductModel.ProcessIds = new ObservableCollection<string>();
                    }
                    ProductModel.ProcessIds.Add(item.Id);
                }
            }
        }

        #region 拖拽

        public void DragEnter(IDropInfo dropInfo)
        {
            var source = dropInfo.Data as ObservableProcessModel;
            var target = dropInfo.TargetItem as ObservableProcessModel;
            if (source != null && target != null)
            {
                dropInfo.Effects = System.Windows.DragDropEffects.Move;
                dropInfo.EffectText = "移动到当前位置！";
                dropInfo.DropTargetAdorner = DropTargetAdorners.Highlight;
            }
        }

    

        public void DragOver(IDropInfo dropInfo)
        {
            DragEnter(dropInfo);
        }

        public void DragLeave(IDropInfo dropInfo)
        {
            
        }

        public void Drop(IDropInfo dropInfo)
        {
            if (dropInfo.Data is ObservableProcessModel source && dropInfo.TargetItem is ObservableProcessModel targetItem)
            {

                int targetIndex = ProcessModels.IndexOf(targetItem);
                ProcessModels.Remove(source);
                ProcessModels.Insert(targetIndex, source);

                // 如有必要，更新列表中所有项的索引
                UpdateIndices(ProcessModels);
            }

        }


        private void UpdateIndices(ObservableCollection<ObservableProcessModel> targetList)
        {
            for (int i = 0; i < targetList.Count; i++)
            {
                targetList[i].ProcessNumber = i + 1; // Set Index based on the new order
            }
        }

        #endregion

        #region 坐标

        public IRelayCommand AddCompCommand => new RelayCommand(() =>
        {
            if (CompPointsModels == null) CompPointsModels = new ObservableCollection<ObservableCompPointModel>();
            CompPointsModels.Add(new ObservableCompPointModel() { CompName = $"元件{CompPointsModels.Count + 1}" });
        });

        private async void SaveComp()
        {
            if(CompPointsModels != null && CompPointsModels.Count>0)
            {
                var temp = CompPointsModels.MapTo<ObservableCollection<ObservableCompPointModel>,List<CompPointModel>>();
                List<CompPointModel> newList = new List<CompPointModel>();
                List<CompPointModel> oldList = new List<CompPointModel>();
                temp.ForEach(item =>
                {
                    if (string.IsNullOrEmpty(item.Id))
                    {
                        item.Id = Guid.NewGuid().ToString();
                        item.ProductParamId = ProductModel.Id;
                        newList.Add(item);
                    }
                    else oldList.Add(item);
                });
                await _compService.AddRange(newList);
                await _compService.UpdateRange(oldList);
            }
        }


        private async void LoadComp()
        {
            var temp = await _compService.getByWhereIF(!string.IsNullOrEmpty(ProductModel.Id),item => item.ProductParamId == ProductModel.Id);
            CompPointsModels = temp.MapTo<List<CompPointModel>, ObservableCollection<ObservableCompPointModel>>();
        }

        public IRelayCommand<ObservableCompPointModel> DeleteCompCommand => new RelayCommand<ObservableCompPointModel>(async (model) =>
        {
            if (!string.IsNullOrEmpty(model.Id))
            {
                var result = await _compService.Delete(model.Id);
                if (result) CompPointsModels.Remove(model);
            }
            else CompPointsModels.Remove(model);
        });
        #endregion

        #region 自动生成步骤
        public IRelayCommand GenerateProceessCommand => new RelayCommand(async () =>
        {
            
            var result = await Dialog.Show<CompInfoDialog>()
            .Initialize<CompInfoDialogViewModel>(async vm =>
            {
                vm.Title = "生成步骤";
                if(ProductModel.ProcessIds != null && ProductModel.ProcessIds.Count > 0)
                {
                    foreach (var item in ProductModel.ProcessIds)
                    {
                        var temp = await _processParaService.getByWhere(s2=>s2.Id == item);
                        if (temp.Count > 0)
                        {
                            await _equipmentService.DeleteWhere(s3 => s3.Id == temp[0].EquipmentParaId);
                        }
                        await _processParaService.DeleteWhere(s4 => s4.Id == item);
                    }
                }
                ProductModel.ProcessIds = new ObservableCollection<string>();
                if (string.IsNullOrEmpty(ProductModel.CompInfoId))
                {
                    vm.CurrentData = new ObservableCompModel();
                    vm.IsInsert = true;
                }
                else
                {
                    vm.IsInsert = false;
                    var temp = await _compInfoService.getFirstOrDefault(item=>item.Id == ProductModel.CompInfoId);
                    vm.CurrentData = temp.MapTo<CompInfoModel, ObservableCompModel>();
                }
                vm.SaveSuccessedEvent += Vm_SaveSuccessedEvent;
                vm.NotifyCompInfoEvent += Vm_NotifyCompInfoEvent;
            })
            .GetResultAsync<bool>();
            if (result) RefreshEquipment();
        });

        private void Vm_NotifyCompInfoEvent(string obj)
        {
            ProductModel.CompInfoId = obj;
        }

        private async void Vm_SaveSuccessedEvent(ObservableCollection<ObservableProcessModel> obj)
        {
            try
            {
                if (ProcessModels.Count > 0)
                {
                    foreach (var item in ProcessModels)
                    {
                        if (!string.IsNullOrEmpty(item.EquipmentParaId))
                        {
                           var temp2 =  await _equipmentService.Delete(item.EquipmentParaId);
                        }
                    }
                    
                }
                var process = ProcessModels.Select(x => x.Id).ToList();

                if (process != null) {
                    process.ForEach(async item =>
                    {
                        await _processParaService.Delete(item);
                    });
                }
                ProcessModels.Clear();
                RefreshEquipment();
                ProcessModels = obj;
            }catch(Exception ex)
            {
                _logger.LogInfo(ex.ToString());
            }
            
        }
        #endregion
    }
}
