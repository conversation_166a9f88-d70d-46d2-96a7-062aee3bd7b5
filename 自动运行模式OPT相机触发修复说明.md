# 自动运行模式OPT相机触发修复说明

## 问题描述

用户反馈的问题：
1. **触发时机问题**：当用户点击开始自动运行时，OPT相机的触发模式应该立即打开，而不是只有切换到拍照步骤时才打开
2. **时序问题**：自动运行时可能出现OPT打开了触发模式，但实时画面还没来得及关闭，然后就取了实时画面里的图片，这是不对的

## 修复方案

### 1. 修改自动运行模式触发时机

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`

**修改方法**: `HandleAutoRunModeChange`

**修复内容**:
- **原逻辑**: 只有当前步骤是拍照步骤时才设置OPT相机触发模式
- **新逻辑**: 开始自动运行时立即设置OPT相机触发模式，不等待切换到拍照步骤

```csharp
// 修复前
if (CurrentProcess != null &&
    CurrentProcess.ProcessType == ProcessTypeEnum.TAKEPICTURE &&
    CurrentProcess.CameraType == CameraTypeEnum.Main)
{
    SetOptCameraFrameTriggerMode();
}

// 修复后
// 开始自动运行时立即设置OPT相机触发模式
NotifyLog("📷 开始自动运行，立即设置OPT相机触发模式");
SetOptCameraFrameTriggerMode();
```

### 2. 简化步骤切换逻辑

**文件**: `IPM.Vision\ViewModel\Pages\VisionPageViewModel.cs`

**修改方法**: `HandleProcessChangeInAutoMode`

**修复内容**:
- **原逻辑**: 步骤切换时重复设置/关闭OPT相机触发模式
- **新逻辑**: OPT相机触发模式在自动运行开始时就已设置，步骤切换时只需控制设备AutoTake

### 3. 修复实时画面时序问题

**文件**: `IPM.Vision\BLL\OptVisionService.cs`

**修复内容**:

#### 3.1 增加触发模式稳定延迟
```csharp
// 修复前
private readonly TimeSpan _triggerModeStabilizeDelay = TimeSpan.FromMilliseconds(100);

// 修复后
private readonly TimeSpan _triggerModeStabilizeDelay = TimeSpan.FromMilliseconds(500);
```

#### 3.2 增加实时画面停止等待时间
```csharp
// 在setLineTriggerConf方法中
// 修复前
Thread.Sleep(100);

// 修复后
AddLog("等待实时画面完全停止...");
Thread.Sleep(300); // 增加到300ms，确保实时画面的残留帧都被清理
```

#### 3.3 在触发模式下完全停止实时画面处理
```csharp
// 修复前：无论什么模式都处理实时画面
var copy = CreateFrameCopy(ref frame);
_frameQueue.Enqueue(copy);
_frameEvent.Set();

// 修复后：只有在非触发模式下才处理实时画面
if (!_isTriggerMode)
{
    var copy = CreateFrameCopy(ref frame);
    _frameQueue.Enqueue(copy);
    _frameEvent.Set();
}
else
{
    // 触发模式下跳过实时画面帧处理
    if (_frameCount % 100 == 0)
    {
        _logger.LogInfo($"[TriggerMode] 跳过实时画面帧处理，当前为触发模式");
    }
}
```

#### 3.4 严格的触发模式稳定期检查
```csharp
// 在帧回调中增加严格检查
var timeSinceTriggerModeSet = triggerStartTime - _triggerModeSetTime;
if (timeSinceTriggerModeSet < _triggerModeStabilizeDelay)
{
    // 触发模式刚设置，跳过这个帧（可能是连续采集的残留帧）
    _logger.LogInfo($"[TriggerStabilize] 跳过残留帧，距离设置: {timeSinceTriggerModeSet.TotalMilliseconds:F0}ms");
    
    // **关键修复：在稳定期内，不处理任何帧，包括实时画面**
    // 直接返回，不进行后续的帧队列处理
    return;
}
```

## 修复效果

1. **触发时机修复**: 用户点击开始自动运行时，OPT相机触发模式立即打开
2. **时序问题修复**: 
   - 触发模式设置前清空所有残留帧
   - 增加500ms稳定延迟，确保实时画面完全停止
   - 在触发模式下完全停止实时画面帧的处理
   - 严格过滤触发模式稳定期内的所有帧

3. **逻辑简化**: 步骤切换时不再重复设置相机触发模式，只控制设备AutoTake

## 测试建议

1. **基本功能测试**:
   - 点击开始自动运行，确认OPT相机触发模式立即打开
   - 确认实时画面在触发模式下停止显示
   - 确认触发拍照功能正常工作

2. **时序测试**:
   - 快速开始/停止自动运行，确认没有时序冲突
   - 在自动运行过程中切换步骤，确认触发模式保持稳定

3. **边界测试**:
   - 在相机连接/断开状态下测试自动运行
   - 测试异常情况下的错误处理

## 注意事项

1. 修改后OPT相机在整个自动运行期间都处于触发模式，只有在拍照步骤时才会打开设备AutoTake
2. 实时画面在自动运行期间会停止显示，这是正常的预期行为
3. 触发模式稳定延迟增加到500ms，可能会略微影响自动运行的启动速度，但能确保时序正确性
