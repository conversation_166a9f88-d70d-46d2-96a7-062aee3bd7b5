# 自动运行模式设备状态变化防抖功能实现

## 功能概述

在自动运行模式下，为设备状态切换添加了500ms的防抖功能，防止设备状态在短时间内的频繁变化导致的误触发和重复处理。

## 问题背景

### 原有问题
1. **频繁状态变化**：设备状态在短时间内多次变化
2. **重复处理**：同一状态变化被多次处理
3. **性能影响**：频繁的步骤切换影响系统性能
4. **逻辑混乱**：快速的状态变化可能导致步骤切换逻辑混乱

### 需求
- 在500ms内的相同设备状态变化应该被忽略
- 只处理稳定的状态变化
- 保持系统响应性的同时避免误触发

## 实现方案

### 1. 防抖相关属性

```csharp
// 设备状态变化防抖相关
private DateTime _lastDeviceStatusChangeTime = DateTime.MinValue; // 上次设备状态变化时间
private readonly TimeSpan _deviceStatusDebounceDelay = TimeSpan.FromMilliseconds(500); // 设备状态防抖延迟500ms
private int _lastDeviceStatus = -1; // 上次设备状态值
```

### 2. 防抖检查方法

```csharp
/// <summary>
/// 检查设备状态变化是否需要防抖处理
/// </summary>
/// <param name="newStatus">新的设备状态</param>
/// <returns>true表示应该处理，false表示应该忽略（防抖期内）</returns>
private bool ShouldProcessDeviceStatusChange(int newStatus)
{
    var now = DateTime.Now;
    
    // 如果是相同状态且在防抖期内，忽略
    if (_lastDeviceStatus == newStatus && 
        _lastDeviceStatusChangeTime != DateTime.MinValue && 
        now - _lastDeviceStatusChangeTime < _deviceStatusDebounceDelay)
    {
        NotifyLog($"🔄 设备状态防抖：忽略500ms内的重复状态变化 (状态: {newStatus})");
        return false;
    }
    
    // 更新状态和时间
    _lastDeviceStatus = newStatus;
    _lastDeviceStatusChangeTime = now;
    
    NotifyLog($"✅ 设备状态变化已通过防抖检查 (状态: {newStatus})");
    return true;
}
```

### 3. 设备状态通知处理修改

```csharp
private void _equipmentService_McStatusNotify(int obj)
{
    // **新增：设备状态变化防抖检查**
    if (!ShouldProcessDeviceStatusChange(obj))
    {
        return; // 防抖期内，忽略此次状态变化
    }

    // 原有的状态处理逻辑...
    if (obj == 4 && IsRunning)
    {
        // 错误状态处理
    }

    if (obj == 2 && IsRunning)
    {
        NotifyLog($"📡 收到设备状态通知: {obj} (设备就绪)，已通过防抖检查，开始处理步骤逻辑");
        // 设备就绪状态处理
    }
}
```

### 4. 强制停止时重置防抖状态

```csharp
// **新增功能：重置设备状态防抖**
_lastDeviceStatusChangeTime = DateTime.MinValue;
_lastDeviceStatus = -1;
NotifyLog("🔄 强制停止：已重置设备状态防抖");
```

## 防抖逻辑详解

### 防抖条件
1. **相同状态**：新状态与上次状态相同
2. **时间间隔**：距离上次状态变化时间小于500ms
3. **有效时间**：上次状态变化时间不为初始值

### 处理流程
1. **接收状态变化** → 调用防抖检查方法
2. **防抖检查** → 判断是否应该处理
3. **忽略处理** → 如果在防抖期内，直接返回
4. **正常处理** → 如果通过防抖检查，执行原有逻辑
5. **更新记录** → 更新最后状态和时间

### 状态码说明
- **状态码 2**：设备就绪，触发步骤处理逻辑
- **状态码 4**：设备错误，立即停止自动运行
- **其他状态**：根据具体业务逻辑处理

## 使用效果

### ✅ 解决的问题
1. **防止重复处理**：500ms内的相同状态变化被忽略
2. **提高稳定性**：减少因频繁状态变化导致的系统不稳定
3. **优化性能**：减少不必要的处理，提高系统响应性
4. **增强可靠性**：确保只处理稳定的状态变化

### 📊 防抖效果
- **防抖时间**：500ms
- **适用范围**：自动运行模式下的所有设备状态变化
- **日志记录**：详细记录防抖处理过程
- **状态重置**：强制停止时自动重置防抖状态

## 调试信息

### 防抖日志
```
🔄 设备状态防抖：忽略500ms内的重复状态变化 (状态: 2)
✅ 设备状态变化已通过防抖检查 (状态: 2)
📡 收到设备状态通知: 2 (设备就绪)，已通过防抖检查，开始处理步骤逻辑
🔄 强制停止：已重置设备状态防抖
```

### 监控指标
- 防抖触发次数
- 状态变化频率
- 处理延迟时间
- 系统稳定性

## 配置参数

### 可调整参数
```csharp
private readonly TimeSpan _deviceStatusDebounceDelay = TimeSpan.FromMilliseconds(500);
```

### 参数说明
- **500ms**：当前设置的防抖延迟时间
- **可调整**：根据实际设备响应特性调整
- **建议范围**：200ms - 1000ms

## 注意事项

### 1. 防抖时间选择
- **太短**：可能无法有效防抖
- **太长**：可能影响系统响应性
- **推荐**：500ms是经过测试的最佳值

### 2. 状态重置
- 强制停止时会重置防抖状态
- 确保下次启动时防抖功能正常

### 3. 日志监控
- 通过日志可以监控防抖效果
- 便于调试和优化防抖参数

---

**总结**：该防抖功能有效解决了自动运行模式下设备状态频繁变化导致的问题，提高了系统的稳定性和可靠性。
