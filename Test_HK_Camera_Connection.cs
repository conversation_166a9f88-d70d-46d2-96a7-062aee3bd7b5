using System;
using System.Threading.Tasks;
using IPM.Vision.BLL;
using IPM.Vision.Common;
using IPM.Vision.ViewModel.ObservableModel;

namespace IPM.Vision.Tests
{
    /// <summary>
    /// 海康相机连接测试类
    /// 用于验证修复后的连接功能
    /// </summary>
    public class HKCameraConnectionTest
    {
        private readonly HKVisionService _hkVisionService;
        private readonly GlobalCameraManager _globalCameraManager;
        private readonly NLogHelper _logger;

        public HKCameraConnectionTest(HKVisionService hkVisionService, 
            GlobalCameraManager globalCameraManager, NLogHelper logger)
        {
            _hkVisionService = hkVisionService;
            _globalCameraManager = globalCameraManager;
            _logger = logger;
        }

        /// <summary>
        /// 执行完整的海康相机连接测试
        /// </summary>
        public async Task<bool> RunCompleteConnectionTest()
        {
            _logger.LogInfo("=== 开始海康相机连接测试 ===");

            try
            {
                // 测试1：基础连接测试
                bool basicTest = await TestBasicConnection();
                _logger.LogInfo($"基础连接测试: {(basicTest ? "通过" : "失败")}");

                // 测试2：页面切换测试
                bool pageSwitchTest = await TestPageSwitchRecovery();
                _logger.LogInfo($"页面切换测试: {(pageSwitchTest ? "通过" : "失败")}");

                // 测试3：重连测试
                bool reconnectTest = await TestReconnection();
                _logger.LogInfo($"重连测试: {(reconnectTest ? "通过" : "失败")}");

                // 测试4：诊断功能测试
                bool diagnosticTest = TestDiagnosticFunction();
                _logger.LogInfo($"诊断功能测试: {(diagnosticTest ? "通过" : "失败")}");

                bool allTestsPassed = basicTest && pageSwitchTest && reconnectTest && diagnosticTest;
                _logger.LogInfo($"=== 测试完成，总体结果: {(allTestsPassed ? "全部通过" : "存在失败")} ===");

                return allTestsPassed;
            }
            catch (Exception ex)
            {
                _logger.LogError($"测试过程异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试基础连接功能
        /// </summary>
        private async Task<bool> TestBasicConnection()
        {
            try
            {
                _logger.LogInfo("--- 测试1: 基础连接功能 ---");

                // 检查初始状态
                bool initialConnected = _hkVisionService.IsConnected;
                _logger.LogInfo($"初始连接状态: {initialConnected}");

                // 如果未连接，尝试连接
                if (!initialConnected)
                {
                    _logger.LogInfo("尝试建立连接...");
                    _hkVisionService.RefreshAndConnect();
                    
                    // 等待连接完成
                    await Task.Delay(3000);
                    
                    bool afterConnect = _hkVisionService.IsConnected;
                    _logger.LogInfo($"连接后状态: {afterConnect}");
                    
                    return afterConnect;
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"基础连接测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试页面切换后的画面恢复功能
        /// </summary>
        private async Task<bool> TestPageSwitchRecovery()
        {
            try
            {
                _logger.LogInfo("--- 测试2: 页面切换画面恢复 ---");

                if (!_hkVisionService.IsConnected)
                {
                    _logger.LogInfo("相机未连接，跳过页面切换测试");
                    return false;
                }

                // 模拟页面切换：暂停流
                _logger.LogInfo("模拟切换到其他页面（暂停流）...");
                _globalCameraManager.PauseHkStream();
                await Task.Delay(1000);

                bool streamPaused = !_globalCameraManager.IsHkStreamActive;
                _logger.LogInfo($"流暂停状态: {streamPaused}");

                // 模拟切换回海康页面：恢复流
                _logger.LogInfo("模拟切换回海康页面（恢复流）...");
                await _globalCameraManager.StartHkStreamAsync();
                await Task.Delay(2000);

                bool streamResumed = _globalCameraManager.IsHkStreamActive;
                _logger.LogInfo($"流恢复状态: {streamResumed}");

                return streamPaused && streamResumed;
            }
            catch (Exception ex)
            {
                _logger.LogError($"页面切换测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试重连功能
        /// </summary>
        private async Task<bool> TestReconnection()
        {
            try
            {
                _logger.LogInfo("--- 测试3: 重连功能 ---");

                // 检查重连前状态
                bool beforeReconnect = _hkVisionService.IsConnected;
                _logger.LogInfo($"重连前状态: {beforeReconnect}");

                // 执行重连
                _logger.LogInfo("执行强制重连...");
                await _globalCameraManager.ForceResetHkCameraAsync();
                await Task.Delay(2000);

                // 检查重连后状态
                bool afterReconnect = _hkVisionService.IsConnected;
                _logger.LogInfo($"重连后状态: {afterReconnect}");

                return afterReconnect;
            }
            catch (Exception ex)
            {
                _logger.LogError($"重连测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试诊断功能
        /// </summary>
        private bool TestDiagnosticFunction()
        {
            try
            {
                _logger.LogInfo("--- 测试4: 诊断功能 ---");

                // 执行诊断
                _logger.LogInfo("执行相机诊断...");
                _hkVisionService.DiagnoseHKCameraIssues();
                
                // 执行全局诊断
                _logger.LogInfo("执行全局相机状态诊断...");
                _globalCameraManager.DiagnosteCameraStatus();

                _logger.LogInfo("诊断功能执行完成");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"诊断功能测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 快速连接验证（用于日常检查）
        /// </summary>
        public bool QuickConnectionCheck()
        {
            try
            {
                _logger.LogInfo("执行快速连接检查...");

                bool isConnected = _hkVisionService.IsConnected;
                bool isGrabbing = _hkVisionService.IsGrabbing;
                int deviceCount = _hkVisionService.DeviceCount;

                _logger.LogInfo($"连接状态: {isConnected}");
                _logger.LogInfo($"采集状态: {isGrabbing}");
                _logger.LogInfo($"设备数量: {deviceCount}");

                // 如果连接但未采集，尝试启动采集
                if (isConnected && !isGrabbing)
                {
                    _logger.LogInfo("连接正常但未采集，尝试启动采集...");
                    _hkVisionService.StartGrap();
                }

                return isConnected;
            }
            catch (Exception ex)
            {
                _logger.LogError($"快速连接检查异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 生成测试报告
        /// </summary>
        public void GenerateTestReport()
        {
            _logger.LogInfo("=== 海康相机状态报告 ===");
            _logger.LogInfo($"SDK初始化: {_hkVisionService.SDKInited}");
            _logger.LogInfo($"设备连接: {_hkVisionService.IsConnected}");
            _logger.LogInfo($"采集状态: {_hkVisionService.IsGrabbing}");
            _logger.LogInfo($"设备数量: {_hkVisionService.DeviceCount}");
            _logger.LogInfo($"全局管理器连接状态: {_globalCameraManager.IsHkCameraConnected}");
            _logger.LogInfo($"全局管理器流状态: {_globalCameraManager.IsHkStreamActive}");
            _logger.LogInfo("========================");
        }
    }
}
