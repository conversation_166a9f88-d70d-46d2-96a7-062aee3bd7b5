﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Controls;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Camera.Com;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.ObservableModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace IPM.Vision.ViewModel.CustomControls
{
    public class CameraCtrlControlViewModel:ViewModelBase
    {
        private readonly ObservableGlobalState _globalState;
        private ObservableMainCameraParaModel _mainCameraParaModel;
        private readonly MainCameraParaService _cameraService;
        
        public CameraCtrlControlViewModel(ObservableGlobalState globalState,IMainCameraParaService mainCameraParaService)
        {
            _globalState = globalState;
            _cameraService = (MainCameraParaService)mainCameraParaService;
            _globalState.ProcessChangedEvent += _globalState_ProcessChangedEvent;
        }

        public ObservableMainCameraParaModel CameraParamModel
        {
            get => _mainCameraParaModel;
            set => SetProperty(ref _mainCameraParaModel, value);
        }

        public PopupWindow ShowControl { get; set; }

        private void _globalState_ProcessChangedEvent(ObservableProcessModel obj)
        {
            if(obj.MainCameraPara != null)
                CameraParamModel = obj.MainCameraPara;
            else
                CameraParamModel = new ObservableMainCameraParaModel();
        }

        public IRelayCommand LoadCommand => new RelayCommand(() =>
        {
            if(_globalState.CurrentProcessModel != null)
                CameraParamModel = _globalState.CurrentProcessModel.MainCameraPara;
            else
                CameraParamModel = new ObservableMainCameraParaModel();
        });

        public IRelayCommand UnLoadCommand => new RelayCommand(() =>
        {
            _globalState.ProcessChangedEvent -= _globalState_ProcessChangedEvent;

        });

        public IRelayCommand<RoutedEventArgs> LeftPanelLoadCommand => new RelayCommand<RoutedEventArgs>((e) => {
            var source = e.Source as UniformSpacingPanel;
            if (source != null)
            {
                var child = source.Children;
                if (child != null && child.Count > 0)
                {
                    foreach (var item in child)
                    {
                        var observable = item as IObserver;
                        if (observable != null) _cameraService.AddSource(observable);

                    }
                }
            }
        });
        public IRelayCommand<RoutedEventArgs> RightPanelLoadCommand => new RelayCommand<RoutedEventArgs>((e) => {
            var source = e.Source as UniformSpacingPanel;
            if (source != null)
            {
                var child = source.Children;
                if (child != null && child.Count > 0)
                {
                    foreach (var item in child)
                    {
                        var observable = item as IObserver;
                        if (observable != null) _cameraService.AddSource(observable);
                    }
                }
                _cameraService.GetAllPropertyDesc();
                _cameraService.GetAllProperty();
            }
        });

        public IRelayCommand SaveCommand => new RelayCommand(async () => {
            if (!string.IsNullOrEmpty(CameraParamModel.Id))
            {
                await _cameraService.Update(CameraParamModel.MapTo<ObservableMainCameraParaModel, MainCameraParamModel>());
            }
            if(ShowControl != null) ShowControl.Close();
        });


    }
}
