﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.Com
{
    public static class FileHelper
    {
        /// <summary>
        /// 创建文件夹。如果文件夹已经存在，则不执行操作。
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        public static void CreateFolder(string folderPath)
        {
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }
        }
        /// <summary>
        /// 创建一个空文件，如果文件已存在则抛出异常。
        /// </summary>
        /// <param name="filePath">要创建的文件的路径。</param>
        public static void CreateFileWithoutWriting(string filePath)
        {
            // 使用FileMode.CreateNew确保文件不存在时才创建
            using (FileStream fs = new FileStream(filePath, FileMode.CreateNew))
            {
                // 文件创建成功，但不写入内容，流会自动关闭
            }
        }

        /// <summary>
        /// 拼接文件
        /// </summary>
        /// <param name="folderPath"></param>
        /// <param name="fileName"></param>
        /// <returns></returns>
        public static string ConcatFile(string folderPath, string fileName)
        {
            return Path.Combine(folderPath, fileName);
        }

        /// <summary>
        /// 获取文件名
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        public static string GetFileName(string filePath)
        {
            return Path.GetFileName(filePath);
        }

        /// <summary>
        /// 读取文件内容，处理文件被占用的异常并重试。
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件内容</returns>
        public static string ReadFile(string filePath)
        {
            return RetryOnFileLock(() =>
            {
                using (var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                using (var reader = new StreamReader(stream))
                {
                    return reader.ReadToEnd();
                }
            });
        }

        public static bool FileIsExists(string filePath)
        {
            return File.Exists(filePath);
        }

        public static bool FolderIsExists(string path)
        {
            return Directory.Exists(path);
        }

        /// <summary>
        /// 写入内容到文件，处理文件被占用的异常并重试。
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="content">写入内容</param>
        public static void WriteFile(string filePath, string content)
        {
            RetryOnFileLock(() =>
            {
                using (var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None))
                using (var writer = new StreamWriter(stream))
                {
                    writer.Write(content);
                }
            });
        }

        /// <summary>
        /// 复制文件，处理文件被占用的异常并重试。
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        /// <param name="overwrite">是否覆盖</param>
        public static void CopyFile(string sourceFilePath, string destinationFilePath, bool overwrite = false)
        {
            RetryOnFileLock(() =>
            {
                using (var sourceStream = new FileStream(sourceFilePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                using (var destinationStream = new FileStream(destinationFilePath, FileMode.Create, FileAccess.Write))
                {
                    sourceStream.CopyTo(destinationStream);
                }
            });
        }

        /// <summary>
        /// 删除文件，处理文件被占用的异常并重试。
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public static void DeleteFile(string filePath)
        {
            RetryOnFileLock(() =>
            {
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            });
        }

        /// <summary>
        /// 检查文件是否存在。
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件是否存在</returns>
        public static bool FileExists(string filePath)
        {
            return File.Exists(filePath);
        }

        /// <summary>
        /// 尝试执行文件操作，处理文件被占用的情况并支持重试机制。
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="delay">重试之间的延迟时间（毫秒）</param>
        private static void RetryOnFileLock(Action action, int maxRetries = 3, int delay = 500)
        {
            RetryOnFileLock(() =>
            {
                action();
                return true;
            }, maxRetries, delay);
        }

        /// <summary>
        /// 尝试执行文件操作，处理文件被占用的情况并支持重试机制。
        /// </summary>
        /// <typeparam name="T">返回值类型</typeparam>
        /// <param name="func">要执行的操作</param>
        /// <param name="maxRetries">最大重试次数</param>
        /// <param name="delay">重试之间的延迟时间（毫秒）</param>
        /// <returns>操作结果</returns>
        private static T RetryOnFileLock<T>(Func<T> func, int maxRetries = 3, int delay = 500)
        {
            int attempts = 0;
            while (true)
            {
                try
                {
                    return func();
                }
                catch (IOException ex) when (IsFileLocked(ex))
                {
                    attempts++;
                    if (attempts >= maxRetries)
                    {
                        throw new IOException($"文件被占用并超过了重试次数: {ex.Message}", ex);
                    }
                    Thread.Sleep(delay);
                }
            }
        }

        /// <summary>
        /// 判断异常是否是由于文件被占用导致的。
        /// </summary>
        /// <param name="ex">捕获的 IOException</param>
        /// <returns>是否是文件占用异常</returns>
        private static bool IsFileLocked(IOException ex)
        {
            int errorCode = System.Runtime.InteropServices.Marshal.GetHRForException(ex) & 0xFFFF;
            return errorCode == 32 || errorCode == 33; // 错误码 32 和 33 表示文件被占用
        }


        /// <summary>
        /// 获取指定路径所在驱动器的磁盘剩余空间百分比。
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <returns>磁盘剩余空间的百分比</returns>
        public static double GetFreeSpacePercentage(string folderPath)
        {
            if (string.IsNullOrEmpty(folderPath)) return 0;
            string rootPath = Path.GetPathRoot(folderPath);
            DriveInfo drive = new DriveInfo(rootPath);

            if (drive.IsReady)
            {
                long totalSpace = drive.TotalSize;
                long freeSpace = drive.AvailableFreeSpace;

                if (totalSpace == 0)
                {
                    throw new DivideByZeroException("总磁盘空间为0，无法计算百分比。");
                }

                double percentage = (double)freeSpace / totalSpace * 100;
                return percentage;
            }
            else
            {
                throw new InvalidOperationException($"驱动器 {rootPath} 尚未准备好。");
            }
        }

        public static string GenerateFileName(string directoryPath, string fileName)
        {
            // 文件全路径
            // 获取文件名和扩展名
            string fileWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
            string extension = Path.GetExtension(fileName);

            // 文件全路径
            string fullPath = Path.Combine(directoryPath, fileName);

            // 如果文件不存在，直接返回原始文件名
            if (!File.Exists(fullPath))
            {
                return fileName; // 返回原始文件名
            }

            // 正则表达式：匹配文件名为 fileWithoutExtension-数字 的模式
            string pattern = $@"^{Regex.Escape(fileWithoutExtension)}-(\d+){Regex.Escape(extension)}$";

            // 获取该目录下的所有符合 pattern 模式的文件，并提取数字
            var existingFiles = Directory
                .GetFiles(directoryPath, $"{fileWithoutExtension}-*{extension}")
                .Select(Path.GetFileName) // 只保留文件名
                .Where(f => Regex.IsMatch(f, pattern)) // 匹配正则
                .Select(f => int.Parse(Regex.Match(f, pattern).Groups[1].Value)) // 提取数字部分
                .ToList();

            int fileCount = existingFiles.Count > 0 ? existingFiles.Max() + 1 : 1; // 找到最大数字并递增

            // 生成新的文件名，保留后缀
            string newFileName = $"{fileWithoutExtension}-{fileCount}{extension}";
            return newFileName; // 返回新的文件名
        }
    }
}
