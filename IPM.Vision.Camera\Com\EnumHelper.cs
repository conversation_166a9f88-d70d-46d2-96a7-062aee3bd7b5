﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.Com
{
    public static class EnumHelper
    {
        public static string GetDescription(this Enum value)
        {
            var type = value.GetType();
            var name = Enum.GetName(type, value);
            if (string.IsNullOrEmpty(name)) return value.ToString();
            var field = type.GetField(name);
            var des = field.GetCustomAttribute<DescriptionAttribute>();
            if (des == null) return value.ToString();
            return des.Description;

        }


    }
}
