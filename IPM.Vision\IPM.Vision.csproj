﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="packages\EntityFramework.6.4.4\build\EntityFramework.props" Condition="Exists('packages\EntityFramework.6.4.4\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5D42AA3E-77A3-4580-97FF-A1FC7E088A2E}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>IPM.Vision</RootNamespace>
    <AssemblyName>IPM.Vision</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>logo.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=10.0.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>packages\AutoMapper.10.1.1\lib\net461\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Crypto, Version=1.8.9.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>packages\Portable.BouncyCastle.1.8.9\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>packages\BouncyCastle.Cryptography.2.5.0\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="ClosedXML.Parser, Version=*******, Culture=neutral, PublicKeyToken=1d5f7376574c51ec, processorArchitecture=MSIL">
      <HintPath>packages\ClosedXML.Parser.1.2.0\lib\netstandard2.0\ClosedXML.Parser.dll</HintPath>
    </Reference>
    <Reference Include="CommunityToolkit.Mvvm, Version=8.2.0.0, Culture=neutral, PublicKeyToken=4aff67a105548ee2, processorArchitecture=MSIL">
      <HintPath>packages\CommunityToolkit.Mvvm.8.2.0\lib\netstandard2.0\CommunityToolkit.Mvvm.dll</HintPath>
    </Reference>
    <Reference Include="CsvHelper, Version=3*******, Culture=neutral, PublicKeyToken=8c4959082be5c823, processorArchitecture=MSIL">
      <HintPath>packages\CsvHelper.33.0.1\lib\net47\CsvHelper.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml, Version=3.0.1.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>packages\DocumentFormat.OpenXml.3.0.1\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="DocumentFormat.OpenXml.Framework, Version=3.0.1.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>packages\DocumentFormat.OpenXml.Framework.3.0.1\lib\net46\DocumentFormat.OpenXml.Framework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\EntityFramework.6.4.4\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\EntityFramework.6.4.4\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="ExcelNumberFormat, Version=1.1.0.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca, processorArchitecture=MSIL">
      <HintPath>packages\ExcelNumberFormat.1.1.0\lib\net20\ExcelNumberFormat.dll</HintPath>
    </Reference>
    <Reference Include="GongSolutions.WPF.DragDrop, Version=*******, Culture=neutral, PublicKeyToken=91f1945125b7a587, processorArchitecture=MSIL">
      <HintPath>packages\gong-wpf-dragdrop.3.2.1\lib\net47\GongSolutions.WPF.DragDrop.dll</HintPath>
    </Reference>
    <Reference Include="halcondotnet, Version=19.11.0.0, Culture=neutral, PublicKeyToken=4973bed59ddbf2b8, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\Game\halcon-21.11.0.0-x64-win64\repository\packages.mvtec.com\halcon\halcon-21.11-progress\halcon-21.11.0.0-runtime-x64-win64\bin\dotnet35\halcondotnet.dll</HintPath>
    </Reference>
    <Reference Include="HandyControl, Version=3.5.1.0, Culture=neutral, PublicKeyToken=45be8712787a1e5b, processorArchitecture=MSIL">
      <HintPath>packages\HandyControl.3.5.1\lib\net472\HandyControl.dll</HintPath>
    </Reference>
    <Reference Include="LiveCharts, Version=0.9.7.0, Culture=neutral, PublicKeyToken=0bc1f845d1ebb8df, processorArchitecture=MSIL">
      <HintPath>packages\LiveCharts.0.9.7\lib\net45\LiveCharts.dll</HintPath>
    </Reference>
    <Reference Include="LiveCharts.Wpf, Version=0.9.7.0, Culture=neutral, PublicKeyToken=0bc1f845d1ebb8df, processorArchitecture=MSIL">
      <HintPath>packages\LiveCharts.Wpf.0.9.7\lib\net45\LiveCharts.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Connections.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Connections.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Connections.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Hosting.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Hosting.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Hosting.Server.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Hosting.Server.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Http.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Http.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Extensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Http.Extensions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Http.Features, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Http.Features.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Http.Features.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Server.Kestrel, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Server.Kestrel.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Server.Kestrel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Server.Kestrel.Core, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Server.Kestrel.Core.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Server.Kestrel.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Server.Kestrel.Https, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Server.Kestrel.Https.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Server.Kestrel.Https.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.2.2.1\lib\netstandard2.0\Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNetCore.WebUtilities, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.AspNetCore.WebUtilities.2.2.0\lib\netstandard2.0\Microsoft.AspNetCore.WebUtilities.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.HashCode, Version=6.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.HashCode.6.0.0\lib\net462\Microsoft.Bcl.HashCode.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Configuration.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Configuration.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Configuration.Binder.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.EnvironmentVariables, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Configuration.EnvironmentVariables.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.FileExtensions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Configuration.FileExtensions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Configuration.FileExtensions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.DependencyInjection.2.2.0\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.DependencyInjection.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.FileProviders.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileProviders.Physical, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.FileProviders.Physical.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileProviders.Physical.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.FileSystemGlobbing, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.FileSystemGlobbing.2.2.0\lib\netstandard2.0\Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Hosting.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Hosting.Abstractions.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Hosting.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Logging.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Logging.Abstractions.6.0.4\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.ObjectPool, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.ObjectPool.2.2.0\lib\netstandard2.0\Microsoft.Extensions.ObjectPool.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Options.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Primitives.2.2.0\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Net.Http.Headers, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Net.Http.Headers.2.2.0\lib\netstandard2.0\Microsoft.Net.Http.Headers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Win32.Primitives.4.3.0\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="MvCameraControl.Net, Version=*******, Culture=neutral, PublicKeyToken=a3c7c5e3a730cd12, processorArchitecture=AMD64">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\IPM.Vision.Camera\DLL\MvCameraControl.Net.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>packages\NLog.5.3.4\lib\net46\NLog.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Bindings.Https, Version=1.4.365.0, Culture=neutral, PublicKeyToken=bfa7a73c5cf4b6e8, processorArchitecture=MSIL">
      <HintPath>packages\OPCFoundation.NetStandard.Opc.Ua.Bindings.Https.1.4.365.23\lib\net462\Opc.Ua.Bindings.Https.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Client, Version=1.5.374.0, Culture=neutral, PublicKeyToken=bfa7a73c5cf4b6e8, processorArchitecture=MSIL">
      <HintPath>packages\OPCFoundation.NetStandard.Opc.Ua.Client.1.5.374.158\lib\net472\Opc.Ua.Client.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Configuration, Version=1.5.374.0, Culture=neutral, PublicKeyToken=bfa7a73c5cf4b6e8, processorArchitecture=MSIL">
      <HintPath>packages\OPCFoundation.NetStandard.Opc.Ua.Configuration.1.5.374.158\lib\net472\Opc.Ua.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Core, Version=1.5.374.0, Culture=neutral, PublicKeyToken=bfa7a73c5cf4b6e8, processorArchitecture=MSIL">
      <HintPath>packages\OPCFoundation.NetStandard.Opc.Ua.Core.1.5.374.158\lib\net472\Opc.Ua.Core.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Gds.Client.Common, Version=1.5.374.0, Culture=neutral, PublicKeyToken=bfa7a73c5cf4b6e8, processorArchitecture=MSIL">
      <HintPath>packages\OPCFoundation.NetStandard.Opc.Ua.Gds.Client.Common.1.5.374.158\lib\net472\Opc.Ua.Gds.Client.Common.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Gds.Server.Common, Version=1.5.374.0, Culture=neutral, PublicKeyToken=bfa7a73c5cf4b6e8, processorArchitecture=MSIL">
      <HintPath>packages\OPCFoundation.NetStandard.Opc.Ua.Gds.Server.Common.1.5.374.158\lib\net472\Opc.Ua.Gds.Server.Common.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Security.Certificates, Version=1.5.374.0, Culture=neutral, PublicKeyToken=bfa7a73c5cf4b6e8, processorArchitecture=MSIL">
      <HintPath>packages\OPCFoundation.NetStandard.Opc.Ua.Security.Certificates.1.5.374.158\lib\net472\Opc.Ua.Security.Certificates.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Server, Version=1.5.374.0, Culture=neutral, PublicKeyToken=bfa7a73c5cf4b6e8, processorArchitecture=MSIL">
      <HintPath>packages\OPCFoundation.NetStandard.Opc.Ua.Server.1.5.374.158\lib\net472\Opc.Ua.Server.dll</HintPath>
    </Reference>
    <Reference Include="OpcUaHelper, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\OpcUaHelper.2.2.1\lib\net461\OpcUaHelper.dll</HintPath>
    </Reference>
    <Reference Include="OPTSDK_Net">
      <HintPath>..\IPM.Vision.Camera\DLL\OPTSDK_Net.dll</HintPath>
    </Reference>
    <Reference Include="RBush, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\RBush.3.2.0\lib\netstandard1.2\RBush.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.Fonts, Version=*******, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>packages\SixLabors.Fonts.1.0.0\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="SqlSugar, Version=*********, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>packages\SqlSugar.*********\lib\SqlSugar.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.AppContext, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.AppContext.4.3.0\lib\net463\System.AppContext.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=1.2.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Collections.Immutable.1.5.0\lib\netstandard2.0\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Console, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Console.4.3.0\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite.EF6, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>packages\System.Data.SQLite.EF6.1.0.118.0\lib\net46\System.Data.SQLite.EF6.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite.Linq, Version=1.0.118.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>packages\System.Data.SQLite.Linq.1.0.118.0\lib\net46\System.Data.SQLite.Linq.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=6.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.DiagnosticSource.6.0.1\lib\net461\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.Tracing.4.3.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Formats.Asn1, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Formats.Asn1.8.0.1\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Globalization.Calendars.4.3.0\lib\net46\System.Globalization.Calendars.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Compression.ZipFile.4.3.0\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Packaging.8.0.0\lib\net462\System.IO.Packaging.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Pipelines.4.5.2\lib\netstandard2.0\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Ports, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Ports.8.0.0\lib\net462\System.IO.Ports.dll</HintPath>
    </Reference>
    <Reference Include="System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Linq.4.3.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Linq.Expressions.4.3.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Net.Http.4.3.0\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Reflection.Metadata, Version=1.4.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Reflection.Metadata.1.6.0\lib\netstandard2.0\System.Reflection.Metadata.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.4.3.0\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.Extensions.4.3.0\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.InteropServices.4.3.0\lib\net463\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Cng, Version=4.3.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Cng.4.5.0\lib\net47\System.Security.Cryptography.Cng.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encodings.Web, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.Encodings.Web.4.5.0\lib\netstandard2.0\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Text.RegularExpressions.4.3.0\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Xml.ReaderWriter.4.3.0\lib\net46\System.Xml.ReaderWriter.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="WindowsFormsIntegration" />
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Compile Include="BLL\APIServices.cs" />
    <Compile Include="BLL\BarcodeRuleService.cs" />
    <Compile Include="BLL\BaseService.cs" />
    <Compile Include="BLL\CheckService.cs" />
    <Compile Include="BLL\CompInfoService.cs" />
    <Compile Include="BLL\CompService.cs" />
    <Compile Include="BLL\EquipmentController.cs" />
    <Compile Include="BLL\EquipmentService.cs" />
    <Compile Include="BLL\FocusPointService.cs" />
    <Compile Include="BLL\GlobalCameraManager.cs" />
    <Compile Include="BLL\HKVisionService.cs" />
    <Compile Include="BLL\interfaces\IBarcodeRuleService.cs" />
    <Compile Include="BLL\interfaces\ICompInfoService.cs" />
    <Compile Include="BLL\interfaces\ICompService.cs" />
    <Compile Include="BLL\interfaces\IEquipmentService.cs" />
    <Compile Include="BLL\interfaces\IFocusPointService.cs" />
    <Compile Include="BLL\interfaces\ILightParamService.cs" />
    <Compile Include="BLL\interfaces\IMainCameraParaService.cs" />
    <Compile Include="BLL\interfaces\IProcessParaService.cs" />
    <Compile Include="BLL\interfaces\IProductParaService.cs" />
    <Compile Include="BLL\interfaces\IReportService.cs" />
    <Compile Include="BLL\interfaces\IService.cs" />
    <Compile Include="BLL\interfaces\IUserService.cs" />
    <Compile Include="BLL\LightParamService.cs" />
    <Compile Include="BLL\LoginService.cs" />
    <Compile Include="BLL\MainCameraParaService.cs" />
    <Compile Include="BLL\MESHelper.cs" />
    <Compile Include="BLL\OptVisionService.cs" />
    <Compile Include="BLL\ProcessParaService.cs" />
    <Compile Include="BLL\ProductParaService.cs" />
    <Compile Include="BLL\ReportService.cs" />
    <Compile Include="BLL\UserService.cs" />
    <Compile Include="BLL\WeightService.cs" />
    <Compile Include="Common\CommEnum.cs" />
    <Compile Include="Common\Converts\BooleanInvertConverter.cs" />
    <Compile Include="Common\Converts\BoolToVisiblityConvert.cs" />
    <Compile Include="Common\Converts\CameraToVisiable.cs" />
    <Compile Include="Common\Converts\CameraTypeConvert.cs" />
    <Compile Include="Common\Converts\CheckValueConvert.cs" />
    <Compile Include="Common\Converts\ContentToVisiableConvert.cs" />
    <Compile Include="Common\Converts\EnumToVisibleConvert.cs" />
    <Compile Include="Common\Converts\HalfConverter.cs" />
    <Compile Include="Common\Converts\ListToStringConverter.cs" />
    <Compile Include="Common\Converts\NullToStringConvert.cs" />
    <Compile Include="Common\Converts\NullToVisibilityConverter.cs" />
    <Compile Include="Common\Converts\Number2VisibilityConverter.cs" />
    <Compile Include="Common\Converts\StorageVisiableConvert.cs" />
    <Compile Include="Common\Converts\TakePictureTypeConvert.cs" />
    <Compile Include="Common\Converts\UriToBitmapConverter.cs" />
    <Compile Include="Common\Converts\UsbAndPortVisiblityConvert.cs" />
    <Compile Include="Common\DBCommon\AppDbContext.cs" />
    <Compile Include="Common\DBCommon\IBaseRepository.cs" />
    <Compile Include="Common\DBCommon\IDbContext.cs" />
    <Compile Include="Common\DBCommon\RepositoryBase.cs" />
    <Compile Include="Common\FileHelper.cs" />
    <Compile Include="Common\ImageHelper.cs" />
    <Compile Include="Common\JsonHelper.cs" />
    <Compile Include="Common\JsonPropertyContractResolver.cs" />
    <Compile Include="Common\OPCHelper\HOpcEvents.cs" />
    <Compile Include="Common\OPCHelper\OpcEventArgs.cs" />
    <Compile Include="Common\OPCHelper\OpcUAHelper.cs" />
    <Compile Include="Common\RestHelper.cs" />
    <Compile Include="Common\SerialPortHelper.cs" />
    <Compile Include="Common\ExcelHelper.cs" />
    <Compile Include="DAL\BarcodeRuleRepository.cs" />
    <Compile Include="DAL\CompInfoRepository.cs" />
    <Compile Include="DAL\CompRepository.cs" />
    <Compile Include="DAL\EquipmentRepository.cs" />
    <Compile Include="DAL\FocusPointRepository.cs" />
    <Compile Include="DAL\Interfaces\IBarcodeRuleRepository.cs" />
    <Compile Include="DAL\Interfaces\ICompInfoRepository.cs" />
    <Compile Include="DAL\Interfaces\ICompRepository.cs" />
    <Compile Include="DAL\Interfaces\IFocusPointRepository.cs" />
    <Compile Include="DAL\Interfaces\ILightParaRepository.cs" />
    <Compile Include="DAL\Interfaces\IMainCameraParaRepository.cs" />
    <Compile Include="DAL\Interfaces\IEquipmentRepository.cs" />
    <Compile Include="DAL\Interfaces\IProcessParaRepository.cs" />
    <Compile Include="DAL\Interfaces\IProductParaRepository.cs" />
    <Compile Include="DAL\Interfaces\IReportRepository.cs" />
    <Compile Include="DAL\Interfaces\IUserRepository.cs" />
    <Compile Include="DAL\LightParaRepository.cs" />
    <Compile Include="DAL\MainCameraParaRepository.cs" />
    <Compile Include="DAL\ProcessParaRepository.cs" />
    <Compile Include="DAL\ProductParaRepository.cs" />
    <Compile Include="DAL\ReportRepository.cs" />
    <Compile Include="DAL\UserRepository.cs" />
    <Compile Include="GlobalConstants.cs" />
    <Compile Include="Layout\MenuControl.xaml.cs">
      <DependentUpon>MenuControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="LEvents\HDelegates.cs" />
    <Compile Include="LEvents\HEquipmentStatusArgs.cs" />
    <Compile Include="LEvents\HEventCode.cs" />
    <Compile Include="LEvents\HObservable.cs" />
    <Compile Include="LEvents\IHObservable.cs" />
    <Compile Include="Mappers\AutoMapperConfiguration.cs" />
    <Compile Include="Mappers\MappingExtensions.cs" />
    <Compile Include="Model\BarcodeRuleModel.cs" />
    <Compile Include="Model\BasicModel.cs" />
    <Compile Include="Model\CompInfoModel.cs" />
    <Compile Include="Model\CompPointModel.cs" />
    <Compile Include="Model\ConfigModel.cs" />
    <Compile Include="Model\CustomNameModel.cs" />
    <Compile Include="Model\EnumItem.cs" />
    <Compile Include="Model\EquipmentInfoModel.cs" />
    <Compile Include="Model\EquipmentModel.cs" />
    <Compile Include="Model\FocusPointModel.cs" />
    <Compile Include="Model\LightParamModel.cs" />
    <Compile Include="Model\MainCameraParamModel.cs" />
    <Compile Include="Model\MarkCameraParamModel.cs" />
    <Compile Include="Model\MenuModel.cs" />
    <Compile Include="Model\OpcNodeModel.cs" />
    <Compile Include="Model\PcbPlaceModel.cs" />
    <Compile Include="Model\ProcessModel.cs" />
    <Compile Include="Model\ProductParamModel.cs" />
    <Compile Include="Model\ReportModel.cs" />
    <Compile Include="Model\RestHelperModel.cs" />
    <Compile Include="Model\RestModel.cs" />
    <Compile Include="Model\UserModel.cs" />
    <Compile Include="ViewModel\CustomControls\CameraCtrlControlViewModel.cs" />
    <Compile Include="ViewModel\CustomControls\CaptureViewModel.cs" />
    <Compile Include="ViewModel\CustomControls\EquipmentCtrlControlViewModel.cs" />
    <Compile Include="ViewModel\CustomControls\HKVisionControlViewModel.cs" />
    <Compile Include="ViewModel\CustomControls\InfoControlViewModel.cs" />
    <Compile Include="ViewModel\CustomControls\LightCtrlControlViewModel.cs" />
    <Compile Include="ViewModel\CustomControls\MenuControlViewModel.cs" />
    <Compile Include="ViewModel\CustomControls\CanonEvfBoxViewModel.cs" />
    <Compile Include="ViewModel\CustomControls\OptCameraControlViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\BarcodeRuleDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\CameraCtrlDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\CompInfoDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\EquipmentCtrlDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\ImportPinCoordinatesDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\LightCtrlDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\ProcessManageDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\ProductChangedDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\ProductManageDialogViewModel.cs" />
    <Compile Include="ViewModel\Dialogs\UserManageDialogViewModel.cs" />
    <Compile Include="ViewModel\Layout\MenuControlViewModel.cs" />
    <Compile Include="ViewModel\LoginWindowViewModel.cs" />
    <Compile Include="ViewModel\MainWindowViewModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableBarcodeRuleModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableCompModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableCompPointModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableConfigModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableCustomNameModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableEquipmentModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableFocusPointModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableFocusPosition.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableGlobalState.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableLightModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableLogModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableMainCameraParaModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservablePinCoordinateModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservablePinPosition.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableProductModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableProcessModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableReportModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableUserInfoModel.cs" />
    <Compile Include="ViewModel\ObservableModel\ObservableUserModel.cs" />
    <Compile Include="ViewModel\Pages\BarcodeConfigPageViewModel.cs" />
    <Compile Include="ViewModel\Pages\CameraControlViewModel.cs" />
    <Compile Include="ViewModel\Pages\EquipmentControlViewModel.cs" />
    <Compile Include="ViewModel\Pages\EquipmentManageViewModel.cs" />
    <Compile Include="ViewModel\Pages\LightControlViewModel.cs" />
    <Compile Include="ViewModel\Pages\ManualReviewPageViewModel.cs" />
    <Compile Include="ViewModel\Pages\ProductManagePageViewModel.cs" />
    <Compile Include="ViewModel\Pages\ReportPageViewModel.cs" />
    <Compile Include="ViewModel\Pages\SettingPageViewModel.cs" />
    <Compile Include="ViewModel\Pages\UserManageViewModel.cs" />
    <Compile Include="ViewModel\Pages\VisionPageViewModel.cs" />
    <Compile Include="ViewModel\TestWindowViewModel.cs" />
    <Compile Include="ViewModel\ViewModelBase.cs" />
    <Compile Include="ViewModel\ViewModelLocator.cs" />
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Common\NLogHelper.cs" />
    <Compile Include="Views\CanonControls\AeComboBox.cs" />
    <Compile Include="Views\CanonControls\AfModeComboBox.cs" />
    <Compile Include="Views\CanonControls\AvComboBox.cs" />
    <Compile Include="Views\CanonControls\CanonEvfBox.xaml.cs">
      <DependentUpon>CanonEvfBox.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CanonControls\DownloadProgress.cs" />
    <Compile Include="Views\CanonControls\DriveModeComboBox.cs" />
    <Compile Include="Views\CanonControls\EvfAfModeComboBox.cs" />
    <Compile Include="Views\CanonControls\ExposureCompComboBox.cs" />
    <Compile Include="Views\CanonControls\ImageQualityComboBox.cs" />
    <Compile Include="Views\CanonControls\IsoComboBox.cs" />
    <Compile Include="Views\CanonControls\LDownLoadProgress.cs" />
    <Compile Include="Views\CanonControls\PictureStyleComboBox.cs" />
    <Compile Include="Views\CanonControls\PropertyComboBox.cs" />
    <Compile Include="Views\CanonControls\TvComboBox.cs" />
    <Compile Include="Views\CanonControls\WhiteBalanceComboBox.cs" />
    <Compile Include="Views\CustomControls\CameraCtrlControl.xaml.cs">
      <DependentUpon>CameraCtrlControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomControls\CaptureView.xaml.cs">
      <DependentUpon>CaptureView.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomControls\DownloadProgress.cs" />
    <Compile Include="Views\CustomControls\EquipmentCtrlControl.xaml.cs">
      <DependentUpon>EquipmentCtrlControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomControls\HKCameraControl.xaml.cs">
      <DependentUpon>HKCameraControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomControls\InfoControl.xaml.cs">
      <DependentUpon>InfoControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomControls\LightCtrlControl.xaml.cs">
      <DependentUpon>LightCtrlControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomControls\LogInfoControl.xaml.cs">
      <DependentUpon>LogInfoControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\CustomControls\OPTCameraControl.xaml.cs">
      <DependentUpon>OPTCameraControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\BarcodeRuleDoialog.xaml.cs">
      <DependentUpon>BarcodeRuleDoialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\CameraCtrlDialog.xaml.cs">
      <DependentUpon>CameraCtrlDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\ChangeProductDialog.xaml.cs">
      <DependentUpon>ChangeProductDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\CompInfoDialog.xaml.cs">
      <DependentUpon>CompInfoDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\DefineDialogContainer.xaml.cs">
      <DependentUpon>DefineDialogContainer.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\EquipmentCtrlDialog.xaml.cs">
      <DependentUpon>EquipmentCtrlDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\ImportPinCoordinatesDialog.xaml.cs">
      <DependentUpon>ImportPinCoordinatesDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\LightCtrlDialog.xaml.cs">
      <DependentUpon>LightCtrlDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\ProcessManageDialog.xaml.cs">
      <DependentUpon>ProcessManageDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\ProductChangedDialog.xaml.cs">
      <DependentUpon>ProductChangedDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\ProductManageDialog.xaml.cs">
      <DependentUpon>ProductManageDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\UserManageDialog.xaml.cs">
      <DependentUpon>UserManageDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Dialogs\WaterPositionDialog.xaml.cs">
      <DependentUpon>WaterPositionDialog.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\LoginWindow.xaml.cs">
      <DependentUpon>LoginWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\BarcodeConfigPage.xaml.cs">
      <DependentUpon>BarcodeConfigPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\CameraControl.xaml.cs">
      <DependentUpon>CameraControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\EquipmentControl.xaml.cs">
      <DependentUpon>EquipmentControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\EquipmentManagePage.xaml.cs">
      <DependentUpon>EquipmentManagePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\LightControl.xaml.cs">
      <DependentUpon>LightControl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\ManualReviewPage.xaml.cs">
      <DependentUpon>ManualReviewPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\ProcessCtrl.xaml.cs">
      <DependentUpon>ProcessCtrl.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\ProductManagePage.xaml.cs">
      <DependentUpon>ProductManagePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\ReportPage.xaml.cs">
      <DependentUpon>ReportPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\SettingPage.xaml.cs">
      <DependentUpon>SettingPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\UserManagePage.xaml.cs">
      <DependentUpon>UserManagePage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\Pages\VisionPage.xaml.cs">
      <DependentUpon>VisionPage.xaml</DependentUpon>
    </Compile>
    <Compile Include="Views\TestWindow.xaml.cs">
      <DependentUpon>TestWindow.xaml</DependentUpon>
    </Compile>
    <Page Include="Layout\MenuControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Styles\BaseStyle.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Styles\FontResource.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\CanonControls\CanonEvfBox.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CustomControls\CameraCtrlControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CustomControls\CaptureView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CustomControls\EquipmentCtrlControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CustomControls\HKCameraControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CustomControls\InfoControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CustomControls\LightCtrlControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CustomControls\LogInfoControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\CustomControls\OPTCameraControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\BarcodeRuleDoialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\CameraCtrlDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\ChangeProductDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\CompInfoDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\DefineDialogContainer.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\EquipmentCtrlDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\ImportPinCoordinatesDialog.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\Dialogs\LightCtrlDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\ProcessManageDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\ProductChangedDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\ProductManageDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\UserManageDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Dialogs\WaterPositionDialog.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\LoginWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\MainWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\BarcodeConfigPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\CameraControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\EquipmentControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\EquipmentManagePage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\LightControl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\ManualReviewPage.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\Pages\ProcessCtrl.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\ProductManagePage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\ReportPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\SettingPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\UserManagePage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\Pages\VisionPage.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\TestWindow.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <Resource Include="Assets\Icon\fontawesome-webfont.ttf">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <None Include="config\menu.lof">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="nlog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="config\opc_config.lof">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="packages.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\IPM.Vision.Camera\IPM.Vision.Camera.csproj">
      <Project>{9a4e650c-f4e4-4248-86ba-8c767e856779}</Project>
      <Name>IPM.Vision.Camera</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\404.png" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\login-bg.jpg">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
    <Resource Include="Assets\logo.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="icon.ico" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\noimg.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\logo.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Resource Include="logo.ico" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\login.jpg">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="OPTSDK_Net.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Resource Include="OPTSDK_Net.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="packages\CommunityToolkit.Mvvm.8.2.0\build\netstandard2.0\CommunityToolkit.Mvvm.targets" Condition="Exists('packages\CommunityToolkit.Mvvm.8.2.0\build\netstandard2.0\CommunityToolkit.Mvvm.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>这台计算机上缺少此项目引用的 NuGet 程序包。使用"NuGet 程序包还原"可下载这些程序包。有关更多信息，请参见 http://go.microsoft.com/fwlink/?LinkID=322105。缺少的文件是 {0}。</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('packages\CommunityToolkit.Mvvm.8.2.0\build\netstandard2.0\CommunityToolkit.Mvvm.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\CommunityToolkit.Mvvm.8.2.0\build\netstandard2.0\CommunityToolkit.Mvvm.targets'))" />
    <Error Condition="!Exists('packages\EntityFramework.6.4.4\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\EntityFramework.6.4.4\build\EntityFramework.props'))" />
    <Error Condition="!Exists('packages\EntityFramework.6.4.4\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\EntityFramework.6.4.4\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
  </Target>
  <Import Project="packages\EntityFramework.6.4.4\build\EntityFramework.targets" Condition="Exists('packages\EntityFramework.6.4.4\build\EntityFramework.targets')" />
  <Import Project="packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.118.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
</Project>