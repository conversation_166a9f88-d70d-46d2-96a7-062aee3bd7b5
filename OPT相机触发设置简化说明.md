# OPT相机触发设置简化说明

## 需求描述

用户要求自动拍摄模式下OPT相机不要设置乱七八糟的属性，就按默认属性来，打开触发设置成帧触发就可以了。

## 修改内容

### 简化前的setLineTriggerConf方法

原来的方法包含了大量复杂的优化设置：
- 帧数据缓存个数优化（从8增加到16）
- GigE网络传输优化（包大小设置为1500字节）
- 包间隔时间设置（50ms）
- 各种高频触发优化参数

### 简化后的setLineTriggerConf方法

现在只保留最基本的帧触发设置：

```csharp
public bool setLineTriggerConf()
{
    try
    {
        AddLog("正在设置OPT相机线触发模式...");

        // 清空连续采集的残留帧
        AddLog("清空连续采集的残留帧...");
        ClearFrameQueue();

        // 等待实时画面完全停止
        AddLog("等待实时画面完全停止...");
        Thread.Sleep(300);

        // 启用触发模式
        var result = _camera.OPT_SetEnumFeatureSymbol("TriggerMode", "On");
        if (result != OPTDefine.OPT_OK)
        {
            AddLog($"启用触发模式失败，错误码: {result}", true);
            return false;
        }

        // 设置触发源为Line1（硬件触发）
        result = _camera.OPT_SetEnumFeatureSymbol("TriggerSource", "Line1");
        if (result != OPTDefine.OPT_OK)
        {
            AddLog($"设置触发源失败，错误码: {result}", true);
            return false;
        }

        // 设置触发选择器为帧开始
        result = _camera.OPT_SetEnumFeatureSymbol("TriggerSelector", "FrameStart");
        if (result != OPTDefine.OPT_OK)
        {
            AddLog($"设置触发选择器失败，错误码: {result}", true);
            return false;
        }

        AddLog("✅ OPT相机线触发模式设置完成");
        return true;
    }
    catch (Exception ex)
    {
        AddLog($"设置线触发配置时异常: {ex.Message}", true);
        return false;
    }
}
```

## 保留的核心设置

### 1. 基本触发配置
- **TriggerMode**: "On" - 启用触发模式
- **TriggerSource**: "Line1" - 设置为硬件触发（Line1）
- **TriggerSelector**: "FrameStart" - 设置为帧开始触发

### 2. 时序控制
- **ClearFrameQueue()**: 清空连续采集的残留帧
- **Thread.Sleep(300)**: 等待实时画面完全停止

## 删除的复杂设置

### 1. 缓冲区优化
- ~~OPT_SetBufferCount(16)~~ - 帧数据缓存个数设置
- ~~高频触发缓冲区优化~~

### 2. 网络传输优化
- ~~GevSCPSPacketSize设置~~ - 网络包大小优化
- ~~OPT_GIGE_SetInterPacketTimeout~~ - 包间隔时间设置
- ~~网络传输效率优化~~

### 3. 高频触发优化
- ~~10ms高频触发专用优化~~
- ~~网络拥塞防护设置~~
- ~~传输性能调优参数~~

## 简化的优点

### 1. 稳定性提升
- 减少了复杂参数设置可能引起的兼容性问题
- 使用相机默认属性，更加稳定可靠

### 2. 维护性改善
- 代码更简洁，易于理解和维护
- 减少了调试复杂度

### 3. 通用性增强
- 不依赖特定的网络环境或硬件配置
- 适用于更多不同的使用场景

## 功能保证

简化后的设置仍然保证：
- ✅ 正确的帧触发功能
- ✅ 硬件触发信号响应
- ✅ 时序控制和帧清理
- ✅ 异常处理和错误返回

## 注意事项

1. **默认属性使用**: 相机将使用SDK的默认缓冲区大小和网络设置
2. **性能影响**: 在极高频触发场景下可能需要根据实际情况调整
3. **兼容性**: 简化后的设置具有更好的通用兼容性

## 测试建议

1. **基本功能测试**: 验证帧触发功能是否正常工作
2. **稳定性测试**: 长时间运行测试，确认稳定性
3. **性能测试**: 在实际使用频率下测试性能表现
4. **兼容性测试**: 在不同网络环境下测试兼容性
