﻿using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Common
{
    public class SerialPortHelper : IDisposable
    {
        private SerialPort _serialPort;
        public event Action<string> NotifyDataEvent;
        private static StringBuilder _receivedData = new StringBuilder();
        public SerialPortHelper(string portName, int BaudRate = 19200)
        {
            this._serialPort = new SerialPort(portName);
            this._serialPort.BaudRate = BaudRate;
            this._serialPort.DataBits = 8;
            this._serialPort.StopBits = StopBits.One;
            this._serialPort.Parity = Parity.None;
            _serialPort.DataReceived += _serialPort_DataReceived;
        }

        private void _serialPort_DataReceived(object sender, SerialDataReceivedEventArgs e)
        {
            string data = _serialPort.ReadExisting();
            _receivedData.Append(data);
            if (_receivedData.ToString().Contains("\n"))
            {
                string completeMessage = _receivedData.ToString();
                try
                {
                    double datas = Convert.ToDouble(completeMessage);
                    if (NotifyDataEvent != null) NotifyDataEvent(datas.ToString());
                }
                catch (Exception ex)
                {

                }
                _receivedData.Clear();
            }
        }

        public void Open()
        {
            if (this._serialPort.IsOpen)
                return;
            this._serialPort.Open();
        }

        public void Close()
        {
            if (!this._serialPort.IsOpen)
                return;
            this._serialPort.Close();
        }

        public void Write(string data)
        {
            try
            {
                if (!this._serialPort.IsOpen)
                    return;
                byte[] bytes = Encoding.ASCII.GetBytes(data);
                this._serialPort.Write(bytes, 0, bytes.Length);
            }
            catch (Exception ex)
            {
            }
        }

        public string ReadLine()
        {
            if (!this._serialPort.IsOpen)
                return (string)null;
            byte[] numArray = new byte[1024];
            int count = this._serialPort.Read(numArray, 0, numArray.Length);
            return Encoding.ASCII.GetString(numArray, 0, count);
        }

        public string ReadLineUTF8()
        {
            if (!this._serialPort.IsOpen)
                return (string)null;
            byte[] numArray = new byte[1024];
            int count = this._serialPort.Read(numArray, 0, numArray.Length);
            return Encoding.UTF8.GetString(numArray, 0, count);
        }

        public async Task WriteAsync(string data)
        {
            if (!this._serialPort.IsOpen)
                return;
            byte[] asciiData = Encoding.ASCII.GetBytes(data);
            await this._serialPort.BaseStream.WriteAsync(asciiData, 0, asciiData.Length);
            asciiData = (byte[])null;
        }

        public async Task<string> ReadLineAsync()
        {
            if (!this._serialPort.IsOpen)
                return (string)null;
            byte[] buffer = new byte[1024];
            int bytesRead = await this._serialPort.BaseStream.ReadAsync(buffer, 0, buffer.Length);
            return Encoding.ASCII.GetString(buffer, 0, bytesRead);
        }

        public void Dispose()
        {
            this.Close();
            this._serialPort.Dispose();
        }
    }
}
