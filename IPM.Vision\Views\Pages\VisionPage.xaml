﻿<Page
    x:Class="IPM.Vision.Views.Pages.VisionPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="clr-namespace:IPM.Vision.Views.CustomControls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:hc="https://handyorg.github.io/handycontrol"
    xmlns:local="clr-namespace:IPM.Vision.Views.Pages"
    xmlns:common="clr-namespace:IPM.Vision.Common"
    xmlns:lvc="clr-namespace:LiveCharts.Wpf;assembly=LiveCharts.Wpf"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"

    Title="VisionPage"
    d:DesignHeight="950"
    d:DesignWidth="1500"
    DataContext="{Binding VisionPageViewModel, Source={StaticResource Locator}}"
    mc:Ignorable="d">
    

    
    <hc:Interaction.Triggers>
        <hc:EventTrigger EventName="Loaded">
            <hc:EventToCommand Command="{Binding LoadCommand}" />
        </hc:EventTrigger>
        <hc:EventTrigger EventName="Unloaded">
            <hc:EventToCommand Command="{Binding UnLoadCommand}" />
        </hc:EventTrigger>
    </hc:Interaction.Triggers>
    <Border>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="1.5*" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            
            <!-- 移除遮罩层，只使用按钮的enabled属性控制 -->
            <!--  左半边  -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="400" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  产品切换  -->
                <Border
                    Grid.Row="0"
                    BorderBrush="Gray"
                    BorderThickness="0.3"
                    CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            Background="#283643"
                            CornerRadius="5 5 0 0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <hc:SimpleText
                                    Grid.Column="0"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Foreground="White"
                                    Text="产品" />
                            </Grid>
                        </Border>
                        <Border Grid.Row="1" Padding="10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="Auto" />
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Grid Grid.Row="0">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*" />
                                        <ColumnDefinition Width="Auto" />
                                    </Grid.ColumnDefinitions>
                                    <hc:TextBox
                                        Grid.Column="0"
                                        Margin="0,0,20,0"
                                        hc:TitleElement.Title="产品代号"
                                        hc:TitleElement.TitlePlacement="Left"
                                        BorderBrush="Black"
                                        FontWeight="Bold"
                                        Foreground="Black"
                                        IsEnabled="False"
                                        Text="{Binding CurrentProductModel.ProductNumber}" />
                                    <Button
                                        Grid.Column="1"
                                        Command="{Binding ChangedProductCommand}"
                                        Content="切换产品"
                                        Style="{StaticResource ButtonDashedPrimary}"
                                        IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}" />
                                </Grid>
                                <hc:TextBox
                                    Grid.Row="1"
                                    Margin="0,5,0,0"
                                    hc:TitleElement.Title="产品名称"
                                    hc:TitleElement.TitlePlacement="Left"
                                    BorderBrush="Black"
                                    FontWeight="Bold"
                                    Foreground="Black"
                                    IsEnabled="False"
                                    Text="{Binding CurrentProductModel.ProductName}" />

                            </Grid>
                        </Border>
                    </Grid>
                </Border>

                <!--  步骤  -->
                <Border
                    Grid.Row="1"
                    Margin="0,10,0,0"
                    BorderBrush="Gray"
                    BorderThickness="0.3"
                    CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            Background="#283643"
                            CornerRadius="5 5 0 0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <hc:UniformSpacingPanel
                                    Grid.Column="0"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    Orientation="Horizontal"
                                    Spacing="10">
                                    <hc:SimpleText
                                        VerticalAlignment="Center"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Foreground="White"
                                        Text="步骤" />
                                </hc:UniformSpacingPanel>
                            </Grid>
                        </Border>


                        <Border Grid.Row="1" Padding="2">
                            <DataGrid
                                Grid.Row="1"
                                Margin="2"
                                hc:DataGridAttach.ApplyDefaultStyle="False"
                                hc:DataGridAttach.CanUnselectAllWithBlankArea="False"
                                hc:DataGridAttach.ShowRowNumber="False"
                                hc:DataGridAttach.ShowSelectAllButton="False"
                                AutoGenerateColumns="False"
                                CanUserReorderColumns="False"
                                CellStyle="{StaticResource LDataGridTextCenterColumnStyle}"
                                ColumnHeaderStyle="{StaticResource LDataGridTextCenterHeaderStyle}"
                                HeadersVisibility="Column"
                                ItemsSource="{Binding ProcessDataList}"
                                RowHeaderWidth="60"
                                SelectedItem="{Binding CurrentProcess, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                SelectionMode="Single"
                                SelectionUnit="FullRow"
                                IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}"
                                >

                                <DataGrid.Style>
                                    <Style TargetType="DataGrid" BasedOn="{StaticResource DataGridBaseStyle}">
                                        <Style.Triggers>
                                            <!-- 当引脚位置列表为空或null时隐藏行详情 -->
                                            <DataTrigger Binding="{Binding CurrentProcess.PinPositions.Count}" Value="0">
                                                <Setter Property="RowDetailsVisibilityMode" Value="Collapsed"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding CurrentProcess.PinPositions}" Value="{x:Null}">
                                                <Setter Property="RowDetailsVisibilityMode" Value="Collapsed"/>
                                            </DataTrigger>
                                            <!-- 当当前步骤不是拍照类型时隐藏行详情 -->
                                            <DataTrigger Binding="{Binding CurrentProcess.ProcessType}" Value="{x:Static common:ProcessTypeEnum.POINT}">
                                                <Setter Property="RowDetailsVisibilityMode" Value="Collapsed"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding CurrentProcess.ProcessType}" Value="{x:Static common:ProcessTypeEnum.QRCODE}">
                                                <Setter Property="RowDetailsVisibilityMode" Value="Collapsed"/>
                                            </DataTrigger>
                                            <!-- 自动运行时的禁用样式 -->
                                            <DataTrigger Binding="{Binding IsEnabled, RelativeSource={RelativeSource Self}}" Value="False">
                                                <Setter Property="Opacity" Value="0.5"/>
                                                <Setter Property="Background" Value="#F0F0F0"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                        <Setter Property="RowDetailsVisibilityMode" Value="VisibleWhenSelected"/>
                                    </Style>
                                </DataGrid.Style>
                                
                                <hc:Interaction.Triggers>
                                    <hc:EventTrigger EventName="Loaded">
                                        <hc:EventToCommand Command="{Binding DataGridLoadedCommand}" PassEventArgsToCommand="True" />
                                    </hc:EventTrigger>
                                </hc:Interaction.Triggers>
                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow">
                                        <Style.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                                <!--  选中时的背景色  -->
                                            </Trigger>
                                        </Style.Triggers>
                                      
                                    </Style>
                                </DataGrid.RowStyle>

                                
                                

                                <DataGrid.RowDetailsTemplate>
                                    <DataTemplate>
                                        <Border Padding="10" Background="#F5F5F5">
                                            <Grid>

                                                <Grid.Style>
                                                    <Style TargetType="Grid">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding PinPositions.Count}" Value="0">
                                                                <Setter Property="Visibility" Value="Collapsed"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Grid.Style>

                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="Auto"/>
                                                    <RowDefinition Height="*"/>
                                                </Grid.RowDefinitions>

                                                <TextBlock Grid.Row="0" 
                                                           Text="双击行可将坐标发送到设备" 
                                                           FontSize="12" 
                                                           Foreground="#283643" 
                                                           Margin="0,0,0,5"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"/>
                                                
                                                <DataGrid Grid.Row="1"
                                                     ItemsSource="{Binding PinPositions}"
                                                     MaxHeight="240"
                                                     AutoGenerateColumns="False"
                                                     HeadersVisibility="Column"
                                                     BorderThickness="1"
                                                     BorderBrush="LightGray"
                                                     CanUserAddRows="False"
                                                     CanUserDeleteRows="False"
                                                     RowHeaderWidth="60"
                                                     IsReadOnly="False"
                                                     SelectedItem="{Binding DataContext.CurrentPinPosition,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=Page}, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                                     SelectionMode="Single"
                                                     Background="White"
                                                     IsEnabled="{Binding DataContext.IsRunning,RelativeSource={RelativeSource Mode=FindAncestor,AncestorType=Page}, Converter={StaticResource BooleanInvertConverter}}">



                                                    <DataGrid.Resources>
                                                        <Style TargetType="ScrollViewer">
                                                            <Setter Property="Template">
                                                                <Setter.Value>
                                                                    <ControlTemplate TargetType="ScrollViewer">
                                                                        <Grid>
                                                                            <Grid.RowDefinitions>
                                                                                <RowDefinition Height="Auto"/>
                                                                                <RowDefinition Height="*"/>
                                                                            </Grid.RowDefinitions>
                                                                            <ScrollContentPresenter Grid.Row="0" 
                                                                  Content="{TemplateBinding Content}"
                                                                  ContentTemplate="{TemplateBinding ContentTemplate}"/>
                                                                            <ScrollBar Grid.Row="1" 
                                                                                 Name="PART_VerticalScrollBar"
                                                                                 Orientation="Vertical"
                                                                                 Maximum="{TemplateBinding ScrollableHeight}"
                                                                                 Value="{TemplateBinding VerticalOffset}"
                                                                                 ViewportSize="{TemplateBinding ViewportHeight}"
                                                                                 Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"/>
                                                                        </Grid>
                                                                    </ControlTemplate>
                                                                </Setter.Value>
                                                            </Setter>
                                                        </Style>
                                                    </DataGrid.Resources>

                                                    <DataGrid.RowStyle>
                                                        <Style TargetType="DataGridRow">
                                                            <EventSetter Event="MouseDoubleClick" Handler="DataGridRow_MouseDoubleClick"/>
                                                            <Style.Triggers>
                                                                <Trigger Property="IsSelected" Value="True">
                                                                    <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                                                    <!--  选中时的背景色  -->
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </DataGrid.RowStyle>

                                                    <DataGrid.Columns>
                                                        <DataGridTextColumn Header="引脚编号"
                                                          Binding="{Binding Index}"
                                                          Width="80"
                                                          IsReadOnly="True"/>
                                                        <DataGridTextColumn Header="边"
                                                          Binding="{Binding EdgeName}"
                                                          Width="60"
                                                          IsReadOnly="True"/>
                                                        <DataGridTextColumn Header="X"
                                                          Binding="{Binding X, StringFormat=F3}"
                                                          Width="80"
                                                          IsReadOnly="True"/>
                                                        <DataGridTextColumn Header="Y"
                                                          Binding="{Binding Y, StringFormat=F3}"
                                                          Width="80"
                                                          IsReadOnly="True"/>
                                                        <DataGridTextColumn Header="Z"
                                                          Binding="{Binding Z, StringFormat=F3}"
                                                          Width="80"
                                                          IsReadOnly="True"/>
                                                        <DataGridTextColumn Header="R"
                                                          Binding="{Binding R, StringFormat=F3}"
                                                          Width="80"
                                                          IsReadOnly="True"/>
                                                        <DataGridTextColumn Header="T"
                                                          Binding="{Binding T, StringFormat=F3}"
                                                          Width="80"
                                                          IsReadOnly="True"/>
                                                    </DataGrid.Columns>

                                               
                                                    <DataGrid.CellStyle>
                                                        <Style TargetType="DataGridCell">
                                                            <Setter Property="Padding" Value="0" />
                                                            <Setter Property="Margin" Value="0" />
                                                            <Setter Property="VerticalAlignment" Value="Center" />
                                                            <Setter Property="HorizontalAlignment" Value="Center" />
                                                            <Style.Triggers>
                                                                <Trigger Property="IsSelected" Value="True">
                                                                    <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                                                    <Setter Property="BorderBrush" Value="Transparent" />
                                                                    <Setter Property="BorderThickness" Value="0" />
                                                                </Trigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </DataGrid.CellStyle>

                                                    <DataGrid.ColumnHeaderStyle>
                                                        <Style TargetType="DataGridColumnHeader">
                                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                                            <Setter Property="VerticalContentAlignment" Value="Center"/>
                                                            <Setter Property="Background" Value="#F5F5F5"/>
                                                            <Setter Property="Padding" Value="5"/>
                                                            <Setter Property="FontWeight" Value="Bold"/>
                                                        </Style>
                                                    </DataGrid.ColumnHeaderStyle>
                                                </DataGrid>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </DataGrid.RowDetailsTemplate>

                                <DataGrid.Columns>
                                    <DataGridTextColumn
                                        Binding="{Binding ProcessNumber}"
                                        CanUserSort="False"
                                        Header="步骤序号"
                                        IsReadOnly="True">
                                        <DataGridTextColumn.CellStyle>
                                            <Style TargetType="DataGridCell">
                                                <Setter Property="Padding" Value="0" />
                                                <Setter Property="Margin" Value="0" />
                                                <Setter Property="VerticalAlignment" Value="Center" />
                                                <Setter Property="HorizontalAlignment" Value="Center" />
                                                <!--  自定义列的单元格样式，保持背景色不变  -->
                                                <Style.Triggers>
                                                    <Trigger Property="IsSelected" Value="True">
                                                        <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                                        <Setter Property="BorderBrush" Value="Transparent" />
                                                        <Setter Property="BorderThickness" Value="0" />
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </DataGridTextColumn.CellStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn
                                        Width="*"
                                        Binding="{Binding ParamName}"
                                        CanUserSort="False"
                                        Header="步骤名称"
                                        IsReadOnly="True">
                                        <DataGridTextColumn.CellStyle>
                                            <Style TargetType="DataGridCell">
                                                <Setter Property="Padding" Value="0" />
                                                <Setter Property="Margin" Value="0" />
                                                <Setter Property="VerticalAlignment" Value="Center" />
                                                <Setter Property="HorizontalAlignment" Value="Center" />
                                                <!--  自定义列的单元格样式，保持背景色不变  -->
                                                <Style.Triggers>
                                                    <Trigger Property="IsSelected" Value="True">
                                                        <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                                        <Setter Property="BorderBrush" Value="Transparent" />
                                                        <Setter Property="BorderThickness" Value="0" />
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </DataGridTextColumn.CellStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn
                                        Width="*"
                                        Binding="{Binding ProcessType}"
                                        CanUserSort="False"
                                        Header="步骤类型"
                                        IsReadOnly="True">
                                        <DataGridTextColumn.CellStyle>
                                            <Style TargetType="DataGridCell">
                                                <Setter Property="Padding" Value="0" />
                                                <Setter Property="Margin" Value="0" />
                                                <Setter Property="VerticalAlignment" Value="Center" />
                                                <Setter Property="HorizontalAlignment" Value="Center" />
                                                <!--  自定义列的单元格样式，保持背景色不变  -->
                                                <Style.Triggers>
                                                    <Trigger Property="IsSelected" Value="True">
                                                        <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                                        <Setter Property="BorderBrush" Value="Transparent" />
                                                        <Setter Property="BorderThickness" Value="0" />
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </DataGridTextColumn.CellStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn
                                        Width="*"
                                        Binding="{Binding EquipmentPara.ParamName}"
                                        CanUserSort="False"
                                        Header="设备参数"
                                        IsReadOnly="True">
                                        <DataGridTextColumn.CellStyle>
                                            <Style TargetType="DataGridCell">
                                                <Setter Property="Padding" Value="0" />
                                                <Setter Property="Margin" Value="0" />
                                                <Setter Property="VerticalAlignment" Value="Center" />
                                                <Setter Property="HorizontalAlignment" Value="Center" />
                                                <!--  自定义列的单元格样式，保持背景色不变  -->
                                                <Style.Triggers>
                                                    <Trigger Property="IsSelected" Value="True">
                                                        <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                                        <Setter Property="BorderBrush" Value="Transparent" />
                                                        <Setter Property="BorderThickness" Value="0" />
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </DataGridTextColumn.CellStyle>
                                    </DataGridTextColumn>
                                    <!--<DataGridTextColumn Header="执行状态" Width="*" CanUserSort="False" IsReadOnly="True" Binding="{Binding LightModel.ParamName}">
                                    <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                    <Setter Property="Padding" Value="0" />
                                    <Setter Property="Margin" Value="0" />
                                    <Setter Property="VerticalAlignment" Value="Center" />
                                    <Setter Property="HorizontalAlignment" Value="Center" />
                                    -->
                                    <!--  自定义列的单元格样式，保持背景色不变  -->
                                    <!--
                                    <Style.Triggers>
                                    <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="{DynamicResource PrimaryBrush}" />
                                    <Setter Property="BorderBrush" Value="Transparent" />
                                    <Setter Property="BorderThickness" Value="0" />
                                    </Trigger>
                                    </Style.Triggers>
                                    </Style>
                                    </DataGridTextColumn.CellStyle>
                                    </DataGridTextColumn>-->
                                </DataGrid.Columns>
                            </DataGrid>
                        </Border>
                    </Grid>
                </Border>

                <!--  Mark相机  -->
                <Border
                    Grid.Row="2"
                    Margin="0,10,0,0"
                    BorderBrush="Gray"
                    BorderThickness="0.3"
                    CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            Background="#283643"
                            CornerRadius="5 5 0 0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <hc:SimpleText
                                    Grid.Column="0"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Foreground="White"
                                    Text="MARK" />
                                <!--<Button Content="设为原点"
                                Style="{StaticResource ButtonDashed.Small}"
                                Foreground="White"
                                Grid.Column="1"
                                Command="{Binding TakePositionCommand}"
                                HorizontalAlignment="Right"
                                Margin="0 0 5 0"/>-->
                            </Grid>
                        </Border>
                        <Border Grid.Row="1">
                            <controls:HKCameraControl />
                        </Border>
                    </Grid>
                </Border>
            </Grid>

            <!--  中间  -->
            <Grid Grid.Column="1" Margin="5,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="2*" />
                    <RowDefinition Height="1*" />
                </Grid.RowDefinitions>

                <Border Grid.Row="0" Margin="0,0,0,5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 相机连接状态警告 -->
                        <Border Grid.Row="0" 
                                Margin="0,0,0,10"
                                Background="#FFF3CD" 
                                BorderBrush="#FFEAA7" 
                                BorderThickness="1" 
                                CornerRadius="5" 
                                Padding="10,5"
                                Visibility="{Binding IsCameraConnecting, Converter={StaticResource Boolean2VisibilityConverter}}">
                            <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                                <TextBlock Text="⚠" FontSize="16" Foreground="#856404" VerticalAlignment="Center"/>
                                <TextBlock Text="相机正在连接中，部分功能可能受限..." 
                                           FontSize="12" 
                                           Foreground="#856404" 
                                           VerticalAlignment="Center"/>
                                <TextBlock Text="{Binding CameraConnectionStatus}" 
                                           FontSize="12" 
                                           FontWeight="Bold"
                                           Foreground="#856404" 
                                           VerticalAlignment="Center"/>
                            </hc:UniformSpacingPanel>
                        </Border>
                        
                        <!-- 控制按钮 -->
                        <hc:UniformSpacingPanel Grid.Row="1" Orientation="Horizontal" Spacing="10">
                            <Button
                                Width="85"
                                Height="45"
                                Command="{Binding BeginCommand}"
                                IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}"
                                Style="{StaticResource ButtonSuccess}">
                                <Button.Content>
                                    <hc:UniformSpacingPanel
                                        HorizontalAlignment="Center"
                                        Orientation="Vertical"
                                        Spacing="2">
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            FontFamily="{StaticResource FontAwesome}"
                                            FontSize="16"
                                            Text="&#xf04b;"
                                            TextAlignment="Center" />
                                        <TextBlock Text="开始" FontSize="11" HorizontalAlignment="Center" />
                                    </hc:UniformSpacingPanel>
                                </Button.Content>
                            </Button>
                            <Button
                                Width="85"
                                Height="45"
                                Command="{Binding ForceStopCommand}"
                                Style="{StaticResource ButtonWarning}"
                                ToolTip="紧急停止：强制重置运行状态">
                                
                                <Button.Content>
                                    <hc:UniformSpacingPanel
                                        HorizontalAlignment="Center"
                                        Orientation="Vertical"
                                        Spacing="2">
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            FontFamily="{StaticResource FontAwesome}"
                                            FontSize="16"
                                            Text="&#xf256;"
                                            TextAlignment="Center" />
                                        <TextBlock Text="强停" FontSize="11" HorizontalAlignment="Center" />
                                    </hc:UniformSpacingPanel>
                                </Button.Content>
                            </Button>
                            <Button
                                Width="85"
                                Height="45"
                                Command="{Binding TakePictureCommand}"
                                IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}"
                                Style="{StaticResource ButtonPrimary}">
                                <Button.Content>
                                    <hc:UniformSpacingPanel
                                        HorizontalAlignment="Center"
                                        Orientation="Vertical"
                                        Spacing="2">
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            FontFamily="{StaticResource FontAwesome}"
                                            FontSize="16"
                                            Text="&#xf030;"
                                            TextAlignment="Center" />
                                        <TextBlock Text="拍照" FontSize="11" HorizontalAlignment="Center" />
                                    </hc:UniformSpacingPanel>
                                </Button.Content>
                            </Button>
                            <Button
                                Width="85"
                                Height="45"
                                Command="{Binding ResetErrorCommand}"
                                IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}"
                                Style="{StaticResource ButtonDanger}">
                                <Button.Content>
                                    <hc:UniformSpacingPanel
                                        HorizontalAlignment="Center"
                                        Orientation="Vertical"
                                        Spacing="2">
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            FontFamily="{StaticResource FontAwesome}"
                                            FontSize="16"
                                            Text="&#xf071;"
                                            TextAlignment="Center" />
                                        <TextBlock Text="清警" FontSize="11" HorizontalAlignment="Center" />
                                    </hc:UniformSpacingPanel>
                                </Button.Content>
                            </Button>
                            <Button
                                Width="85"
                                Height="45"
                                Command="{Binding RestAllCommand}"
                                IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}"
                                Style="{StaticResource ButtonInfo}">
                                <Button.Content>
                                    <hc:UniformSpacingPanel
                                        HorizontalAlignment="Center"
                                        Orientation="Vertical"
                                        Spacing="2">
                                        <TextBlock
                                            HorizontalAlignment="Center"
                                            FontFamily="{StaticResource FontAwesome}"
                                            FontSize="16"
                                            Text="&#xf021;"
                                            TextAlignment="Center" />
                                        <TextBlock Text="重置" FontSize="11" HorizontalAlignment="Center" />
                                    </hc:UniformSpacingPanel>
                                </Button.Content>
                            </Button>

                            <!-- <Button
                                Width="85"
                                Height="45"
                                Command="{Binding OpenTestCommand}"
                                IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}"
                                Style="{StaticResource ButtonDashed}">
                                <Button.Content>
                                    <hc:UniformSpacingPanel
                                        HorizontalAlignment="Center"
                                        VerticalAlignment="Center"
                                        Orientation="Vertical"
                                        Spacing="2">
                                        <TextBlock
                                            Text="&#xf085;"
                                            FontFamily="{StaticResource FontAwesome}"
                                            FontSize="16"
                                            HorizontalAlignment="Center" />
                                        <TextBlock Text="测试" FontSize="11" HorizontalAlignment="Center" />
                                    </hc:UniformSpacingPanel>
                                </Button.Content>
                            </Button> -->

                            <!-- 是否检验复选框 -->
                            <CheckBox
                                IsChecked="{Binding IsDetectionEnabled}"
                                IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}"
                                VerticalAlignment="Center"
                                Margin="15,0,0,0"
                                FontSize="14"
                                FontWeight="Bold">
                                <CheckBox.Content>
                                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                                        <TextBlock 
                                            Text="&#xf06e;" 
                                            FontFamily="{StaticResource FontAwesome}" 
                                            FontSize="16" 
                                            HorizontalAlignment="Center"
                                            Foreground="#2563eb" />
                                        <TextBlock 
                                            Text="是否检验" 
                                            FontSize="11" 
                                            HorizontalAlignment="Center"
                                            Margin="0,2,0,0" />
                                    </StackPanel>
                                </CheckBox.Content>
                            </CheckBox>
                        </hc:UniformSpacingPanel>
                    </Grid>
                </Border>

                <Border
                    Grid.Row="1"
                    Margin="0,0,0,16"
                    BorderBrush="Gray"
                    BorderThickness="0.3"
                    CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            Background="#283643"
                            CornerRadius="5 5 0 0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <hc:SimpleText
                                    Grid.Column="0"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Foreground="White"
                                    Text="采集" />
                            </Grid>
                        </Border>

                        <Border Grid.Row="1" Margin="10,5,0,5">
                            <hc:UniformSpacingPanel Orientation="Horizontal" Spacing="10">
                                <hc:TextBox
                                    Width="320"
                                    hc:TitleElement.Title="产品编号"
                                    hc:TitleElement.TitlePlacement="Left"
                                    Text="{Binding SerialNumber, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                    IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}" />
                                <hc:TextBox
                                    Width="320"
                                    hc:TitleElement.Title="作业计划号"
                                    hc:TitleElement.TitlePlacement="Left"
                                    Text="{Binding OrderNumber, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                    IsEnabled="{Binding IsRunning, Converter={StaticResource BooleanInvertConverter}}" />
                            </hc:UniformSpacingPanel>
                        </Border>

                        <Border Grid.Row="2">
                            <controls:OPTCameraControl />
                        </Border>

                        <Border Grid.Row="3" Margin="5,0,0,5">
                            <!-- 相机离线时的提示 -->
                            <Border Background="#F8D7DA" 
                                    BorderBrush="#F5C6CB" 
                                    BorderThickness="1" 
                                    CornerRadius="5" 
                                    Padding="8,5">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding CameraConnectionStatus}" Value="相机离线">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding CameraConnectionStatus}" Value="连接异常">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="⚠ 相机离线：拍照功能不可用，其他功能正常" 
                                           FontSize="11" 
                                           Foreground="#721C24" 
                                           HorizontalAlignment="Center"/>
                            </Border>
                        </Border>
                    </Grid>
                </Border>

                <Border
                    Grid.Row="2"
                    Margin="0,-6,0,0"
                    BorderBrush="Gray"
                    BorderThickness="0.3"
                    CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            Background="#283643"
                            CornerRadius="5 5 0 0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <hc:SimpleText
                                    Grid.Column="0"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Foreground="White"
                                    Text="照片" />
                            </Grid>
                        </Border>

                        <Border
                            x:Name="picture_container"
                            Grid.Row="1"
                            Margin="2">
                            <ListBox
                                Width="{Binding ElementName=picture_container, Path=ActualWidth}"
                                ItemsSource="{Binding ReportList}"
                                ScrollViewer.HorizontalScrollBarVisibility="Hidden"
                                ScrollViewer.VerticalScrollBarVisibility="Auto"
                                SelectedItem="{Binding CurrentSelect}"
                                Style="{StaticResource LListBoxStyle}">
                                <ListBox.ContextMenu>
                                    <ContextMenu>
                                        <MenuItem Command="{Binding OpenPathCommand}" Header="打开文件所在位置" />
                                        <MenuItem Command="{Binding PreviewCommand}" Header="预览图片" />
                                    </ContextMenu>
                                </ListBox.ContextMenu>

                                <ListBox.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <hc:UniformSpacingPanel
                                            Width="{Binding ElementName=picture_container, Path=ActualWidth}"
                                            ChildWrapping="Wrap"
                                            Orientation="Horizontal"
                                            Spacing="10" />
                                    </ItemsPanelTemplate>
                                </ListBox.ItemsPanel>
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <Grid Margin="0,15,0,15">
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="*" />
                                                <RowDefinition Height="Auto" />
                                            </Grid.RowDefinitions>
                                            <Image
                                                Grid.Row="0"
                                                Width="140"
                                                Height="70"
                                                Source="{Binding PicturePath, IsAsync=True, Converter={StaticResource UriToBitmapConverter}}"
                                                Stretch="Fill" />
                                            <hc:UniformSpacingPanel
                                                Grid.Row="1"
                                                Margin="0,10,0,0"
                                                Orientation="Vertical"
                                                Spacing="5">
                                                <TextBlock Width="160">
                                                    <Run Text="照片名称:" />
                                                    <Run Text="{Binding PictureName}" />
                                                </TextBlock>
                                                <TextBlock Width="160">
                                                    <Run Text="拍摄时间:" />
                                                    <Run Text="{Binding CreateTime, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" />
                                                </TextBlock>
                                            </hc:UniformSpacingPanel>
                                        </Grid>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </Border>
                    </Grid>
                </Border>
            </Grid>

            <!--  右半边  -->
            <Grid Grid.Column="2" Margin="5,0,0,0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Border
                    Grid.Row="0"
                    Margin="0,10,0,0"
                    BorderBrush="Gray"
                    BorderThickness="0.3"
                    CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            Background="#283643"
                            CornerRadius="5 5 0 0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <hc:SimpleText
                                    Grid.Column="0"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Foreground="White"
                                    Text="当前元件引脚合格" />
                                <hc:UniformSpacingPanel
                                    Grid.Column="1"
                                    Margin="0,0,5,0"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    Orientation="Horizontal"
                                    Spacing="5">
                                    <hc:SimpleText
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Foreground="White"
                                        Text="总数:" />
                                    <hc:SimpleText
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Foreground="White"
                                        Text="{Binding TotalPinCount}" />
                                    <hc:SimpleText
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Foreground="White"
                                        Text="已检:" />
                                    <hc:SimpleText
                                        Grid.Column="0"
                                        VerticalAlignment="Center"
                                        FontSize="16"
                                        FontWeight="Bold"
                                        Foreground="White"
                                        Text="{Binding CheckCount}" />
                                </hc:UniformSpacingPanel>
                            </Grid>
                        </Border>
                        <Border Grid.Row="1">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                </Grid.RowDefinitions>
                                <Border Grid.Row="0">
                                    <lvc:PieChart LegendLocation="Top" Series="{Binding PieCharts}" />
                                </Border>
                            </Grid>
                        </Border>
                    </Grid>
                </Border>
                <Border
                    Grid.Row="1"
                    Margin="0,10,0,0"
                    BorderBrush="Gray"
                    BorderThickness="0.3"
                    CornerRadius="5">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border
                            Grid.Row="0"
                            Background="#283643"
                            CornerRadius="5 5 0 0">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="*" />
                                </Grid.ColumnDefinitions>
                                <hc:SimpleText
                                    Grid.Column="0"
                                    Margin="5,0,0,0"
                                    VerticalAlignment="Center"
                                    FontSize="16"
                                    FontWeight="Bold"
                                    Foreground="White"
                                    Text="检测" />
                                <hc:SimpleText
                                    Grid.Column="1"
                                    Margin="0,0,5,0"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Center"
                                    FontSize="12"
                                    FontWeight="Bold"
                                    Foreground="LightYellow"
                                    Text="{Binding DetectionStatus}"
                                    Visibility="{Binding PendingResultCount, Converter={StaticResource Number2VisibilityConverter}}" />
                            </Grid>
                        </Border>
                        <Border Grid.Row="1" Padding="5">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="*" />
                                    <RowDefinition Height="35" />
                                </Grid.RowDefinitions>
                                <Border
                                    Grid.Row="0"
                                    BorderBrush="Gray"
                                    BorderThickness="0.5"
                                    CornerRadius="5">
                                    <Grid>
                                        <Image Source="{Binding ObservableReportData.CheckFilePath, IsAsync=True, Converter={StaticResource UriToBitmapConverter}}" Stretch="Fill">
                                            <Image.ContextMenu>
                                                <ContextMenu>
                                                    <MenuItem
                                                        Command="{Binding OpenCheckPathCommand}"
                                                        CommandParameter="{Binding ObservableReportData.CheckFilePath}"
                                                        Header="打开文件所在位置" />
                                                    <MenuItem
                                                        Command="{Binding PreviewCheckCommand}"
                                                        CommandParameter="{Binding ObservableReportData.CheckFilePath}"
                                                        Header="预览图片" />
                                                </ContextMenu>
                                            </Image.ContextMenu>
                                        </Image>

                                    </Grid>
                                </Border>
                                <Border Grid.Row="1" Margin="0,10,0,0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*" />
                                            <ColumnDefinition Width="*" />
                                        </Grid.ColumnDefinitions>
                                        <Grid Grid.Column="0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <TextBlock
                                                Grid.Column="0"
                                                FontSize="18"
                                                Text="名称："
                                                TextAlignment="Center" />
                                            <TextBlock
                                                Grid.Column="1"
                                                FontSize="18"
                                                Text="{Binding ObservableReportData.ProcessName}" />
                                        </Grid>
                                        <Grid Grid.Column="1">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="Auto" />
                                                <ColumnDefinition Width="*" />
                                            </Grid.ColumnDefinitions>
                                            <TextBlock
                                                Grid.Column="0"
                                                FontSize="18"
                                                Text="结果："
                                                TextAlignment="Center" />
                                            <TextBlock
                                                Grid.Column="1"
                                                FontSize="18"
                                                Foreground="{Binding ObservableReportData.Status, Converter={StaticResource CheckValueConvert}}"
                                                Text="{Binding ObservableReportData.Status, Converter={StaticResource StatusTextConvert}}" />
                                        </Grid>
                                    </Grid>
                                </Border>
                            </Grid>
                        </Border>
                    </Grid>
                </Border>

                <Border
                    Grid.Row="2"
                    Margin="0,10,0,0"
                    BorderBrush="Gray"
                    BorderThickness="0.3"
                    CornerRadius="5">
                    <controls:InfoControl />
                </Border>
            </Grid>
        </Grid>
    </Border>
</Page>
