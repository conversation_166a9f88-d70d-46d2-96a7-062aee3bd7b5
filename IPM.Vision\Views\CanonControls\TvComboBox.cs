﻿using IPM.Vision.Camera.Com;
using IPM.Vision.Camera.EDSDKLib.Events;
using IPM.Vision.Camera.EDSDKLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;

namespace IPM.Vision.Views.CanonControls
{
    public class TvComboBox : PropertyComboBox, IObserver
    {
        private EDSDK.EdsPropertyDesc _desc;
        public TvComboBox()
        {
            if (this.items == null) items = new Dictionary<uint, string>();
            items.Add(0x04, "Auto");
            items.Add(0x10, "30''");
            items.Add(0x13, "25''");
            items.Add(0x15, "20''");
            items.Add(0x18, "15''");
            items.Add(0x1B, "13''");
            items.Add(0x1D, "10''");
            items.Add(0x20, "8''");
            items.Add(0x24, "6''");
            items.Add(0x25, "5''");
            items.Add(0x28, "4''");
            items.Add(0x2B, "3''2");
            items.Add(0x2C, "3''");
            items.Add(0x2D, "2''5");
            items.Add(0x30, "2''");
            items.Add(0x33, "1''6");
            items.Add(0x34, "1''5");
            items.Add(0x35, "1''3");
            items.Add(0x38, "1''");
            items.Add(0x3B, "0''8");
            items.Add(0x3C, "0''7");
            items.Add(0x3D, "0''6");
            items.Add(0x40, "0''5");
            items.Add(0x43, "0''4");
            items.Add(0x45, "0''3");
            items.Add(0x48, "4");
            items.Add(0x4B, "5");
            items.Add(0x4D, "6");
            items.Add(0x50, "8");
            items.Add(0x54, "10");
            items.Add(0x58, "15");
            items.Add(0x5C, "20");
            items.Add(0x5D, "25");
            items.Add(0x60, "30");
            items.Add(0x63, "40");
            items.Add(0x64, "45");
            items.Add(0x65, "50");
            items.Add(0x68, "60");
            items.Add(0x6B, "80");
            items.Add(0x6C, "90");
            items.Add(0x6D, "100");
            items.Add(0x70, "125");
            items.Add(0x73, "160");
            items.Add(0x74, "180");
            items.Add(0x75, "200");
            items.Add(0x78, "250");
            items.Add(0x7B, "320");
            items.Add(0x7C, "350");
            items.Add(0x7D, "400");
            items.Add(0x80, "500");
            items.Add(0x83, "640");
            items.Add(0x84, "750");
            items.Add(0x85, "800");
            items.Add(0x88, "1000");
            items.Add(0x8B, "1250");
            items.Add(0x8C, "1500");
            items.Add(0x8D, "1600");
            items.Add(0x90, "2000");
            items.Add(0x93, "2500");
            items.Add(0x94, "3000");
            items.Add(0x95, "3200");
            items.Add(0x98, "4000");
            items.Add(0x9B, "5000");
            items.Add(0x9C, "6000");
            items.Add(0x9D, "6400");
            items.Add(0xA0, "8000");
            this.DisplayMemberPath = "Value";
            this.SelectedValuePath = "Key";
        }
        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            if (e.AddedItems.Count > 0)
            {
                var selectedItem = (KeyValuePair<uint, string>)e.AddedItems[0];
                CameraController.ActionPerformed(new ActionEvent(ActionEvent.Command.SET_TV, (IntPtr)selectedItem.Key));

            }
            base.OnSelectionChanged(e);
        }

        public void UpdateAsync(Observable observable, CameraEvent e)
        {
            CanonCameraModel model = (CanonCameraModel)observable;
            CameraEvent.Type eventType = CameraEvent.Type.NONE;

            if ((eventType = e.GetEventType()) == CameraEvent.Type.PROPERTY_CHANGED || eventType == CameraEvent.Type.PROPERTY_DESC_CHANGED)
            {
                uint propertyID = (uint)e.GetArg();


                if (propertyID == EDSDK.PropID_Tv)
                {
                    uint property = model.Tv;

                    //Update property
                    switch (eventType)
                    {
                        case CameraEvent.Type.PROPERTY_CHANGED:
                            this.UpdateProperty(property);
                            break;

                        case CameraEvent.Type.PROPERTY_DESC_CHANGED:
                            _desc = model.TvDesc;
                            this.UpdatePropertyDesc(ref _desc);
                            this.UpdateProperty(property);
                            break;
                    }
                }
            }
        }
    }
}
