﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableUserInfoModel:ViewModelBase
    {
        private string id;

        public string Id
        {
            get => id;
            set => SetProperty(ref id, value);
        }

        private string account;
        public string Account
        {
            get => account;
            set => SetProperty(ref account, value);
        }

        private string password;
        public string Password
        {
            get => password;
            set => SetProperty(ref password, value);
        }

        private string userName;
        public string UserName
        {
            get => userName;
            set => SetProperty(ref userName, value);
        }

        private ObservableCollection<string> menuId;
        public ObservableCollection<string> MenuId
        {
            get => menuId;
            set => SetProperty(ref menuId, value);
        }

        private List<string> menuName = new List<string>();
        public List<string> MenuName
        {
            get => menuName;
            set => SetProperty(ref menuName, value);
        }

        private bool isSupper = false;
        public bool IsSupper
        {
            get => isSupper;
            set => SetProperty(ref isSupper, value);
        }
    }
}
