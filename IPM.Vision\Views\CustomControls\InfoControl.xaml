﻿<UserControl x:Class="IPM.Vision.Views.CustomControls.InfoControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:IPM.Vision.Views.CustomControls"
             xmlns:hc="https://handyorg.github.io/handycontrol"
             xmlns:controls="clr-namespace:IPM.Vision.Views.CustomControls"
             DataContext="{Binding InfoControlViewModel, Source={StaticResource Locator}}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="30"/>
            <RowDefinition Height="40"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Border Grid.Row="0" CornerRadius="5 5 0 0" Background="#283643">
            <hc:SimpleText Text="日志" FontSize="16" Foreground="White" VerticalAlignment="Center" FontWeight="Bold" Margin="5 0 0 0"/>
        </Border>

        <Border Grid.Row="1" Padding="5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0" HorizontalAlignment="Left">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="设备状态:" FontSize="16" VerticalAlignment="Center" HorizontalAlignment="Right"/>
                    <TextBlock
                        Grid.Column="1"
                        Foreground="{Binding EquipmentStatus}"
                        FontFamily="{StaticResource FontAwesome}"
                        Text="&#xf111;"
                        FontSize="22"
                        VerticalAlignment="Center"
                        Margin="10 0 0 0"/>
                </Grid>
                <Grid Grid.Column="1" HorizontalAlignment="Left">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="采集相机:" FontSize="16" VerticalAlignment="Center" HorizontalAlignment="Right"/>
                    <TextBlock  Grid.Column="1" Foreground="{Binding MainCameraStatus}" FontFamily="{StaticResource FontAwesome}" Text="&#xf111;" FontSize="22" VerticalAlignment="Center" Margin="10 0 0 0"/>
                </Grid>

                <Grid Grid.Column="2" HorizontalAlignment="Left">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <TextBlock Grid.Column="0" Text="原点相机:" FontSize="16" VerticalAlignment="Center" HorizontalAlignment="Right"/>
                    <TextBlock Grid.Column="1"
                               Foreground="{Binding LightStatus,Mode=TwoWay,UpdateSourceTrigger=PropertyChanged}"
                               FontFamily="{StaticResource FontAwesome}"
                               Text="&#xf111;"
                               FontSize="22"
                               VerticalAlignment="Center"
                               Margin="10 0 0 0"/>
                </Grid>
            </Grid>
        </Border>

        <Border Grid.Row="2" Margin="2">
            <ScrollViewer VerticalScrollBarVisibility="Auto"  Grid.Row="1">
                <ItemsControl Background="#f5f5f5" BorderBrush="Gray" BorderThickness="0.5" Padding="5" ItemsSource="{Binding Logs}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding Message}"
                                       Foreground="{Binding Color}"
                                       TextWrapping="Wrap" />
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </ScrollViewer>
        </Border>
    </Grid>
</UserControl>
