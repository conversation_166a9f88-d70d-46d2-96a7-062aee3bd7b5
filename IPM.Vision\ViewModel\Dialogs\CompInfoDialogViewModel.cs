﻿using CommunityToolkit.Mvvm.Input;
using HandyControl.Tools.Extension;
using IPM.Vision.BLL;
using IPM.Vision.BLL.interfaces;
using IPM.Vision.Mappers;
using IPM.Vision.Model;
using IPM.Vision.ViewModel.CustomControls;
using IPM.Vision.ViewModel.ObservableModel;
using IPM.Vision.Common;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.Dialogs
{
    public class CompInfoDialogViewModel : ViewModelBase, IDialogResultable<bool>
    {
        private readonly CompInfoService _compInfoService;
        private readonly EquipmentService _equipmentService;
        private readonly OptCameraControlViewModel _optCameraControlViewModel;
        private ObservableCompModel _currentData;
        public event Action<ObservableCollection<ObservableProcessModel>> SaveSuccessedEvent;
        private readonly ObservableGlobalState _globalState;
        public event Action<string> NotifyCompInfoEvent;
        //private ObservableCollection<ObservableProcessModel> oneModels = new ObservableCollection<ObservableProcessModel>();
        //private ObservableCollection<ObservableProcessModel> twoModels = new ObservableCollection<ObservableProcessModel>();
        //private ObservableCollection<ObservableProcessModel> threeModels = new ObservableCollection<ObservableProcessModel>();
        //private ObservableCollection<ObservableProcessModel> fourModels = new ObservableCollection<ObservableProcessModel>();
        //private ObservableCollection<ObservableProcessModel> resultModels = new ObservableCollection<ObservableProcessModel>();
        //private ObservableCollection<ObservableEquipmentModel> oneEquipment = new ObservableCollection<ObservableEquipmentModel>();
        //private ObservableCollection<ObservableEquipmentModel> twoEquipment = new ObservableCollection<ObservableEquipmentModel>();
        //private ObservableCollection<ObservableEquipmentModel> threeEquipment = new ObservableCollection<ObservableEquipmentModel>();
        //private ObservableCollection<ObservableEquipmentModel> fourEquipment = new ObservableCollection<ObservableEquipmentModel>();
        private bool _isInsert = false;
        
        private string _title = string.Empty;

        private bool _showMainCamera = true;

        /// <summary>
        /// 引脚起点位置选项列表
        /// </summary>
        public List<EnumItem> PinStartPositionList { get; } = EnumHelper.GetEnumList<PinStartPositionEnum>();

        /// <summary>
        /// 引脚拍摄方向选项列表
        /// </summary>
        public List<EnumItem> PinDirectionList { get; } = EnumHelper.GetEnumList<PinDirectionEnum>();

        public CompInfoDialogViewModel(ICompInfoService compInfoService,IEquipmentService equipmentService, ObservableGlobalState globalState,OptCameraControlViewModel optCameraControlViewModel)
        {
            _compInfoService = (CompInfoService)compInfoService;
            _optCameraControlViewModel = optCameraControlViewModel;
            _equipmentService = (EquipmentService)equipmentService;
            _globalState = globalState;
            
            // 注意：不在这里设置对话框模式，因为这是全局单例ViewModel
            // 设置对话框模式应该在对话框打开时处理
        }

        public ObservableCompModel CurrentData
        {
            get => _currentData;
            set => SetProperty(ref _currentData, value);
        }

        public bool ShowMainCamera
        {
            get => _showMainCamera;
            set => SetProperty(ref _showMainCamera, value);
        }

        public string Title
        {
            get => _title;
            set => SetProperty(ref _title, value);
        }

        public bool IsInsert
        {
            get => _isInsert;
            set => SetProperty(ref _isInsert, value);
        }

        private bool _result = false;
        public bool Result { get => _result; set => SetProperty(ref _result, value); }
        public Action CloseAction { get; set; }

        public IRelayCommand CloseCommand => new RelayCommand(() =>
        {
            Result = false;
            CloseAction?.Invoke();
        });
        public IRelayCommand<string> GetCommand => new RelayCommand<string>((param) => {

            var temp = Convert.ToInt32(param);
            Task.Factory.StartNew(() => {
                switch (temp)
                {
                    case 1:
                        CurrentData.X1 = _equipmentService.ReadX();
                        CurrentData.Y1 = _equipmentService.ReadY();
                        CurrentData.R1 = _equipmentService.ReadR();
                        CurrentData.Z1 = _equipmentService.ReadZ();
                        CurrentData.T1 = _equipmentService.ReadT();
                        break;
                    case 2:
                        CurrentData.X2 = _equipmentService.ReadX();
                        CurrentData.Y2 = _equipmentService.ReadY();
                        CurrentData.R2 = _equipmentService.ReadR();
                        CurrentData.Z2 = _equipmentService.ReadZ();
                        CurrentData.T2 = _equipmentService.ReadT();
                        break;
                    case 3:
                        CurrentData.X3 = _equipmentService.ReadX();
                        CurrentData.Y3 = _equipmentService.ReadY();
                        CurrentData.R3 = _equipmentService.ReadR();
                        CurrentData.Z3 = _equipmentService.ReadZ();
                        CurrentData.T3 = _equipmentService.ReadT();
                        break;
                    case 4:
                        CurrentData.X4 = _equipmentService.ReadX();
                        CurrentData.Y4 = _equipmentService.ReadY();
                        CurrentData.R4 = _equipmentService.ReadR();
                        CurrentData.Z4 = _equipmentService.ReadZ();
                        CurrentData.T4 = _equipmentService.ReadT();
                        break;
                    case 5:
                        CurrentData.MarkX = _equipmentService.ReadX();
                        CurrentData.MarkY = _equipmentService.ReadY();
                        break;
                }

            });
            
        
        });

        public IRelayCommand<string> SendCommand => new RelayCommand<string>((param) => {

            var temp = Convert.ToInt32(param);
            Task.Factory.StartNew(() => {
                switch (temp)
                {
                    case 1:
                        //_equipmentService.SetXY(CurrentData.X1, CurrentData.Y1);
                        _equipmentService.WriteXYArray(new float[] { CurrentData.X1 }, new float[] { CurrentData.Y1 }, 1);
                        _equipmentService.SetR(CurrentData.R1);
                        _equipmentService.SetT(CurrentData.T1);
                        _equipmentService.SetZAsync(CurrentData.Z1);
                        break;
                    case 2:
                        //_equipmentService.SetXY(CurrentData.X2, CurrentData.Y2);
                        _equipmentService.WriteXYArray(new float[] { CurrentData.X2 }, new float[] { CurrentData.Y2 }, 1);
                        _equipmentService.SetR(CurrentData.R2);
                        _equipmentService.SetT(CurrentData.T2);
                        _equipmentService.SetZAsync(CurrentData.Z2);
                        break;
                    case 3:
                        //_equipmentService.SetXY(CurrentData.X3, CurrentData.Y3);
                        _equipmentService.WriteXYArray(new float[] { CurrentData.X3 }, new float[] { CurrentData.Y3 }, 1);
                        _equipmentService.SetR(CurrentData.R3);
                        _equipmentService.SetT(CurrentData.T3);
                        _equipmentService.SetZAsync(CurrentData.Z3);
                        break;
                    case 4:
                        //_equipmentService.SetXY(CurrentData.X4, CurrentData.Y4);
                        _equipmentService.WriteXYArray(new float[] { CurrentData.X4 }, new float[] { CurrentData.Y4 }, 1);
                        _equipmentService.SetR(CurrentData.R4);
                        _equipmentService.SetT(CurrentData.T4);
                        _equipmentService.SetZAsync(CurrentData.Z4);
                        break;
                    case 5:
                        _equipmentService.WriteXYArray(new float[] { CurrentData.MarkX }, new float[] { CurrentData.MarkY }, 1);
                        break;
                }

            });
        });


        #region 老方法
        //public IRelayCommand<string> GenerateDataCommand => new RelayCommand<string>((para) =>
        //{
        //    Task.Factory.StartNew(() => {
        //        var temp = Convert.ToInt32(para);
        //        switch (temp)
        //        {
        //            case 1:

        //                resultModels.Clear();
        //                oneEquipment.Clear();
        //                oneModels.Clear();
        //                if (CurrentData.Pin1 == 0) return;
        //                GenerateOne();
        //                CurrentData.X2 = oneEquipment.Last().X + CurrentData.X1More;
        //                CurrentData.Y2 = oneEquipment.Last().Y + CurrentData.Y1More;
        //                CurrentData.Z2 = oneEquipment.Last().Z;
        //                CurrentData.R2 = oneEquipment.Last().R;
        //                CurrentData.T2 = oneEquipment.Last().T;
        //                CurrentData.Step3 =  -CurrentData.Step1;
        //                if (CurrentData.Pin2 == 0) CurrentData.Pin2 = CurrentData.Pin1;
        //                if (CurrentData.Pin3 == 0) CurrentData.Pin3 = CurrentData.Pin1;
        //                if (CurrentData.Pin4 == 0) CurrentData.Pin4 = CurrentData.Pin1;
        //                break;
        //            case 2:
        //                twoEquipment.Clear();
        //                twoModels.Clear();
        //                if (CurrentData.Pin2 == 0) return;
        //                GenerateTwo();
        //                CurrentData.X3 = twoEquipment.Last().X + CurrentData.X2More;
        //                CurrentData.Y3 = twoEquipment.Last().Y + CurrentData.Y2More;
        //                CurrentData.Z3 = twoEquipment.Last().Z;
        //                CurrentData.R3 = twoEquipment.Last().R;
        //                CurrentData.T3 = twoEquipment.Last().T + CurrentData.T3Rotation;
        //                CurrentData.Step4 =  -CurrentData.Step2;
        //                break;
        //            case 3:
        //                threeModels.Clear();
        //                threeEquipment.Clear();
        //                if (CurrentData.Pin3 == 0) return;
        //                GenerateThree();
        //                CurrentData.X4 = threeEquipment.Last().X + CurrentData.X3More;
        //                CurrentData.Y4 = threeEquipment.Last().Y +CurrentData.Y3More;
        //                CurrentData.Z4 = threeEquipment.Last().Z;
        //                CurrentData.R4 = threeEquipment.Last().R;
        //                CurrentData.T4 = threeEquipment.Last().T + CurrentData.T4Rotation;
        //                break;
        //            case 4:
        //                fourEquipment.Clear();
        //                fourModels.Clear();
        //                GenerateFour();
        //                break;
        //        }
        //    });
        //});

        //private void GenerateOne()
        //{
        //    if (CurrentData.Pin1 > 0)
        //    {
        //        bool position = CurrentData.Step1 > 0;
        //        int index = position ? 1 : CurrentData.Pin1;
        //        for (int i = 1; i <= CurrentData.Pin1; i++)
        //        {
        //            float equipmentY = oneEquipment.Count == 0 ? CurrentData.Y1 : oneEquipment[oneEquipment.Count - 1].Y + CurrentData.Step1;
        //            string equipId = Guid.NewGuid().ToString();

        //            ObservableEquipmentModel equipmentModel = new ObservableEquipmentModel()
        //            {
        //                ParamName = $"PIN-{index}-{Guid.NewGuid().ToString().Substring(0, 5)}",
        //                Id = equipId,
        //                Y = equipmentY,
        //                Z = CurrentData.Z1,
        //                R = CurrentData.R1,
        //                T = CurrentData.T1,
        //                CreateTime = DateTime.Now,
        //                Operator = _globalState.LoginUser.Account,
        //                X = CurrentData.X1,
        //            };
        //            oneEquipment.Add(equipmentModel);
        //            ObservableProcessModel model = new ObservableProcessModel()
        //            {
        //                ProcessType = Common.ProcessTypeEnum.TAKEPICTURE,
        //                CameraType = Common.CameraTypeEnum.Main,
        //                ProcessNumber = i,
        //                ParamName = $"PIN-{index}",

        //                EquipmentParaId = equipId,
        //            };
        //            oneModels.Add(model);
        //            resultModels.Add(model);
        //            if(position) index++;
        //            else index--;
        //        }

        //    }
        //}

        //private void GenerateTwo()
        //{
        //    if (CurrentData.Pin2 > 0)
        //    {
        //        int stepNumber = 0;
        //        bool position = CurrentData.Step1 > 0;
        //        int index = position ? CurrentData.Pin1 + 1 : CurrentData.Pin1 + CurrentData.Pin2;
        //        if (resultModels.Count > 0)
        //            stepNumber = resultModels[resultModels.Count - 1].ProcessNumber;
        //        for (int i = 1; i <= CurrentData.Pin2; i++)
        //        {
        //            stepNumber++;
        //            float equipmentY = twoEquipment.Count == 0 ? CurrentData.X2 : twoEquipment[twoEquipment.Count - 1].X - CurrentData.Step2;
        //            string equipId = Guid.NewGuid().ToString();

        //            ObservableEquipmentModel equipmentModel = new ObservableEquipmentModel()
        //            {
        //                ParamName = $"PIN-{index}-{Guid.NewGuid().ToString().Substring(0, 5)}",
        //                Id = equipId,
        //                Y = CurrentData.Y2,
        //                Z = CurrentData.Z2,
        //                R = CurrentData.R2,
        //                T = CurrentData.T2,
        //                X = equipmentY,
        //                CreateTime = DateTime.Now,
        //                Operator = _globalState.LoginUser.Account,
        //            };
        //            twoEquipment.Add(equipmentModel);
        //            ObservableProcessModel model = new ObservableProcessModel()
        //            {
        //                ProcessType = Common.ProcessTypeEnum.TAKEPICTURE,
        //                CameraType = Common.CameraTypeEnum.Main,
        //                ProcessNumber = stepNumber,
        //                ParamName = $"PIN-{index}",
        //                EquipmentParaId = equipId,
        //            };


        //            twoModels.Add(model);
        //            resultModels.Add(model);
        //            if (position) index++;
        //            else index--;
        //        }

        //    }
        //}

        //private void GenerateThree()
        //{
        //    if (CurrentData.Pin3 > 0)
        //    {
        //        bool position = CurrentData.Step1 > 0;
        //        int index = position ? CurrentData.Pin1 + CurrentData.Pin2 + 1 : CurrentData.Pin1 + CurrentData.Pin2 + CurrentData.Pin3;
        //        int stepNumber = 0;
        //        if (resultModels.Count > 0)
        //            stepNumber = resultModels[resultModels.Count - 1].ProcessNumber;
        //        for (int i = 1; i <= CurrentData.Pin3; i++)
        //        {
        //            stepNumber++;
        //            float equipmentX = threeEquipment.Count == 0 ? CurrentData.Y3 : threeEquipment[threeEquipment.Count - 1].Y + CurrentData.Step3;
        //            string equipId = Guid.NewGuid().ToString();

        //            ObservableEquipmentModel equipmentModel = new ObservableEquipmentModel()
        //            {
        //                ParamName = $"PIN-{index}-{Guid.NewGuid().ToString().Substring(0, 5)}",
        //                Id = equipId,
        //                Y = equipmentX,
        //                Z = CurrentData.Z3,
        //                R = CurrentData.R3,
        //                T = CurrentData.T3,
        //                X = CurrentData.X3,
        //                CreateTime = DateTime.Now,
        //                Operator = _globalState.LoginUser.Account,
        //            };
        //            threeEquipment.Add(equipmentModel);
        //            ObservableProcessModel model = new ObservableProcessModel()
        //            {
        //                ProcessType = Common.ProcessTypeEnum.TAKEPICTURE,
        //                CameraType = Common.CameraTypeEnum.Main,
        //                ProcessNumber = stepNumber,
        //                ParamName = $"PIN-{index}",
        //                EquipmentParaId = equipId,
        //            };
        //            threeModels.Add(model);
        //            resultModels.Add(model);
        //            if (position) index++;
        //            else index--;
        //        }
        //    }
        //}

        //private void GenerateFour()
        //{
        //    if (CurrentData.Pin4 > 0)
        //    {
        //        bool position = CurrentData.Step1 > 0;
        //        int index = position ? CurrentData.Pin1 + CurrentData.Pin2 + CurrentData.Pin3 + 1 : CurrentData.Pin1 + CurrentData.Pin2 + CurrentData.Pin3 + CurrentData.Pin4;
        //        int stepNumber = 0;
        //        if (resultModels.Count > 0)
        //            stepNumber = resultModels[resultModels.Count - 1].ProcessNumber;
        //        for (int i = 1; i <= CurrentData.Pin4; i++)
        //        {
        //            stepNumber++;
        //            float equipmentY = fourEquipment.Count == 0 ? CurrentData.X4 : fourEquipment[fourEquipment.Count - 1].X + CurrentData.Step4;
        //            string equipId = Guid.NewGuid().ToString();

        //            ObservableEquipmentModel equipmentModel = new ObservableEquipmentModel()
        //            {
        //                ParamName = $"PIN-{index}-{Guid.NewGuid().ToString().Substring(0, 5)}",
        //                Id = equipId,
        //                Y = CurrentData.Y4,
        //                Z = CurrentData.Z4,
        //                R = CurrentData.R4,
        //                T = CurrentData.T4,
        //                X = equipmentY,
        //                CreateTime = DateTime.Now,
        //                Operator = _globalState.LoginUser.Account,
        //            };
        //            fourEquipment.Add(equipmentModel);
        //            ObservableProcessModel model = new ObservableProcessModel()
        //            {
        //                ProcessType = Common.ProcessTypeEnum.TAKEPICTURE,
        //                CameraType = Common.CameraTypeEnum.Main,
        //                ProcessNumber = stepNumber,
        //                ParamName = $"PIN-{index}",
        //                EquipmentParaId = equipId,
        //            };
        //            fourModels.Add(model);
        //            resultModels.Add(model);
        //            if (position) index++;
        //            else index--;
        //        }
        //    }
        //}
        //public IRelayCommand<string> AutoMoveTestCommand => new RelayCommand<string>((param) =>
        //{
        //    switch (param)
        //    {
        //        case "1":
        //            GenerateOne();
        //            SendToPlc(CurrentData.X1, CurrentData.Y1, CurrentData.Z1, CurrentData.R1, CurrentData.T1, param);
        //            break;
        //        case "2":
        //            GenerateTwo();
        //            SendToPlc(CurrentData.X2, CurrentData.Y2, CurrentData.Z2, CurrentData.R2, CurrentData.T2, param);
        //            break;
        //        case "3":
        //            GenerateThree();
        //            SendToPlc(CurrentData.X3, CurrentData.Y3, CurrentData.Z3, CurrentData.R3, CurrentData.T3, param);
        //            break;
        //        case "4":
        //            GenerateFour();
        //            SendToPlc(CurrentData.X4, CurrentData.Y4, CurrentData.Z4, CurrentData.R4, CurrentData.T4, param);
        //            break;
        //    }
        //});

        //public IRelayCommand SaveCommand => new RelayCommand(async () =>
        //{

        //    oneModels.Clear();
        //    twoModels.Clear();
        //    threeModels.Clear();
        //    fourModels.Clear();
        //    resultModels.Clear();
        //    GenerateOne();
        //    GenerateTwo();
        //    GenerateThree();
        //    GenerateFour();
        //    var s = CurrentData.MapTo<ObservableCompModel, CompInfoModel>();

        //    if (IsInsert)
        //    {
        //        s.Id = Guid.NewGuid().ToString();
        //        await _compInfoService.Add(s);
        //        NotifyCompInfoEvent?.Invoke(s.Id);
        //    }
        //    else
        //    {
        //        await _compInfoService.Update(s);
        //    }
        //    if (CurrentData.IsAutoTake)
        //    {
        //        ObservableCollection<ObservableProcessModel> tempResult = new ObservableCollection<ObservableProcessModel>();
        //        List<ObservableEquipmentModel> das = new List<ObservableEquipmentModel>();
        //        oneEquipment[0].ParamName = "第一边开始坐标";
        //        twoEquipment[0].ParamName = "第二边开始坐标";
        //        threeEquipment[0].ParamName = "第三边开始坐标";
        //        fourEquipment[0].ParamName = "第四边开始坐标";

        //        das.Add(oneEquipment[0]);
        //        das.Add(twoEquipment[0]);
        //        das.Add(threeEquipment[0]);
        //        das.Add(fourEquipment[0]);
        //        Result = await _equipmentService.AddRange(das.MapTo<List<ObservableEquipmentModel>, List<EquipmentModel>>());
        //        oneModels[0].ParamName = "第一边";
        //        oneModels[0].ProcessNumber = 1;
        //        twoModels[0].ParamName = "第二边";
        //        twoModels[0].ProcessNumber = 2;
        //        threeModels[0].ParamName = "第三边";
        //        threeModels[0].ProcessNumber = 3;
        //        fourModels[0].ParamName = "第四边";
        //        fourModels[0].ProcessNumber = 4;

        //        tempResult.Add(oneModels[0]);
        //        tempResult.Add(twoModels[0]);
        //        tempResult.Add(threeModels[0]);
        //        tempResult.Add(fourModels[0]);
        //        SaveSuccessedEvent?.Invoke(tempResult);
        //        CloseAction?.Invoke();
        //        return;
        //    }
        //    List<ObservableEquipmentModel> da = new List<ObservableEquipmentModel>();
        //    List<ObservableProcessModel> ps = new List<ObservableProcessModel>();
        //    if (CurrentData.IsLinked1)
        //    {
        //        da = da.Concat(oneEquipment).ToList();
        //        ps = ps.Concat(oneModels).ToList();
        //    }
        //    if (CurrentData.IsLinked2)
        //    {
        //        da = da.Concat(twoEquipment).ToList();
        //        ps = ps.Concat(twoModels).ToList();
        //    }
        //    if (CurrentData.IsLinked3)
        //    {
        //        da = da.Concat(threeEquipment).ToList();
        //        ps = ps.Concat(threeModels).ToList();
        //    }
        //    if (CurrentData.IsLinked4)
        //    {
        //        da = da.Concat(fourEquipment).ToList();
        //        ps = ps.Concat(fourModels).ToList();
        //    }

        //    Result = await _equipmentService.AddRange(da.MapTo<List<ObservableEquipmentModel>, List<EquipmentModel>>());
        //    SaveSuccessedEvent?.Invoke(new ObservableCollection<ObservableProcessModel>(ps));
        //    CloseAction?.Invoke();
        //});

        //private void SendToPlc(float x, float y, float z, float r, float t, string typeParam)
        //{
        //    _equipmentService.SetXSpeed(CurrentData.MoveSpeed);
        //    _equipmentService.SetYSpeed(CurrentData.MoveSpeed);
        //    _equipmentService.SetXAcce(CurrentData.AceSpeed);
        //    _equipmentService.SetYAcce(CurrentData.AceSpeed);
        //    if (typeParam == "2")
        //        x = x - CalculateTimeAndDistance(CurrentData.AceSpeed, CurrentData.MoveSpeed).Distance;
        //    if (typeParam == "4")
        //        x = x + CalculateTimeAndDistance(CurrentData.AceSpeed, CurrentData.MoveSpeed).Distance;
        //    if (typeParam == "1")
        //        y = y + CalculateTimeAndDistance(CurrentData.AceSpeed, CurrentData.MoveSpeed).Distance;
        //    if (typeParam == "3")
        //        y = y - CalculateTimeAndDistance(CurrentData.AceSpeed, CurrentData.MoveSpeed).Distance;
        //    _equipmentService.SetXY(x, y);
        //    _equipmentService.SetZAsync(z);
        //    _equipmentService.SetR(r);
        //    _equipmentService.SetT(t);
        //    Thread.Sleep(300);
        //    if (typeParam == "1" && oneEquipment.Count > 0)
        //    {
        //        var endY = oneEquipment.LastOrDefault().Y - 50;
        //        _equipmentService.SetXY(x, endY);
        //    }
        //    if (typeParam == "2" && twoEquipment.Count > 0)
        //    {
        //        var endX = oneEquipment.LastOrDefault().X + 50;
        //        _equipmentService.SetXY(endX, y);
        //    }
        //    if (typeParam == "3" && threeEquipment.Count > 0)
        //    {
        //        var endY = oneEquipment.LastOrDefault().Y + 50;
        //        _equipmentService.SetXY(x, endY);
        //    }
        //    if (typeParam == "4" && fourEquipment.Count > 0)
        //    {
        //        var endX = oneEquipment.LastOrDefault().X - 50;
        //        _equipmentService.SetXY(endX, y);
        //    }
        //}
        #endregion
        
        #region 新方法

        public IRelayCommand SaveCommand => new RelayCommand(async () =>
        {
            if (CurrentData.IsAutoTake)
            {
                var s = CurrentData.MapTo<ObservableCompModel, CompInfoModel>();
                if (IsInsert)
                {
                    s.Id = Guid.NewGuid().ToString();
                    await _compInfoService.Add(s);
                    NotifyCompInfoEvent?.Invoke(s.Id);
                }
                else
                {
                    await _compInfoService.Update(s);
                }
                var result = await GenerateAuotoData();
                
                SaveSuccessedEvent?.Invoke(new ObservableCollection<ObservableProcessModel>(result));
                
                CloseAction?.Invoke();
            }

        });

        public IRelayCommand<string> GenerateDataCommand => new RelayCommand<string>((param) =>
        {
            switch (param)
            {
                case "1":
                    if (CurrentData.Pin1 == 0) return;
                    GenerateNextEdgeData(1);
                    break;
                case "2":
                    if (CurrentData.Pin2 == 0) return;
                    GenerateNextEdgeData(2);
                    break;
                case "3":
                    if (CurrentData.Pin3 == 0) return;
                    GenerateNextEdgeData(3);
                    break;
            }
        });

        /// <summary>
        /// 根据自定义起点和方向生成下一边的数据
        /// </summary>
        /// <param name="currentEdge">当前边的索引 (1-3)</param>
        private void GenerateNextEdgeData(int currentEdge)
        {
            // 获取边的顺序，根据起点位置和方向确定
            var edgeSequence = GetEdgeSequence();

            // 获取当前边在序列中的索引
            int currentIndex = Array.IndexOf(edgeSequence, currentEdge);
            if (currentIndex == -1 || currentIndex >= 3) return; // 最后一边不需要生成下一边

            // 获取下一边的索引
            int nextEdge = edgeSequence[currentIndex + 1];

            // 根据当前边和下一边计算坐标和步进值
            CalculateNextEdgeCoordinates(currentEdge, nextEdge);
        }

        /// <summary>
        /// 计算下一边的坐标和步进值
        /// </summary>
        /// <param name="currentEdge">当前边</param>
        /// <param name="nextEdge">下一边</param>
        private void CalculateNextEdgeCoordinates(int currentEdge, int nextEdge)
        {
            // 获取当前边的数据
            var (currentX, currentY, currentZ, currentT, currentPin, currentStep, currentXMore, currentYMore) = GetEdgeData(currentEdge);

            // 计算当前边的结束位置（最后一个pin的位置）
            float endX = currentX;
            float endY = currentY;

            // 根据边的方向计算结束位置 - 修复：使用(currentPin-1)而不是currentPin
            var direction = GetEdgeDirection(currentEdge, CurrentData.PinDirection == PinDirectionEnum.Clockwise);
            endX += direction.deltaX * (currentPin - 1) * Math.Abs(currentStep);
            endY += direction.deltaY * (currentPin - 1) * Math.Abs(currentStep);

            // 设置下一边的起始位置
            float nextX = endX + currentXMore;
            float nextY = endY + currentYMore;

            // 设置下一边的数据，保持相同的pin数量和步进值绝对值
            SetEdgeData(nextEdge, nextX, nextY, currentZ, currentT, currentPin, Math.Abs(currentStep));
        }

        /// <summary>
        /// 根据起点位置和方向获取边的拍摄顺序 - 参考ProductChangedDialogViewModel的正确实现
        /// </summary>
        /// <returns>边的顺序数组 [1,2,3,4]</returns>
        private int[] GetEdgeSequence()
        {
            // 边编号定义：1=上边, 2=右边, 3=下边, 4=左边
            
            // 定义各起始位置对应的逆时针边序列
            // 0=左上角: 上边→右边→下边→左边 [1,2,3,4]
            // 1=右上角: 上边→左边→下边→右边 [1,4,3,2]
            // 2=右下角: 右边→上边→左边→下边 [2,1,4,3]
            // 3=左下角: 下边→右边→上边→左边 [3,2,1,4]
            int[,] counterClockwiseSequences = new int[4,4]
            {
                { 1,2,3,4 }, // 左上角开始逆时针
                { 1, 4, 3, 2 }, // 右上角开始逆时针
                { 2, 1, 4, 3 }, // 右下角开始逆时针
                { 3, 2, 1, 4 }  // 左下角开始逆时针
            };

            int startPosition = (int)CurrentData.PinStartPosition;
            bool isClockwise = CurrentData.PinDirection == PinDirectionEnum.Clockwise;

            // 获取基础序列（逆时针）
            int[] sequence = new int[4];
            for (int i = 0; i < 4; i++)
            {
                sequence[i] = counterClockwiseSequences[startPosition, i];
            }

            // 如果是顺时针，则反转序列（除了第一个元素保持不变）
            if (isClockwise)
            {
                Array.Reverse(sequence, 1, 3);
            }

            return sequence;
        }

        /// <summary>
        /// 获取边的方向向量 - 参考ProductChangedDialogViewModel的正确实现
        /// </summary>
        /// <param name="edge">边的索引 (1-4)</param>
        /// <param name="isClockwise">是否为顺时针方向</param>
        /// <returns>方向向量 (deltaX, deltaY)</returns>
        private (int deltaX, int deltaY) GetEdgeDirection(int edge, bool isClockwise)
        {
            if (!isClockwise) // 逆时针（默认模式）
            {
                switch (edge)
                {
                    case 1: return (0, 1);  // 上边：Y轴正方向
                    case 2: return (1, 0);  // 右边：X轴正方向  
                    case 3: return (0, -1); // 下边：Y轴负方向
                    case 4: return (-1, 0); // 左边：X轴负方向
                    default: return (0, 0);
                }
            }
            else // 顺时针
            {
                switch (edge)
                {
                    case 1: return (1, 0);   // 上边：X轴正方向
                    case 2: return (0, 1);   // 右边：Y轴正方向
                    case 3: return (-1, 0);  // 下边：X轴负方向
                    case 4: return (0, -1);  // 左边：Y轴负方向
                    default: return (0, 0);
                }
            }
        }

        /// <summary>
        /// 获取指定边的数据
        /// </summary>
        private (float x, float y, float z, float t, int pin, float step, float xMore, float yMore) GetEdgeData(int edge)
        {
            switch (edge)
            {
                case 1: return (CurrentData.X1, CurrentData.Y1, CurrentData.Z1, CurrentData.T1, CurrentData.Pin1, CurrentData.Step1, CurrentData.X1More, CurrentData.Y1More);
                case 2: return (CurrentData.X2, CurrentData.Y2, CurrentData.Z2, CurrentData.T2, CurrentData.Pin2, CurrentData.Step2, CurrentData.X2More, CurrentData.Y2More);
                case 3: return (CurrentData.X3, CurrentData.Y3, CurrentData.Z3, CurrentData.T3, CurrentData.Pin3, CurrentData.Step3, CurrentData.X3More, CurrentData.Y3More);
                case 4: return (CurrentData.X4, CurrentData.Y4, CurrentData.Z4, CurrentData.T4, CurrentData.Pin4, CurrentData.Step4, CurrentData.X4More, CurrentData.Y4More);
                default: return (0, 0, 0, 0, 0, 0, 0, 0);
            }
        }

        /// <summary>
        /// 设置指定边的数据
        /// </summary>
        private void SetEdgeData(int edge, float x, float y, float z, float t, int pin, float step)
        {
            switch (edge)
            {
                case 2:
                    CurrentData.X2 = x;
                    CurrentData.Y2 = y;
                    CurrentData.Z2 = z;
                    CurrentData.T2 = t;
                    CurrentData.Pin2 = pin;
                    CurrentData.Step2 = step;
                    break;
                case 3:
                    CurrentData.X3 = x;
                    CurrentData.Y3 = y;
                    CurrentData.Z3 = z;
                    CurrentData.T3 = t;
                    CurrentData.Pin3 = pin;
                    CurrentData.Step3 = step;
                    break;
                case 4:
                    CurrentData.X4 = x;
                    CurrentData.Y4 = y;
                    CurrentData.Z4 = z;
                    CurrentData.T4 = t;
                    CurrentData.Pin4 = pin;
                    CurrentData.Step4 = step;
                    break;
            }
        }

        private async Task<List<ObservableProcessModel>> GenerateAuotoData()
        {
            List<EquipmentModel> models = new List<EquipmentModel>();
            List<ObservableProcessModel> process = new List<ObservableProcessModel>();
            int index = 1;
            if (CurrentData.IsLinked5)
            {
                var equipment = new EquipmentModel()
                {
                    Id = Guid.NewGuid().ToString(),
                    ParamName = $"Mark-{Guid.NewGuid().ToString("B")}",
                    X = CurrentData.MarkX,
                    Y = CurrentData.MarkY,
                    R = 49F,
                    CreateTime = DateTime.Now,
                    Operator = _globalState.LoginUser.Account
                };
                models.Add(equipment);
                process.Add(new ObservableProcessModel()
                {
                    ParamName = "Mark点识别",
                    CameraType = Common.CameraTypeEnum.Child,
                    ProcessNumber = index,
                    EquipmentParaId = equipment.Id,
                    ProcessType = Common.ProcessTypeEnum.POINT
                });
                index++;
            }
            if (CurrentData.IsLinked1)
            {
                var equipment = new EquipmentModel()
                {
                    Id = Guid.NewGuid().ToString(),
                    ParamName = $"Lin1-{Guid.NewGuid().ToString("B")}",
                    X = CurrentData.X1,
                    Y = CurrentData.Y1,
                    Z = CurrentData.Z1,
                    T = CurrentData.T1,
                    R = CurrentData.R1,
                    CreateTime = DateTime.Now,
                    Operator = _globalState.LoginUser.Account
                };
                models.Add(equipment);
                process.Add(new ObservableProcessModel()
                {
                    ParamName = "Side1",
                    CameraType = Common.CameraTypeEnum.Main,
                    ProcessNumber = index,
                    EquipmentParaId = equipment.Id,
                    ProcessType = Common.ProcessTypeEnum.TAKEPICTURE
                });
                index++;
            }
            if (CurrentData.IsLinked2)
            {
                var equipment = new EquipmentModel()
                {
                    Id = Guid.NewGuid().ToString(),
                    ParamName = $"Lin2-{Guid.NewGuid().ToString("B")}",
                    X = CurrentData.X2,
                    Y = CurrentData.Y2,
                    Z = CurrentData.Z2,
                    T = CurrentData.T2,
                    R = CurrentData.R2,
                    CreateTime = DateTime.Now,
                    Operator = _globalState.LoginUser.Account
                };
                models.Add(equipment);
                process.Add(new ObservableProcessModel()
                {
                    ParamName = "Side2",
                    CameraType = Common.CameraTypeEnum.Main,
                    ProcessNumber = index,
                    EquipmentParaId = equipment.Id,
                    ProcessType = Common.ProcessTypeEnum.TAKEPICTURE
                });
                index++;
            }
            if (CurrentData.IsLinked3)
            {
                var equipment = new EquipmentModel()
                {
                    Id = Guid.NewGuid().ToString(),
                    ParamName = $"Lin3-{Guid.NewGuid().ToString("B")}",
                    X = CurrentData.X3,
                    Y = CurrentData.Y3,
                    Z = CurrentData.Z3,
                    T = CurrentData.T3,
                    R = CurrentData.R3,
                    CreateTime = DateTime.Now,
                    Operator = _globalState.LoginUser.Account
                };
                models.Add(equipment);
                process.Add(new ObservableProcessModel()
                {
                    ParamName = "Side3",
                    CameraType = Common.CameraTypeEnum.Main,
                    ProcessNumber = index,
                    EquipmentParaId = equipment.Id,
                    ProcessType = Common.ProcessTypeEnum.TAKEPICTURE
                });
                index++;
            }
            if (CurrentData.IsLinked4)
            {
                var equipment = new EquipmentModel()
                {
                    Id = Guid.NewGuid().ToString(),
                    ParamName = $"Lin4-{Guid.NewGuid().ToString("B")}",
                    X = CurrentData.X4,
                    Y = CurrentData.Y4,
                    Z = CurrentData.Z4,
                    T = CurrentData.T4,
                    R = CurrentData.R4,
                    CreateTime = DateTime.Now,
                    Operator = _globalState.LoginUser.Account
                };
                models.Add(equipment);
                process.Add(new ObservableProcessModel()
                {
                    ParamName = "Side4",
                    CameraType = Common.CameraTypeEnum.Main,
                    ProcessNumber = index,
                    EquipmentParaId = equipment.Id,
                    ProcessType = Common.ProcessTypeEnum.TAKEPICTURE
                });
            }

            await _equipmentService.AddRange(models);
            return process;
        }

        #endregion


        public IRelayCommand<string> ChangeCommand => new RelayCommand<string>((param) =>
        {
            switch (param)
            {
                case "0":
                    _optCameraControlViewModel.ScaleAX = _optCameraControlViewModel.ScaleAX == 1 ? -1 : 1;
                    _optCameraControlViewModel.ScaleAY = 1;
                    break;
                case "1":
                    _optCameraControlViewModel.ScaleBY = _optCameraControlViewModel.ScaleBY == 1 ? -1 : 1;
                    _optCameraControlViewModel.ScaleBX = 1;
                    break;
                case "2":
                    _optCameraControlViewModel.RotateAngle += 90;
                    break;
                case "3":
                    _optCameraControlViewModel.RotateAngle -= 90;
                    break;
                case "4":
                    _equipmentService.ResetError();
                    break;
            }
        });

        

      

        /// <summary>
        /// 计算达到目标速度所需的时间和距离
        /// </summary>
        /// <param name="acceleration">加速度（毫米/平方秒）</param>
        /// <param name="targetSpeed">目标速度（毫米/秒）</param>
        /// <returns>包含时间和距离的结果</returns>
        public (double Time, float Distance) CalculateTimeAndDistance(double acceleration, double targetSpeed)
        {
            if (acceleration <= 0)
                throw new ArgumentException("加速度必须大于 0");

            // 计算时间
            double time = targetSpeed / acceleration;

            // 计算位移
            float distance = (float)(0.5 * acceleration * Math.Pow(time, 2));

            return (time, distance);
        }


        public IRelayCommand<string> FocusCommand => new RelayCommand<string>((param) =>
        {
            switch (param)
            {
                case "1":
                    ShowMainCamera = false;
                    break;
                case "2":
                    ShowMainCamera = true;
                    break;
            }
        });

        /// <summary>
        /// 测试方法：验证自定义起点和方向的引脚计算逻辑
        /// </summary>
        public void TestPinCalculation()
        {
            // 测试用例1：左上角起点，逆时针方向（默认）
            CurrentData.PinStartPosition = PinStartPositionEnum.TopLeft;
            CurrentData.PinDirection = PinDirectionEnum.CounterClockwise;
            CurrentData.Pin1 = 5;
            CurrentData.Step1 = 2.0f;
            CurrentData.X1 = 10.0f;
            CurrentData.Y1 = 10.0f;
            CurrentData.X1More = 1.0f;
            CurrentData.Y1More = 1.0f;

            GenerateNextEdgeData(1);

            // 验证结果 - 逆时针：第1边(上边)→第2边(右边)
            var edge1Direction = GetEdgeDirection(1, false); // 上边：(0,1)
            var lastPinX = CurrentData.X1 + edge1Direction.deltaX * (CurrentData.Pin1 - 1) * Math.Abs(CurrentData.Step1);
            var lastPinY = CurrentData.Y1 + edge1Direction.deltaY * (CurrentData.Pin1 - 1) * Math.Abs(CurrentData.Step1);
            var expectedX2 = lastPinX + CurrentData.X1More;
            var expectedY2 = lastPinY + CurrentData.Y1More;

            System.Diagnostics.Debug.WriteLine($"=== Pin计算测试（修复后） ===");
            System.Diagnostics.Debug.WriteLine($"测试1 - 左上角逆时针:");
            System.Diagnostics.Debug.WriteLine($"第1边最后一个pin位置: ({lastPinX:F1}, {lastPinY:F1})");
            System.Diagnostics.Debug.WriteLine($"期望第2边起始位置: ({expectedX2:F1}, {expectedY2:F1})");
            System.Diagnostics.Debug.WriteLine($"实际第2边位置: X2={CurrentData.X2:F1}, Y2={CurrentData.Y2:F1}, Step2={CurrentData.Step2:F1}");
            System.Diagnostics.Debug.WriteLine($"计算是否正确: X匹配={Math.Abs(CurrentData.X2 - expectedX2) < 0.01f}, Y匹配={Math.Abs(CurrentData.Y2 - expectedY2) < 0.01f}");

            // 测试用例2：右上角起点，顺时针方向
            CurrentData.PinStartPosition = PinStartPositionEnum.TopRight;
            CurrentData.PinDirection = PinDirectionEnum.Clockwise;
            CurrentData.Pin1 = 5;
            CurrentData.Step1 = 2.0f;
            CurrentData.X1 = 10.0f;
            CurrentData.Y1 = 10.0f;
            CurrentData.X1More = 1.0f;
            CurrentData.Y1More = 1.0f;

            GenerateNextEdgeData(1);

            // 验证结果 - 顺时针：第1边(上边)→第4边(左边)
            var edge1DirectionCW = GetEdgeDirection(1, true); // 上边顺时针：(1,0)
            var lastPinXCW = CurrentData.X1 + edge1DirectionCW.deltaX * (CurrentData.Pin1 - 1) * Math.Abs(CurrentData.Step1);
            var lastPinYCW = CurrentData.Y1 + edge1DirectionCW.deltaY * (CurrentData.Pin1 - 1) * Math.Abs(CurrentData.Step1);
            var expectedX4 = lastPinXCW + CurrentData.X1More;
            var expectedY4 = lastPinYCW + CurrentData.Y1More;

            System.Diagnostics.Debug.WriteLine($"测试2 - 右上角顺时针:");
            System.Diagnostics.Debug.WriteLine($"第1边最后一个pin位置: ({lastPinXCW:F1}, {lastPinYCW:F1})");
            System.Diagnostics.Debug.WriteLine($"期望第4边起始位置: ({expectedX4:F1}, {expectedY4:F1})");
            System.Diagnostics.Debug.WriteLine($"实际第4边位置: X4={CurrentData.X4:F1}, Y4={CurrentData.Y4:F1}, Step4={CurrentData.Step4:F1}");
            System.Diagnostics.Debug.WriteLine($"计算是否正确: X匹配={Math.Abs(CurrentData.X4 - expectedX4) < 0.01f}, Y匹配={Math.Abs(CurrentData.Y4 - expectedY4) < 0.01f}");
            System.Diagnostics.Debug.WriteLine($"=============================");
        }

    }
}
