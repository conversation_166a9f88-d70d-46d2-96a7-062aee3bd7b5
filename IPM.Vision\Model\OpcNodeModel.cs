﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Model
{
    public class OpcNodeModel
    {
        public int Buzzer { get; set; }
        public int DisableBuzzer { get; set; }
        public int Restart { get; set; }
        public int Button1 { get; set; }
        public int Button2 { get; set; }
        public int CBeginMove { get; set; }
        public int CReset { get; set; }
        public int CXPoint { get; set; }
        public int CYPoint { get; set; }
        public int EError { get; set; }
        public int EMCStatus { get; set; } = 0;
        public int EResetError { get; set; }
        public int OAngle { get; set; }
        public int OBeginMove { get; set; }
        public int OReset { get; set; }
        public int RAngle { get; set; }
        public int RBeginMove { get; set; }
        public int RReset { get; set; }
        public int TAngle { get; set; }
        public int TBeginMove { get; set; }
        public int TReset { get; set; }
        public int ZBeginMove { get; set; }
        public int ZPoint { get; set; }
        public int ZReset { get; set; }
        public int WRestart {  get; set; }

        public int RPosition { get; set; }
        public int ZPosition { get; set; }
        public int XPosition { get; set; }
        public int YPosition { get; set; }
        public int TPosition { get;set; }

        public int XVelocity { get; set; }
        public int YVelocity { get; set; }

        public int XAcceleration { get; set; }
        public int YAcceleration { get; set; }

        public int XDcceleration { get; set; }

        public int YDcceleration { get; set; }

        public int PinLength { get; set; }
        public int CXPointArray { get; set; }
        public int CYPointArray { get; set; }
        public int AutoTake { get;set; }
        public int Suspend { get; set; }
    }
}
