﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class SetPropertyCommand : BasicCommand
    {
        private uint _propertyID;

        private object _data;

        public SetPropertyCommand(ref CanonCameraModel model, uint propertyID, object data)
            : base(ref model)
        {
            _propertyID = propertyID;
            _data = data;
        }

        public override bool Execute()
        {
            uint num = 0u;
            num = EDSDK.EdsSetPropertyData(_model.Camera, _propertyID, 0, Marshal.SizeOf(_data), _data);
            switch (num)
            {
                case 129u:
                    {
                        CameraEvent e2 = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e2);
                        return true;
                    }
                default:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e);
                        break;
                    }
                case 0u:
                    break;
            }

            return true;
        }
    }
}
