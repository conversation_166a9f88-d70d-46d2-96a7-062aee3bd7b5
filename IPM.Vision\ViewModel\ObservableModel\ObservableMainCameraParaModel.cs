﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableMainCameraParaModel:ViewModelBase
    {
        private string _id;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _paramName;
        public string ParamName
        {
            get => _paramName;
            set => SetProperty(ref _paramName, value);
        }

        private uint _ae;
        public uint Ae
        {
            get => _ae;
            set => SetProperty(ref _ae, value);
        }

        private uint _driveMode;
        public uint DriveMode
        {
            get => _driveMode;
            set => SetProperty(ref _driveMode, value);
        }

        private uint _av;
        public uint Av
        {
            get => _av;
            set => SetProperty(ref _av, value);
        }

        private uint _exposure;
        public uint Exposure
        {
            get => _exposure;
            set => SetProperty(ref _exposure, value);
        }

        private uint _afMode;
        public uint AfMode
        {
            get => _afMode;
            set => SetProperty(ref _afMode, value);
        }

        private uint _evfAfMode;
        public uint EvfAfMode
        {
            get => _evfAfMode;
            set => SetProperty(ref _evfAfMode, value);
        }

        private uint _iso;
        public uint Iso
        {
            get => _iso;
            set => SetProperty(ref _iso, value);
        }

        private uint _whiteBalance;
        public uint WhiteBalance
        {
            get => _whiteBalance;
            set => SetProperty(ref _whiteBalance, value);
        }

        private uint _tv;
        public uint Tv
        {
            get => _tv;
            set => SetProperty(ref _tv, value);
        }

        private uint _pictureStyle;
        public uint PictureStyle
        {
            get => _pictureStyle;
            set => SetProperty(ref _pictureStyle, value);
        }

        private uint _imageQuality;
        public uint ImageQuality
        {
            get => _imageQuality;
            set => SetProperty(ref _imageQuality, value);
        }

        private string _operator;
        public string Operator
        {
            get => _operator;
            set => SetProperty(ref _operator, value);
        }

        private DateTime _createTime;
        public DateTime CreateTime
        {
            get => _createTime;
            set => SetProperty(ref _createTime, value);
        }

    }
}
