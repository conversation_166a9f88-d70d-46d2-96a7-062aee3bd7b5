﻿using IPM.Vision.Camera.Com;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.Camera.EDSDKLib.Commands
{
    public class GetPropertyDescCommand : BasicCommand
    {
        private uint _propertyID;

        public GetPropertyDescCommand(ref CanonCameraModel model, uint propertyID)
            : base(ref model)
        {
            _propertyID = propertyID;
        }

        public override bool Execute()
        {
            uint num = 0u;
            num = GetPropertyDesc(_propertyID);
            switch (num)
            {
                case 41218u:
                    return false;
                case 129u:
                    {
                        CameraEvent e2 = new CameraEvent(CameraEvent.Type.DEVICE_BUSY, IntPtr.Zero);
                        _model.NotifyObservers(e2);
                        return false;
                    }
                default:
                    {
                        CameraEvent e = new CameraEvent(CameraEvent.Type.ERROR, (IntPtr)num);
                        _model.NotifyObservers(e);
                        break;
                    }
                case 0u:
                    break;
            }

            return true;
        }

        private uint GetPropertyDesc(uint propertyID)
        {
            uint num = 0u;
            EDSDK.EdsPropertyDesc outPropertyDesc = default(EDSDK.EdsPropertyDesc);
            if (propertyID == 65535)
            {
                if (num == 0)
                {
                    num = GetPropertyDesc(1078u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(1025u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(262u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(1030u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(1029u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(1026u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(1027u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(1031u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(256u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(1294u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(16778275u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(276u);
                }

                if (num == 0)
                {
                    num = GetPropertyDesc(16778333u);
                }

                return num;
            }

            if (num == 0)
            {
                num = EDSDK.EdsGetPropertyDesc(_model.Camera, propertyID, out outPropertyDesc);
            }

            if (num == 0)
            {
                _model.SetPropertyDesc(propertyID, ref outPropertyDesc);
            }

            if (num == 0)
            {
                CameraEvent e = new CameraEvent(CameraEvent.Type.PROPERTY_DESC_CHANGED, (IntPtr)propertyID);
                _model.NotifyObservers(e);
            }

            return num;
        }
    }
}
