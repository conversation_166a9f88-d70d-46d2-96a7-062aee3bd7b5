﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public partial class ObservableProductModel : ViewModelBase
    {
        private string _id;
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        private string _productNumber;
        public string ProductNumber
        {
            get => _productNumber;
            set => SetProperty(ref _productNumber, value);
        }

        private string _productName;
        public string ProductName
        {
            get => _productName;
            set => SetProperty(ref _productName, value);
        }

        private float _compHeight;
        public float CompHeight
        {
            get => _compHeight;
            set => SetProperty(ref _compHeight, value);
        }

        private string _operator;
        public string Operator
        {
            get => _operator;
            set => SetProperty(ref _operator, value);
        }

        private string _compInfoId;
        public string CompInfoId
        {
            get => _compInfoId;
            set => SetProperty(ref _compInfoId, value);
        }

        private string _checkModel;
        public string CheckModel
        {
            get => _checkModel;
            set => SetProperty(ref _checkModel, value);
        }

        private DateTime _createTime;
        public DateTime CreateTime
        {
            get => _createTime;
            set => SetProperty(ref _createTime, value);
        }

        private ObservableCollection<string> _processIds;
        public ObservableCollection<string> ProcessIds
        {
            get => _processIds;
            set => SetProperty(ref _processIds, value);
        }

        private ObservableCollection<ObservableProcessModel> _stepList;
        public ObservableCollection<ObservableProcessModel> StepList
        {
            get => _stepList;
            set => SetProperty(ref _stepList, value);
        }

        private ObservableCompModel _compModel;
        public ObservableCompModel CompModel
        {
            get => _compModel;
            set => SetProperty(ref _compModel, value);
        }

    }

}
