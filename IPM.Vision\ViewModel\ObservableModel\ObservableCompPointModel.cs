﻿using IPM.Vision.Common;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace IPM.Vision.ViewModel.ObservableModel
{
    public class ObservableCompPointModel : ViewModelBase
    {
        private string _id;
        public string Id
        {
            get => _id; 
            set => SetProperty(ref _id, value);
        }

        private string _compName;
        public string CompName
        {
            get => _compName;
            set => SetProperty(ref _compName, value);
        }

        private string _productParamId;
        public string ProductParamId
        {
            get => _productParamId;
            set => SetProperty(ref _productParamId, value);
        }

        private double _compWidth;
        public double CompWidth
        {
            get => _compWidth;
            set => SetProperty(ref _compWidth, value);
        }

        private double _compHeight;
        public double CompHeight
        {
            get => _compHeight;
            set => SetProperty(ref _compHeight, value);
        }

        private double _compX;
        public double CompX
        {
            get => _compX;
            set => SetProperty(ref _compX, value);
        }

        private double _compY;
        public double CompY
        {
            get => _compY;
            set => SetProperty(ref _compY, value);
        }

        private double _pictureX;
        public double PictureX
        {
            get => _pictureX;
            set => SetProperty(ref _pictureX, value);
        }

        private double _pictureY;
        public double PictureY
        {
            get => _pictureY;
            set => SetProperty(ref _pictureY, value);
        }

        private double _reelX;
        public double ReelX
        {
            get => _reelX;
            set => SetProperty(ref _reelX, value);
        }

        private double _reelY;
        public double ReelY
        {
            get => _reelY;
            set => SetProperty(ref _reelY, value);
        }

        private string _refNumber;
        public string RefNumber
        {
            get => _refNumber;
            set => SetProperty(ref _refNumber, value);
        }

        private int _pinNumber;
        public int PinNumber
        {
            get => _pinNumber;
            set => SetProperty(ref _pinNumber, value);
        }

        private int _xPinNumber;
        public int XPinNumber
        {
            get => _xPinNumber;
            set => SetProperty(ref _xPinNumber, value);
        }

        private int _yPinNumber;
        public int YPinNumber
        {
            get => _yPinNumber;
            set => SetProperty(ref _yPinNumber, value);
        }

        private double _pinFetch;
        public double PinFetch
        {
            get => _pinFetch;
            set => SetProperty(ref _pinFetch, value);
        }

        private PinTypeEnum _pinType = 0;
        public PinTypeEnum PinType
        {
            get => _pinType;
            set => SetProperty(ref _pinType, value);
        }

        private string _pinTypeName;
        public string PinTypeName
        {
            get => _pinTypeName;
            set => SetProperty(ref _pinTypeName, value);
        }
    }
}
