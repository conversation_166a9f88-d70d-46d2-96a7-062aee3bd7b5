# 海康相机连接方式修改总结

## 修改背景

原有的海康相机连接方式一直显示连接不上，需要改用更直接的连接方式，通过设备型号"MV-CS050-10GM"来匹配海康相机。

## 修改内容

### 1. GetFirstOrDefaultCamera方法 - 完全重写

**修改前**：调用统一的RefreshAndConnect方法
```csharp
public async void GetFirstOrDefaultCamera()
{
    AddLog("GetFirstOrDefaultCamera() 调用，转向统一连接流程");
    await Task.Run(() => RefreshAndConnect());
}
```

**修改后**：使用新的直接连接方式
```csharp
public async void GetFirstOrDefaultCamera()
{
    try
    {
        // 1. 检查Mark相机地址配置
        if (string.IsNullOrEmpty(_globalState.AppConfig.MarkCameraAddress)) return;
        
        // 2. 初始化SDK
        if (!_sdkInited) InitSDK();
        
        // 3. 枚举设备
        DeviceTLayerType enumType = DeviceTLayerType.MvGigEDevice | DeviceTLayerType.MvUsbDevice;
        HKIsOK = DeviceEnumerator.EnumDevices(enumType, out deviceInfoList);
        
        // 4. 查找指定型号的海康相机
        var markCamera = deviceInfoList.Where(item => item.ModelName == "MV-CS050-10GM").FirstOrDefault();
        
        if (markCamera != null)
        {
            // 5. 创建并打开设备
            if (_currentDevice == null) _currentDevice = DeviceFactory.CreateDevice(markCamera);
            if (!_currentDevice.IsConnected) HKIsOK = _currentDevice.Open();
            
            // 6. 配置千兆网参数
            if (_currentDevice is IGigEDevice)
            {
                int packetSize;
                HKIsOK = (_currentDevice as IGigEDevice).GetOptimalPacketSize(out packetSize);
                if (packetSize > 0)
                {
                    HKIsOK = _currentDevice.Parameters.SetIntValue("GevSCPSPacketSize", packetSize);
                }
            }
            
            // 7. 等待设备稳定
            await Task.Delay(1000);
            
            // 8. 设置采集模式和触发模式
            HKIsOK = _currentDevice.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");
            HKIsOK = _currentDevice.Parameters.SetEnumValueByString("TriggerMode", "Off");
            
            // 9. 设置连接状态并触发事件
            IsConnected = true;
            CameraConnectedEvent?.Invoke();
        }
    }
    catch (Exception ex)
    {
        AddLog($"连接海康相机异常: {ex.Message}", true);
        _logger.LogError(ex);
    }
}
```

### 2. RefreshAndConnect方法 - 简化重构

**修改前**：复杂的设备查找和IP地址匹配逻辑
- 使用FindTargetCamera()方法进行复杂的设备匹配
- 依赖IP地址配置进行设备识别
- 调用ConfigureDeviceParameters()进行复杂的参数配置
- 启动采集线程

**修改后**：直接的设备型号匹配
```csharp
public void RefreshAndConnect()
{
    try
    {
        // 1. 清理当前连接
        CompleteCleanup();
        
        // 2. 检查配置
        if (string.IsNullOrEmpty(_globalState.AppConfig.MarkCameraAddress)) return;
        
        // 3. 初始化SDK
        if (!_sdkInited) InitSDK();
        
        // 4. 枚举设备
        HKIsOK = DeviceEnumerator.EnumDevices(enumType, out deviceInfoList);
        
        // 5. 查找指定型号的海康相机
        var markCamera = deviceInfoList.Where(item => item.ModelName == "MV-CS050-10GM").FirstOrDefault();
        
        if (markCamera != null)
        {
            // 6. 创建并打开设备
            _currentDevice = DeviceFactory.CreateDevice(markCamera);
            HKIsOK = _currentDevice.Open();
            
            // 7. 简化的参数配置
            // 配置千兆网参数
            // 设置采集模式
            // 关闭触发模式
            
            // 8. 设置连接状态
            IsConnected = true;
            CameraConnectedEvent?.Invoke();
        }
    }
    catch (Exception ex)
    {
        AddLog($"海康相机连接异常: {ex.Message}", true);
        CompleteCleanup();
    }
}
```

### 3. 移除的复杂逻辑

1. **FindTargetCamera()方法**：不再使用复杂的设备查找逻辑
2. **IP地址匹配**：不再依赖IP地址进行设备识别
3. **ConfigureDeviceParameters()方法**：简化为直接的参数设置
4. **采集线程启动**：简化连接流程，专注于设备连接

## 新连接方式的优势

### 1. 简单直接
- 直接通过设备型号"MV-CS050-10GM"匹配
- 避免复杂的IP地址配置和验证
- 减少连接失败的可能性

### 2. 更可靠
- 设备型号匹配比IP地址匹配更准确
- 减少网络配置问题的影响
- 简化的参数配置减少出错概率

### 3. 易于维护
- 代码逻辑更清晰
- 减少了复杂的条件判断
- 错误处理更直接

## 关键配置参数

### 1. 设备型号匹配
```csharp
var markCamera = deviceInfoList.Where(item => item.ModelName == "MV-CS050-10GM").FirstOrDefault();
```

### 2. 千兆网参数配置
```csharp
if (_currentDevice is IGigEDevice)
{
    int packetSize;
    HKIsOK = (_currentDevice as IGigEDevice).GetOptimalPacketSize(out packetSize);
    if (packetSize > 0)
    {
        HKIsOK = _currentDevice.Parameters.SetIntValue("GevSCPSPacketSize", packetSize);
    }
}
```

### 3. 采集模式设置
```csharp
// 连续采集模式
HKIsOK = _currentDevice.Parameters.SetEnumValueByString("AcquisitionMode", "Continuous");

// 关闭触发模式
HKIsOK = _currentDevice.Parameters.SetEnumValueByString("TriggerMode", "Off");
```

## 使用说明

1. **确保设备型号正确**：当前代码硬编码为"MV-CS050-10GM"，如果使用其他型号需要修改
2. **检查Mark相机地址配置**：虽然不再用于IP匹配，但仍需要配置以启用Mark相机功能
3. **管理员权限**：海康相机连接可能需要管理员权限

## 注意事项

1. **设备型号硬编码**：当前使用固定的设备型号"MV-CS050-10GM"
2. **错误处理**：保留了完整的错误处理和日志记录
3. **兼容性**：新的连接方式与现有的GlobalCameraManager架构兼容

这种新的连接方式应该能够解决之前连接不上的问题，提供更稳定可靠的海康相机连接。
